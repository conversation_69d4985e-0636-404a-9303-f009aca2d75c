## Tasks
- [x] run cypher editor locally
## Ref
- [ANTLR Lab: learn, test, and experiment with ANTLR grammars online!](http://lab.antlr.org/)
## 如何用antlr4生成JS parser
- ref: [Fetching Title#vhab](https://github.com/antlr/antlr4/blob/master/doc/javascript-target.md)
### Notes
1. 生成JS parser code
```
$ antlr4 -Dlanguage=JavaScript MyGrammar.g4
```
2.  install antlr JS runtime: [antlr4 - npm](https://www.npmjs.com/package/antlr4)
3. 运行
```js
import antlr4 from 'antlr4';
import MyGrammarLexer from './MyGrammarLexer.js';
import MyGrammarParser from './MyGrammarParser.js';
import MyGrammarListener from './MyGrammarListener.js';

const input = "your text to parse here"
const chars = new antlr4.InputStream(input);
const lexer = new MyGrammarLexer(chars);
const tokens = new antlr4.CommonTokenStream(lexer);
const parser = new MyGrammarParser(tokens);
const tree = parser.MyStartRule();
```
## Integrate antlr4 grammar with codemirror6
### Ref
- [Syntax highlighting with ANTLR grammar - v6 - discuss.CodeMirror](https://discuss.codemirror.net/t/syntax-highlighting-with-antlr-grammar/5981)
```
We don’t have a way to generate error-tolerant, incremental parsers with ANTLR.
```
### [Connecting ANTLR to CodeMirror 6: Building a Language Server \| The Trevor Harmon](https://thetrevorharmon.com/blog/connecting-antlr-to-codemirror-6-building-a-language-server/)
- define all of the token types that the language server supports and set up a mapping between the Lexer’s types and our typescript types:
```typescript
const supportedTokens = [
  'const',
  'let',
  'semicolon',
  'assign',
  'blockComment',
  'lineComment',
  'number',
  'string',
  'identifier',
  'unknown',
] as const;

export type Token = (typeof supportedTokens)[number];

```
```typescript
import {ZephyrLexer} from './antlr/ZephyrLexer';

const lexerTokenToTokenLookup: {[key: number]: Token} = {
  [ZephyrLexer.CONST]: 'const',
  [ZephyrLexer.LET]: 'let',
  [ZephyrLexer.SEMICOLON]: 'semicolon',
  [ZephyrLexer.ASSIGN]: 'assign',
  [ZephyrLexer.BLOCK_COMMENT]: 'blockComment',
  [ZephyrLexer.LINE_COMMENT]: 'lineComment',
  [ZephyrLexer.NUMBER]: 'number',
  [ZephyrLexer.STRING]: 'string',
  [ZephyrLexer.IDENTIFIER]: 'identifier',
};
```
- 生成token stream
```typescript
const languageServer = new LanguageServer();
const tokens = languageServer
  .getTokenStream(`const tokens = 'are great!';`)
  .map((token) => ({
    index: token.tokenIndex,
    range: [token.startIndex, token.stopIndex],
    type: languageServer.getTokenTypeForIndex(token.type),
  }));
```
 - A Lezer parse tree is made up of a collection of nodes, and Lezer represents those nodes with the [`NodeType`](https://lezer.codemirror.net/docs/ref/#common.NodeType) class. It isn't a node directly, but a typed description of a node.
 -  We provide Lezer information about what node types exist by passing it a [`NodeSet`](https://lezer.codemirror.net/docs/ref/#common.NodeSet)
 - we need to create a mapping between our language server's token types and Lezer node types
```typescript
import {NodeType} from '@lezer/common';

export const tokenToNodeType: {[key in Token | 'document']: NodeType} = {
  document: NodeType.define({id: 0, name: 'document', top: true}),
  const: NodeType.define({id: 1, name: 'const'}),
  let: NodeType.define({id: 2, name: 'let'}),
  semicolon: NodeType.define({id: 3, name: 'semicolon'}),
  assign: NodeType.define({id: 4, name: 'assign'}),
  number: NodeType.define({id: 5, name: 'number'}),
  string: NodeType.define({id: 6, name: 'string'}),
  identifier: NodeType.define({id: 7, name: 'identifier'}),
  unknown: NodeType.define({id: 8, name: 'unknown'}),
  blockComment: NodeType.define({id: 9, name: 'blockComment'}),
  lineComment: NodeType.define({id: 10, name: 'lineComment'}),
};
```
- 实现一个解析器适配器。继承`Parser` 类，实现`startParse`等函数
	- 将token stream转换成lezer可理解的缓冲区，lezer用之构造tree
	- 缓冲区使用postfix order
- 定义Language和LanguageSupport，定义highlight
## Cypher-Editor
- How to configure codemirror?
- EditorSupport