# 🏠 Dashboard

> [!info]+ Welcome to Your Knowledge Vault
> Today is `=date(today)` | Last modified: `=this.file.mtime`

```meta-bind-button
style: primary
label: 📝 Open Daily Note
hidden: false
id: daily-note
action: { "type": "command", "command": "daily-notes" }
```
---
> [!multi-column]
>
>> [!note]+ Important Notes
>> - [[Depression|🧠 Depression]]
>> - [[资源Resources|📚 Resources]]
>> - [[灵感Ideas|💡 Ideas]]
>> - [[人生观Life lessons|🌱 Life Lessons]]
>> - [[Inbox\|📥 Inbox]]
>
>> [!tip]+ 🎯 Make Progress
>> - [[projects_index|📋 Projects Index]]
>> - [[guitar_practice|🎸 Guitar Practice]]
>> - [[meditation_practice|🧘 Meditation Practice]]
>> - [[碎片知识News|📰 碎片News]]

---

## 🚀 Active Projects
```dataview
TABLE
  category as Category,
  choice(progress != null AND target != null AND target != 0, round((progress / target) * 100) + "%", "0%") as Progress,
  started as Started
FROM #project OR #course OR #book
WHERE !contains(file.path, "Template/") AND (status = "🔄 In Progress" OR contains(status, "In Progress"))
SORT file.mtime DESC
```

---

> [!multi-column]
>
>> [!note]+ TG Unscheduled 
>> ```tasks
>> not done
>> path includes TG
>> no scheduled date
>> no due date
>> show tree
>> shortmode
>> ```
>
>> [!example]+ 📋 TG Tasks
>> ```tasks
>> (not done) OR (done today)
>> path includes TG
>> (has scheduled date) OR (has due date)
>> show tree
>> shortmode
>> ```

---

## 🗂️ Vault Statistics
- **Total Notes**: `$=dv.pages().length`
- **Notes This Week**: `$=dv.pages().where(p => p.file.ctime >= dv.date('today') - dv.duration('7 days')).length`
- **Orphaned Notes**: `$=dv.pages().where(p => p.file.inlinks.length === 0 && p.file.outlinks.length === 0).length`

---