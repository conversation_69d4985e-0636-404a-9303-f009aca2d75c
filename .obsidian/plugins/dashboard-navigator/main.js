/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/main.ts
var main_exports = {};
__export(main_exports, {
  DEFAULT_SETTINGS: () => DEFAULT_SETTINGS,
  default: () => DNPlugin
});
module.exports = __toCommonJS(main_exports);
var import_obsidian4 = require("obsidian");

// src/settings.ts
var import_obsidian = require("obsidian");
var DNSettingTab = class extends import_obsidian.PluginSettingTab {
  constructor(app, plugin) {
    super(app, plugin);
    this.plugin = plugin;
  }
  display() {
    const { containerEl } = this;
    containerEl.empty();
    new import_obsidian.Setting(containerEl).setName("Open dashboard navigator").addButton((btn) => {
      btn.setButtonText("Open").setTooltip("Open dashboard navigator").onClick((evt) => {
        this.plugin.DN_MODAL.open();
      });
    });
    new import_obsidian.Setting(containerEl).setName("Default view").setDesc("Select view when opening window").addDropdown((sel) => {
      this.dropdownDefaultView = sel;
      sel.addOption("1", "Dashboard");
      sel.addOption("2", "Navigator");
      sel.onChange(async (val) => {
        this.plugin.settings.default_view = parseInt(val);
        this.plugin.DN_MODAL.default_view = this.plugin.settings.default_view;
        await this.plugin.saveSettings();
      }), sel.setValue(this.plugin.settings.default_view.toString());
    }).addExtraButton((btn) => {
      btn.setIcon("rotate-ccw");
      btn.setTooltip("Restore default");
      btn.onClick(() => {
        this.dropdownDefaultView.setValue(DEFAULT_SETTINGS.default_view.toString());
        this.plugin.settings.default_view = DEFAULT_SETTINGS.default_view;
        this.plugin.DN_MODAL.default_view = this.plugin.settings.default_view;
        this.plugin.saveSettings();
      });
    });
    new import_obsidian.Setting(containerEl).setName("Navigator table layout").setDesc("Select table results layout").addDropdown((sel) => {
      this.dropdownTableLayout = sel;
      sel.addOption("dn-tbl-default", "Default");
      sel.addOption("dn-tbl-row", "Row striped");
      sel.addOption("dn-tbl-column", "Column striped");
      sel.addOption("dn-tbl-bordered", "Bordered");
      sel.onChange(async (val) => {
        this.plugin.settings.selected_table_layout = val;
        this.plugin.DN_MODAL.selected_table_layout = this.plugin.settings.selected_table_layout;
        await this.plugin.saveSettings();
      }), sel.setValue(this.plugin.settings.selected_table_layout.toString());
    }).addExtraButton((btn) => {
      btn.setIcon("rotate-ccw");
      btn.setTooltip("Restore default");
      btn.onClick(() => {
        this.dropdownTableLayout.setValue(DEFAULT_SETTINGS.selected_table_layout.toString());
        this.plugin.settings.selected_table_layout = DEFAULT_SETTINGS.selected_table_layout;
        this.plugin.DN_MODAL.selected_table_layout = this.plugin.settings.selected_table_layout;
        this.plugin.saveSettings();
      });
    });
    new import_obsidian.Setting(containerEl).setName("Date format").setDesc("Select date format").addDropdown((sel) => {
      this.dropdownDateFormat = sel;
      sel.addOption("YYYY-MM-DD HH:mm:ss", "YYYY-MM-DD HH:mm:ss");
      sel.addOption("YYYY-MM-DD HH:mm", "YYYY-MM-DD HH:mm");
      sel.addOption("YYYY-MM-DD", "YYYY-MM-DD");
      sel.addOption("DD/MM/YYYY HH:mm:ss", "DD/MM/YYYY HH:mm:ss");
      sel.addOption("DD/MM/YYYY HH:mm", "DD/MM/YYYY HH:mm");
      sel.addOption("DD/MM/YYYY", "DD/MM/YYYY");
      sel.onChange(async (val) => {
        this.plugin.settings.date_format = val;
        this.plugin.DN_MODAL.date_format = this.plugin.settings.date_format;
        await this.plugin.saveSettings();
      }), sel.setValue(this.plugin.settings.date_format.toString());
    }).addExtraButton((btn) => {
      btn.setIcon("rotate-ccw");
      btn.setTooltip("Restore default");
      btn.onClick(() => {
        this.dropdownDateFormat.setValue(DEFAULT_SETTINGS.date_format.toString());
        this.plugin.settings.date_format = DEFAULT_SETTINGS.date_format;
        this.plugin.DN_MODAL.date_format = this.plugin.settings.date_format;
        this.plugin.saveSettings();
      });
    });
    new import_obsidian.Setting(containerEl).setName("Font size").setDesc("Select font size in pixels for results and links").addSlider((sli) => {
      this.sliderFontSize = sli;
      let slider_val;
      if (this.plugin.settings.font_size) {
        slider_val = this.plugin.settings.font_size;
      } else {
        slider_val = DEFAULT_SETTINGS.font_size;
      }
      sli.setDynamicTooltip();
      sli.setLimits(12, 24, 1);
      sli.setValue(slider_val);
      sli.onChange((val) => {
        this.plugin.settings.font_size = val;
        this.plugin.dnSetFontSize(val);
        this.plugin.saveSettings();
      });
    }).addExtraButton((btn) => {
      btn.setIcon("rotate-ccw");
      btn.setTooltip("Restore default");
      btn.onClick(() => {
        this.sliderFontSize.setValue(DEFAULT_SETTINGS.font_size);
        this.plugin.settings.font_size = DEFAULT_SETTINGS.font_size;
        this.plugin.dnSetFontSize(this.plugin.settings.font_size);
        this.plugin.saveSettings();
      });
    });
    new import_obsidian.Setting(containerEl).setName("Files per page").setDesc("Number of results per page").addDropdown((sel) => {
      this.dropdownFilesPerPage = sel;
      sel.addOption("10", "10");
      sel.addOption("20", "20");
      sel.addOption("30", "30");
      sel.addOption("50", "50");
      sel.addOption("100", "100");
      sel.onChange(async (val) => {
        this.plugin.settings.files_per_page = parseInt(val);
        this.plugin.DN_MODAL.files_per_page = this.plugin.settings.files_per_page;
        await this.plugin.saveSettings();
      }), sel.setValue(this.plugin.settings.files_per_page.toString());
    }).addExtraButton((btn) => {
      btn.setIcon("rotate-ccw");
      btn.setTooltip("Restore default");
      btn.onClick(() => {
        this.dropdownFilesPerPage.setValue(DEFAULT_SETTINGS.files_per_page.toString());
        this.plugin.settings.files_per_page = DEFAULT_SETTINGS.files_per_page;
        this.plugin.DN_MODAL.files_per_page = this.plugin.settings.files_per_page;
        this.plugin.saveSettings();
      });
    });
    new import_obsidian.Setting(containerEl).setName("Recent files").setDesc("Number of recent files per category").addDropdown((sel) => {
      this.dropdownRecentFiles = sel;
      sel.addOption("3", "3");
      sel.addOption("4", "4");
      sel.addOption("5", "5");
      sel.addOption("10", "10");
      sel.onChange(async (val) => {
        this.plugin.settings.num_recent_files = parseInt(val);
        this.plugin.DN_MODAL.num_recent_files = this.plugin.settings.num_recent_files;
        await this.plugin.saveSettings();
      }), sel.setValue(this.plugin.settings.num_recent_files.toString());
    }).addExtraButton((btn) => {
      btn.setIcon("rotate-ccw");
      btn.setTooltip("Restore default");
      btn.onClick(() => {
        this.dropdownRecentFiles.setValue(DEFAULT_SETTINGS.num_recent_files.toString());
        this.plugin.settings.num_recent_files = DEFAULT_SETTINGS.num_recent_files;
        this.plugin.DN_MODAL.num_recent_files = this.plugin.settings.num_recent_files;
        this.plugin.saveSettings();
      });
    });
    new import_obsidian.Setting(containerEl).setName("Hide column: Ext").setDesc("Navigator: Hide file extension column").addToggle((toggle) => {
      this.toggleHideExtColumn = toggle;
      toggle.setValue(this.plugin.settings.hide_ext).onChange(async (val) => {
        this.plugin.settings.hide_ext = val;
        this.plugin.dnUpdateHideColumn("ext", val);
        await this.plugin.saveSettings();
      });
    }).addExtraButton((btn) => {
      btn.setIcon("rotate-ccw");
      btn.setTooltip("Restore default");
      btn.onClick(() => {
        this.toggleHideExtColumn.setValue(DEFAULT_SETTINGS.hide_ext);
        this.plugin.settings.hide_ext = DEFAULT_SETTINGS.hide_ext;
        this.plugin.dnUpdateHideColumn("ext", DEFAULT_SETTINGS.hide_ext);
        this.plugin.saveSettings();
      });
    });
    new import_obsidian.Setting(containerEl).setName("Hide column: Path").setDesc("Navigator: Hide path column").addToggle((toggle) => {
      this.toggleHidePathColumn = toggle;
      toggle.setValue(this.plugin.settings.hide_path).onChange(async (val) => {
        this.plugin.settings.hide_path = val;
        this.plugin.dnUpdateHideColumn("path", val);
        await this.plugin.saveSettings();
      });
    }).addExtraButton((btn) => {
      btn.setIcon("rotate-ccw");
      btn.setTooltip("Restore default");
      btn.onClick(() => {
        this.toggleHidePathColumn.setValue(DEFAULT_SETTINGS.hide_path);
        this.plugin.settings.hide_path = DEFAULT_SETTINGS.hide_path;
        this.plugin.dnUpdateHideColumn("path", DEFAULT_SETTINGS.hide_path);
        this.plugin.saveSettings();
      });
    });
    new import_obsidian.Setting(containerEl).setName("Hide column: Size").setDesc("Navigator: Hide size column").addToggle((toggle) => {
      this.toggleHideSizeColumn = toggle;
      toggle.setValue(this.plugin.settings.hide_size).onChange(async (val) => {
        this.plugin.settings.hide_size = val;
        this.plugin.dnUpdateHideColumn("size", val);
        await this.plugin.saveSettings();
      });
    }).addExtraButton((btn) => {
      btn.setIcon("rotate-ccw");
      btn.setTooltip("Restore default");
      btn.onClick(() => {
        this.toggleHideSizeColumn.setValue(DEFAULT_SETTINGS.hide_size);
        this.plugin.settings.hide_size = DEFAULT_SETTINGS.hide_size;
        this.plugin.dnUpdateHideColumn("size", DEFAULT_SETTINGS.hide_size);
        this.plugin.saveSettings();
      });
    });
    new import_obsidian.Setting(containerEl).setName("Hide column: Date").setDesc("Navigator: Hide date column").addToggle((toggle) => {
      this.toggleHideDateColumn = toggle;
      toggle.setValue(this.plugin.settings.hide_date).onChange(async (val) => {
        this.plugin.settings.hide_date = val;
        this.plugin.dnUpdateHideColumn("date", val);
        await this.plugin.saveSettings();
      });
    }).addExtraButton((btn) => {
      btn.setIcon("rotate-ccw");
      btn.setTooltip("Restore default");
      btn.onClick(() => {
        this.toggleHideDateColumn.setValue(DEFAULT_SETTINGS.hide_date);
        this.plugin.settings.hide_date = DEFAULT_SETTINGS.hide_date;
        this.plugin.dnUpdateHideColumn("date", DEFAULT_SETTINGS.hide_date);
        this.plugin.saveSettings();
      });
    });
    new import_obsidian.Setting(containerEl).setName("Hide column: Tags").setDesc("Navigator: Hide tags column").addToggle((toggle) => {
      this.toggleHideTagsColumn = toggle;
      toggle.setValue(this.plugin.settings.hide_tags).onChange(async (val) => {
        this.plugin.settings.hide_tags = val;
        this.plugin.dnUpdateHideColumn("tags", val);
        await this.plugin.saveSettings();
      });
    }).addExtraButton((btn) => {
      btn.setIcon("rotate-ccw");
      btn.setTooltip("Restore default");
      btn.onClick(() => {
        this.toggleHideTagsColumn.setValue(DEFAULT_SETTINGS.hide_tags);
        this.plugin.settings.hide_tags = DEFAULT_SETTINGS.hide_tags;
        this.plugin.dnUpdateHideColumn("tags", DEFAULT_SETTINGS.hide_tags);
        this.plugin.saveSettings();
      });
    });
    new import_obsidian.Setting(containerEl).setName("Hide column: Frontmatter").setDesc("Navigator: Hide frontmatter properties column").addToggle((toggle) => {
      this.toggleHideFrontmatterColumn = toggle;
      toggle.setValue(this.plugin.settings.hide_frontmatter).onChange(async (val) => {
        this.plugin.settings.hide_frontmatter = val;
        this.plugin.dnUpdateHideColumn("frontmatter", val);
        await this.plugin.saveSettings();
      });
    }).addExtraButton((btn) => {
      btn.setIcon("rotate-ccw");
      btn.setTooltip("Restore default");
      btn.onClick(() => {
        this.toggleHideFrontmatterColumn.setValue(DEFAULT_SETTINGS.hide_frontmatter);
        this.plugin.settings.hide_frontmatter = DEFAULT_SETTINGS.hide_frontmatter;
        this.plugin.dnUpdateHideColumn("frontmatter", DEFAULT_SETTINGS.hide_frontmatter);
        this.plugin.saveSettings();
      });
    });
    new import_obsidian.Setting(containerEl).setName("Excluded file extensions").setDesc("File extensions to exclude, separated by commas").addText((text) => {
      this.textExcludedExtensions = text;
      text.setPlaceholder("File extensions to exclude").setValue(this.plugin.settings.excluded_ext).onChange(async (val) => {
        this.plugin.settings.excluded_ext = val;
        this.plugin.DN_MODAL.excluded_extensions = this.plugin.dnGetExcludedExtensions(val);
        await this.plugin.saveSettings();
      });
    }).addExtraButton((btn) => {
      btn.setIcon("rotate-ccw");
      btn.setTooltip("Restore default");
      btn.onClick(() => {
        this.textExcludedExtensions.setValue("");
        this.plugin.settings.excluded_ext = "";
        this.plugin.DN_MODAL.excluded_extensions = [];
        this.plugin.saveSettings();
      });
    });
    new import_obsidian.Setting(containerEl).setName("Excluded folders").setDesc("List of folder paths to exclude, separated by commas").addText((text) => {
      this.textExcludedFolders = text;
      text.setPlaceholder("Folder paths to exclude").setValue(this.plugin.settings.excluded_path).onChange(async (val) => {
        this.plugin.settings.excluded_path = val;
        this.plugin.DN_MODAL.excluded_folders = this.plugin.dnGetExcludedFolders(val);
        await this.plugin.saveSettings();
      });
    }).addExtraButton((btn) => {
      btn.setIcon("rotate-ccw");
      btn.setTooltip("Restore default");
      btn.onClick(() => {
        this.textExcludedFolders.setValue("");
        this.plugin.settings.excluded_path = "";
        this.plugin.DN_MODAL.excluded_folders = [];
        this.plugin.saveSettings();
      });
    });
    new import_obsidian.Setting(containerEl).setName("Toggle colored files").setDesc("Turn on/off colored files").addToggle((toggle) => {
      this.toggleColoredFiles = toggle;
      toggle.setValue(this.plugin.settings.colored_files).onChange(async (val) => {
        this.plugin.settings.colored_files = val;
        this.plugin.DN_MODAL.colored_files = val;
        this.plugin.DN_MODAL.dnToggleColoredFiles();
        await this.plugin.saveSettings();
      });
    }).addExtraButton((btn) => {
      btn.setIcon("rotate-ccw");
      btn.setTooltip("Restore default");
      btn.onClick(() => {
        this.toggleColoredFiles.setValue(DEFAULT_SETTINGS.colored_files);
        this.plugin.settings.colored_files = DEFAULT_SETTINGS.colored_files;
        this.plugin.DN_MODAL.colored_files = DEFAULT_SETTINGS.colored_files;
        this.plugin.DN_MODAL.dnToggleColoredFiles();
        this.plugin.saveSettings();
      });
    });
    new import_obsidian.Setting(containerEl).setName("Color: Notes").setDesc("Color of notes").addColorPicker((color) => {
      this.colorCompNotes = color;
      color.setValue(this.plugin.settings.color_notes).onChange(async (val) => {
        this.plugin.settings.color_notes = val;
        this.plugin.DN_MODAL.color_notes = val;
        this.plugin.DN_MODAL.dnSetCustomColors();
        await this.plugin.saveSettings();
      });
    }).addExtraButton((btn) => {
      btn.setIcon("rotate-ccw");
      btn.setTooltip("Restore default");
      btn.onClick(() => {
        this.colorCompNotes.setValue(DEFAULT_SETTINGS.color_notes);
        this.plugin.settings.color_notes = DEFAULT_SETTINGS.color_notes;
        this.plugin.DN_MODAL.color_notes = DEFAULT_SETTINGS.color_notes;
        this.plugin.DN_MODAL.dnSetCustomColors();
        this.plugin.saveSettings();
      });
    });
    new import_obsidian.Setting(containerEl).setName("Color: Canvas").setDesc("Color of canvas").addColorPicker((color) => {
      this.colorCompCanvas = color;
      color.setValue(this.plugin.settings.color_canvas).onChange(async (val) => {
        this.plugin.settings.color_canvas = val;
        this.plugin.DN_MODAL.color_canvas = val;
        this.plugin.DN_MODAL.dnSetCustomColors();
        await this.plugin.saveSettings();
      });
    }).addExtraButton((btn) => {
      btn.setIcon("rotate-ccw");
      btn.setTooltip("Restore default");
      btn.onClick(() => {
        this.colorCompCanvas.setValue(DEFAULT_SETTINGS.color_canvas);
        this.plugin.settings.color_canvas = DEFAULT_SETTINGS.color_canvas;
        this.plugin.DN_MODAL.color_canvas = DEFAULT_SETTINGS.color_canvas;
        this.plugin.DN_MODAL.dnSetCustomColors();
        this.plugin.saveSettings();
      });
    });
    new import_obsidian.Setting(containerEl).setName("Color: Images").setDesc("Color of images").addColorPicker((color) => {
      this.colorCompImages = color;
      color.setValue(this.plugin.settings.color_images).onChange(async (val) => {
        this.plugin.settings.color_images = val;
        this.plugin.DN_MODAL.color_images = val;
        this.plugin.DN_MODAL.dnSetCustomColors();
        await this.plugin.saveSettings();
      });
    }).addExtraButton((btn) => {
      btn.setIcon("rotate-ccw");
      btn.setTooltip("Restore default");
      btn.onClick(() => {
        this.colorCompImages.setValue(DEFAULT_SETTINGS.color_images);
        this.plugin.settings.color_images = DEFAULT_SETTINGS.color_images;
        this.plugin.DN_MODAL.color_images = DEFAULT_SETTINGS.color_images;
        this.plugin.DN_MODAL.dnSetCustomColors();
        this.plugin.saveSettings();
      });
    });
    new import_obsidian.Setting(containerEl).setName("Color: Videos").setDesc("Color of videos").addColorPicker((color) => {
      this.colorCompVideos = color;
      color.setValue(this.plugin.settings.color_videos).onChange(async (val) => {
        this.plugin.settings.color_videos = val;
        this.plugin.DN_MODAL.color_videos = val;
        this.plugin.DN_MODAL.dnSetCustomColors();
        await this.plugin.saveSettings();
      });
    }).addExtraButton((btn) => {
      btn.setIcon("rotate-ccw");
      btn.setTooltip("Restore default");
      btn.onClick(() => {
        this.colorCompVideos.setValue(DEFAULT_SETTINGS.color_videos);
        this.plugin.settings.color_videos = DEFAULT_SETTINGS.color_videos;
        this.plugin.DN_MODAL.color_videos = DEFAULT_SETTINGS.color_videos;
        this.plugin.DN_MODAL.dnSetCustomColors();
        this.plugin.saveSettings();
      });
    });
    new import_obsidian.Setting(containerEl).setName("Color: Audios").setDesc("Color of audios").addColorPicker((color) => {
      this.colorCompAudios = color;
      color.setValue(this.plugin.settings.color_audios).onChange(async (val) => {
        this.plugin.settings.color_audios = val;
        this.plugin.DN_MODAL.color_audios = val;
        this.plugin.DN_MODAL.dnSetCustomColors();
        await this.plugin.saveSettings();
      });
    }).addExtraButton((btn) => {
      btn.setIcon("rotate-ccw");
      btn.setTooltip("Restore default");
      btn.onClick(() => {
        this.colorCompAudios.setValue(DEFAULT_SETTINGS.color_audios);
        this.plugin.settings.color_audios = DEFAULT_SETTINGS.color_audios;
        this.plugin.DN_MODAL.color_audios = DEFAULT_SETTINGS.color_audios;
        this.plugin.DN_MODAL.dnSetCustomColors();
        this.plugin.saveSettings();
      });
    });
    new import_obsidian.Setting(containerEl).setName("Color: PDF").setDesc("Color of PDF files").addColorPicker((color) => {
      this.colorCompPdf = color;
      color.setValue(this.plugin.settings.color_pdf).onChange(async (val) => {
        this.plugin.settings.color_pdf = val;
        this.plugin.DN_MODAL.color_pdf = val;
        this.plugin.DN_MODAL.dnSetCustomColors();
        await this.plugin.saveSettings();
      });
    }).addExtraButton((btn) => {
      btn.setIcon("rotate-ccw");
      btn.setTooltip("Restore default");
      btn.onClick(() => {
        this.colorCompPdf.setValue(DEFAULT_SETTINGS.color_pdf);
        this.plugin.settings.color_pdf = DEFAULT_SETTINGS.color_pdf;
        this.plugin.DN_MODAL.color_pdf = DEFAULT_SETTINGS.color_pdf;
        this.plugin.DN_MODAL.dnSetCustomColors();
        this.plugin.saveSettings();
      });
    });
    new import_obsidian.Setting(containerEl).setName("Color: Other files").setDesc("Color of other files").addColorPicker((color) => {
      this.colorCompOther = color;
      color.setValue(this.plugin.settings.color_other).onChange(async (val) => {
        this.plugin.settings.color_other = val;
        this.plugin.DN_MODAL.color_other = val;
        this.plugin.DN_MODAL.dnSetCustomColors();
        await this.plugin.saveSettings();
      });
    }).addExtraButton((btn) => {
      btn.setIcon("rotate-ccw");
      btn.setTooltip("Restore default");
      btn.onClick(() => {
        this.colorCompOther.setValue(DEFAULT_SETTINGS.color_other);
        this.plugin.settings.color_other = DEFAULT_SETTINGS.color_other;
        this.plugin.DN_MODAL.color_other = DEFAULT_SETTINGS.color_other;
        this.plugin.DN_MODAL.dnSetCustomColors();
        this.plugin.saveSettings();
      });
    });
  }
};

// src/dn.ts
var import_obsidian2 = require("obsidian");

// src/utils/format.ts
function formatFileSize(fileSize) {
  if (fileSize === 0) {
    return "0";
  }
  const formattedSize = fileSize.toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  return formattedSize;
}
function getFolderStructure(folder) {
  const folders = folder.split("/").slice(0, -1);
  if (folders.length === 0) {
    return "/";
  }
  return "/" + folders.join("/");
}
function formatFileSizeKBMB(total_bytes) {
  if (total_bytes < 1024) {
    return "";
  } else if (total_bytes < 1048576) {
    return ` (${(total_bytes / 1024).toFixed(2)} KB)`;
  } else {
    return ` (${(total_bytes / (1024 * 1024)).toFixed(2)} MB)`;
  }
}

// src/utils/tags.ts
function getTagsPerFile(file) {
  var _a, _b;
  const cur_file = this.app.vault.getAbstractFileByPath(file.path);
  if (cur_file !== null) {
    const tags = (_a = this.app.metadataCache.getFileCache(file)) == null ? void 0 : _a.tags;
    const frontmatter_tags = (_b = this.app.metadataCache.getFileCache(file)) == null ? void 0 : _b.frontmatter;
    const arrTags = [];
    if (tags) {
      for (let i = 0, len = tags.length; i < len; i++) {
        if (arrTags.indexOf(tags[i].tag) < 0) {
          arrTags.push(tags[i].tag);
        }
      }
    }
    if (frontmatter_tags !== void 0 && frontmatter_tags.tags) {
      for (let i = 0, len = frontmatter_tags.tags.length; i < len; i++) {
        const fmTag = "#" + frontmatter_tags.tags[i];
        if (arrTags.indexOf(fmTag) < 0) {
          arrTags.push(fmTag);
        }
      }
    }
    return arrTags.join(" ");
  } else {
    return "";
  }
}
function getPropsPerFile(file) {
  const fileProperties = [];
  const cache = this.app.metadataCache.getFileCache(file);
  if (cache == null ? void 0 : cache.frontmatter) {
    for (const [key, value] of Object.entries(cache.frontmatter)) {
      fileProperties.push(`'${key}: ${value}'`);
    }
  }
  return fileProperties.join(" \n");
}

// src/utils/dnpiechart.ts
var DNPieChart = class {
  constructor(canvas, margin = 10, fontSize = 12, legendWidth = 50, labelColor = "#828282") {
    this._canvas = canvas;
    const ctx = canvas.getContext("2d");
    if (ctx) {
      this._ctx = ctx;
    }
    this._data = [];
    this._margin = margin;
    this._fontSize = fontSize;
    this._legendWidth = legendWidth;
    this._labelColor = labelColor;
  }
  addData(value, color, label) {
    this._data.push({ value, color, label });
  }
  draw() {
    const { width, height } = this._canvas;
    const availWidth = width - 2 * this._margin;
    const availHeight = height - 2 * this._margin;
    const scale = Math.min(availWidth / width, availHeight / height);
    const radius = Math.min(availWidth, availHeight) / 2 * scale;
    this._cX = width / 2;
    this._cY = height / 2;
    this._ctx.clearRect(0, 0, width, height);
    const totalValue = this._data.reduce((acc, curr) => acc + curr.value, 0);
    let currentAngle = -Math.PI / 2;
    this._data.forEach((slice) => {
      const sliceAngle = slice.value / totalValue * 2 * Math.PI;
      this.drawSlice(radius, sliceAngle, slice.color, currentAngle);
      currentAngle += sliceAngle;
    });
    this.drawLegend(2, 20);
  }
  drawSlice(radius, sliceAngle, color, startAngle) {
    this._ctx.beginPath();
    this._ctx.arc(this._cX, this._cY, radius, startAngle, startAngle + sliceAngle);
    this._ctx.lineTo(this._cX, this._cY);
    this._ctx.fillStyle = color;
    this._ctx.closePath();
    this._ctx.fill();
  }
  drawLegend(x, y) {
    const lineHeight = this._fontSize + 5;
    this._data.forEach((slice, index) => {
      const textX = x + this._legendWidth / 5;
      const textY = y + index * lineHeight - 10;
      this._ctx.fillStyle = slice.color;
      this._ctx.fillRect(textX - 10, textY, 5, 5);
      this._ctx.fillStyle = this._labelColor;
      this._ctx.font = `${this._fontSize}px sans-serif`;
      this._ctx.fillText(`${slice.label} (${slice.value})`, textX, textY + 8);
    });
  }
};

// src/utils/dntablemanager.ts
var DNTableManager = class {
  constructor(tbl) {
    this.table = document.querySelector(tbl);
  }
  getCells() {
    return this.table.querySelectorAll("td, th");
  }
  hideColumns(columnNames) {
    const cells = this.getCells();
    if (columnNames.length === 0) {
      for (let i = 0; i < cells.length; i++) {
        cells[i].classList.remove("dn-hidden");
      }
    } else {
      for (let i = 0; i < cells.length; i++) {
        const colIndex = i % 7;
        const columnName = this.getColumnNames()[colIndex];
        if (columnNames.includes(columnName)) {
          cells[i].classList.add("dn-hidden");
        } else {
          cells[i].classList.remove("dn-hidden");
        }
      }
    }
  }
  getColumnNames() {
    return ["name", "ext", "path", "size", "date", "tags", "frontmatter"];
  }
};

// src/dn.ts
var import_obsidian3 = require("obsidian");
var DNModal = class extends import_obsidian2.Modal {
  constructor(app) {
    super(app);
    this._sort_order = "desc";
    this._sort_column = "modified";
    this._selected_category = "";
    this._TABLE_LAYOUTS = ["dn-tbl-default", "dn-tbl-row", "dn-tbl-column", "dn-tbl-bordered"];
    this.selected_table_layout = "dn-tbl-default";
    this.selected_sort_value = "modified-desc";
    this.num_recent_files = 5;
    this.files_per_page = 20;
    this.date_format = "YYYY-MM-DD HH:mm";
    this.default_view = 1;
    this.excluded_extensions = [];
    this.excluded_folders = [];
    // File colors
    this.color_notes = "#bf48ff";
    this.color_images = "#007fff";
    this.color_canvas = "#ff7f28";
    this.color_videos = "#d34848";
    this.color_audios = "#bfbf00";
    this.color_pdf = "#00a300";
    this.color_other = "#828282";
    this.colored_files = false;
    // Hide columns
    this.hide_columns = [];
    this._previewComponent = new import_obsidian2.Component();
    this.dnHandleIntersection = (entries) => {
      entries.forEach((entry) => {
        if (!entry.isIntersecting) {
          entry.target.removeEventListener("contextmenu", this.dnHandleClick);
          entry.target.removeEventListener("click", this.dnHandleClick);
          entry.target.removeEventListener("dblclick", this.dnHandleDblClick);
        }
      });
    };
    this.intersectionObserver = new IntersectionObserver(this.dnHandleIntersection);
  }
  async onOpen() {
    var _a;
    const { contentEl } = this;
    this._previewComponent.load();
    this._hoverDiv = this.contentEl.createEl("div", { cls: "dn-preview" });
    await this.updateModalData();
    const leaf = (_a = this.app.workspace) == null ? void 0 : _a.getMostRecentLeaf();
    if (leaf !== null) {
      this._leaf = leaf;
    }
    this.dnCreateMainUI(contentEl);
    this.dnSetView(this.default_view);
    this.dnSetSelectLayoutValue(this.selected_table_layout);
    this.dnSetSelectSortValue(this.selected_sort_value);
    this.dnToggleColoredFiles();
    this._isDraggingPreview = false;
    this._hoverDivLeft = "";
    this._hoverDivTop = "";
  }
  async updateModalData() {
    this._files = [];
    this._folders = [];
    this._recent = [];
    this._last_opened = [];
    this._notes = [];
    this._images = [];
    this._canvas = [];
    this._audios = [];
    this._videos = [];
    this._pdf = [];
    this._other = [];
    const dnFilesAndFolders = this.app.vault.getAllLoadedFiles();
    for (const absF of dnFilesAndFolders) {
      if (absF instanceof import_obsidian2.TFile) {
        this._files.push(absF);
      } else if (absF instanceof import_obsidian2.TFolder && !absF.isRoot()) {
        this._folders.push(absF);
      }
    }
    this._files_excluded_filters = this._files.filter(
      (file) => {
        return !this.excluded_extensions.includes(file.extension.toLowerCase()) && !this.excluded_folders.some((folder) => file.path.startsWith(folder));
      }
    );
    this._files_results = this._files_excluded_filters;
    await this.dnOrganizeFiles({ arr: this._files_excluded_filters });
    this._recent = await this.dnGetRecentFiles(this._files_excluded_filters);
    const arrStrLastOpened = this.app.workspace.getLastOpenFiles();
    arrStrLastOpened.forEach(async (file) => {
      const f_temp = await this.app.vault.getAbstractFileByPath(file);
      if (f_temp instanceof import_obsidian2.TFile) {
        this._last_opened.push(f_temp);
      }
    });
  }
  async dnCreateMainUI(el) {
    const mainContainer = el.createEl("div", { cls: "dn-container" });
    mainContainer.setAttribute("id", "dn-container");
    this.dnCreateInputSearch(mainContainer);
    const topNav = mainContainer.createEl("div", { cls: "dn-top-nav" });
    const leftTopNav = topNav.createEl("div");
    const rightTopNav = topNav.createEl("div");
    this._BTN_DASHBOARD = leftTopNav.createEl("button", { text: "Dashboard", cls: "mod-cta" });
    this._BTN_DASHBOARD.onClickEvent((evt) => {
      this.dnSetView(1);
    });
    this._BTN_NAVIGATOR = leftTopNav.createEl("button", { text: "Navigator" });
    this._BTN_NAVIGATOR.onClickEvent((evt) => {
      this.dnModalSearchVault(this._INPUT_SEARCH.value);
      this.dnSetView(2);
    });
    this.labelLayout = rightTopNav.createEl("span", {
      text: "Layout:",
      cls: "dn-tbl-label"
    });
    this.labelLayout.setAttribute("id", "dn-label-layout");
    this._SELECT_TABLE_LAYOUT = rightTopNav.createEl("select", {
      cls: "dropdown tbl-select"
    });
    this._SELECT_TABLE_LAYOUT.createEl("option", { text: "Default", value: "dn-tbl-default" });
    this._SELECT_TABLE_LAYOUT.createEl("option", { text: "Row striped", value: "dn-tbl-row" });
    this._SELECT_TABLE_LAYOUT.createEl("option", { text: "Column striped", value: "dn-tbl-column" });
    this._SELECT_TABLE_LAYOUT.createEl("option", { text: "Bordered", value: "dn-tbl-bordered" });
    this._SELECT_TABLE_LAYOUT.addEventListener("change", () => {
      this.dnSelectTableLayout();
    });
    this.labelSort = rightTopNav.createEl("span", {
      text: "Sort by:",
      cls: "dn-tbl-label"
    });
    this.labelSort.setAttribute("id", "dn-label-sort");
    this._SELECT_SORT = rightTopNav.createEl("select", {
      cls: "dropdown"
    });
    this._SELECT_SORT.setAttribute("id", "dn-select-sort");
    this._SELECT_SORT.addEventListener("change", () => {
      this.dnSortColumnWithSelect();
    });
    this._SELECT_SORT.createEl("option", { text: "Name (A to Z)", value: "name-asc" });
    this._SELECT_SORT.createEl("option", { text: "Name (Z to A)", value: "name-desc" });
    this._SELECT_SORT.createEl("option", { text: "Extension (A to Z)", value: "ext-asc" });
    this._SELECT_SORT.createEl("option", { text: "Extension (Z to A)", value: "ext-desc" });
    this._SELECT_SORT.createEl("option", { text: "Path (A to Z)", value: "path-asc" });
    this._SELECT_SORT.createEl("option", { text: "Path (Z to A)", value: "path-desc" });
    this._SELECT_SORT.createEl("option", { text: "Size (smallest to largest)", value: "size-asc" });
    this._SELECT_SORT.createEl("option", { text: "Size (largest to smallest)", value: "size-desc" });
    this._SELECT_SORT.createEl("option", { text: "Date/time (oldest to newest)", value: "modified-asc" });
    this._SELECT_SORT.createEl("option", { text: "Date/time (newest to oldest)", value: "modified-desc" });
    this._VIEW_DASHBOARD = mainContainer.createEl("div", { cls: "dn-flex" });
    this._VIEW_NAVIGATOR = mainContainer.createEl("div", { cls: "dn-display-none" });
    this._divSearchResults = this._VIEW_NAVIGATOR.createEl("div", { cls: "dn-div-table" });
    this.dnShowModalSearchResults({ f: this._files_results, el: this._divSearchResults, leaf: this._leaf });
    const divVaultStats = this._VIEW_DASHBOARD.createEl("div");
    divVaultStats.setAttribute("id", "dn-vault-stats");
    const divVaultGraph = this._VIEW_DASHBOARD.createEl("div");
    divVaultGraph.setAttribute("id", "dn-vault-graph");
    const divLastOpenedFiles = this._VIEW_DASHBOARD.createEl("div");
    divLastOpenedFiles.setAttribute("id", "dn-last-opened-files");
    const divRecentFiles = this._VIEW_DASHBOARD.createEl("div");
    divRecentFiles.setAttribute("id", "dn-recent-files");
    const divRecentNotes = this._VIEW_DASHBOARD.createEl("div");
    divRecentNotes.setAttribute("id", "dn-recent-notes");
    const divCanvas = this._VIEW_DASHBOARD.createEl("div");
    divCanvas.setAttribute("id", "dn-canvas");
    const divImages = this._VIEW_DASHBOARD.createEl("div");
    divImages.setAttribute("id", "dn-images");
    const divAudios = this._VIEW_DASHBOARD.createEl("div");
    divAudios.setAttribute("id", "dn-audios");
    const divVideos = this._VIEW_DASHBOARD.createEl("div");
    divVideos.setAttribute("id", "dn-videos");
    const divPDFs = this._VIEW_DASHBOARD.createEl("div");
    divPDFs.setAttribute("id", "dn-pdfs");
    const divOther = this._VIEW_DASHBOARD.createEl("div");
    divOther.setAttribute("id", "dn-other");
    await this.dnCreateBtn(
      divVaultStats,
      "dn-btn-notes",
      "Notes",
      this._notes,
      this._divSearchResults,
      this._leaf
    );
    await this.dnCreateBtn(
      divVaultStats,
      "dn-btn-canvas",
      "Canvases",
      this._canvas,
      this._divSearchResults,
      this._leaf
    );
    await this.dnCreateBtn(
      divVaultStats,
      "dn-btn-images",
      "Images",
      this._images,
      this._divSearchResults,
      this._leaf
    );
    await this.dnCreateBtn(
      divVaultStats,
      "dn-btn-audios",
      "Audios",
      this._audios,
      this._divSearchResults,
      this._leaf
    );
    await this.dnCreateBtn(
      divVaultStats,
      "dn-btn-videos",
      "Videos",
      this._videos,
      this._divSearchResults,
      this._leaf
    );
    await this.dnCreateBtn(
      divVaultStats,
      "dn-btn-pdf",
      "PDFs",
      this._pdf,
      this._divSearchResults,
      this._leaf
    );
    await this.dnCreateBtn(
      divVaultStats,
      "dn-btn-other",
      "Other",
      this._other,
      this._divSearchResults,
      this._leaf
    );
    const canvasPieChart1 = divVaultGraph.createEl("canvas");
    canvasPieChart1.setAttribute("id", "dashboard-canvas");
    const styles = getComputedStyle(document.body);
    const labelColor = styles.getPropertyValue("--text-muted");
    const pieChart1 = new DNPieChart(canvasPieChart1, 10, 12, 50, labelColor);
    pieChart1.addData(this._notes.length, this.color_notes, "Notes");
    pieChart1.addData(this._images.length, this.color_images, "Images");
    pieChart1.addData(this._canvas.length, this.color_canvas, "Canvas");
    pieChart1.addData(this._videos.length, this.color_videos, "Videos");
    pieChart1.addData(this._audios.length, this.color_audios, "Audios");
    pieChart1.addData(this._pdf.length, this.color_pdf, "PDF");
    pieChart1.addData(this._other.length, this.color_other, "Other");
    pieChart1.draw();
    const divStatsFrame = divVaultGraph.createEl("div", { cls: "dn-stats-files-folders" });
    divStatsFrame.createEl("div", { cls: "dn-stats-files", text: "Files: " + this._files_excluded_filters.length });
    divStatsFrame.createEl("div", { cls: "dn-stats-folders", text: "Folders: " + this._folders.length });
    await this.dnCreateRecentFiles("Recently opened", divLastOpenedFiles, this._last_opened, this.num_recent_files);
    await this.dnCreateRecentFiles("Recent files", divRecentFiles, this._recent, this.num_recent_files);
    await this.dnCreateRecentFiles("Recent notes", divRecentNotes, this._notes, this.num_recent_files);
    await this.dnCreateRecentFiles("Recent canvases", divCanvas, this._canvas, this.num_recent_files);
    await this.dnCreateRecentFiles("Recent images", divImages, this._images, this.num_recent_files);
    await this.dnCreateRecentFiles("Recent audios", divAudios, this._audios, this.num_recent_files);
    await this.dnCreateRecentFiles("Recent videos", divVideos, this._videos, this.num_recent_files);
    await this.dnCreateRecentFiles("Recent PDFs", divPDFs, this._pdf, this.num_recent_files);
    await this.dnCreateRecentFiles("Recent other files", divOther, this._other, this.num_recent_files);
  }
  async dnCreateBtn(elDiv, btnId, btnTitle, btnCategoryFiles, displayEl, leaf) {
    const btn = elDiv.createEl("div", { cls: "dn-btn-stats" });
    btn.setAttribute("id", btnId);
    btn.createEl("span", { cls: "dn-btn-stats-label", text: btnTitle });
    btn.createEl("span", { cls: "dn-btn-stats-icon" });
    btn.createEl("span", { cls: "dn-btn-stats-number", text: btnCategoryFiles.length.toString() });
    btn.onClickEvent((evt) => {
      this._files_results = btnCategoryFiles;
      this._INPUT_SEARCH.value = "@" + btnTitle.toLocaleLowerCase() + " ";
      this.dnModalSearchVault(this._INPUT_SEARCH.value);
      this._INPUT_SEARCH.focus();
    });
    return btn;
  }
  dnCreateInputSearch(el) {
    const searchContainer = el.createEl("div", { cls: "dn-search-input-container" });
    this._INPUT_SEARCH = searchContainer.createEl("input", { type: "search", placeholder: "Search..." });
    this._INPUT_SEARCH.setAttribute("id", "dn-input-filter");
    this._INPUT_SEARCH.spellcheck = false;
    this._INPUT_SEARCH.focus();
    searchContainer.createEl("div", { cls: "search-input-clear-button" }).onClickEvent((evt) => {
      this._INPUT_SEARCH.value = "";
      this._INPUT_SEARCH.focus();
      this.dnModalSearchVault(this._INPUT_SEARCH.value);
    });
    this._INPUT_SEARCH.addEventListener("input", (0, import_obsidian2.debounce)(() => this.dnModalSearchVault(this._INPUT_SEARCH.value), 300, true));
  }
  async dnModalSearchVault(val) {
    this.dnSetView(2);
    const search_raw_vals = /!(?:"(?:\\"|[^"])*"|'(?:\\'|[^'])*')|"(?:\\"|[^"])*"|'(?:\\'|[^'])*'|\S+/g;
    let searchParams = val.toLowerCase().trim().match(search_raw_vals);
    if (!searchParams) {
      return this.searchAction("", this._files_excluded_filters);
    }
    searchParams = searchParams.map((param) => {
      if (param.startsWith('"') && param.endsWith('"')) {
        return param.slice(1, -1);
      } else if (param.startsWith("'") && param.endsWith("'")) {
        return param.slice(1, -1);
      } else if (param.startsWith(".")) {
        return "\\" + param + "$";
      } else if (param.startsWith("/") && param.length === 1) {
        return "^/$";
      } else {
        return param;
      }
    });
    const firstParam = searchParams[0];
    await this.searchAction(firstParam, this._files_excluded_filters);
    const remainingParams = searchParams.slice(1);
    remainingParams.every(async (p) => {
      await this.searchAction(p, this._files_results);
    });
  }
  // Search
  async searchAction(val, files) {
    let rExp;
    const isExcludeSearch = val.startsWith("!") && val.length >= 1;
    let excludeParam = isExcludeSearch ? val.slice(1) : val;
    if (excludeParam.startsWith('"') && excludeParam.endsWith('"')) {
      excludeParam = excludeParam.slice(1, -1);
    } else if (excludeParam.startsWith("'") && excludeParam.endsWith("'")) {
      excludeParam = excludeParam.slice(1, -1);
    }
    try {
      if (isExcludeSearch) {
        rExp = new RegExp("", "iu");
      } else {
        if (val === "!") {
          rExp = new RegExp("", "iu");
        } else {
          rExp = new RegExp(val.toLowerCase(), "iu");
        }
      }
    } catch (error) {
      return;
    }
    const isDateSearch = val.startsWith("@");
    if (this._INPUT_SEARCH.value.includes("@")) {
      this._INPUT_SEARCH.classList.add("dn-input-datesearch");
    } else {
      this._INPUT_SEARCH.classList.remove("dn-input-datesearch");
    }
    if (isExcludeSearch) {
      let isMatch;
      if (excludeParam === "/") {
        this._files_results = files.filter((file) => {
          isMatch = getFolderStructure(file.path).toLowerCase() === "/";
          return isExcludeSearch ? !isMatch : isMatch;
        });
      } else {
        this._files_results = files.filter((file) => {
          isMatch = file.name.toLowerCase().includes(excludeParam) || getFolderStructure(file.path).toLowerCase().includes(excludeParam) || (0, import_obsidian3.moment)(file.stat.mtime).format(this.date_format).toLowerCase().includes(excludeParam) || getTagsPerFile(file).toLowerCase().includes(excludeParam) || getPropsPerFile(file).toLowerCase().includes(excludeParam);
          return isExcludeSearch ? !isMatch : isMatch;
        });
      }
    } else {
      this._files_results = files.filter(
        (file) => {
          if (isDateSearch) {
            const dateSearch = val.slice(1).toLowerCase().split(" ");
            return this.dnHandleSpecialSearch(dateSearch[0], file);
          } else {
            return this.dnHandleNormalSearch(rExp, file);
          }
        }
      );
    }
    this.dnSortFilteredFiles(false);
    await this.dnShowModalSearchResults({ f: this._files_results, el: this._divSearchResults, leaf: this._leaf });
    const tbody = document.getElementById("dn-table-results");
    if (tbody !== null) {
      const tr = tbody.getElementsByTagName("tr");
      for (let i = 0, len = tr.length; i < len; i++) {
        const allTds = tr[i].querySelectorAll("td");
        let isResult = false;
        for (let j = 0; j < allTds.length; j++) {
          const td = allTds[j];
          const tdVal = td.innerText.toLowerCase();
          if (rExp.test(tdVal) || isDateSearch) {
            isResult = true;
            break;
          }
        }
        if (isResult) {
          tr[i].classList.remove("dn-display-none");
        } else {
          tr[i].classList.add("dn-display-none");
        }
      }
    }
  }
  async dnShowModalSearchResults({ f, el, leaf, currentPage = 1 }) {
    el.empty();
    const paginationContainer = this._divSearchResults.createEl("div", { cls: "dn-pagination" });
    const table = el.createEl("table", { cls: this.selected_table_layout });
    table.setAttribute("id", "dn-table");
    const thead = table.createEl("thead");
    const tr = thead.createEl("tr");
    this._th1 = tr.createEl("th", { text: "Name" });
    this._th2 = tr.createEl("th", { text: "Ext" });
    this._th3 = tr.createEl("th", { text: "Path" });
    this._th4 = tr.createEl("th", { text: "Size" });
    this._th5 = tr.createEl("th", { text: "Date" });
    this._th6 = tr.createEl("th", { text: "Tags" });
    this._th7 = tr.createEl("th", { text: "Frontmatter" });
    this._th1.addEventListener("dblclick", () => this.dnAlternateSortColumn("name"));
    this._th2.addEventListener("dblclick", () => this.dnAlternateSortColumn("ext"));
    this._th3.addEventListener("dblclick", () => this.dnAlternateSortColumn("path"));
    this._th4.addEventListener("dblclick", () => this.dnAlternateSortColumn("size"));
    this._th5.addEventListener("dblclick", () => this.dnAlternateSortColumn("modified"));
    const tbody = table.createEl("tbody");
    tbody.setAttribute("id", "dn-table-results");
    if (f.length > 0) {
      if (this.intersectionObserver) {
        this.intersectionObserver.disconnect();
      }
      this._total_pages = Math.ceil(f.length / this.files_per_page);
      const paginatedData = f.slice((currentPage - 1) * this.files_per_page, currentPage * this.files_per_page);
      paginatedData.forEach(async (file) => {
        const tr2 = tbody.createEl("tr");
        tr2.addEventListener("contextmenu", (evt) => {
          this.dnHandleClick(evt, file);
        });
        tr2.addEventListener("click", (evt) => {
          this.dnHandleClick(evt, file);
        });
        tr2.addEventListener("dblclick", (evt) => {
          this.dnHandleDblClick(evt, file);
        });
        tr2.addEventListener("mouseover", async (evt) => {
          this.dnHandleHoverPreview(evt, file);
        });
        this.intersectionObserver.observe(tr2);
        tr2.removeEventListener("mouseover", async (evt) => {
          this.dnHandleHoverPreview(evt, file);
        });
        const td1 = tr2.createEl("td");
        td1.createEl("a", { cls: this.dnSetFileIconClass(file.extension), text: file.name }).onClickEvent((evt) => {
          if (leaf !== null && file !== null) {
            this.dnOpenFileAlt(file, evt);
          }
        });
        const fExt = file.extension;
        const fSize = formatFileSize(file.stat.size);
        const fMTime = (0, import_obsidian3.moment)(file.stat.mtime).format(this.date_format);
        const fCTime = (0, import_obsidian3.moment)(file.stat.ctime).format(this.date_format);
        const td2 = tr2.createEl("td");
        td2.createEl("a", { cls: "dn-ext", text: fExt, title: fExt }).onClickEvent((evt) => {
          if (evt.button === 2) {
            evt.preventDefault();
          } else {
            this._INPUT_SEARCH.value = "." + fExt;
            this.dnModalSearchVault(this._INPUT_SEARCH.value);
          }
        });
        const td3 = tr2.createEl("td");
        const folder_path = getFolderStructure(file.path);
        td3.createEl("a", { cls: "dn-folder-path", text: folder_path, title: file.path }).onClickEvent((evt) => {
          if (evt.button === 2) {
            evt.preventDefault();
          } else {
            this._INPUT_SEARCH.value = folder_path;
            this.dnModalSearchVault(this._INPUT_SEARCH.value + "$");
          }
        });
        tr2.createEl("td", { text: fSize, title: fSize + " bytes" });
        tr2.createEl("td", { text: fMTime, title: fCTime + " - Created\n" + fMTime + " - Modified" });
        const tags_per_file = getTagsPerFile(file);
        const props_per_file = getPropsPerFile(file);
        const td6 = tr2.createEl("td", { title: tags_per_file });
        if (tags_per_file !== "") {
          const fTags = tags_per_file.split(" ");
          fTags.forEach((tag) => {
            td6.createEl("a", { cls: "tag", text: tag, href: tag }).onClickEvent((evt) => {
              if (evt.button === 2) {
                evt.preventDefault();
              } else {
                this._INPUT_SEARCH.value = tag;
                this.dnModalSearchVault(this._INPUT_SEARCH.value);
              }
            });
          });
        }
        const td7 = tr2.createEl("td");
        if (props_per_file !== "") {
          const fProps = props_per_file.split("\n");
          fProps.forEach((prop) => {
            td7.createEl("a", { cls: "dn-tag", text: prop, title: props_per_file }).onClickEvent((evt) => {
              if (evt.button === 2) {
                evt.preventDefault();
              } else {
                this._INPUT_SEARCH.value = prop;
                this.dnModalSearchVault(this._INPUT_SEARCH.value);
              }
            });
          });
        }
      });
      paginationContainer.empty();
      paginationContainer.createEl("div", { cls: "dn-pagination-total-results", text: `File(s): ${f.length} ` });
      const rightPagDiv = paginationContainer.createEl("div", { cls: "dn-pagination-current-page", text: `Page ${currentPage} of ${this._total_pages} ` });
      const btnPrev = rightPagDiv.createEl("button", { cls: "dn-btn-prev", text: "\u25C0", title: "Previous" });
      if (currentPage === 1) {
        btnPrev.disabled = true;
      } else {
        btnPrev.disabled = false;
      }
      btnPrev.addEventListener("click", () => {
        if (currentPage > 1) {
          this.dnShowModalSearchResults({ f, el, leaf, currentPage: currentPage - 1 });
        }
      });
      const btnNext = rightPagDiv.createEl("button", { cls: "dn-btn-next", text: "\u25B6", title: "Next" });
      if (currentPage === this._total_pages) {
        btnNext.disabled = true;
      } else {
        btnNext.disabled = false;
      }
      btnNext.addEventListener("click", () => {
        if (currentPage < this._total_pages) {
          this.dnShowModalSearchResults({ f, el, leaf, currentPage: currentPage + 1 });
        }
      });
      this.dnUpdateSortIndicators(
        this._sort_column,
        this._sort_order,
        this._th1,
        this._th2,
        this._th3,
        this._th4,
        this._th5
      );
      const dnTableManager = new DNTableManager("#dn-table");
      dnTableManager.hideColumns(this.hide_columns);
    } else {
      tr.empty();
      paginationContainer.createEl("div", { cls: "dn-pagination-total-results", text: `File(s): 0 ` });
      this._divSearchResults.createEl("p", { cls: "dn-no-results-found", text: "No files found." });
    }
  }
  async dnSortFilteredFiles(toggle) {
    switch (this._sort_column) {
      case "name":
      case "path":
      case "ext":
        this.dnSortColumnString(this._sort_column, this._sort_order, toggle);
        break;
      case "size":
      case "modified":
        this.dnSortColumnNumber(this._sort_column, this._sort_order, toggle);
        break;
    }
  }
  dnSortColumnWithSelect() {
    const val = this._SELECT_SORT.value;
    if (this.dnIsValidSort(val)) {
      const selSort = val.split("-");
      this._sort_column = selSort[0];
      this._sort_order = selSort[1];
      switch (this._sort_column) {
        case "name":
        case "path":
        case "ext":
          this.dnSortColumnString(this._sort_column, this._sort_order, false);
          break;
        case "size":
        case "modified":
          this.dnSortColumnNumber(this._sort_column, this._sort_order, false);
          break;
      }
      this.dnShowModalSearchResults({ f: this._files_results, el: this._divSearchResults, leaf: this._leaf });
    }
  }
  dnIsValidSort(val) {
    if ([
      "name-asc",
      "name-desc",
      "path-asc",
      "path-desc",
      "ext-asc",
      "ext-desc",
      "size-asc",
      "size-desc",
      "modified-asc",
      "modified-desc"
    ].includes(val)) {
      return true;
    } else {
      return false;
    }
  }
  dnIsValidLayout(val) {
    if (this._TABLE_LAYOUTS.includes(val)) {
      return true;
    } else {
      return false;
    }
  }
  dnAlternateSortColumn(colName) {
    switch (colName) {
      case "name":
        this.dnSortColumnString("name", this._sort_order, true);
        break;
      case "path":
        this.dnSortColumnString("path", this._sort_order, true);
        break;
      case "ext":
        this.dnSortColumnString("ext", this._sort_order, true);
        break;
      case "size":
        this.dnSortColumnNumber("size", this._sort_order, true);
        break;
      case "modified":
        this.dnSortColumnNumber("modified", this._sort_order, true);
        break;
    }
    this.dnShowModalSearchResults({ f: this._files_results, el: this._divSearchResults, leaf: this._leaf });
  }
  dnUpdateSortIndicators(activeColumn, sortOrder, col1, col2, col3, col4, col5) {
    col1.classList.remove("sort-active", "sort-asc", "sort-desc");
    col2.classList.remove("sort-active", "sort-asc", "sort-desc");
    col3.classList.remove("sort-active", "sort-asc", "sort-desc");
    col4.classList.remove("sort-active", "sort-asc", "sort-desc");
    col5.classList.remove("sort-active", "sort-asc", "sort-desc");
    let activeThCell = col5;
    switch (activeColumn) {
      case "name":
        activeThCell = col1;
        break;
      case "ext":
        activeThCell = col2;
        break;
      case "path":
        activeThCell = col3;
        break;
      case "size":
        activeThCell = col4;
        break;
      case "modified":
        activeThCell = col5;
        break;
    }
    activeThCell.classList.add("sort-active");
    activeThCell.classList.add(sortOrder === "asc" ? "sort-asc" : "sort-desc");
  }
  dnSortColumnString(sortColumn, sortOrder, toggleSortOrder) {
    const supportedColumns = ["name", "path", "ext"];
    if (!supportedColumns.includes(sortColumn)) {
      return;
    }
    if (toggleSortOrder) {
      if (this._sort_column === sortColumn) {
        sortOrder = sortOrder === "asc" ? "desc" : "asc";
      } else {
        this._sort_column = sortColumn;
        sortOrder = "desc";
      }
      this._sort_order = sortOrder;
    }
    const sortValue = sortColumn + "-" + this._sort_order;
    this.dnSetSelectSortValue(sortValue);
    this._files_results.sort((fileA, fileB) => {
      let sortA;
      let sortB;
      let folderStructureA;
      let folderStructureB;
      switch (sortColumn) {
        case "name":
          sortA = fileA.name.toLowerCase();
          sortB = fileB.name.toLowerCase();
          break;
        case "ext":
          sortA = fileA.extension.toLowerCase();
          sortB = fileB.extension.toLowerCase();
          break;
        case "path":
          folderStructureA = getFolderStructure(fileA.path);
          folderStructureB = getFolderStructure(fileB.path);
          sortA = folderStructureA.toLowerCase();
          sortB = folderStructureB.toLowerCase();
          break;
        default:
          sortA = fileA.name.toLowerCase();
          sortB = fileB.name.toLowerCase();
      }
      if (sortOrder === "asc") {
        return sortA.localeCompare(sortB);
      } else if (sortOrder === "desc") {
        return sortB.localeCompare(sortA);
      } else {
        return sortA.localeCompare(sortB);
      }
    });
  }
  dnSortColumnNumber(sortColumn, sortOrder, toggleSortOrder) {
    const supportedColumns = ["size", "modified"];
    if (!supportedColumns.includes(sortColumn)) {
      return;
    }
    if (toggleSortOrder) {
      if (this._sort_column === sortColumn) {
        sortOrder = sortOrder === "asc" ? "desc" : "asc";
      } else {
        this._sort_column = sortColumn;
        sortOrder = "desc";
      }
      this._sort_order = sortOrder;
    }
    const sortValue = sortColumn + "-" + this._sort_order;
    this.dnSetSelectSortValue(sortValue);
    this._files_results.sort((fileA, fileB) => {
      let sortA = 0;
      let sortB = 0;
      switch (sortColumn) {
        case "size":
          sortA = fileA.stat.size;
          sortB = fileB.stat.size;
          break;
        case "modified":
          sortA = fileA.stat.mtime;
          sortB = fileB.stat.mtime;
          break;
      }
      if (sortOrder === "asc") {
        return sortA - sortB;
      } else if (sortOrder === "desc") {
        return sortB - sortA;
      } else {
        return sortA - sortB;
      }
    });
  }
  async dnGetRecentFiles(files) {
    const arrRecentFiles = files;
    return arrRecentFiles.sort((a, b) => b.stat.mtime - a.stat.mtime).slice(0, this.num_recent_files);
  }
  async dnCreateRecentFiles(title, divF, files, num_files) {
    if (files.length === 0) {
      divF.createEl("h3", { cls: "dn-subtitles", text: title });
      divF.createEl("p", { cls: "dn-no-results-found", text: "No files found." });
      divF.classList.add("dn-display-none");
    } else {
      divF.createEl("h3", { cls: "dn-subtitles", text: title });
      let sortedFiles = [];
      if (title === "Recently opened") {
        sortedFiles = files.slice(0, this.num_recent_files);
      } else {
        sortedFiles = await this.dnGetRecentFiles(files);
      }
      sortedFiles.forEach((sfile) => {
        const aLink = divF.createEl("a", {
          cls: this.dnSetFileIconClass(sfile.extension),
          text: sfile.basename,
          title: sfile.path
        });
        aLink.onClickEvent((evt) => {
          if (sfile !== null) {
            this.dnOpenFileAlt(sfile, evt);
          }
        });
        if (sfile.extension !== "md") {
          divF.createEl("span", { cls: "nav-file-tag", text: sfile.extension });
        }
        divF.createEl("br");
        aLink.addEventListener("mouseover", (evt) => this.dnHandleHoverPreview(evt, sfile));
      });
    }
  }
  async dnOrganizeFiles({ arr }) {
    const arrNotes = [];
    const arrImages = [];
    const arrAudios = [];
    const arrCanvas = [];
    const arrVideos = [];
    const arrPDFs = [];
    const arrOther = [];
    const extensions = {
      "md": arrNotes,
      // Images
      "avif": arrImages,
      "bmp": arrImages,
      "gif": arrImages,
      "ico": arrImages,
      "jpeg": arrImages,
      "jpg": arrImages,
      "png": arrImages,
      "raw": arrImages,
      "svg": arrImages,
      "tif": arrImages,
      "tiff": arrImages,
      "webp": arrImages,
      // Audios
      "aac": arrAudios,
      "aif": arrAudios,
      "aifc": arrAudios,
      "aiff": arrAudios,
      "flac": arrAudios,
      "m4a": arrAudios,
      "mp3": arrAudios,
      "ogg": arrAudios,
      "wav": arrAudios,
      "webm": arrAudios,
      // Videos
      "avi": arrVideos,
      "mov": arrVideos,
      "mkv": arrVideos,
      "mp4": arrVideos,
      // PDF and other formats
      "pdf": arrPDFs,
      "canvas": arrCanvas
    };
    for (let i = 0, len = arr.length; i < len; i++) {
      const f = arr[i].extension.toLowerCase();
      const targetArr = extensions[f];
      if (targetArr) {
        targetArr.push(arr[i]);
      } else {
        arrOther.push(arr[i]);
      }
    }
    this._notes = arrNotes;
    this._images = arrImages;
    this._audios = arrAudios;
    this._videos = arrVideos;
    this._pdf = arrPDFs;
    this._canvas = arrCanvas;
    this._other = arrOther;
  }
  dnSetFileIconClass(ext) {
    const file_extension = ext.toLowerCase();
    const extensions = {
      "md": "note",
      // Images
      "avif": "image",
      "bmp": "image",
      "gif": "image",
      "ico": "image",
      "jpeg": "image",
      "jpg": "image",
      "png": "image",
      "raw": "image",
      "svg": "image",
      "tif": "image",
      "tiff": "image",
      "webp": "image",
      // Audios
      "aac": "audio",
      "aif": "audio",
      "aifc": "audio",
      "aiff": "audio",
      "flac": "audio",
      "m4a": "audio",
      "mp3": "audio",
      "ogg": "audio",
      "wav": "audio",
      "webm": "audio",
      // Videos
      "avi": "video",
      "mov": "video",
      "mkv": "video",
      "mp4": "video",
      // PDF and other formats
      "pdf": "pdf",
      "canvas": "canvas"
    };
    if (file_extension in extensions) {
      return "dn-f-" + extensions[file_extension];
    } else {
      return "dn-f-other";
    }
  }
  // Custom Colors
  dnSetCustomColors() {
    document.body.style.setProperty("--dn-notes-color", this.color_notes);
    document.body.style.setProperty("--dn-images-color", this.color_images);
    document.body.style.setProperty("--dn-canvas-color", this.color_canvas);
    document.body.style.setProperty("--dn-videos-color", this.color_videos);
    document.body.style.setProperty("--dn-audios-color", this.color_audios);
    document.body.style.setProperty("--dn-pdfs-color", this.color_pdf);
    document.body.style.setProperty("--dn-other-color", this.color_other);
  }
  dnToggleColoredFiles() {
    const dnMainContainer = document.getElementById("dn-container");
    if (this.colored_files) {
      dnMainContainer == null ? void 0 : dnMainContainer.classList.add("dn-colored-files");
    } else {
      dnMainContainer == null ? void 0 : dnMainContainer.classList.remove("dn-colored-files");
    }
    this.dnSetCustomColors();
  }
  dnSetView(view) {
    const divElements = [this._VIEW_DASHBOARD, this._VIEW_NAVIGATOR];
    const topNavBtns = [this._BTN_DASHBOARD, this._BTN_NAVIGATOR];
    divElements.forEach((el) => {
      el.classList.add("dn-display-none");
      el.classList.remove("dn-flex");
    });
    topNavBtns.forEach((btn) => btn.classList.remove("mod-cta"));
    switch (view) {
      case 1:
        this._VIEW_DASHBOARD.classList.remove("dn-display-none");
        this._VIEW_DASHBOARD.classList.add("dn-flex");
        this._BTN_DASHBOARD.classList.add("mod-cta");
        this.dnHideTopRightNav();
        break;
      case 2:
        this._VIEW_NAVIGATOR.classList.remove("dn-display-none");
        this._VIEW_NAVIGATOR.classList.add("dn-flex");
        this._BTN_NAVIGATOR.classList.add("mod-cta");
        this.dnShowTopRightNav();
        break;
      default:
        this._VIEW_DASHBOARD.classList.remove("dn-display-none");
        this._VIEW_DASHBOARD.classList.add("dn-flex");
        this._BTN_DASHBOARD.classList.add("mod-cta");
        this.dnHideTopRightNav();
    }
  }
  dnShowTopRightNav() {
    this._SELECT_SORT.classList.remove("dn-display-none");
    this._SELECT_TABLE_LAYOUT.classList.remove("dn-display-none");
    this.labelLayout.classList.remove("dn-display-none");
    this.labelSort.classList.remove("dn-display-none");
  }
  dnHideTopRightNav() {
    this._SELECT_SORT.classList.add("dn-display-none");
    this._SELECT_TABLE_LAYOUT.classList.add("dn-display-none");
    this.labelLayout.classList.add("dn-display-none");
    this.labelSort.classList.add("dn-display-none");
  }
  dnSetSelectSortValue(val) {
    if (this.dnIsValidSort(val)) {
      this.selected_sort_value = val;
      this._SELECT_SORT.value = this.selected_sort_value;
    }
  }
  dnSetSelectLayoutValue(val) {
    if (this.dnIsValidLayout(val)) {
      this._SELECT_TABLE_LAYOUT.value = val;
      this.dnSelectTableLayout();
    }
  }
  dnSelectTableLayout() {
    const val = this._SELECT_TABLE_LAYOUT.value;
    if (this._TABLE_LAYOUTS.includes(val)) {
      const tbl = document.getElementById("dn-table");
      this._TABLE_LAYOUTS.forEach((layout) => tbl == null ? void 0 : tbl.classList.remove(layout));
      tbl == null ? void 0 : tbl.classList.add(val);
      this.selected_table_layout = val;
    }
  }
  dnSelectTableRow(evt) {
    if (!evt || typeof evt !== "object") {
      return;
    }
    if (evt.target instanceof HTMLTableCellElement) {
      const allTr = document.querySelectorAll("#dn-table tr");
      allTr.forEach((row) => row.classList.remove("tbl-selected"));
      const clickedTr = evt.target.parentElement;
      clickedTr.classList.add("tbl-selected");
    }
  }
  dnGenerateContextMenu(evt, file) {
    this._DN_CTX_MENU = new import_obsidian2.Menu();
    this._DN_CTX_MENU.addItem(
      (item) => item.setTitle("Open").setIcon("mouse-pointer-2").onClick(() => {
        this.app.workspace.getLeaf(false).openFile(file);
        this.close();
      })
    );
    this._DN_CTX_MENU.addSeparator();
    this._DN_CTX_MENU.addItem(
      (item) => item.setTitle("Open in new tab").setIcon("file-plus").onClick(() => {
        this.app.workspace.getLeaf("tab").openFile(file);
        this.close();
      })
    );
    this._DN_CTX_MENU.addItem(
      (item) => item.setTitle("Open to the right").setIcon("separator-vertical").onClick(() => {
        this.app.workspace.getLeaf("split").openFile(file);
        this.close();
      })
    );
    this._DN_CTX_MENU.addItem(
      (item) => item.setTitle("Open in new window").setIcon("picture-in-picture-2").onClick(() => {
        this.app.workspace.getLeaf("window").openFile(file);
      })
    );
    this._DN_CTX_MENU.addSeparator();
    this._DN_CTX_MENU.addItem(
      (item) => item.setTitle("Show preview").setIcon("eye").onClick((evt2) => {
        this.dnShowPreviewFile(evt2, file);
      })
    );
    this._DN_CTX_MENU.addSeparator();
    this._DN_CTX_MENU.addItem(
      (item) => item.setTitle("Frontmatter").setIcon("text").onClick(() => {
        const fpModal = new import_obsidian2.Modal(this.app);
        fpModal.contentEl.setAttribute("class", "dn-frontmatter-modal");
        fpModal.contentEl.createEl("h4", { text: "Frontmatter" });
        const rowName = fpModal.contentEl.createEl("div", { cls: "dn-property-row" });
        rowName.createEl("div", { text: "Name: ", cls: "dn-property-name-sm" });
        rowName.createEl("div", { text: file.name, cls: "dn-property-value" });
        const rowPath = fpModal.contentEl.createEl("div", { cls: "dn-property-row" });
        rowPath.createEl("div", { text: "Path: ", cls: "dn-property-name-sm" });
        rowPath.createEl("div", { text: getFolderStructure(file.path), cls: "dn-property-value" });
        fpModal.contentEl.createEl("br");
        fpModal.contentEl.createEl("span", { text: "Frontmatter: ", cls: "dn-properties" });
        fpModal.contentEl.createEl("br");
        const frontmatterDiv = fpModal.contentEl.createEl("div", { cls: "dn-properties-frontmatter-modal" });
        frontmatterDiv.setAttribute("contenteditable", "true");
        frontmatterDiv.setAttribute("spellcheck", "false");
        const curProps = getPropsPerFile(file);
        if (curProps) {
          const prop = curProps.split(" \n");
          for (let i = 0, len = prop.length; i < len; i++) {
            frontmatterDiv.createEl("a", { text: prop[i], cls: "dn-fproperties" }).onClickEvent((evt2) => {
              if (evt2.button === 2) {
                evt2.preventDefault();
              } else {
                fpModal.close();
                this._INPUT_SEARCH.value = prop[i];
                this.dnModalSearchVault(this._INPUT_SEARCH.value);
              }
            });
            frontmatterDiv.createEl("br");
          }
        } else {
          frontmatterDiv.createEl("span", { text: "No frontmatter" });
        }
        fpModal.contentEl.createEl("br");
        const divBottom = fpModal.contentEl.createEl("div", { cls: "dn-div-bottom-properties" });
        const btnPropsOpen = divBottom.createEl("button", { text: "Open", cls: "dn-btn-properties-open-file" });
        btnPropsOpen.onClickEvent(() => {
          fpModal.close();
          this.dnOpenFile(file);
        });
        const btnCloseProps = divBottom.createEl("button", { text: "Close", cls: "dn-btn-properties-close" });
        btnCloseProps.onClickEvent(() => {
          fpModal.close();
        });
        fpModal.open();
        frontmatterDiv.blur();
      })
    );
    this._DN_CTX_MENU.addItem(
      (item) => item.setTitle("File properties").setIcon("file-cog").onClick(() => {
        const mdFileProps = new import_obsidian2.Modal(this.app);
        mdFileProps.contentEl.setAttribute("class", "dn-properties-modal");
        mdFileProps.contentEl.createEl("h4", { text: "File properties" });
        const rowName = mdFileProps.contentEl.createEl("div", { cls: "dn-property-row" });
        rowName.createEl("div", { text: "Name: ", cls: "dn-property-name" });
        rowName.createEl("div", { text: file.name, cls: "dn-property-value" });
        const rowExt = mdFileProps.contentEl.createEl("div", { cls: "dn-property-row" });
        rowExt.createEl("div", { text: "Extension: ", cls: "dn-property-name" });
        const rowExtValue = rowExt.createEl("div", { cls: "dn-property-value" });
        rowExtValue.createEl("span", { text: file.extension, cls: "nav-file-tag" });
        const rowPath = mdFileProps.contentEl.createEl("div", { cls: "dn-property-row" });
        rowPath.createEl("div", { text: "Path: ", cls: "dn-property-name" });
        rowPath.createEl("div", { text: getFolderStructure(file.path), cls: "dn-property-value" });
        mdFileProps.contentEl.createEl("br");
        const rowSize = mdFileProps.contentEl.createEl("div", { cls: "dn-property-row" });
        rowSize.createEl("div", { text: "Size: ", cls: "dn-property-name" });
        rowSize.createEl("div", { text: formatFileSize(file.stat.size) + " bytes" + formatFileSizeKBMB(file.stat.size) });
        mdFileProps.contentEl.createEl("br");
        const rowDateCreated = mdFileProps.contentEl.createEl("div", { cls: "dn-property-row" });
        rowDateCreated.createEl("div", { text: "Created: ", cls: "dn-property-name" });
        rowDateCreated.createEl("div", { text: (0, import_obsidian3.moment)(file.stat.ctime).format(this.date_format) });
        const rowDateModified = mdFileProps.contentEl.createEl("div", { cls: "dn-property-row" });
        rowDateModified.createEl("div", { text: "Modified: ", cls: "dn-property-name" });
        rowDateModified.createEl("div", { text: (0, import_obsidian3.moment)(file.stat.mtime).format(this.date_format) });
        mdFileProps.contentEl.createEl("br");
        const rowTags = mdFileProps.contentEl.createEl("div", { cls: "dn-property-row" });
        rowTags.createEl("div", { text: "Tag(s): ", cls: "dn-property-name" });
        const propTags = rowTags.createEl("div");
        const curTags = getTagsPerFile(file);
        if (curTags !== "") {
          const tags = curTags.split(" ");
          for (let i = 0, len = tags.length; i < len; i++) {
            propTags.createEl("a", { text: tags[i], href: tags[i], cls: "tag" }).onClickEvent((evt2) => {
              if (evt2.button === 2) {
                evt2.preventDefault();
              } else {
                mdFileProps.close();
                this._INPUT_SEARCH.value = tags[i];
                this.dnModalSearchVault(this._INPUT_SEARCH.value);
              }
            });
          }
        } else {
          propTags.createEl("span", { text: "No tags" });
        }
        mdFileProps.contentEl.createEl("br");
        mdFileProps.contentEl.createEl("span", { text: "Frontmatter: ", cls: "dn-properties" });
        mdFileProps.contentEl.createEl("br");
        const frontmatterProps = mdFileProps.contentEl.createEl("div", { cls: "dn-properties-frontmatter" });
        frontmatterProps.setAttribute("contenteditable", "true");
        frontmatterProps.setAttribute("spellcheck", "false");
        const curProps = getPropsPerFile(file);
        if (curProps) {
          const prop = curProps.split(" \n");
          for (let i = 0, len = prop.length; i < len; i++) {
            frontmatterProps.createEl("a", { text: prop[i], cls: "dn-fproperties" }).onClickEvent((evt2) => {
              if (evt2.button === 2) {
                evt2.preventDefault();
              } else {
                mdFileProps.close();
                this._INPUT_SEARCH.value = prop[i];
                this.dnModalSearchVault(this._INPUT_SEARCH.value);
              }
            });
            frontmatterProps.createEl("br");
          }
        } else {
          frontmatterProps.createEl("span", { text: "No frontmatter" });
        }
        mdFileProps.contentEl.createEl("br");
        const divBottom = mdFileProps.contentEl.createEl("div", { cls: "dn-div-bottom-properties" });
        const btnPropsOpen = divBottom.createEl("button", { text: "Open", cls: "dn-btn-properties-open-file" });
        btnPropsOpen.onClickEvent(() => {
          mdFileProps.close();
          this.dnOpenFile(file);
        });
        const btnCloseProps = divBottom.createEl("button", { text: "Close", cls: "dn-btn-properties-close" });
        btnCloseProps.onClickEvent(() => {
          mdFileProps.close();
        });
        mdFileProps.open();
        frontmatterProps.blur();
      })
    );
    this._DN_CTX_MENU.showAtMouseEvent(evt);
  }
  dnHandleClick(evt, file) {
    if (!evt || typeof evt !== "object" || !(file instanceof import_obsidian2.TFile)) {
      return;
    }
    this.dnSelectTableRow(evt);
    if (evt.button === 2) {
      evt.preventDefault();
      this.dnGenerateContextMenu(evt, file);
    }
  }
  dnHandleDblClick(evt, file) {
    if (!evt || typeof evt !== "object" || !(file instanceof import_obsidian2.TFile)) {
      return;
    }
    evt.preventDefault();
    this.dnSelectTableRow(evt);
    this.dnOpenFile(file);
  }
  dnHandleHoverPreview(evt, file) {
    evt.stopImmediatePropagation();
    if (evt.ctrlKey || evt.metaKey) {
      this.dnShowPreviewFile(evt, file);
    }
  }
  dnShowPreviewFile(evt, file) {
    this._hoverDiv.empty();
    const topBar = this._hoverDiv.createEl("div", { cls: "dn-preview-top-bar" });
    const btnClosePreview = topBar.createEl("div", { cls: "modal-close-button" });
    btnClosePreview.onClickEvent((evt2) => {
      evt2.stopPropagation();
      this.dnHidePreview();
    });
    const previewTop = topBar.createEl("div", "dn-preview-titlebar");
    const divPreviewName = previewTop.createEl("div", { cls: "dn-property-row" });
    divPreviewName.createEl("div", { text: "Name: ", cls: "dn-property-name-sm" });
    divPreviewName.createEl("div", { text: file.name, cls: "dn-property-value" });
    const divPreviewPath = previewTop.createEl("div", { cls: "dn-property-row" });
    divPreviewPath.createEl("div", { text: "Path: ", cls: "dn-property-name-sm" });
    divPreviewPath.createEl("div", { text: getFolderStructure(file.path), cls: "dn-property-value" });
    const divButtons = topBar.createEl("div", { cls: "dn-div-top-preview-btns" });
    const btnPreviewOpenFile = divButtons.createEl("button", { text: "Open", cls: "dn-btn-properties-open-file" });
    btnPreviewOpenFile.onClickEvent(() => {
      this.dnHidePreview();
      this.close();
      this.dnOpenFile(file);
    });
    const btnPreviewOpenFileNewTab = divButtons.createEl("button", { text: "Open in new tab", cls: "dn-btn-properties-open-file" });
    btnPreviewOpenFileNewTab.onClickEvent(() => {
      this.dnHidePreview();
      this.close();
      this.app.workspace.getLeaf("tab").openFile(file);
    });
    const btnPreviewOpenFileNewWindow = divButtons.createEl("button", { text: "Open in new window", cls: "dn-btn-properties-open-file" });
    btnPreviewOpenFileNewWindow.onClickEvent(() => {
      this.dnHidePreview();
      this.app.workspace.getLeaf("window").openFile(file);
    });
    this._hoverRender = this._hoverDiv.createEl("div", { cls: "dn-pr-content" });
    try {
      import_obsidian2.MarkdownRenderer.render(
        this.app,
        "![[" + (0, import_obsidian2.normalizePath)(file.path) + "]]",
        this._hoverRender,
        (0, import_obsidian2.normalizePath)(file.path),
        this._previewComponent
      );
    } catch (error) {
      return;
    }
    this._hoverDiv.style.display = "block";
    previewTop.addEventListener("mousedown", (evt2) => this.dnHoverDragOnMouseDown(evt2));
    this._hoverDiv.addEventListener("mousemove", (evt2) => this.dnHoverDragOnMouseMove(evt2));
    this._hoverDiv.addEventListener("mouseup", (evt2) => this.dnHoverDragOnMouseUp(evt2));
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;
    const divW = this._hoverDiv.offsetWidth;
    const divH = this._hoverDiv.offsetHeight;
    if (this._hoverDivLeft === "") {
      this._hoverDiv.style.left = ((screenWidth - divW) / 2).toString() + "px";
      this._hoverDiv.style.top = ((screenHeight - divH) / 2).toString() + "px";
    }
    previewTop.removeEventListener("mousedown", (evt2) => this.dnHoverDragOnMouseDown(evt2));
  }
  dnHidePreview() {
    this._isDraggingPreview = false;
    this._hoverDiv.style.display = "none";
    this._hoverDiv.empty();
  }
  dnHandleNormalSearch(rExp, file) {
    return rExp.test(file.name.toLowerCase()) || rExp.test(getFolderStructure(file.path).toLowerCase()) || rExp.test((0, import_obsidian3.moment)(file.stat.mtime).format(this.date_format)) || rExp.test(getTagsPerFile(file).toLowerCase()) || rExp.test(getPropsPerFile(file).toLowerCase());
  }
  dnHandleSpecialSearch(search, file) {
    const mtime = (0, import_obsidian3.moment)(file.stat.mtime);
    switch (search) {
      case "d":
      case "day":
      case "today":
        return mtime.isSame((0, import_obsidian3.moment)(), "day");
      case "d-1":
      case "day-1":
      case "yesterday":
        return mtime.isBetween((0, import_obsidian3.moment)().subtract(1, "days"), (0, import_obsidian3.moment)(), "day", "[]");
      case "d-2":
      case "day-2":
        return mtime.isBetween((0, import_obsidian3.moment)().subtract(2, "days"), (0, import_obsidian3.moment)(), "day", "[]");
      case "d-3":
      case "day-3":
        return mtime.isBetween((0, import_obsidian3.moment)().subtract(3, "days"), (0, import_obsidian3.moment)(), "day", "[]");
      case "d-4":
      case "day-4":
        return mtime.isBetween((0, import_obsidian3.moment)().subtract(4, "days"), (0, import_obsidian3.moment)(), "day", "[]");
      case "d-5":
      case "day-5":
        return mtime.isBetween((0, import_obsidian3.moment)().subtract(5, "days"), (0, import_obsidian3.moment)(), "day", "[]");
      case "d-6":
      case "day-6":
        return mtime.isBetween((0, import_obsidian3.moment)().subtract(6, "days"), (0, import_obsidian3.moment)(), "day", "[]");
      case "d-7":
      case "day-7":
      case "w":
      case "week":
        return mtime.isBetween((0, import_obsidian3.moment)().subtract(7, "days"), (0, import_obsidian3.moment)(), "day", "[]");
      case "m":
      case "month":
        return mtime.isSame((0, import_obsidian3.moment)(), "month");
      case "m-1":
      case "month-1":
        return mtime.isBetween((0, import_obsidian3.moment)().subtract(1, "month"), (0, import_obsidian3.moment)(), "month", "[]");
      case "m-2":
      case "month-2":
        return mtime.isBetween((0, import_obsidian3.moment)().subtract(2, "month"), (0, import_obsidian3.moment)(), "month", "[]");
      case "m-3":
      case "month-3":
        return mtime.isBetween((0, import_obsidian3.moment)().subtract(3, "month"), (0, import_obsidian3.moment)(), "month", "[]");
      case "m-4":
      case "month-4":
        return mtime.isBetween((0, import_obsidian3.moment)().subtract(4, "month"), (0, import_obsidian3.moment)(), "month", "[]");
      case "m-5":
      case "month-5":
        return mtime.isBetween((0, import_obsidian3.moment)().subtract(5, "month"), (0, import_obsidian3.moment)(), "month", "[]");
      case "m-6":
      case "month-6":
        return mtime.isBetween((0, import_obsidian3.moment)().subtract(6, "month"), (0, import_obsidian3.moment)(), "month", "[]");
      case "m-7":
      case "month-7":
        return mtime.isBetween((0, import_obsidian3.moment)().subtract(7, "month"), (0, import_obsidian3.moment)(), "month", "[]");
      case "m-8":
      case "month-8":
        return mtime.isBetween((0, import_obsidian3.moment)().subtract(8, "month"), (0, import_obsidian3.moment)(), "month", "[]");
      case "m-9":
      case "month-9":
        return mtime.isBetween((0, import_obsidian3.moment)().subtract(9, "month"), (0, import_obsidian3.moment)(), "month", "[]");
      case "m-10":
      case "month-10":
        return mtime.isBetween((0, import_obsidian3.moment)().subtract(10, "month"), (0, import_obsidian3.moment)(), "month", "[]");
      case "m-11":
      case "month-11":
        return mtime.isBetween((0, import_obsidian3.moment)().subtract(11, "month"), (0, import_obsidian3.moment)(), "month", "[]");
      case "m-12":
      case "month-12":
        return mtime.isBetween((0, import_obsidian3.moment)().subtract(12, "month"), (0, import_obsidian3.moment)(), "month", "[]");
      case "y":
      case "year":
        return mtime.isSame((0, import_obsidian3.moment)(), "year");
      case "n":
      case "notes":
        return this._notes.includes(file);
      case "c":
      case "canvases":
      case "canvas":
        return this._canvas.includes(file);
      case "i":
      case "images":
        return this._images.includes(file);
      case "a":
      case "audios":
        return this._audios.includes(file);
      case "v":
      case "videos":
        return this._videos.includes(file);
      case "p":
      case "pdf":
      case "pdfs":
        return this._pdf.includes(file);
      case "o":
      case "other":
      case "others":
        return this._other.includes(file);
      default:
        return false;
    }
  }
  dnOpenFileAlt(f, evt) {
    if (!evt || typeof evt !== "object" || !(f instanceof import_obsidian2.TFile)) {
      return;
    }
    try {
      if (evt.button === 0 && (evt.ctrlKey || evt.metaKey)) {
        this.app.workspace.getLeaf("tab").openFile(f);
      } else if (evt.button === 1) {
        this.app.workspace.getLeaf("tab").openFile(f);
      } else if (evt.button === 0) {
        this.dnOpenFile(f);
      } else if (evt.button === 2 && !(evt.target instanceof HTMLTableCellElement)) {
        evt.preventDefault();
        this.dnGenerateContextMenu(evt, f);
      }
    } catch (er) {
      return;
    }
  }
  dnOpenFile(file) {
    this.app.workspace.getLeaf(false).openFile(file);
    this.close();
  }
  dnHoverDragOnMouseDown(evt) {
    evt.stopPropagation();
    this._isDraggingPreview = true;
    this.initialX = evt.screenX - this._hoverDiv.offsetLeft;
    this.initialY = evt.screenY - this._hoverDiv.offsetTop;
    this.previousX = evt.screenX;
    this.previousY = evt.screenY;
  }
  dnHoverDragOnMouseMove(evt) {
    evt.stopPropagation();
    if (this._isDraggingPreview) {
      const newX = evt.screenX - this.initialX;
      const newY = evt.screenY - this.initialY;
      if (Math.abs(evt.screenX - this.previousX) > 5 || Math.abs(evt.screenY - this.previousY) > 5) {
        this._hoverDiv.style.left = newX + "px";
        this._hoverDiv.style.top = newY + "px";
        this.previousX = evt.screenX;
        this.previousY = evt.screenY;
      }
      this._hoverDivLeft = newX + "px";
      this._hoverDivTop = newY + "px";
    }
  }
  dnHoverDragOnMouseUp(evt) {
    evt.stopPropagation();
    this._isDraggingPreview = false;
  }
  onClose() {
    const { contentEl } = this;
    contentEl.empty();
    this._previewComponent.unload();
    if (this._INPUT_SEARCH && this._INPUT_SEARCH.removeEventListener) {
      this._INPUT_SEARCH.removeEventListener("input", (0, import_obsidian2.debounce)(() => this.dnModalSearchVault(this._INPUT_SEARCH.value), 300, true));
    }
    this._th1.removeEventListener("dblclick", () => this.dnAlternateSortColumn("name"));
    this._th2.removeEventListener("dblclick", () => this.dnAlternateSortColumn("ext"));
    this._th3.removeEventListener("dblclick", () => this.dnAlternateSortColumn("path"));
    this._th4.removeEventListener("dblclick", () => this.dnAlternateSortColumn("size"));
    this._th5.removeEventListener("dblclick", () => this.dnAlternateSortColumn("modified"));
    this._SELECT_SORT.removeEventListener("change", () => {
      this.dnSortColumnWithSelect();
    });
    this._hoverDiv.removeEventListener("mousemove", (evt) => this.dnHoverDragOnMouseMove(evt));
    this._hoverDiv.removeEventListener("mouseup", (evt) => this.dnHoverDragOnMouseUp(evt));
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
    }
  }
};

// src/main.ts
var DEFAULT_SETTINGS = {
  default_view: 1,
  font_size: 16,
  selected_table_layout: "dn-tbl-default",
  date_format: "YYYY-MM-DD HH:mm",
  files_per_page: 20,
  num_recent_files: 5,
  excluded_ext: "",
  excluded_path: "",
  color_notes: "#bf48ff",
  color_images: "#007fff",
  color_canvas: "#ff7f28",
  color_videos: "#d34848",
  color_audios: "#bfbf00",
  color_pdf: "#00a300",
  color_other: "#828282",
  colored_files: false,
  hide_ext: false,
  hide_path: false,
  hide_size: false,
  hide_date: false,
  hide_tags: false,
  hide_frontmatter: false,
  hide_columns: []
};
var DNPlugin = class extends import_obsidian4.Plugin {
  async onload() {
    await this.loadSettings();
    this.DN_MODAL = new DNModal(this.app);
    this.DN_MODAL.default_view = this.settings.default_view;
    this.DN_MODAL.date_format = this.settings.date_format;
    this.DN_MODAL.num_recent_files = this.settings.num_recent_files;
    this.DN_MODAL.files_per_page = this.settings.files_per_page;
    this.DN_MODAL.selected_table_layout = this.settings.selected_table_layout;
    this.DN_MODAL.excluded_extensions = this.dnGetExcludedExtensions(this.settings.excluded_ext);
    this.DN_MODAL.excluded_folders = this.dnGetExcludedFolders(this.settings.excluded_path);
    this.dnSetFontSize(this.settings.font_size);
    this.DN_MODAL.colored_files = this.settings.colored_files;
    this.DN_MODAL.color_notes = this.settings.color_notes;
    this.DN_MODAL.color_canvas = this.settings.color_canvas;
    this.DN_MODAL.color_images = this.settings.color_images;
    this.DN_MODAL.color_videos = this.settings.color_videos;
    this.DN_MODAL.color_audios = this.settings.color_audios;
    this.DN_MODAL.color_pdf = this.settings.color_pdf;
    this.DN_MODAL.color_other = this.settings.color_other;
    this.DN_MODAL.hide_columns = this.dnSetHiddenColumns(this.settings.hide_columns);
    this.addRibbonIcon("gauge", "Open dashboard navigator", (evt) => {
      this.DN_MODAL.default_view = this.settings.default_view;
      this.DN_MODAL.open();
    });
    this.addCommand({
      id: "dashboard",
      name: "Open dashboard",
      callback: () => {
        this.DN_MODAL.default_view = 1;
        this.DN_MODAL.open();
      }
    });
    this.addCommand({
      id: "navigator",
      name: "Open navigator",
      callback: () => {
        this.DN_MODAL.default_view = 2;
        this.DN_MODAL.open();
      }
    });
    this.addSettingTab(new DNSettingTab(this.app, this));
  }
  dnSetFontSize(val) {
    if (val >= 12 || val <= 24) {
      document.body.style.setProperty("--dn-font-size", val.toString() + "px");
    }
  }
  dnSetHiddenColumns(arrCols) {
    const allowedCols = ["ext", "path", "size", "date", "tags", "frontmatter"];
    arrCols = arrCols.filter((col) => allowedCols.includes(col));
    if (arrCols.length <= 6 && arrCols.some((col) => ["ext", "path", "size", "date", "tags", "frontmatter"].includes(col))) {
      return arrCols;
    } else {
      this.settings.hide_columns = [];
      this.settings.hide_ext = false;
      this.settings.hide_path = false;
      this.settings.hide_size = false;
      this.settings.hide_date = false;
      this.settings.hide_tags = false;
      this.settings.hide_frontmatter = false;
      this.saveSettings();
      return [];
    }
  }
  dnUpdateHideColumn(col, val) {
    const allowedCols = ["ext", "path", "size", "date", "tags", "frontmatter"];
    if (allowedCols.includes(col) && val === true) {
      if (!this.settings.hide_columns.includes(col)) {
        this.settings.hide_columns.push(col);
        this.DN_MODAL.hide_columns = this.settings.hide_columns;
      }
    } else {
      this.settings.hide_columns = this.settings.hide_columns.filter((c) => c !== col);
      this.DN_MODAL.hide_columns = this.settings.hide_columns;
    }
  }
  dnGetExcludedFolders(foldersString) {
    if (foldersString === "") {
      return [];
    }
    const folders = foldersString.split(",").map((folder) => folder.trim());
    return folders.map((folder) => folder.replace(/^\/|\/$|\.\./g, "")).filter((folder) => folder !== "");
  }
  dnGetExcludedExtensions(excluded_ext) {
    if (excluded_ext === "") {
      return [];
    }
    return excluded_ext.split(",").map((extension) => extension.trim());
  }
  onunload() {
  }
  async loadSettings() {
    this.settings = Object.assign(
      {},
      DEFAULT_SETTINGS,
      await this.loadData()
    );
  }
  async saveSettings() {
    await this.saveData(this.settings);
  }
};


/* nosourcemap */