---
doc_type: weread-highlights-reviews
bookId: "3300082609"
reviewCount: 0
noteCount: 3
author: 斯蒂芬·沃尔弗拉姆
cover: https://cdn.weread.qq.com/weread/cover/24/cpplatform_4cn8w4tmgzntjobg9ffeny/t6_cpplatform_4cn8w4tmgzntjobg9ffeny1703648785.jpg
progress: 73%
readingTime: 2小时49分钟
readingDate: 2025-01-14
isbn: 9787115618085
lastReadDate: 2025-01-19

---
# 元数据
> [!abstract] 这就是ChatGPT
> - ![ 这就是ChatGPT|200](https://cdn.weread.qq.com/weread/cover/24/cpplatform_4cn8w4tmgzntjobg9ffeny/t6_cpplatform_4cn8w4tmgzntjobg9ffeny1703648785.jpg)
> - 书名： 这就是ChatGPT
> - 作者： 斯蒂芬·沃尔弗拉姆
> - 简介： ChatGPT是OpenAI开发的人工智能聊天机器人程序，于2022年11月推出。它能够自动生成一些表面上看起来像人类写的文字，这是一件很厉害且出乎大家意料的事。那么，它是如何做到的呢？又为何能做到呢？本书会大致介绍ChatGPT的内部机制，然后探讨一下为什么它能很好地生成我们认为有意义的文本。
> - 出版时间： 2023-07-01 00:00:00
> - ISBN： 9787115618085
> - 分类： 计算机-人工智能
> - 出版社： 人民邮电出版社有限公司
> - PC地址：https://weread.qq.com/web/reader/74332a90813ab86c4g019d98

# 高亮划线

## 封面

## 版权信息

## 导读序 奇事·奇人·奇书

### 奇人

### 奇书

## 前言

## 第一篇 ChatGPT在做什么？它为何能做到这些？

### 概率从何而来

### 什么是模型

### 类人任务(human-like task)的模型

### 神经网络

### 机器学习和神经网络的训练

### 神经网络训练的实践和学问

### “足够大的神经网络当然无所不能！”

### “嵌入”的概念

> 📌 可以将嵌入视为一种尝试通过数的数组来表示某些东西“本质”的方法，其特性是“相近的事物”由相近的数表示。 
> ⏱ 2025-01-19 11:15:44 ^3300082609-15-546-597

### ChatGPT的内部原理

> 📌 ChatGPT（或者说它基于的GPT-3网络）到底是在做什么呢？它的总体目标是，根据所接受的训练（查看来自互联网的数十亿页文本，等等），以“合理”的方式续写文本。所以在任意给定时刻，它都有一定量的文本，而目标是为要添加的下一个标记做出适当的选择。 
> ⏱ 2025-01-19 16:48:51 ^3300082609-16-1029-1152

> 📌 它的操作分为三个基本阶段。第一阶段，它获取与目前的文本相对应的标记序列，并找到表示这些标记的一个嵌入（即由数组成的数组）。第二阶段，它以“标准的神经网络的方式”对此嵌入进行操作，值“像涟漪一样依次通过”网络中的各层，从而产生一个新的嵌入（即一个新的数组）。第三阶段，它获取此数组的最后一部分，并据此生成包含约50000个值的数组，这些值就成了各个可能的下一个标记的概率。（没错，使用的标记数量恰好与英语常用词的数量相当，尽管其中只有约3000个标记是完整的词，其余的则是片段。 
> ⏱ 2025-01-19 16:49:27 ^3300082609-16-1181-1419

### ChatGPT的训练

### 在基础训练之外

### 真正让ChatGPT发挥作用的是什么

### 意义空间和语义运动定律

### 语义语法和计算语言的力量

### 那么，ChatGPT到底在做什么？它为什么能做到这些？

### 致谢

## 第二篇 利用Wolfram|Alpha为ChatGPT赋予计算知识超能力

### ChatGPT和Wolfram|Alpha

### 一个简单的例子

### 再举几个例子

### 前方的路

### 相关资源

# 读书笔记

# 本书评论

