## 结语

祝贺您完成了第二单元的 `LangGraph` 模块！🥳

您现在已经掌握了使用 LangGraph 构建结构化工作流的基础知识，这些工作流可以直接投入生产环境。

本模块只是您 LangGraph 学习之旅的起点。对于更深入的学习内容，我们推荐：

- 探索 [LangGraph 官方文档](https://github.com/langchain-ai/langgraph)
- 参加 LangChain Academy 的 [LangGraph 入门课程](https://academy.langchain.com/courses/intro-to-langgraph)
- 亲自构建一些项目！

在下一个单元中，您将探索真实的应用场景。是时候将理论付诸实践了！

我们非常重视 **您对课程的想法和改进建议** 。如果有任何反馈，请👉 [填写此表单](https://docs.google.com/forms/d/e/1FAIpQLSe9VaONn0eglax0uTwi29rIn4tM7H2sYmmybmG5jJNlE5v0xA/viewform?usp=dialog)

### 持续学习，保持激情！🤗

尊敬的先生/女士！🎩🦇

\-Alfred-

[< \> Update on GitHub](https://github.com/huggingface/agents-course/blob/main/units/zh-CN/unit2/langgraph/conclusion.mdx)

测试你对 LangGraph 的理解

[← 快速测验1](https://huggingface.co/learn/agents-course/zh-CN/unit2/langgraph/quiz1) [Agentic RAG 用例简介 →](https://huggingface.co/learn/agents-course/zh-CN/unit3/agentic-rag/introduction)