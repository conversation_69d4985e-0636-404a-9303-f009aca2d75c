## Demo
- 展示之前的设计
	- 缺点
		1. 查看query需要跳转到另一个tab，运行query又要跳转回去
		2. 输入query参数不够方便。用户无法直接看到参数类型。并且手动地编辑json，容易引起类型错误或格式错误
- 新的设计
	- 布局的改变
		-  在新的设计中，用户仍然可以创建文件，并在其中运行所有支持的gsql指令。但我们将它放在了次要地位，以突显出query的重要性。
		- 将之前的"Query" tab改成了“Graph" tab，在这个tab下展示了所有的graph。展开一个graph，用户可以看到该graph的schema list和query list。
	- graph list
		- 展开schema list，会列出所有的vertex type和edge type。如果点击某个type，这会唤起schema designer组件，并且在schema graph中自动选中该type。
		- query list列出当前所有的gsql queries。点击一个query，会出现一个query的预览页面，其中显示了query的参数和内容。用户可以点击下方的按钮进行query相关的操作。
			- 如果用户要编辑query，可以选择新建一个文件或者将query内容append到当前打开的文件。编辑完query后，点击右上方的运行文件按钮，query就保存到了gsql。
			- 如果用户点击install，安装的进度条会显示在下方的result区域。
			- 点击运行会出现运行query的面板。目前这里显示了供用户输入参数的表单，在后续还会添加更多的内容，比如query的api路径、构建出的json payload等。
				- 对于不同的参数类型，我们提供了不同类型的input组件，并且自动填充了默认参数以及参数声明语句中的节点类型等。
			- 用户可以在下方设置运行时的时间限制和内存限制。如果是在非udf模式下安装的query，还可以开启query profile。(演示运行build_concept_transaction_risk)
	- graph algorithms
		- 我们增加了graph algoritms的支持。用户可以展开不同的类型来浏览该类型下的graph algorithms，点击一个具体的算法会出现预览页面。
		- 在预览页面中可以看到算法的描述及代码。对于算法，我们提供了三个操作：1. 点击learn会跳转到官方的学习页面；2. 点击复制可以在文件中显示query内容；3.点击install可以将算法安装到某一个graph中。
## Speech
以下是根据你提供的梗概扩展的完整英文演讲稿，适合用于你在团队中的 UI 改进 Demo 演示：

---

**Demo Speech: New UI Improvements for the Graph Query Editor**

Hi everyone.  Today, I will walk you through the latest UI improvements we've made to the GSQL Editor in Savanna. This update addresses several usability issues and enhance the developer experience. Since the development is not completely completed yet, there are still some functionalities to be added, and the UI is not in the best state.

---

### 🔙 **Previous Design: What Needed Improvement**

Let’s start by briefly revisiting our previous design and identifying some of the issues that led us to make this change.

1. The first issue is **Fragmented Workflow Between Tabs**  
    Previously, if you wanted to view a GSQL query, you had to switch to a separate “Query” tab. But to actually run the query, you had to jump back. This constant tab-switching broke the user's focus and made the workflow inefficient.
    
2. The second issues is **Inconvenient Parameter Input**  
    Another issue was the process of inputting query parameters. Users had no easy way to see parameter types or requirements. Instead, they had to manually construct JSON input, which was not only inconvenient but also error-prone—especially when dealing with type mismatches or incorrect formats.

---

### ✨ **New Design: Overview and Key Changes**

Now let’s talk about what’s new.

#### 🔄 **Layout Redesign**

This is our new UI. In the new UI, users can still create and manage files as before, and run any GSQL statements in the file. However, we’ve intentionally moved this feature to a secondary position in the UI. Because we want to highlight the position of graph and **queries**.

We’ve renamed the old “Query” tab to **“Graphs”**, and under this tab, users can now access and interact with all graphs. When a graph is expanded, it reveals two sections:

1. **Schema List**  
    The Schema lists all **vertex types** and **edge types** within the graph. Clicking on an item will trigger the schema designer component, and the corresponding type will be selected within the schema graph.
    
2. **Query List**  
	Query list all available GSQL queries for that graph. Clicking on a query opens a **preview panel** showing the query's parameters and code. From this panel, users can perform a number of actions:
    
    - To **edit** a query, we can either create a new file or append the query content to the currently open file. Once editing is complete, simply click the "Run File" button in the top right, and the query will be saved to GSQL.
        
    - Clicking **Install** will displays a installation progress in the bottom result area.
        
    - Clicking **Run** opens a **query execution panel**, where users can now enter input via a smart form rather than a json string. We will continue to add more contents to this panel, like the api endpoint for the query and generated json payload.
            
    The form uses different input component based on the parameter type. It auto-populates default values and vertex types if they are showed in the parameter declaration.
    
    At the bottom, users can configure **runtime options** like timeout and memory usage. And if the query is installed in non=UDF mode, they can toggle on the **Query Profiler**.
    
    _(Here we can demonstrate running the `build_concept_transaction_risk` query as an example.)_
    

---

### 📈 **New Feature: Graph Algorithms Support**

We’ve also added support for **Graph Algorithms**. Users can browse and explore algorithms by category. Clicking on a algorithm opens a **preview page** that includes a description and the full algorithm code. We provide three actions here: **Learn** – which links directly to our official documentation for in-depth study
1. **Copy** – which appends the algorithm's query code to the current file
2. **Install** – which installs the algorithm to a selected graph
    
---
## Tasks
- [-] use null
- [x] fix: pass map parameter
- [x] parameter invalidation
- [x] handle bigint
- [x] #task double merge to 3.10.4: https://github.com/tigergraph/tools/pull/2564 https://github.com/tigergraph/gus/pull/1110 ⏳ 2025-06-09 ✅ 2025-06-11
- [x] close other tab, close all tab
- [x] show run query parameter in command title
- [x] interpreted mode
- [ ] reproduce interpret issue in 4.2
- [ ] refactor run query drawer to support restpp endpoints
	- [x] refactor snippets component to support restpp endpoints
	- [x] #task refactor run query drawer to support restpp endpoints ⏳ 2025-06-05 ✅ 2025-06-06
		- [x] code snippets
		- [x] execute
## ParamType
```json
// vertex with vertexType
{
    "paramName": "ver",
    "paramType": {
        "type": "VERTEX",
        "vertexType": "Card"
    }
}

// vertex list
{
    "paramName": "concept_verts",
    "paramType": {
        "type": "LIST",
        "elementType": {
            "type": "VERTEX",
            "vertexType": "Concept"
        }
    }
}

// simple type
{
    "paramName": "print_results",
    "paramType": {
        "type": "BOOL"
    },
    "paramDefaultValue": "false"
}

// datetime
{
    "paramName": "min_createTime",
    "paramType": {
        "type": "DATETIME"
    },
    "paramDefaultValue": "1546732800"
}
```
- convertGSQLParams
## Query Parameters
- [Queries :: GSQL Language Reference](https://docs.tigergraph.com/gsql-ref/4.1/querying/query-operations#_query_parameters)
- [Data Types :: GSQL Language Reference](https://docs.tigergraph.com/gsql-ref/4.1/querying/data-types#_query_parameter_types)
```ebnf
parameterType := INT
               | UINT
               | FLOAT
               | DOUBLE
               | STRING
               | BOOL
               | VERTEX ["<" vertexType ">"]
               | DATETIME
               | [ SET | BAG | LIST] "<" baseType ">"
               | MAP<basetype, basetype>
               | FILE

baseType := INT
          | UINT
          | FLOAT
          | DOUBLE
          | STRING
          | BOOL
          | VERTEX ["<" vertexType ">"]
          | DATETIME
```
- Vertex
	- Set or bag of typed vertex parameters `VERTEX<type>`
		- [ "person3", "person4" ]
	- Set or bag of generic `VERTEX` parameters
		- [ ("person1","Person"),("11","Post") ]
## Build params from user inputs
- `buildParamsForInstalledMode`
- `buildParamsForInterpretedMode`
## Parameters response
```json
// map<string, double>
{
    "query": {
        "default": "single_card_lookup",
        "type": "STRING"
    },
    "ver": {
        "id_type": "Card",
        "index": 0,
        "is_id": "true",
        "min_count": 0,
        "type": "STRING"
    }
}
```
### Restpp
```json
{
    "query": {
        "default": "single_card_lookup",
        "type": "STRING"
    },
    "read_committed": {
        "max_count": 1,
        "min_count": 0,
        "type": "BOOL"
    },
    "ver": {
        "id_type": "Card",
        "is_id": "true",
        "min_count": 0,
        "type": "STRING"
    }
}
```
## QA
- what is map?
- what is "use NULL"