# Mastering Bitcoin: Programming the Open Blockchain (3rd Edition)

## ## ch03_bitcoin-core
- 提供这种功能的软件被称为全验证节点，因为它会根据系统中的每一条规则验证每一笔已确认的比特币交易。全验证节点（简称*全节点*）还可能提供工具和数据，帮助理解比特币的工作原理以及当前网络中正在发生的事情。
- 开源
- Bitcoin Core
- *Bitcoin Core* 是比特币系统的参考实现，意味着它提供了技术每个部分应如何实现的参考。Bitcoin Core 实现了比特币的所有方面，包括钱包、交易和区块验证引擎、区块构建工具以及比特币点对点通信的所有现代部分。
- 尽管 Bitcoin Core 作为系统中许多主要部分的参考实现，比特币白皮书描述了系统的几个早期部分。自 2011 年以来，系统的大多数主要部分都记录在一组 [比特币改进提案（BIPs）](https://oreil.ly/BCXAQ) 中
- 
- 
- 
- 
- 标签
- 发布候选版本
- 根据你是否选择索引所有交易并保留区块链的完整副本，你可能还需要大量的磁盘空间——如果你计划运行 Bitcoin Core 多年，至少需要 1 TB。默认情况下，比特币节点还会将交易和区块传输到其他节点（称为“对等节点”），消耗上传互联网带宽。如果你的互联网连接有限、数据上限较低或按千兆字节计费，你可能不应该在其上运行比特币节点，或者以限制其带宽的方式运行它（参见 <<bitcoincorenode_config>>）
- 
- 
- 为什么你会想运行一个节点？以下是一些最常见的原因：
- 你不想依赖任何第三方来验证你收到的交易。
- 你不想向第三方披露哪些交易属于你的钱包。
- 你正在开发比特币软件，需要依赖比特币节点以编程方式（API）访问网络和区块链。
- 你正在构建必须根据比特币共识规则验证交易的应用程序。通常，比特币软件公司会运行多个节点。
- 你想支持比特币。运行一个节点并使用它来验证你收到的交易，可以使网络更加健壮。
- 
- 任何
- 
- 
- 
- 
- 
- Blockchain
- 
- 
- 
- 
- 高度
- 哈希
- 区块头哈希
- 深度
- 点上）的过程（函数）。在
- 
- 
- 然而，大多数流行的编程语言中都有库可以“包装”Bitcoin Core API，使其更加简单。我们将使用 +python-bitcoinlib+ 库来简化 API 访问。
- 
- 
- 
- 
- 
- 
- 比特币生态系统中有许多替代客户端、库、工具包，甚至全节点实现

