[[install]]
## Notes
```bash
docker run -d -p 14022:22 -p 14240:14240 -v ~/projects/product:/home/<USER>/product --name tigergraph tginternal/tigergraph:4.1.0

docker exec -it tigergraph /bin/bash

scp -P 14022 ~/.ssh/id_ed25519  tigergraph@localhost:/home/<USER>/.ssh/id_ed25519

```
### Kafka timeout when installing TG in docker
Change the `utils/check_utils` in the installation pkg line 1140. Remove the `^`.

`ssh_sudo_cmd "$ip" "pkill -9 -f '/var/tmp/netcat/nc -k -l'" &>/dev/null`

After that, restart the container and retry install process.