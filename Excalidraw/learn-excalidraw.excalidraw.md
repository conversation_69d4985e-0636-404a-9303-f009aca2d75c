---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'



# Drag

# Excalidraw Data
## Text Elements
[[09-20]] ^aPeepKfE

# heading ^5xEHb886

[[Excalidraw/learn-excalidraw.excalidraw.md#^rhLfKW2A]] ^cy7UvrVM

## Embedded Files
f67a9714282ff8c78b47e80eac1c8817b3389150: [[cropped_excalidraw-modifiers]]

%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebTiAdho6IIR9BA4oZm4AbXAwUDAi6HhxdCgsKGSiyEYWdi40BIBWfmK61k4AOU4xbgBGADYAZgAWUYAOZv7m4bbIQg5iLG4I

XH7q4sJmABFUiuJuADMCMPmIElXcAAUEBDgAaSOAUU3II8J8fABlWGDVyS4bAaQJvCDMKCkNgAawQAHUSOoBucIVDYb8YP8JIIPGCoX5JBxwtk0P1zmw4EC1DABgAGWnnazKLGoBn5SCYbjOfpjQbaabDYZTWkATlmCXG5xpaGcgxF/XiwwSE2VtJVCUGoz47PBkJhCAAwmx8GxSKsAMQIWnDHkbc6aIHQ5T4pZGk1miSQ6zMSmBTJgiiIyQDHjD

bQJEWRrW0oaDGPNQbnSQIQjKaTcHitHVhBCHUk8OWJnXO4RwACSxBJqByAF1zkdyOkK9wOEIvucXcQicwq632zrNMIls9gulMlXa+chHBiLgDgMEjwEjHhoMJtbNeciBxoascjlRc4eLSazWwSbsLC86gTvgzjqjpwoN9CEYysf60+AGK4fSfaWoGSOoVJgVQSIAvBuALM7+6Hsep54pQAAqlR7geIpHieZ7nCBUAAIJEMojToMERxVOcdRQOYBD

4amRHQBSYJ6JkuCLEwzZoH2+DkqQqaLAQyGgasUEwehcFYTquBCFAbAAErhK+ZSQkICBbqxAASKZpmBgHxM0+QAL5tIUWzXhApCSAAMkcDxwjwuFgrAiCrIQ+jREg5ycjK/SjIMCTxNaEwTCKUzNCK4wJFKXLNBK2hqrMQzytFPCBecgbEEiaAjLS2iCpqwXNDw/RTJGSaaemaA2tl3lFf0PDBTMCRjIyHDMmUbI1LqaKGsapoWvS/W0mCDqXiWQ

iuj1HrlOQHA+rgfpkQ+nw/H8ZRSECILudmeqwgi6XBqSKLbQgGIsuCxqXB2whpt2VZAR1FJUrAdLtcUnY3S2bZcdmdzXkFa4RTqHQNBmozkUwnQcD0HB9Jloy0oMgyFfKU4znOuYLkuMY+f0Erruc2x7ME85oLe94daN5aVrkdYDkOxAjmkGRZNTqk7h9/YdReV7HKcKmLcE7HoEcvm4CKCTeclPBHEcEzYMqmijAkCDriE2D9NggU45ogpBTMg0

ou4ZR5DUYB3TU/TsjTHWLMsnnoLgowQAZRk6pcEjNJgzxqZogWDA5pSrDhYJ29y/T9NllUTEK1qijyRYdQBzjrnyopRmLithRGqVBtwGraDjoWasMzTND5KpZh1yapuVvAvZATIsnXnX6m6vUSJa1q2kNjqjeN7qB9Ns3zWCHxfCdq04hdW1dbtGW8IdXXj6sk+HJdBLvQdOoPdg1LPecFMVhOVvFA2v4IILnH40sKwSLgSRr0sG+oJf33o6SG7h

5qitg/UnADPH7RwYNChjDQCS5lTpzmK7XY+w343l5vaOmDMxzMw4p9FGs5iaAUXMuIYPBQwxibtuXcaCObFC5nA0mfMOpB3bqgZMuBHAtQQhQAS2kIDmnoSEJhygR5PhfG+f+n5Mg/j/PgACZtIA4RooRVYJEFodQolRfAMi6LSTgIxJ8LEiSkAvugrePF/D8RQnQhhPCwSSWknJVggi0BKWoeQ9SZVtIKkzM7fIxkFimWwDABIABVegpAABqABZ

f2TlPQoQ8lyCMzQcqRn6MFdctJxa+UijKCUfJRhikRljMWJds57W4IrMMhUEzBR4FqIqLRSrV20h+CSLVG4LxbhNC0VobQ2m7iNTsrdJrQEHr6JmI8lpL2xOdVe099Sz32vPKZ6IVrLwmXiK6hJiTIi3pSHeT135NwPlTNAk4HyNnPteF+1tr52zWCKFZY0uzrNIV9DqOZrxh2GHVaKgwZg/whtwCugDf6Q16GUIYtJZijFLqMSRFwYFE0oQg2md

zkFM17Pojq05MFwJxpjIYULY7ak5osEhz80XkLYJeeFd4HFSJMegYSORng4AICQcgFAFDBDmi4NwzLPAUG0NyjwrLtD6GIOaAAeuZKyNk7LwQ7EhWlEB9yMvcCyyg7KQikC5UywVlB+XatVXykV4rJXWVsrhWVD5+EKSEZakRv5/wbJoZUVRciECkTBEo9wLrPQMXOExKIrFdFnNJZAU0vEODGMEhBaCDL9W8vVZy1wcahUCoNcK0VErLKmpleJD

qljZLyVsagexrMEAaTqQMXS7iiieIuKZIQRh6AACEACKAApa4GjsIBwkGkTQuZZyaH+NEmUEwZg5QCkqSpgxS5FXSagJO4xtAjBFEqYK8pQxi0KXPZo9JalaT+XuxprVuBN1RK0/unpBlzWGfaHuvS2lXu9EM/09ZRmLIkICYEIhNrPKOjMx1xRz0LMxBPZZa9roPMAuSLZu9dkP3uT2dmTygM/WKbSHyUDFFAL/k0LDAKIYgJBVqKFkxjwA3Raj

LB2LcGhUCuU/GsKEBYJLcWfElMj6IKRaOFFLMdTENQrBTC55yXcxJgi55htcjslNvMU2ls/VCAhAYHYc5cDcE8ZAEV1woRyA0zJyASmEAAHl7AkCcHsE4bZUHwKpXJyAw0nSdhCXOYEBprD0FCDzOzBmICOd7sQFzUBgTIv9HY0gyl7N+fvXTPpFppZHDeMUfznZjOwZ2agOMUXBykGWKQILIWeNheLRF6lyXTR5bi+3BLSWHMVaYGlx6AE64n0+

BkM+QTtiECHWUKhMn9LskMq7S5VxmhOyKENmtrtTLPAAJr0GuJoYgzQ1LhNWkHEdC6w7THzhMY8wxEpqmPCKedspQrLvGGOypMYvmCm3bMiY+6a4NLzU0tqLTYRVfQB3TpdoBwxbuV9gZz6b2vv5stUDSzcQffhDnNABKgNHTGegFetzINIc3vddLEij3k3Y4fPjHVT5NmDWQhYI3b5+wQ0/c5qGsVjESeLWkoYfnAwqqz7owKMzhzVOHIU+GFhM

ZY6Vrjw4is2dp4ZqjWKcE3cKlHRcrNiWS4gBQ68VDsIKpYWw1YfDMgCJBQA94357XiMAzS0C3riJuoUQCyiXqCJqN9Tqf12i2Kk5Q6GwxfF8A64kBYqSBabGKRF/xpxFbSRVomy7a2pkgnXAAFbDGeEcFtEw1uByiTqEOUc+TLjikuEuYsJigx1InBGIptATERl8gsY7QoxnuwMRJVeVSrrCvFTMT36lNwbu9+Z3VL3fY6V3O9PTYuPqmiD4eb6x

4fpR+BgfAH4cw+R2daHOp8To9ujBpre82OlgJ4c4+7wTl6LJxcCn9t0/U6gyrl5fzV1grFjyDnRE52Axw0C6GILc+l0FBRlsELnAqxh1IONxozOOMhhgmjK8rLngv9FMErqhOaGYosMoBapzKJpSmTMULQnSjGqgdwugZga9PKlGugPuEQYwiQbmifFakWkMMIlAKIg6pjngc6o7q6u6j/PbtRFwT6l2i7looGufp7hAGGkYr7gqvStQTwqQfXIH

tYtauFpFmHkSOWgepHm4hNuAFbGsHAHAL8GjPpngcmOkKsLOKQLuG0AwIQAgBQE2gDn3G3N9glmIDfvkBANgCIPNGWBUPoL8F1EDj9qPl4T4aQH4QEU4ePoDpPsDjNC+rbt4b4UzP4WkF+O+pDuMhvsUBEVEWkEEdMnDnMnkakZkOkYEUjvPuvlPGUZEWkQETJKsk/GbCkQ0RUQEY1tsjji9O0QUfoF+CbmIhIrYfkY0RkQwYbmMeUVAJUWwlbqr

jbpsP0RMVUXNHhJEWwBQGYtAeEbMZUc8EsLhFsTsdwlcKcSscwNgFCF8AABoDB0YXZrjHjkahiAHgg3HGj4CzZ0g4yxSFSLhrh4rTAAIQBGBsAGCmHtAEDKQDDVqQDjGdFpDNF3JPxmR0wrHOgkAG57HFDYnEBfa1pNrGimTmhfhfjPDPAJA7AUlghyTKBthzQWgUlfgJAGijB0kImrH65HRpaUScCopkIQBwCBBmDCDMAADiPExAuJjythxOCAc

k18PEzCaAta7WPW14oBeRRAGiqh1KEAEaFhBp5IUk24Ieyk3JdgCeCA2AWQ3wEacAISbAywRxuAWp3mYQ4Ak2EAo84QGmA2+kQAA
```
%%