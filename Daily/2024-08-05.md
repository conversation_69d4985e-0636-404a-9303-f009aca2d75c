## TG Tasks
- [x] router.GET("/workspaces/meta", s.GetMetadata) this API will return supported region map to you.
- [x] make frontend changes to add account id input when secure connection is enabled.
- [x] **[TCE-5089 [BYOC] UI needs tuning to display Private Link Endpoint Service url…](https://graphsql.atlassian.net/browse/TCE-5089?atlOrigin=eyJpIjoiZjk0NDVjZTljMzk5NDhmYWFjZDZmODliMWNmNDhmMDUiLCJwIjoiamlyYS1zbGFjay1pbnQifQ)**
	- [x] [api format change](https://graphsql.atlassian.net/browse/TCE-5089?focusedCommentId=162999)
- [x] Add save icon on file tabs
## Tasks
```tasks
(due today) OR (scheduled today)
group by path
```
## Habits
- [ ] 吉他
- [ ] 内心的安宁
## Moods
> [!example]-
> Calm
> Good
> Depressed
> Worried

## Good things
## Events
## Progress