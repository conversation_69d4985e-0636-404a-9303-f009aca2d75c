## Ref
- [auth0 universal-login update | Auth0 CLI](https://auth0.github.io/auth0-cli/auth0_universal-login_update.html)
- [Auth0 Management API v2](https://auth0.com/docs/api/management/v2)
- [Customize Universal Login Page Templates](https://auth0.com/docs/customize/login-pages/universal-login/customize-templates#using-the-auth0-cli)
## Notes
- Get start
	- create tenant
	- custom domain: This domain is the base URL used to access the Auth0 API and the URL where your users authenticate.
	- create application
- The term `prompt` refers to a specific step of the login flow.
## Customize login page
- An improved branding customization UI experience is available through the **`auth0 universal-login customize`** command.
- `auth0 universal-login templates update`
 - page template
	- variables: `application`, `branding`, `prompt`
	- custom parameters:  You can also use query parameters within the context by passing them to the `/authorize` endpoint when initiating the authentication request. `{{ transaction.params.ext-ga }}`