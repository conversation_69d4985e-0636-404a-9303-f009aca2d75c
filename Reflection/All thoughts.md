---
tags: log
---
# All Daily Thoughts

This note automatically collects all thoughts from daily notes.

```dataviewjs
const habits = {
  guitar: { emoji: "🎸", name: "<PERSON>" },
  journal: { emoji: "📔", name: "Journal" },
  flashcard: { emoji: "🎴", name: "Flashcard" }
};

function getWarnings(currentPage, pages) {
  const warnings = [];

  // Check sleep duration
  if (currentPage.sleepEnd && currentPage.sleepStart) {
    const hours = (new Date(currentPage.sleepEnd) - new Date(currentPage.sleepStart)) / 3600000;
    if (hours >= 9) {
      warnings.push("Over sleep yesterday");
    }
  }
  
  // Check habits
  const currentDate = new Date(currentPage.file.name);
  const yesterdayPage = pages.find(p => p.file.name === new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate() - 1).toISOString().split("T")[0]);
  
  const warningHabits = [];
  let maxContinuousDays = 0;
  for (const [habit, info] of Object.entries(habits)) {
    for (let i = 1; i <= 7; i++) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate() - i);
      const page = pages.find(p => p.file.name === date.toISOString().split("T")[0]);
      if (!page || !page[habit]) {
        if (!warningHabits.includes(info.name)) {
          warningHabits.push(info.name);
        }
        if (i > maxContinuousDays) {
          maxContinuousDays = i;
        }
      } else {
        break;
      }
    }
  }

  if (warningHabits.length > 0) {
    warnings.push(`Missing habits: ${warningHabits.join(", ")} in last ${maxContinuousDays} days`);
  }
  
  // Check yesterday's mood
  if (yesterdayPage && yesterdayPage.mood && yesterdayPage.mood < 5) {
    warnings.push("Bad condition yesterday");
  }
  
  return warnings.join(", ");
}

const pages = dv.pages('"Daily"')
  .where(p => p.thoughts || p.happened || p.sleepStart || p.sleepEnd)
  .sort(p => p.file.name, 'desc');

const rows = pages
  .map(p => {
    const weekday = new Date(p.file.name).getDay();
    const dateStr = p.file.link + 
      (weekday === 6 ? " Sat" : weekday === 0 ? " Sun" : "");
    
    let sleepBadge = null;
    if (p.sleepStart) {
      const hour = new Date(p.sleepStart).getHours();
      const timeStr = new Date(p.sleepStart).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false });
      const style = hour >= 0 && hour < 6 ? 
        'background-color: #dc3545; color: white; padding: 2px 6px; border-radius: 3px' :
        'background-color: #198754; color: white; padding: 2px 6px; border-radius: 3px';
      sleepBadge = `<span style='${style}'>${timeStr}</span>`;
    }
    
    const sleepHours = p.sleepEnd && p.sleepStart ? 
      ((new Date(p.sleepEnd) - new Date(p.sleepStart)) / 3600000).toFixed(1) : 
      null;
    
    const habits = [
      p.guitar ? "🎸" : "",
      p.journal ? "📔" : "",
      p.flashcard ? "🎴" : ""
    ].join("");

    // const thoughtsDisplay = p.thoughts ? 
    //   `<span title="${p.thoughts}" style="color: var(--link-color, #106ba3); text-decoration: underline; cursor: pointer;">View</span>` : 
    //   null;
    
    return [
      dateStr,
      p.happened,
      p.thoughts,
      p.mood,
      sleepBadge,
      sleepHours,
      habits,
      // getWarnings(p, pages)
    ];
  });

  // Add week boundary after Mondays
  const finalRows = [];
  for (let i = 0; i < rows.length; i++) {
    finalRows.push(rows[i]);
    const currentDate = new Date(pages[i].file.name);
    if (currentDate.getDay() === 1) {
      // Add a styled separator row with a single cell spanning all columns
      const separator = [`<div style='height: 2px; background-color: var(--background-modifier-border); margin: 2px 0; padding:0;'></div>`];
      // Fill the rest with null to maintain table structure
      separator.length = rows[i].length;
      finalRows.push(separator);
    }
  }

  dv.table(["Date", "What Happened", "Thoughts", "Mood", "Sleep", "Sleep Hours", "Habits"], finalRows);
```
