.projects--board {
  display: flex;
  column-gap: 8px;
  align-items: flex-start;
  margin: 8px;
}

.projects--board--column {
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--radius-m);
  background-color: var(--background-secondary);
  display: flex;
  gap: var(--size-4-2);
  flex-direction: column;
  padding: var(--size-4-2);

  --board-column-drag-accent: hsla(var(--interactive-accent-hsl), 0.3);
  --board-column-drop-accent: hsla(var(--interactive-accent-hsl), 0.5);

  &:has(.projects--board--card-placeholder) {
    box-shadow: 0px 0px 8px 0px var(--board-column-drop-accent);
    transition: box-shadow 150ms ease-in-out;
  }
}

.projects--board--card {
  background-color: var(--background-primary);
  border-radius: var(--radius-s);
  border: 1px solid var(--background-modifier-border);
  padding: var(--size-4-2);
}

.projects--board--card:hover {
  border: 1px solid var(--background-modifier-border-hover);
}

.projects--board--card-list {
  display: flex;
  flex-direction: column;
  gap: var(--size-4-2);
  min-height: 35px;
  transition: all 150ms ease-in-out;

  &:has(.projects--board--card-placeholder) {
    background: var(--board-column-drop-accent) !important;
  }
}

.projects--gallery--grid {
  display: grid;
  gap: 24px;
}

.projects--gallery--card {
  background-color: var(--background-secondary);
  border-radius: var(--radius-s);
  border: 1px solid var(--background-modifier-border);
  padding: 0;
  margin: 0;
}

.projects--gallery--card:hover {
  border: 1px solid var(--background-modifier-border-hover);
}

.projects--gallery--card__body {
  padding: 8px;
}

.projects--gallery--card__media {
  height: 180px;
  border-top-left-radius: var(--radius-s);
  border-top-right-radius: var(--radius-s);
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid var(--background-modifier-border);
}

.projects--gallery--card__media img {
  width: 100%;
  height: 100%;
  border-top-left-radius: var(--radius-s);
  border-top-right-radius: var(--radius-s);
}

.workspace-leaf-content[data-type=obsidian-projects] .view-content {
  padding: 0;
}

.projects--popover {
  background-color: var(--background-primary);
  border: 1px solid var(--background-modifier-border);
  box-shadow: var(--shadow-s);
  border-radius: var(--radius-m);
  padding: var(--size-4-5);
  max-height: 95vh;
}

/*# sourceMappingURL=styles.css.map */