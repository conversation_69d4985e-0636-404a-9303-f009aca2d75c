---
id: 26blss0e788abdnrjhu6ywr
title: crossword
desc: ''
updated: 1710066320310
created: 1708613085029
---
## Source
- https://cs50.harvard.edu/ai/2024/projects/3/crossword/
  
## Notes
```python
import sys

from crossword import *


class CrosswordCreator():

    def __init__(self, crossword):
        """
        Create new CSP crossword generate.
        """
        self.crossword = crossword
        self.domains = {
            var: self.crossword.words.copy()
            for var in self.crossword.variables
        }

    def letter_grid(self, assignment):
        """
        Return 2D array representing a given assignment.
        """
        letters = [
            [None for _ in range(self.crossword.width)]
            for _ in range(self.crossword.height)
        ]
        for variable, word in assignment.items():
            direction = variable.direction
            for k in range(len(word)):
                i = variable.i + (k if direction == Variable.DOWN else 0)
                j = variable.j + (k if direction == Variable.ACROSS else 0)
                letters[i][j] = word[k]
        return letters

    def print(self, assignment):
        """
        Print crossword assignment to the terminal.
        """
        letters = self.letter_grid(assignment)
        for i in range(self.crossword.height):
            for j in range(self.crossword.width):
                if self.crossword.structure[i][j]:
                    print(letters[i][j] or " ", end="")
                else:
                    print("█", end="")
            print()

    def save(self, assignment, filename):
        """
        Save crossword assignment to an image file.
        """
        from PIL import Image, ImageDraw, ImageFont
        cell_size = 100
        cell_border = 2
        interior_size = cell_size - 2 * cell_border
        letters = self.letter_grid(assignment)

        # Create a blank canvas
        img = Image.new(
            "RGBA",
            (self.crossword.width * cell_size,
             self.crossword.height * cell_size),
            "black"
        )
        font = ImageFont.truetype("assets/fonts/OpenSans-Regular.ttf", 80)
        draw = ImageDraw.Draw(img)

        for i in range(self.crossword.height):
            for j in range(self.crossword.width):

                rect = [
                    (j * cell_size + cell_border,
                     i * cell_size + cell_border),
                    ((j + 1) * cell_size - cell_border,
                     (i + 1) * cell_size - cell_border)
                ]
                if self.crossword.structure[i][j]:
                    draw.rectangle(rect, fill="white")
                    if letters[i][j]:
                        _, _, w, h = draw.textbbox((0, 0), letters[i][j], font=font)
                        draw.text(
                            (rect[0][0] + ((interior_size - w) / 2),
                             rect[0][1] + ((interior_size - h) / 2) - 10),
                            letters[i][j], fill="black", font=font
                        )

        img.save(filename)

    def solve(self):
        """
        Enforce node and arc consistency, and then solve the CSP.
        """
        self.enforce_node_consistency()
        self.ac3()
        return self.backtrack(dict())

    def enforce_node_consistency(self):
        """
        Update `self.domains` such that each variable is node-consistent.
        (Remove any values that are inconsistent with a variable's unary
         constraints; in this case, the length of the word.)
        """
        for (var, domain) in self.domains.items():
            d = domain.copy()
            for word in d:
                if len(word) != var.length:
                    domain.remove(word)

    def revise(self, x, y):
        """
        Make variable `x` arc consistent with variable `y`.
        To do so, remove values from `self.domains[x]` for which there is no
        possible corresponding value for `y` in `self.domains[y]`.

        Return True if a revision was made to the domain of `x`; return
        False if no revision was made.
        """
        revised = False
        for x_word in self.domains[x].copy():
            has_possible_y = False
            for y_word in self.domains[y]:
                if self.crossword.overlaps[x,y] == None: 
                    has_possible_y = True
                    break
                (index1, index2) = self.crossword.overlaps[x,y]
                if x_word[index1] == y_word[index2]:
                    has_possible_y = True
                    break
            if not has_possible_y:
                self.domains[x].remove(x_word)
                revised = True
        return revised

    def ac3(self, arcs=None):
        """
        Update `self.domains` such that each variable is arc consistent.
        If `arcs` is None, begin with initial list of all arcs in the problem.
        Otherwise, use `arcs` as the initial list of arcs to make consistent.

        Return True if arc consistency is enforced and no domains are empty;
        return False if one or more domains end up empty.
        """
        if arcs is None:
            arcs = list()
            for v1 in self.crossword.variables:
                for v2 in self.crossword.neighbors(v1):
                    arcs.append((v1, v2))
        
        while len(arcs) != 0:
            (x, y) = arcs.pop()
            if self.revise(x, y):
                if len(self.domains[x]) == 0:
                    return False
                for z in self.crossword.neighbors(x):
                    if z != y and (z, x) not in arcs:
                        arcs.append((z, x))
        return True
                

    def assignment_complete(self, assignment):
        """
        Return True if `assignment` is complete (i.e., assigns a value to each
        crossword variable); return False otherwise.
        """
        return len(assignment) == len(self.crossword.variables)

    def consistent(self, assignment):
        """
        Return True if `assignment` is consistent (i.e., words fit in crossword
        puzzle without conflicting characters); return False otherwise.
        """
        for (x, word) in assignment.items():
            if len(word) != x.length:
                return False
            for y in self.crossword.neighbors(x):
                if y in assignment:
                    if word == assignment[y]:
                        return False
                    (index1, index2) = self.crossword.overlaps[x, y]
                    if word[index1] != assignment[y][index2]:
                        return False
        
        return True


    def order_domain_values(self, var, assignment):
        """
        Return a list of values in the domain of `var`, in order by
        the number of values they rule out for neighboring variables.
        The first value in the list, for example, should be the one
        that rules out the fewest values among the neighbors of `var`.
        """
        rule_outs = {}
        for v in self.domains[var]:
            rule_outs[v] = 0
            for neighbor in self.crossword.neighbors(var):
                if neighbor not in assignment:
                    (index1, index2) = self.crossword.overlaps[var, neighbor]
                    for j in self.domains[neighbor]:
                        if v[index1] != j[index2]:
                            rule_outs[v] += 1

        list = self.domains[var]
        def sort_func(v):
            return rule_outs[v]
        return sorted(list, key=sort_func)
        

    def select_unassigned_variable(self, assignment):
        """
        Return an unassigned variable not already part of `assignment`.
        Choose the variable with the minimum number of remaining values
        in its domain. If there is a tie, choose the variable with the highest
        degree. If there is a tie, any of the tied variables are acceptable
        return values.
        """
        result = list()
        domain_len = 0
        for v in self.crossword.variables:
            if v not in assignment:
                if len(result) == 0 or len(self.domains[v]) < domain_len:
                    result = list()
                    result.append(v)
                    domain_len = len(self.domains[v])
                elif len(self.domains[v]) == domain_len:
                    result.append(v)

        if len(result) == 0:
            return None
        if len(result) == 1:
            return result[0]
        
        # there is a tie
        old_result = result.copy()
        result = list()
        highest_degree = 0
        for v in old_result:
            neighbors = list(v for v in self.crossword.neighbors(v) if v not in assignment)
            if len(result) == 0 or len(neighbors) > highest_degree:
                result = list()
                result.append(v)
                highest_degree = len(neighbors)
            elif len(neighbors) == highest_degree:
                result.append(v)
        
        return result[0]
        

    def backtrack(self, assignment):
        """
        Using Backtracking Search, take as input a partial assignment for the
        crossword and return a complete assignment if possible to do so.

        `assignment` is a mapping from variables (keys) to words (values).

        If no assignment is possible, return None.
        """
        if self.assignment_complete(assignment):
            return assignment
        
        v = self.select_unassigned_variable(assignment)
        for value in self.domains[v]:
            assignment[v] = value
            if self.consistent(assignment):
                result = self.backtrack(assignment)
                if result != None:
                    return result 
            del assignment[v]
        return None

def main():

    # Check usage
    if len(sys.argv) not in [3, 4]:
        sys.exit("Usage: python generate.py structure words [output]")

    # Parse command-line arguments
    structure = sys.argv[1]
    words = sys.argv[2]
    output = sys.argv[3] if len(sys.argv) == 4 else None

    # Generate crossword
    crossword = Crossword(structure, words)
    creator = CrosswordCreator(crossword)
    assignment = creator.solve()

    # Print result
    if assignment is None:
        print("No solution.")
    else:
        creator.print(assignment)
        if output:
            creator.save(assignment, output)


if __name__ == "__main__":
    main()

```