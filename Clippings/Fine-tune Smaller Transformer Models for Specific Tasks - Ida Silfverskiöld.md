---
title: "Fine-tune Smaller Transformer Models for Specific Tasks - Ida Silfverskiöld"
source: "https://www.ilsilfverskiold.com/articles/fine-tune-smaller-transformer-models"
author:
published: 2023-12-15
created: 2025-05-05
description: "This article will help you understand how to start working with smaller open source NLP models for specific use cases, why it can be more effective, as well as go through how to fine-tune a base model on your own"
tags:
  - "clippings"
---
为了构建这个模型，我从多个社交媒体平台通过其公共 API 端点获取了 50,000 个不同大小的标题。一开始我懒得处理全部数据，但尝试了 3,000 条文本加上另外 8,500 条文本。首次试验用 3,000 个数据点效果尚可，但还不够理想，于是我又回头处理了额外的 5,500 行数据。

我确实使用了 GPT-4 来帮助将文本转换为关键词。我花费了 30 美元处理了 10,000 条文本，每次 API 调用批量处理 10 条。如果你需要构建自己的数据集，这里是脚本的仓库。

你需要确保关键词正是你想要生成的那些，因此我不得不手动检查 8500 行数据。正如所有人都会告诉你的，数据是微调过程中最重要的因素。投入什么，就会得到什么。

Process for Building the Model

If you want to do this manually with their trainer API manually, read on. This will be completely free.

Deciding on Model Architecture

根本区别在于，编码器模型通常从较大的输入生成压缩输出，而解码器模型则通常从较小的输入扩展或生成数据。

If you’re building this dataset from scratch, see this script on generating your new dataset with GPT-4. This will be faster. But even with help, creating your own data is hard work. I manually checked my dataset and it took me two days to properly go through it.

一旦确定了数据集，就需要确保将其划分为训练集、测试集和验证集。根据数据集的大小，通常的划分比例是 80%用于训练，10%用于验证，10%用于测试。