## Tasks
- [x] #task  [add new file and order by file need to adaption the dark mode in gsql editor](https://graphsql.atlassian.net/browse/TCE-5137) ⏳ 2024-08-07 ✅ 2024-08-07
- [x] editor shortcuts 
- [x] tabs style
## Notes
- `const { theme } = useTheme();` 
- `const theme = $theme as CustomTheme;`
- 使用semantic token设置colors  `theme.colors['text.primary']`
- replace IconButton with button kind='text'
```ts
<Button
  size="compact"
  kind="text"
  shape="square"
  onClick={() => {
	setIsEditing(true);
  }}
>
  <PencilIcon size={12} />
</Button>
```
- for svgs, import different svgs based on the theme type
- button KIND.tertiary -> secondary