- change shell scripts ![[Pasted image 20240619154801.png]] ![[Pasted image 20240619154818.png]]
- build
```shell
gle_compile
```
- deploy
```shell
# download gsql.jar and gsql_client.jar to
# /home/<USER>/tigergraph/app/${{ env.TG_VERSION }}/dev/gdk/gsql/lib

cd /home/<USER>/tigergraph/app/${{ env.TG_VERSION }}/dev/gdk/gsql/lib
mv /path/to/gsql.jar .tg_dbs_gsqld.jar
chmod +x .tg_dbs_gsqld.jar
chmod +x gsql_client.jar
gadmin restart gsql -y
