/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __commonJS = (cb, mod) => function __require() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// node_modules/unc-path-regex/index.js
var require_unc_path_regex = __commonJS({
  "node_modules/unc-path-regex/index.js"(exports, module2) {
    "use strict";
    module2.exports = function uncPathRegex() {
      return /^[\\\/]{2,}[^\\\/]+[\\\/]+[^\\\/]+/;
    };
  }
});

// node_modules/is-unc-path/index.js
var require_is_unc_path = __commonJS({
  "node_modules/is-unc-path/index.js"(exports, module2) {
    "use strict";
    var regex = require_unc_path_regex()();
    module2.exports = function(filepath) {
      if (typeof filepath !== "string") {
        throw new TypeError("expected a string");
      }
      return regex.test(filepath);
    };
  }
});

// node_modules/is-relative/index.js
var require_is_relative = __commonJS({
  "node_modules/is-relative/index.js"(exports, module2) {
    "use strict";
    var isUncPath = require_is_unc_path();
    module2.exports = function isRelative(filepath) {
      if (typeof filepath !== "string") {
        throw new TypeError("expected filepath to be a string");
      }
      return !isUncPath(filepath) && !/^([a-z]:)?[\\\/]/i.test(filepath);
    };
  }
});

// node_modules/is-windows/index.js
var require_is_windows = __commonJS({
  "node_modules/is-windows/index.js"(exports, module2) {
    (function(factory) {
      if (exports && typeof exports === "object" && typeof module2 !== "undefined") {
        module2.exports = factory();
      } else if (typeof define === "function" && define.amd) {
        define([], factory);
      } else if (typeof window !== "undefined") {
        window.isWindows = factory();
      } else if (typeof global !== "undefined") {
        global.isWindows = factory();
      } else if (typeof self !== "undefined") {
        self.isWindows = factory();
      } else {
        this.isWindows = factory();
      }
    })(function() {
      "use strict";
      return function isWindows() {
        return process && (process.platform === "win32" || /^(msys|cygwin)$/.test(process.env.OSTYPE));
      };
    });
  }
});

// node_modules/is-absolute/index.js
var require_is_absolute = __commonJS({
  "node_modules/is-absolute/index.js"(exports, module2) {
    "use strict";
    var isRelative = require_is_relative();
    var isWindows = require_is_windows();
    module2.exports = isAbsolute;
    function isAbsolute(fp) {
      if (typeof fp !== "string") {
        throw new TypeError("isAbsolute expects a string.");
      }
      return isWindows() ? isAbsolute.win32(fp) : isAbsolute.posix(fp);
    }
    isAbsolute.posix = function posixPath(fp) {
      return fp.charAt(0) === "/";
    };
    isAbsolute.win32 = function win32(fp) {
      if (/[a-z]/i.test(fp.charAt(0)) && fp.charAt(1) === ":" && fp.charAt(2) === "\\") {
        return true;
      }
      if (fp.slice(0, 2) === "\\\\") {
        return true;
      }
      return !isRelative(fp);
    };
  }
});

// node_modules/path-root-regex/index.js
var require_path_root_regex = __commonJS({
  "node_modules/path-root-regex/index.js"(exports, module2) {
    "use strict";
    module2.exports = function() {
      return /^([a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/]+[^\\\/]+)?([\\\/])?/;
    };
  }
});

// node_modules/path-root/index.js
var require_path_root = __commonJS({
  "node_modules/path-root/index.js"(exports, module2) {
    "use strict";
    var pathRootRegex = require_path_root_regex();
    module2.exports = function(filepath) {
      if (typeof filepath !== "string") {
        throw new TypeError("expected a string");
      }
      var match = pathRootRegex().exec(filepath);
      if (match) {
        return match[0];
      }
    };
  }
});

// node_modules/map-cache/index.js
var require_map_cache = __commonJS({
  "node_modules/map-cache/index.js"(exports, module2) {
    "use strict";
    var hasOwn = Object.prototype.hasOwnProperty;
    module2.exports = MapCache;
    function MapCache(data) {
      this.__data__ = data || {};
    }
    MapCache.prototype.set = function mapSet(key, value) {
      if (key !== "__proto__") {
        this.__data__[key] = value;
      }
      return this;
    };
    MapCache.prototype.get = function mapGet(key) {
      return key === "__proto__" ? void 0 : this.__data__[key];
    };
    MapCache.prototype.has = function mapHas(key) {
      return key !== "__proto__" && hasOwn.call(this.__data__, key);
    };
    MapCache.prototype.del = function mapDelete(key) {
      return this.has(key) && delete this.__data__[key];
    };
  }
});

// node_modules/parse-filepath/index.js
var require_parse_filepath = __commonJS({
  "node_modules/parse-filepath/index.js"(exports, module2) {
    "use strict";
    var path = require("path");
    var isAbsolute = require_is_absolute();
    var pathRoot = require_path_root();
    var MapCache = require_map_cache();
    var cache = new MapCache();
    module2.exports = function(filepath) {
      if (typeof filepath !== "string") {
        throw new TypeError("parse-filepath expects a string");
      }
      if (cache.has(filepath)) {
        return cache.get(filepath);
      }
      var obj = {};
      if (typeof path.parse === "function") {
        obj = path.parse(filepath);
        obj.extname = obj.ext;
        obj.basename = obj.base;
        obj.dirname = obj.dir;
        obj.stem = obj.name;
      } else {
        define2(obj, "root", function() {
          return pathRoot(this.path);
        });
        define2(obj, "extname", function() {
          return path.extname(filepath);
        });
        define2(obj, "ext", function() {
          return this.extname;
        });
        define2(obj, "name", function() {
          return path.basename(filepath, this.ext);
        });
        define2(obj, "stem", function() {
          return this.name;
        });
        define2(obj, "base", function() {
          return this.name + this.ext;
        });
        define2(obj, "basename", function() {
          return this.base;
        });
        define2(obj, "dir", function() {
          var dir = path.dirname(filepath);
          if (dir === ".") {
            return filepath[0] === "." ? dir : "";
          } else {
            return dir;
          }
        });
        define2(obj, "dirname", function() {
          return this.dir;
        });
      }
      obj.path = filepath;
      define2(obj, "absolute", function() {
        return path.resolve(this.path);
      });
      define2(obj, "isAbsolute", function() {
        return isAbsolute(this.path);
      });
      cache.set(filepath, obj);
      return obj;
    };
    function define2(obj, prop, fn2) {
      var cached;
      Object.defineProperty(obj, prop, {
        configurable: true,
        enumerable: true,
        set: function(val) {
          cached = val;
        },
        get: function() {
          return cached || (cached = fn2.call(obj));
        }
      });
    }
  }
});

// main.ts
var main_exports = {};
__export(main_exports, {
  default: () => ObsidianLinksPlugin
});
module.exports = __toCommonJS(main_exports);
var import_obsidian8 = require("obsidian");

// RegExPatterns.ts
var RegExPatterns = class {
};
RegExPatterns.Email = /([a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)/;
//static readonly AbsoluteUri = /[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&\/=]*)/;
RegExPatterns.AbsoluteUri = /^[a-z][a-z+-\.]+:\/\/.+/;
RegExPatterns.RelativePath = /^(?![A-Z]:\\)(?!\.{1,2}(\/|\\))(?!\/\w)([.\w\-\/\\]+)$/;
RegExPatterns.Wikilink = /(!?)\[\[([^\[\]|]*)(\|([^\[\]]*))?\]\]/;
// static readonly Markdownlink = /(!?)\[([^\]\[]*)\]\(([^)(]*)\)/;
//TODO: revise
RegExPatterns.Markdownlink = /(!?)\[([^\]\[]*)\]\(((?:[^()\\]*|\\[\(\)]|(?:\([^()]*\)))*?)\)/;
RegExPatterns.Htmllink = /<a\s+[^>]*href\s*=\s*['"]([^'"]*)['"][^>]*>(.*?)<\/a>/;
RegExPatterns.AutolinkUrl = /<([a-zA-Z]{2,32}:[^>]+)>/;
RegExPatterns.AutolinkMail = /<([a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)>/;
// static readonly PlainUrl = /\b((?:[a-z][\w\-]+:(?:\/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]+|\((?:[^\s()<>]+|(?:\([^\s()<>]+\)))*\))+(?:\((?:[^\s()<>]+|(?:\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:'".,<>?«»“”‘’]))/
//TODO: revise
RegExPatterns.PlainUrl = /\b((?:[a-z][\w\-]+:(?:\/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]+|\([^\s()<>]*\))+(?:\([^\s()<>]*\)|[^\s`!()\[\]{};:'".,<>?«»“”‘’]))/;
RegExPatterns.InvalidNoteNameChars = /[#^\[\]\|*"\/\\<>:?]/;
RegExPatterns.NoteHeading = /^#+\s+(.*)$/;
RegExPatterns.AbsoluteUriCheck = /^(?:[a-z+]+:)?\/\//;
RegExPatterns.AbsoluteFilePathCheck = /^\/|([a-z]:[\/\\])/;
//TODO: fix
RegExPatterns.CodeBlock = /(\`{3}([a-z#\s\"]*?)\n+)(.*?)(\n+\`{3})/;
RegExPatterns.ImageDimentions = /^((\d+)|((\d+)x(\d+)))$/;
RegExPatterns.Frontmatter = /(\-{3}\n+)(.*?)(\n*\-{3}\n)/;

// utils.ts
var import_parse_filepath = __toESM(require_parse_filepath());
var LinkEmbededChar = "!";
var Position = class {
  constructor(start2, end2) {
    this.start = start2;
    this.end = end2;
  }
};
var TextPart = class {
  constructor(content, position) {
    this.content = content;
    this.position = position;
  }
};
var ImageDimensions = class extends TextPart {
  constructor(content, position, width, height) {
    super(content, position);
    this.width = width;
    this.height = height;
  }
};
var DestinationType = /* @__PURE__ */ ((DestinationType2) => {
  DestinationType2["Unknown"] = "unknown";
  DestinationType2["Image"] = "image";
  return DestinationType2;
})(DestinationType || {});
var LinkData = class extends TextPart {
  constructor(type, content, position, destination, text, embedded = false) {
    super(content, position);
    this.type = type;
    this.embedded = embedded;
    this.destinationType = "unknown" /* Unknown */;
    this._destinationInAngleBrackets = false;
    this.type = type;
    this.destination = destination;
    this.text = text;
  }
  get destination() {
    return this._destination;
  }
  set destination(value) {
    this._destination = value;
    this.parseDestination();
    this.paseText();
  }
  get text() {
    return this._imageDimensions ? this._imageText : this._text;
  }
  set text(value) {
    this._text = value;
    this.paseText();
  }
  get imageDimensions() {
    return this._imageDimensions;
  }
  parseDestination() {
    var _a;
    if (!((_a = this._destination) == null ? void 0 : _a.content) || !(this.type === 1 /* Markdown */ || this.type === 2 /* Wiki */)) {
      return;
    }
    const content = this._destination.content;
    let path = "";
    if (isAbsoluteUri(content)) {
      path = new URL(content).pathname;
    } else {
      const hashIdx = this._destination.content.indexOf("#");
      path = hashIdx >= 0 ? content.substring(0, hashIdx) : content;
    }
    const parsedPath = (0, import_parse_filepath.default)(path);
    const imageExtensions = [".png", ".webp", ".jpg", ".jpeg", ".gif", ".bmp", ".svg"];
    if (imageExtensions.includes(parsedPath.ext)) {
      this.destinationType = "image" /* Image */;
    }
  }
  paseText() {
    var _a;
    if (!((_a = this._text) == null ? void 0 : _a.content) || this.destinationType !== "image" /* Image */) {
      return;
    }
    const content = this._text.content;
    const pipeIdx = content.lastIndexOf("|");
    let potentialDimensions;
    let imageText = void 0;
    let dimensionsStart;
    if (pipeIdx >= 0) {
      potentialDimensions = this._text.content.substring(pipeIdx + 1);
      dimensionsStart = this._text.position.start + pipeIdx + 1;
      imageText = this._text.content.substring(0, pipeIdx);
    } else {
      potentialDimensions = this._text.content;
      dimensionsStart = this._text.position.start;
    }
    const match = potentialDimensions.match(RegExPatterns.ImageDimentions);
    if (match) {
      const [, dimensions, singleWidth, , width, height] = match;
      if (imageText == void 0) {
        this._imageText = void 0;
      } else {
        this._imageText = new TextPart(
          imageText,
          new Position(this._text.position.start, this._text.position.start + pipeIdx)
        );
      }
      const dimensionsPosition = new Position(dimensionsStart, dimensionsStart + dimensions.length);
      this._imageDimensions = singleWidth ? new ImageDimensions(dimensions, dimensionsPosition, parseInt(singleWidth)) : new ImageDimensions(dimensions, dimensionsPosition, parseInt(width), parseInt(height));
    } else {
      this._imageText = void 0;
      this._imageDimensions = void 0;
    }
  }
  static parse(linkText) {
    const links = findLinks(linkText, 65535 /* All */, 0, linkText.length);
    return links.length === 1 ? links[0] : null;
  }
};
function parseMarkdownLink(regExp, match, raw, embeddedChar, text, destination) {
  if (match.index === void 0) {
    throw new Error("match: index must be defined.");
  }
  const linkData = new LinkData(1 /* Markdown */, raw, new Position(match.index, match.index + raw.length));
  linkData.embedded = embeddedChar === LinkEmbededChar;
  if (text) {
    const textIdx = raw.indexOf(text);
    linkData.text = new TextPart(text, new Position(textIdx, textIdx + text.length));
  }
  if (destination) {
    const linkIdx = raw.indexOf(destination, linkData.text ? linkData.text.position.end : raw.lastIndexOf("(") + 1);
    const wrappedInAngleBrackets = destination[0] === "<" && destination[destination.length - 1] === ">";
    linkData.destination = wrappedInAngleBrackets ? new TextPart(destination.substring(1, destination.length - 1), new Position(linkIdx + 1, linkIdx + destination.length - 1)) : new TextPart(destination, new Position(linkIdx, linkIdx + destination.length));
    linkData._destinationInAngleBrackets = wrappedInAngleBrackets;
  }
  return linkData;
}
function parseWikiLink(regExp, match, raw, embeddedChar, text, destination) {
  if (match.index === void 0) {
    throw new Error("match: index must be defined.");
  }
  const linkData = new LinkData(2 /* Wiki */, raw, new Position(match.index, match.index + raw.length));
  linkData.embedded = embeddedChar === LinkEmbededChar;
  if (text) {
    const textIdx = raw.lastIndexOf(text);
    linkData.text = new TextPart(text, new Position(textIdx, textIdx + text.length));
  }
  if (destination) {
    const linkIdx = raw.indexOf(destination);
    linkData.destination = new TextPart(destination, new Position(linkIdx, linkIdx + destination.length));
  }
  return linkData;
}
function parseAutolink(regExp, match, raw, destination) {
  if (match.index === void 0) {
    throw new Error("match: index must be defined.");
  }
  if (!destination) {
    throw new Error("destination must not be empty");
  }
  const linkData = new LinkData(8 /* Autolink */, raw, new Position(match.index, match.index + raw.length));
  const destinationStartIdx = raw.indexOf(destination);
  linkData.destination = new TextPart(destination, new Position(destinationStartIdx, destinationStartIdx + destination.length));
  return linkData;
}
function parseHtmlLink(regExp, match, raw, text, destination) {
  if (match.index === void 0) {
    throw new Error("match: index must be defined.");
  }
  const linkData = new LinkData(4 /* Html */, raw, new Position(match.index, match.index + raw.length));
  if (text) {
    const textIdx = raw.lastIndexOf(text);
    linkData.text = new TextPart(text, new Position(textIdx, textIdx + text.length));
  }
  if (destination) {
    const linkIdx = raw.indexOf(destination);
    linkData.destination = new TextPart(destination, new Position(linkIdx, linkIdx + destination.length));
  }
  return linkData;
}
function parsePlainUrl(regExp, match, raw, destination) {
  if (match.index === void 0) {
    throw new Error("match: index must be defined.");
  }
  if (!destination) {
    throw new Error("destination must not be empty");
  }
  const type = destination.startsWith("obsidian://open?vault=") ? 16 /* PlainUrl */ | 32 /* ObsidianUrl */ : 16 /* PlainUrl */;
  const linkData = new LinkData(type, raw, new Position(match.index, match.index + raw.length));
  linkData.destination = new TextPart(destination, new Position(0, destination.length));
  if ((type & 32 /* ObsidianUrl */) === 32 /* ObsidianUrl */) {
    const url = new URL(destination);
    const vault = url.searchParams.get("vault");
    linkData.vault = vault ? vault : void 0;
  }
  return linkData;
}
function replaceAllHtmlLinks(text) {
  const htmlLinkRegEx = /<a\s+[^>]*href\s*=\s*['"]([^'"]*)['"][^>]*>(.*?)<\/a>/gi;
  return text.replace(htmlLinkRegEx, (match, url, text2) => {
    return `[${text2}](${url})`;
  });
}
var headingWithLinksRegEx = /^(#+ .*)(?:(\[(.*)\]\((.*)\))|(\[\[([^\[\]|]+)(?:\|([^\[\]]+))?\]\])|(<a\s[^>]*[^>]*>(.*?)<\/a>))(.*)$/gm;
function hasLinksInHeadings(text) {
  return new RegExp(headingWithLinksRegEx.source, "gm").test(text);
}
var InternalWikilinkWithoutTextAction = /* @__PURE__ */ ((InternalWikilinkWithoutTextAction2) => {
  InternalWikilinkWithoutTextAction2["None"] = "None";
  InternalWikilinkWithoutTextAction2["Delete"] = "Delete";
  InternalWikilinkWithoutTextAction2["ReplaceWithDestination"] = "ReplaceWithDestination";
  InternalWikilinkWithoutTextAction2["ReplaceWithLowestNoteHeading"] = "ReplaceWithLowestNoteHeading";
  return InternalWikilinkWithoutTextAction2;
})(InternalWikilinkWithoutTextAction || {});
function removeLinksFromHeadings(text, options) {
  const result = text.replace(headingWithLinksRegEx, (match, start2, rawMdLink, mdText, mdUrl, rawWikiLink, wkLink, wkText, rawHtmlLink, htmlText, end2, offset2) => {
    let linkText;
    if (rawMdLink) {
      linkText = mdText ? mdText : "";
    } else if (rawWikiLink) {
      if (wkText) {
        linkText = wkText;
      } else {
        linkText = wkLink;
        switch (options.internalWikilinkWithoutTextAction) {
          case "ReplaceWithLowestNoteHeading" /* ReplaceWithLowestNoteHeading */:
            {
              let idx = 0;
              if (wkLink && (idx = wkLink.lastIndexOf("#")) > -1 && idx + 1 <= wkLink.length) {
                const subheading = wkLink.substring(idx + 1);
                if (subheading) {
                  linkText = subheading;
                }
              }
            }
            break;
          case "Delete" /* Delete */:
            linkText = "";
            break;
        }
      }
    } else if (rawHtmlLink) {
      linkText = htmlText ? htmlText : "";
    }
    return start2 + linkText + end2;
  });
  return result;
}
var textWithLinksRegEx = /(?:(\[(.*?)\]\((.*?)\))|(\[\[([^\n\[\]|]+)(?:\|([^\[\]]+))?\]\])|(<a\s[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>))/gm;
function HasLinks(text) {
  return new RegExp(textWithLinksRegEx.source, "gm").test(text);
}
function removeLinks(text) {
  const result = text.replace(new RegExp(textWithLinksRegEx.source, "gm"), (match, rawMdLink, mdText, mdUrl, rawWikiLink, wkLink, wkText, rawHtmlLink, htmlUrl, htmlText, offset2) => {
    let linkText;
    if (rawMdLink) {
      linkText = mdText ? mdText : "";
    } else if (rawWikiLink) {
      linkText = wkText ? wkText : wkLink;
    } else if (rawHtmlLink) {
      linkText = htmlText ? htmlText : "";
    }
    return linkText;
  });
  return result;
}
function removeWhitespaces(str) {
  return str.replace(/\s+/g, " ").trim();
}
async function getPageTitle(url, getPageText) {
  const titleRegEx = /<title[^>]*>([^<]*?)<\/title>/i;
  const text = await getPageText(url);
  if (url.hostname === "www.youtube.com" && url.pathname === "/watch") {
    const titlePrefix = '"playerOverlayVideoDetailsRenderer":{"title":{"simpleText":"';
    const titleStart = text.indexOf(titlePrefix);
    if (titleStart > 0) {
      const titleEnd = text.indexOf('"}', titleStart);
      if (titleEnd > 0) {
        return text.substring(titleStart + titlePrefix.length, titleEnd);
      }
    }
  } else {
    const match = text.match(titleRegEx);
    if (match) {
      const [, title] = match;
      return decodeHtmlEntities(removeWhitespaces(title));
    }
  }
  throw new Error("Page has no title.");
}
function getLinkTitles(linkData) {
  var _a, _b;
  if (!linkData.destination || (linkData.type & (1 /* Markdown */ | 2 /* Wiki */)) == 0) {
    return [];
  }
  const linkContent = linkData.type == 1 /* Markdown */ ? decodeURI((_a = linkData.destination) == null ? void 0 : _a.content) : (_b = linkData.destination) == null ? void 0 : _b.content;
  const hashIdx = linkContent.indexOf("#");
  if (hashIdx > 0 && hashIdx < linkContent.length - 1) {
    return linkContent.substring(hashIdx + 1).split("#");
  }
  return [];
}
function getFileName(path) {
  return path.replace(/^.*[\\\/]/, "");
}
function removeExtension(path, extension = ".md") {
  const extIdx = path.lastIndexOf(extension);
  if (extIdx < 0 || extIdx < path.length - extension.length) {
    return [path, false];
  }
  return [path.substring(0, extIdx), true];
}
function escapeRegex(str) {
  return str.replace(/[/\-\\^$*+?.()|[\]{}]/g, "\\$&");
}
function replaceMarkdownTarget(text, target, newTarget) {
  const regexp = new RegExp("\\[([^\\]]*)?\\]\\((" + escapeRegex(target) + ")\\)", "ig");
  let count = 0;
  return [text.replace(regexp, (match, text2) => {
    count++;
    let destination = encodeURI(newTarget);
    if (destination.indexOf("%20") > 0) {
      destination = `<${destination.replace(/%20/g, " ")}>`;
    }
    return `[${text2}](${destination})`;
  }), count];
}
function decodeHtmlEntities(text) {
  const regexpHe = /&([a-zA-Z\d]+);/gm;
  const charByHe = /* @__PURE__ */ new Map();
  charByHe.set("amp", "&");
  charByHe.set("nbsp", " ");
  charByHe.set("quot", '"');
  charByHe.set("gt", ">");
  charByHe.set("lt", "<");
  return text.replace(regexpHe, (match, he) => {
    const entry = charByHe.get(he);
    return entry != null ? entry : match;
  });
}
function findLinks(text, type, start2, end2) {
  const linksRegex = new RegExp(
    `${RegExPatterns.Markdownlink.source}|${RegExPatterns.Wikilink.source}|${RegExPatterns.AutolinkUrl.source}|${RegExPatterns.AutolinkMail.source}|${RegExPatterns.Htmllink.source}|${RegExPatterns.PlainUrl.source}`,
    "gmi"
  );
  let match;
  const links = new Array();
  let startOffset = start2 ? start2 : 0;
  let endOffset = end2 ? end2 : text.length;
  let linkType = type ? type : 65535 /* All */;
  while (match = linksRegex.exec(text)) {
    const [
      rawMatch,
      mdLinkEmbeded,
      mdLinkText,
      mdLinkDestination,
      wikiLinkEmbeded,
      wikiLinkDestination,
      wikiLinkTextRaw,
      wikiLinkText,
      autoLinkUrlDestination,
      autoLinkMailDestination,
      htmlLinkDestination,
      htmlLinkText,
      plainUrl
    ] = match;
    if (startOffset == endOffset) {
      if (!(startOffset >= match.index && startOffset <= match.index + rawMatch.length)) {
        continue;
      }
    } else {
      if (!(match.index >= startOffset && match.index + rawMatch.length <= endOffset)) {
        continue;
      }
    }
    if (rawMatch.indexOf("](") >= 0 || mdLinkEmbeded || mdLinkText || mdLinkDestination) {
      if (!(linkType & 1 /* Markdown */)) {
        continue;
      }
      const linkData = parseMarkdownLink(linksRegex, match, rawMatch, mdLinkEmbeded, mdLinkText, mdLinkDestination);
      links.push(linkData);
    } else if (rawMatch.indexOf("[[") >= 0 || wikiLinkEmbeded || wikiLinkDestination || wikiLinkText) {
      if (!(linkType & 2 /* Wiki */)) {
        continue;
      }
      const linkData = parseWikiLink(linksRegex, match, rawMatch, wikiLinkEmbeded, wikiLinkText, wikiLinkDestination);
      links.push(linkData);
    } else if (rawMatch.startsWith("<a")) {
      if (!(linkType & 4 /* Html */)) {
        continue;
      }
      const linkData = parseHtmlLink(
        linksRegex,
        match,
        rawMatch,
        htmlLinkText,
        htmlLinkDestination
      );
      links.push(linkData);
    } else if (rawMatch[0] === "<") {
      if (!(linkType & 8 /* Autolink */)) {
        continue;
      }
      const linkData = parseAutolink(
        linksRegex,
        match,
        rawMatch,
        autoLinkUrlDestination ? autoLinkUrlDestination : autoLinkMailDestination
      );
      links.push(linkData);
    } else if (plainUrl) {
      if (!(linkType & 16 /* PlainUrl */)) {
        continue;
      }
      const linkData = parsePlainUrl(linksRegex, match, rawMatch, plainUrl);
      links.push(linkData);
    }
  }
  return links;
}
function getSafeFilename(filename) {
  const regex = new RegExp(RegExPatterns.InvalidNoteNameChars.source, "g");
  if (!filename) {
    return filename;
  }
  return filename.replace(regex, "");
}
function isAbsoluteUri(path) {
  return new RegExp(RegExPatterns.AbsoluteUri.source, "i").test(path);
}
function isSectionLink(path) {
  return path[0] === "#";
}
function isAbsoluteFilePath(path) {
  return new RegExp(RegExPatterns.AbsoluteFilePathCheck.source, "i").test(path);
}
var CodeBlock = class extends TextPart {
  constructor(content, position) {
    super(content, position);
  }
};
function parseCodeBlock(regExp, match, raw) {
  if (match.index === void 0) {
    throw new Error("match: index must be defined.");
  }
  const codeBlock = new CodeBlock(raw, new Position(match.index, match.index + raw.length));
  return codeBlock;
}
function findCodeBlocks(text, start2, end2) {
  const codeBlockRegex = new RegExp(RegExPatterns.CodeBlock.source, "gsi");
  let match;
  const blocks = new Array();
  let startOffset = start2 ? start2 : 0;
  let endOffset = end2 ? end2 : text.length;
  while (match = codeBlockRegex.exec(text)) {
    const [rawMatch, firstLine, header, content, lastLine] = match;
    if (startOffset == endOffset) {
      if (!(startOffset >= match.index && startOffset <= match.index + rawMatch.length)) {
        continue;
      }
    } else {
      if (!(match.index >= startOffset && match.index + rawMatch.length <= endOffset)) {
        continue;
      }
    }
    const block = parseCodeBlock(codeBlockRegex, match, rawMatch);
    blocks.push(block);
  }
  return blocks;
}
function getFrontmatter(text) {
  if (!text || !text.startsWith("---")) {
    return null;
  }
  const match = text.match(new RegExp(RegExPatterns.Frontmatter.source, "gs"));
  if (!match) {
    return null;
  }
  const [frontmatter] = match;
  return new TextPart(frontmatter, new Position(0, frontmatter.length));
}
function getFileExtension(path) {
  const dotIdx = path.lastIndexOf(".");
  if (dotIdx > 0) {
    for (let i = dotIdx; i < path.length; i++) {
      if (path[i] === "/" || path[i] === "\\") {
        return null;
      }
    }
    return path.substring(dotIdx);
  }
  return null;
}
var DestinationType = /* @__PURE__ */ ((DestinationType2) => {
  DestinationType2["None"] = "none";
  DestinationType2["Absolute"] = "absolute";
  DestinationType2["Relative"] = "relative";
  DestinationType2["Shortest"] = "shortest";
  return DestinationType2;
})(DestinationType || {});
function createWikiLink(sourcePath, destination, destinationSubPath, text, dimensions, destinationType = "none" /* None */) {
  return `[[${destination}${destinationSubPath ? "#" + destinationSubPath : ""} ${text ? "|" + text : ""}${dimensions ? "|" + dimensions : ""}]]`;
}
function createMarkdownLink(sourcePath, destination, destinationSubPath, text, dimensions, destinationType = "none" /* None */) {
  return `[${text != null ? text : ""}${dimensions ? "|" + dimensions : ""}](${destination}${destinationSubPath ? "#" + destinationSubPath : ""})`;
}

// suggesters/LinkTextSuggest.ts
var import_obsidian = require("obsidian");
var LinkTextSuggest = class extends import_obsidian.EditorSuggest {
  constructor(context) {
    super(context.app);
    this.suggestContext = context;
  }
  onTrigger(cursor, editor, file) {
    var _a;
    if (!this.suggestContext.provideSuggestions || !this.suggestContext.linkData) {
      return null;
    }
    const linkEnd = editor.offsetToPos((_a = this.suggestContext.linkData) == null ? void 0 : _a.position.end);
    return {
      start: linkEnd,
      end: linkEnd,
      query: ""
    };
  }
  getSuggestions(context) {
    let noteName = this.suggestContext.linkData.type == 1 /* Markdown */ ? (
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      getFileName(decodeURI(this.suggestContext.linkData.destination.content))
    ) : getFileName(this.suggestContext.linkData.destination.content);
    noteName = noteName.substring(0, noteName.indexOf("#"));
    return [
      // last title
      this.suggestContext.titles[this.suggestContext.titles.length - 1],
      // note name with all specified titles
      [noteName, ...this.suggestContext.titles].join(this.suggestContext.titleSeparator),
      // note name
      noteName
    ];
  }
  renderSuggestion(suggestion, el) {
    const outer = el.createDiv({ cls: "ES-suggester-container" });
    outer.createDiv({ cls: "ES-tags" }).setText(`${suggestion}`);
  }
  // TODO: refactor
  selectSuggestion(suggestion) {
    var _a, _b, _c, _d, _e, _f;
    if (!((_a = this.context) == null ? void 0 : _a.editor) || !((_b = this.suggestContext.linkData) == null ? void 0 : _b.destination)) {
      return;
    }
    const linkData = this.suggestContext.linkData;
    if ((linkData == null ? void 0 : linkData.type) == 2 /* Wiki */) {
      const editor = this.context.editor;
      let textStartOffset = linkData.position.start + linkData.destination.position.end;
      if ((_c = linkData.text) == null ? void 0 : _c.content) {
        editor.replaceRange(
          "|" + suggestion,
          editor == null ? void 0 : editor.offsetToPos(textStartOffset),
          editor == null ? void 0 : editor.offsetToPos(textStartOffset + ((_d = linkData.text) == null ? void 0 : _d.content.length) + 1)
        );
      } else {
        editor.replaceRange("|" + suggestion, editor == null ? void 0 : editor.offsetToPos(textStartOffset));
      }
      textStartOffset++;
      editor.setSelection(editor.offsetToPos(textStartOffset), editor.offsetToPos(textStartOffset + suggestion.length));
      this.suggestContext.clearLinkData();
    } else if ((linkData == null ? void 0 : linkData.type) == 1 /* Markdown */) {
      const editor = this.context.editor;
      const textStartOffset = (linkData == null ? void 0 : linkData.position.start) + 1;
      if ((_e = linkData.text) == null ? void 0 : _e.content) {
        editor.replaceRange(
          suggestion,
          editor == null ? void 0 : editor.offsetToPos(textStartOffset),
          editor == null ? void 0 : editor.offsetToPos(textStartOffset + ((_f = linkData.text) == null ? void 0 : _f.content.length))
        );
      } else {
        editor.replaceRange(suggestion, editor == null ? void 0 : editor.offsetToPos(textStartOffset));
      }
      editor.setSelection(editor.offsetToPos(textStartOffset), editor.offsetToPos(textStartOffset + suggestion.length));
      this.suggestContext.clearLinkData();
    }
  }
};

// ui/ReplaceLinkModal.ts
var import_obsidian3 = require("obsidian");

// suggesters/suggest.ts
var import_obsidian2 = require("obsidian");

// node_modules/@popperjs/core/lib/enums.js
var top = "top";
var bottom = "bottom";
var right = "right";
var left = "left";
var auto = "auto";
var basePlacements = [top, bottom, right, left];
var start = "start";
var end = "end";
var clippingParents = "clippingParents";
var viewport = "viewport";
var popper = "popper";
var reference = "reference";
var variationPlacements = /* @__PURE__ */ basePlacements.reduce(function(acc, placement) {
  return acc.concat([placement + "-" + start, placement + "-" + end]);
}, []);
var placements = /* @__PURE__ */ [].concat(basePlacements, [auto]).reduce(function(acc, placement) {
  return acc.concat([placement, placement + "-" + start, placement + "-" + end]);
}, []);
var beforeRead = "beforeRead";
var read = "read";
var afterRead = "afterRead";
var beforeMain = "beforeMain";
var main = "main";
var afterMain = "afterMain";
var beforeWrite = "beforeWrite";
var write = "write";
var afterWrite = "afterWrite";
var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];

// node_modules/@popperjs/core/lib/dom-utils/getNodeName.js
function getNodeName(element) {
  return element ? (element.nodeName || "").toLowerCase() : null;
}

// node_modules/@popperjs/core/lib/dom-utils/getWindow.js
function getWindow(node) {
  if (node == null) {
    return window;
  }
  if (node.toString() !== "[object Window]") {
    var ownerDocument = node.ownerDocument;
    return ownerDocument ? ownerDocument.defaultView || window : window;
  }
  return node;
}

// node_modules/@popperjs/core/lib/dom-utils/instanceOf.js
function isElement(node) {
  var OwnElement = getWindow(node).Element;
  return node instanceof OwnElement || node instanceof Element;
}
function isHTMLElement(node) {
  var OwnElement = getWindow(node).HTMLElement;
  return node instanceof OwnElement || node instanceof HTMLElement;
}
function isShadowRoot(node) {
  if (typeof ShadowRoot === "undefined") {
    return false;
  }
  var OwnElement = getWindow(node).ShadowRoot;
  return node instanceof OwnElement || node instanceof ShadowRoot;
}

// node_modules/@popperjs/core/lib/modifiers/applyStyles.js
function applyStyles(_ref) {
  var state = _ref.state;
  Object.keys(state.elements).forEach(function(name) {
    var style = state.styles[name] || {};
    var attributes = state.attributes[name] || {};
    var element = state.elements[name];
    if (!isHTMLElement(element) || !getNodeName(element)) {
      return;
    }
    Object.assign(element.style, style);
    Object.keys(attributes).forEach(function(name2) {
      var value = attributes[name2];
      if (value === false) {
        element.removeAttribute(name2);
      } else {
        element.setAttribute(name2, value === true ? "" : value);
      }
    });
  });
}
function effect(_ref2) {
  var state = _ref2.state;
  var initialStyles = {
    popper: {
      position: state.options.strategy,
      left: "0",
      top: "0",
      margin: "0"
    },
    arrow: {
      position: "absolute"
    },
    reference: {}
  };
  Object.assign(state.elements.popper.style, initialStyles.popper);
  state.styles = initialStyles;
  if (state.elements.arrow) {
    Object.assign(state.elements.arrow.style, initialStyles.arrow);
  }
  return function() {
    Object.keys(state.elements).forEach(function(name) {
      var element = state.elements[name];
      var attributes = state.attributes[name] || {};
      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]);
      var style = styleProperties.reduce(function(style2, property) {
        style2[property] = "";
        return style2;
      }, {});
      if (!isHTMLElement(element) || !getNodeName(element)) {
        return;
      }
      Object.assign(element.style, style);
      Object.keys(attributes).forEach(function(attribute) {
        element.removeAttribute(attribute);
      });
    });
  };
}
var applyStyles_default = {
  name: "applyStyles",
  enabled: true,
  phase: "write",
  fn: applyStyles,
  effect,
  requires: ["computeStyles"]
};

// node_modules/@popperjs/core/lib/utils/getBasePlacement.js
function getBasePlacement(placement) {
  return placement.split("-")[0];
}

// node_modules/@popperjs/core/lib/utils/math.js
var max = Math.max;
var min = Math.min;
var round = Math.round;

// node_modules/@popperjs/core/lib/utils/userAgent.js
function getUAString() {
  var uaData = navigator.userAgentData;
  if (uaData != null && uaData.brands && Array.isArray(uaData.brands)) {
    return uaData.brands.map(function(item) {
      return item.brand + "/" + item.version;
    }).join(" ");
  }
  return navigator.userAgent;
}

// node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js
function isLayoutViewport() {
  return !/^((?!chrome|android).)*safari/i.test(getUAString());
}

// node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js
function getBoundingClientRect(element, includeScale, isFixedStrategy) {
  if (includeScale === void 0) {
    includeScale = false;
  }
  if (isFixedStrategy === void 0) {
    isFixedStrategy = false;
  }
  var clientRect = element.getBoundingClientRect();
  var scaleX = 1;
  var scaleY = 1;
  if (includeScale && isHTMLElement(element)) {
    scaleX = element.offsetWidth > 0 ? round(clientRect.width) / element.offsetWidth || 1 : 1;
    scaleY = element.offsetHeight > 0 ? round(clientRect.height) / element.offsetHeight || 1 : 1;
  }
  var _ref = isElement(element) ? getWindow(element) : window, visualViewport = _ref.visualViewport;
  var addVisualOffsets = !isLayoutViewport() && isFixedStrategy;
  var x = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;
  var y = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;
  var width = clientRect.width / scaleX;
  var height = clientRect.height / scaleY;
  return {
    width,
    height,
    top: y,
    right: x + width,
    bottom: y + height,
    left: x,
    x,
    y
  };
}

// node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js
function getLayoutRect(element) {
  var clientRect = getBoundingClientRect(element);
  var width = element.offsetWidth;
  var height = element.offsetHeight;
  if (Math.abs(clientRect.width - width) <= 1) {
    width = clientRect.width;
  }
  if (Math.abs(clientRect.height - height) <= 1) {
    height = clientRect.height;
  }
  return {
    x: element.offsetLeft,
    y: element.offsetTop,
    width,
    height
  };
}

// node_modules/@popperjs/core/lib/dom-utils/contains.js
function contains(parent, child) {
  var rootNode = child.getRootNode && child.getRootNode();
  if (parent.contains(child)) {
    return true;
  } else if (rootNode && isShadowRoot(rootNode)) {
    var next = child;
    do {
      if (next && parent.isSameNode(next)) {
        return true;
      }
      next = next.parentNode || next.host;
    } while (next);
  }
  return false;
}

// node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js
function getComputedStyle(element) {
  return getWindow(element).getComputedStyle(element);
}

// node_modules/@popperjs/core/lib/dom-utils/isTableElement.js
function isTableElement(element) {
  return ["table", "td", "th"].indexOf(getNodeName(element)) >= 0;
}

// node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js
function getDocumentElement(element) {
  return ((isElement(element) ? element.ownerDocument : (
    // $FlowFixMe[prop-missing]
    element.document
  )) || window.document).documentElement;
}

// node_modules/@popperjs/core/lib/dom-utils/getParentNode.js
function getParentNode(element) {
  if (getNodeName(element) === "html") {
    return element;
  }
  return (
    // this is a quicker (but less type safe) way to save quite some bytes from the bundle
    // $FlowFixMe[incompatible-return]
    // $FlowFixMe[prop-missing]
    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node
    element.parentNode || // DOM Element detected
    (isShadowRoot(element) ? element.host : null) || // ShadowRoot detected
    // $FlowFixMe[incompatible-call]: HTMLElement is a Node
    getDocumentElement(element)
  );
}

// node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js
function getTrueOffsetParent(element) {
  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837
  getComputedStyle(element).position === "fixed") {
    return null;
  }
  return element.offsetParent;
}
function getContainingBlock(element) {
  var isFirefox = /firefox/i.test(getUAString());
  var isIE = /Trident/i.test(getUAString());
  if (isIE && isHTMLElement(element)) {
    var elementCss = getComputedStyle(element);
    if (elementCss.position === "fixed") {
      return null;
    }
  }
  var currentNode = getParentNode(element);
  if (isShadowRoot(currentNode)) {
    currentNode = currentNode.host;
  }
  while (isHTMLElement(currentNode) && ["html", "body"].indexOf(getNodeName(currentNode)) < 0) {
    var css = getComputedStyle(currentNode);
    if (css.transform !== "none" || css.perspective !== "none" || css.contain === "paint" || ["transform", "perspective"].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === "filter" || isFirefox && css.filter && css.filter !== "none") {
      return currentNode;
    } else {
      currentNode = currentNode.parentNode;
    }
  }
  return null;
}
function getOffsetParent(element) {
  var window2 = getWindow(element);
  var offsetParent = getTrueOffsetParent(element);
  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === "static") {
    offsetParent = getTrueOffsetParent(offsetParent);
  }
  if (offsetParent && (getNodeName(offsetParent) === "html" || getNodeName(offsetParent) === "body" && getComputedStyle(offsetParent).position === "static")) {
    return window2;
  }
  return offsetParent || getContainingBlock(element) || window2;
}

// node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js
function getMainAxisFromPlacement(placement) {
  return ["top", "bottom"].indexOf(placement) >= 0 ? "x" : "y";
}

// node_modules/@popperjs/core/lib/utils/within.js
function within(min2, value, max2) {
  return max(min2, min(value, max2));
}
function withinMaxClamp(min2, value, max2) {
  var v = within(min2, value, max2);
  return v > max2 ? max2 : v;
}

// node_modules/@popperjs/core/lib/utils/getFreshSideObject.js
function getFreshSideObject() {
  return {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  };
}

// node_modules/@popperjs/core/lib/utils/mergePaddingObject.js
function mergePaddingObject(paddingObject) {
  return Object.assign({}, getFreshSideObject(), paddingObject);
}

// node_modules/@popperjs/core/lib/utils/expandToHashMap.js
function expandToHashMap(value, keys) {
  return keys.reduce(function(hashMap, key) {
    hashMap[key] = value;
    return hashMap;
  }, {});
}

// node_modules/@popperjs/core/lib/modifiers/arrow.js
var toPaddingObject = function toPaddingObject2(padding, state) {
  padding = typeof padding === "function" ? padding(Object.assign({}, state.rects, {
    placement: state.placement
  })) : padding;
  return mergePaddingObject(typeof padding !== "number" ? padding : expandToHashMap(padding, basePlacements));
};
function arrow(_ref) {
  var _state$modifiersData$;
  var state = _ref.state, name = _ref.name, options = _ref.options;
  var arrowElement = state.elements.arrow;
  var popperOffsets2 = state.modifiersData.popperOffsets;
  var basePlacement = getBasePlacement(state.placement);
  var axis = getMainAxisFromPlacement(basePlacement);
  var isVertical = [left, right].indexOf(basePlacement) >= 0;
  var len = isVertical ? "height" : "width";
  if (!arrowElement || !popperOffsets2) {
    return;
  }
  var paddingObject = toPaddingObject(options.padding, state);
  var arrowRect = getLayoutRect(arrowElement);
  var minProp = axis === "y" ? top : left;
  var maxProp = axis === "y" ? bottom : right;
  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets2[axis] - state.rects.popper[len];
  var startDiff = popperOffsets2[axis] - state.rects.reference[axis];
  var arrowOffsetParent = getOffsetParent(arrowElement);
  var clientSize = arrowOffsetParent ? axis === "y" ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;
  var centerToReference = endDiff / 2 - startDiff / 2;
  var min2 = paddingObject[minProp];
  var max2 = clientSize - arrowRect[len] - paddingObject[maxProp];
  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;
  var offset2 = within(min2, center, max2);
  var axisProp = axis;
  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset2, _state$modifiersData$.centerOffset = offset2 - center, _state$modifiersData$);
}
function effect2(_ref2) {
  var state = _ref2.state, options = _ref2.options;
  var _options$element = options.element, arrowElement = _options$element === void 0 ? "[data-popper-arrow]" : _options$element;
  if (arrowElement == null) {
    return;
  }
  if (typeof arrowElement === "string") {
    arrowElement = state.elements.popper.querySelector(arrowElement);
    if (!arrowElement) {
      return;
    }
  }
  if (!contains(state.elements.popper, arrowElement)) {
    return;
  }
  state.elements.arrow = arrowElement;
}
var arrow_default = {
  name: "arrow",
  enabled: true,
  phase: "main",
  fn: arrow,
  effect: effect2,
  requires: ["popperOffsets"],
  requiresIfExists: ["preventOverflow"]
};

// node_modules/@popperjs/core/lib/utils/getVariation.js
function getVariation(placement) {
  return placement.split("-")[1];
}

// node_modules/@popperjs/core/lib/modifiers/computeStyles.js
var unsetSides = {
  top: "auto",
  right: "auto",
  bottom: "auto",
  left: "auto"
};
function roundOffsetsByDPR(_ref, win) {
  var x = _ref.x, y = _ref.y;
  var dpr = win.devicePixelRatio || 1;
  return {
    x: round(x * dpr) / dpr || 0,
    y: round(y * dpr) / dpr || 0
  };
}
function mapToStyles(_ref2) {
  var _Object$assign2;
  var popper2 = _ref2.popper, popperRect = _ref2.popperRect, placement = _ref2.placement, variation = _ref2.variation, offsets = _ref2.offsets, position = _ref2.position, gpuAcceleration = _ref2.gpuAcceleration, adaptive = _ref2.adaptive, roundOffsets = _ref2.roundOffsets, isFixed = _ref2.isFixed;
  var _offsets$x = offsets.x, x = _offsets$x === void 0 ? 0 : _offsets$x, _offsets$y = offsets.y, y = _offsets$y === void 0 ? 0 : _offsets$y;
  var _ref3 = typeof roundOffsets === "function" ? roundOffsets({
    x,
    y
  }) : {
    x,
    y
  };
  x = _ref3.x;
  y = _ref3.y;
  var hasX = offsets.hasOwnProperty("x");
  var hasY = offsets.hasOwnProperty("y");
  var sideX = left;
  var sideY = top;
  var win = window;
  if (adaptive) {
    var offsetParent = getOffsetParent(popper2);
    var heightProp = "clientHeight";
    var widthProp = "clientWidth";
    if (offsetParent === getWindow(popper2)) {
      offsetParent = getDocumentElement(popper2);
      if (getComputedStyle(offsetParent).position !== "static" && position === "absolute") {
        heightProp = "scrollHeight";
        widthProp = "scrollWidth";
      }
    }
    offsetParent = offsetParent;
    if (placement === top || (placement === left || placement === right) && variation === end) {
      sideY = bottom;
      var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : (
        // $FlowFixMe[prop-missing]
        offsetParent[heightProp]
      );
      y -= offsetY - popperRect.height;
      y *= gpuAcceleration ? 1 : -1;
    }
    if (placement === left || (placement === top || placement === bottom) && variation === end) {
      sideX = right;
      var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : (
        // $FlowFixMe[prop-missing]
        offsetParent[widthProp]
      );
      x -= offsetX - popperRect.width;
      x *= gpuAcceleration ? 1 : -1;
    }
  }
  var commonStyles = Object.assign({
    position
  }, adaptive && unsetSides);
  var _ref4 = roundOffsets === true ? roundOffsetsByDPR({
    x,
    y
  }, getWindow(popper2)) : {
    x,
    y
  };
  x = _ref4.x;
  y = _ref4.y;
  if (gpuAcceleration) {
    var _Object$assign;
    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? "0" : "", _Object$assign[sideX] = hasX ? "0" : "", _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? "translate(" + x + "px, " + y + "px)" : "translate3d(" + x + "px, " + y + "px, 0)", _Object$assign));
  }
  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + "px" : "", _Object$assign2[sideX] = hasX ? x + "px" : "", _Object$assign2.transform = "", _Object$assign2));
}
function computeStyles(_ref5) {
  var state = _ref5.state, options = _ref5.options;
  var _options$gpuAccelerat = options.gpuAcceleration, gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat, _options$adaptive = options.adaptive, adaptive = _options$adaptive === void 0 ? true : _options$adaptive, _options$roundOffsets = options.roundOffsets, roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;
  var commonStyles = {
    placement: getBasePlacement(state.placement),
    variation: getVariation(state.placement),
    popper: state.elements.popper,
    popperRect: state.rects.popper,
    gpuAcceleration,
    isFixed: state.options.strategy === "fixed"
  };
  if (state.modifiersData.popperOffsets != null) {
    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {
      offsets: state.modifiersData.popperOffsets,
      position: state.options.strategy,
      adaptive,
      roundOffsets
    })));
  }
  if (state.modifiersData.arrow != null) {
    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {
      offsets: state.modifiersData.arrow,
      position: "absolute",
      adaptive: false,
      roundOffsets
    })));
  }
  state.attributes.popper = Object.assign({}, state.attributes.popper, {
    "data-popper-placement": state.placement
  });
}
var computeStyles_default = {
  name: "computeStyles",
  enabled: true,
  phase: "beforeWrite",
  fn: computeStyles,
  data: {}
};

// node_modules/@popperjs/core/lib/modifiers/eventListeners.js
var passive = {
  passive: true
};
function effect3(_ref) {
  var state = _ref.state, instance = _ref.instance, options = _ref.options;
  var _options$scroll = options.scroll, scroll = _options$scroll === void 0 ? true : _options$scroll, _options$resize = options.resize, resize = _options$resize === void 0 ? true : _options$resize;
  var window2 = getWindow(state.elements.popper);
  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);
  if (scroll) {
    scrollParents.forEach(function(scrollParent) {
      scrollParent.addEventListener("scroll", instance.update, passive);
    });
  }
  if (resize) {
    window2.addEventListener("resize", instance.update, passive);
  }
  return function() {
    if (scroll) {
      scrollParents.forEach(function(scrollParent) {
        scrollParent.removeEventListener("scroll", instance.update, passive);
      });
    }
    if (resize) {
      window2.removeEventListener("resize", instance.update, passive);
    }
  };
}
var eventListeners_default = {
  name: "eventListeners",
  enabled: true,
  phase: "write",
  fn: function fn() {
  },
  effect: effect3,
  data: {}
};

// node_modules/@popperjs/core/lib/utils/getOppositePlacement.js
var hash = {
  left: "right",
  right: "left",
  bottom: "top",
  top: "bottom"
};
function getOppositePlacement(placement) {
  return placement.replace(/left|right|bottom|top/g, function(matched) {
    return hash[matched];
  });
}

// node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js
var hash2 = {
  start: "end",
  end: "start"
};
function getOppositeVariationPlacement(placement) {
  return placement.replace(/start|end/g, function(matched) {
    return hash2[matched];
  });
}

// node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js
function getWindowScroll(node) {
  var win = getWindow(node);
  var scrollLeft = win.pageXOffset;
  var scrollTop = win.pageYOffset;
  return {
    scrollLeft,
    scrollTop
  };
}

// node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js
function getWindowScrollBarX(element) {
  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;
}

// node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js
function getViewportRect(element, strategy) {
  var win = getWindow(element);
  var html = getDocumentElement(element);
  var visualViewport = win.visualViewport;
  var width = html.clientWidth;
  var height = html.clientHeight;
  var x = 0;
  var y = 0;
  if (visualViewport) {
    width = visualViewport.width;
    height = visualViewport.height;
    var layoutViewport = isLayoutViewport();
    if (layoutViewport || !layoutViewport && strategy === "fixed") {
      x = visualViewport.offsetLeft;
      y = visualViewport.offsetTop;
    }
  }
  return {
    width,
    height,
    x: x + getWindowScrollBarX(element),
    y
  };
}

// node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js
function getDocumentRect(element) {
  var _element$ownerDocumen;
  var html = getDocumentElement(element);
  var winScroll = getWindowScroll(element);
  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;
  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);
  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);
  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);
  var y = -winScroll.scrollTop;
  if (getComputedStyle(body || html).direction === "rtl") {
    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;
  }
  return {
    width,
    height,
    x,
    y
  };
}

// node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js
function isScrollParent(element) {
  var _getComputedStyle = getComputedStyle(element), overflow = _getComputedStyle.overflow, overflowX = _getComputedStyle.overflowX, overflowY = _getComputedStyle.overflowY;
  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);
}

// node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js
function getScrollParent(node) {
  if (["html", "body", "#document"].indexOf(getNodeName(node)) >= 0) {
    return node.ownerDocument.body;
  }
  if (isHTMLElement(node) && isScrollParent(node)) {
    return node;
  }
  return getScrollParent(getParentNode(node));
}

// node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js
function listScrollParents(element, list) {
  var _element$ownerDocumen;
  if (list === void 0) {
    list = [];
  }
  var scrollParent = getScrollParent(element);
  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);
  var win = getWindow(scrollParent);
  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;
  var updatedList = list.concat(target);
  return isBody ? updatedList : (
    // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here
    updatedList.concat(listScrollParents(getParentNode(target)))
  );
}

// node_modules/@popperjs/core/lib/utils/rectToClientRect.js
function rectToClientRect(rect) {
  return Object.assign({}, rect, {
    left: rect.x,
    top: rect.y,
    right: rect.x + rect.width,
    bottom: rect.y + rect.height
  });
}

// node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js
function getInnerBoundingClientRect(element, strategy) {
  var rect = getBoundingClientRect(element, false, strategy === "fixed");
  rect.top = rect.top + element.clientTop;
  rect.left = rect.left + element.clientLeft;
  rect.bottom = rect.top + element.clientHeight;
  rect.right = rect.left + element.clientWidth;
  rect.width = element.clientWidth;
  rect.height = element.clientHeight;
  rect.x = rect.left;
  rect.y = rect.top;
  return rect;
}
function getClientRectFromMixedType(element, clippingParent, strategy) {
  return clippingParent === viewport ? rectToClientRect(getViewportRect(element, strategy)) : isElement(clippingParent) ? getInnerBoundingClientRect(clippingParent, strategy) : rectToClientRect(getDocumentRect(getDocumentElement(element)));
}
function getClippingParents(element) {
  var clippingParents2 = listScrollParents(getParentNode(element));
  var canEscapeClipping = ["absolute", "fixed"].indexOf(getComputedStyle(element).position) >= 0;
  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;
  if (!isElement(clipperElement)) {
    return [];
  }
  return clippingParents2.filter(function(clippingParent) {
    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== "body";
  });
}
function getClippingRect(element, boundary, rootBoundary, strategy) {
  var mainClippingParents = boundary === "clippingParents" ? getClippingParents(element) : [].concat(boundary);
  var clippingParents2 = [].concat(mainClippingParents, [rootBoundary]);
  var firstClippingParent = clippingParents2[0];
  var clippingRect = clippingParents2.reduce(function(accRect, clippingParent) {
    var rect = getClientRectFromMixedType(element, clippingParent, strategy);
    accRect.top = max(rect.top, accRect.top);
    accRect.right = min(rect.right, accRect.right);
    accRect.bottom = min(rect.bottom, accRect.bottom);
    accRect.left = max(rect.left, accRect.left);
    return accRect;
  }, getClientRectFromMixedType(element, firstClippingParent, strategy));
  clippingRect.width = clippingRect.right - clippingRect.left;
  clippingRect.height = clippingRect.bottom - clippingRect.top;
  clippingRect.x = clippingRect.left;
  clippingRect.y = clippingRect.top;
  return clippingRect;
}

// node_modules/@popperjs/core/lib/utils/computeOffsets.js
function computeOffsets(_ref) {
  var reference2 = _ref.reference, element = _ref.element, placement = _ref.placement;
  var basePlacement = placement ? getBasePlacement(placement) : null;
  var variation = placement ? getVariation(placement) : null;
  var commonX = reference2.x + reference2.width / 2 - element.width / 2;
  var commonY = reference2.y + reference2.height / 2 - element.height / 2;
  var offsets;
  switch (basePlacement) {
    case top:
      offsets = {
        x: commonX,
        y: reference2.y - element.height
      };
      break;
    case bottom:
      offsets = {
        x: commonX,
        y: reference2.y + reference2.height
      };
      break;
    case right:
      offsets = {
        x: reference2.x + reference2.width,
        y: commonY
      };
      break;
    case left:
      offsets = {
        x: reference2.x - element.width,
        y: commonY
      };
      break;
    default:
      offsets = {
        x: reference2.x,
        y: reference2.y
      };
  }
  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;
  if (mainAxis != null) {
    var len = mainAxis === "y" ? "height" : "width";
    switch (variation) {
      case start:
        offsets[mainAxis] = offsets[mainAxis] - (reference2[len] / 2 - element[len] / 2);
        break;
      case end:
        offsets[mainAxis] = offsets[mainAxis] + (reference2[len] / 2 - element[len] / 2);
        break;
      default:
    }
  }
  return offsets;
}

// node_modules/@popperjs/core/lib/utils/detectOverflow.js
function detectOverflow(state, options) {
  if (options === void 0) {
    options = {};
  }
  var _options = options, _options$placement = _options.placement, placement = _options$placement === void 0 ? state.placement : _options$placement, _options$strategy = _options.strategy, strategy = _options$strategy === void 0 ? state.strategy : _options$strategy, _options$boundary = _options.boundary, boundary = _options$boundary === void 0 ? clippingParents : _options$boundary, _options$rootBoundary = _options.rootBoundary, rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary, _options$elementConte = _options.elementContext, elementContext = _options$elementConte === void 0 ? popper : _options$elementConte, _options$altBoundary = _options.altBoundary, altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary, _options$padding = _options.padding, padding = _options$padding === void 0 ? 0 : _options$padding;
  var paddingObject = mergePaddingObject(typeof padding !== "number" ? padding : expandToHashMap(padding, basePlacements));
  var altContext = elementContext === popper ? reference : popper;
  var popperRect = state.rects.popper;
  var element = state.elements[altBoundary ? altContext : elementContext];
  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary, strategy);
  var referenceClientRect = getBoundingClientRect(state.elements.reference);
  var popperOffsets2 = computeOffsets({
    reference: referenceClientRect,
    element: popperRect,
    strategy: "absolute",
    placement
  });
  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets2));
  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect;
  var overflowOffsets = {
    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,
    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,
    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,
    right: elementClientRect.right - clippingClientRect.right + paddingObject.right
  };
  var offsetData = state.modifiersData.offset;
  if (elementContext === popper && offsetData) {
    var offset2 = offsetData[placement];
    Object.keys(overflowOffsets).forEach(function(key) {
      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;
      var axis = [top, bottom].indexOf(key) >= 0 ? "y" : "x";
      overflowOffsets[key] += offset2[axis] * multiply;
    });
  }
  return overflowOffsets;
}

// node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js
function computeAutoPlacement(state, options) {
  if (options === void 0) {
    options = {};
  }
  var _options = options, placement = _options.placement, boundary = _options.boundary, rootBoundary = _options.rootBoundary, padding = _options.padding, flipVariations = _options.flipVariations, _options$allowedAutoP = _options.allowedAutoPlacements, allowedAutoPlacements = _options$allowedAutoP === void 0 ? placements : _options$allowedAutoP;
  var variation = getVariation(placement);
  var placements2 = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function(placement2) {
    return getVariation(placement2) === variation;
  }) : basePlacements;
  var allowedPlacements = placements2.filter(function(placement2) {
    return allowedAutoPlacements.indexOf(placement2) >= 0;
  });
  if (allowedPlacements.length === 0) {
    allowedPlacements = placements2;
  }
  var overflows = allowedPlacements.reduce(function(acc, placement2) {
    acc[placement2] = detectOverflow(state, {
      placement: placement2,
      boundary,
      rootBoundary,
      padding
    })[getBasePlacement(placement2)];
    return acc;
  }, {});
  return Object.keys(overflows).sort(function(a, b) {
    return overflows[a] - overflows[b];
  });
}

// node_modules/@popperjs/core/lib/modifiers/flip.js
function getExpandedFallbackPlacements(placement) {
  if (getBasePlacement(placement) === auto) {
    return [];
  }
  var oppositePlacement = getOppositePlacement(placement);
  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];
}
function flip(_ref) {
  var state = _ref.state, options = _ref.options, name = _ref.name;
  if (state.modifiersData[name]._skip) {
    return;
  }
  var _options$mainAxis = options.mainAxis, checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis, _options$altAxis = options.altAxis, checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis, specifiedFallbackPlacements = options.fallbackPlacements, padding = options.padding, boundary = options.boundary, rootBoundary = options.rootBoundary, altBoundary = options.altBoundary, _options$flipVariatio = options.flipVariations, flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio, allowedAutoPlacements = options.allowedAutoPlacements;
  var preferredPlacement = state.options.placement;
  var basePlacement = getBasePlacement(preferredPlacement);
  var isBasePlacement = basePlacement === preferredPlacement;
  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));
  var placements2 = [preferredPlacement].concat(fallbackPlacements).reduce(function(acc, placement2) {
    return acc.concat(getBasePlacement(placement2) === auto ? computeAutoPlacement(state, {
      placement: placement2,
      boundary,
      rootBoundary,
      padding,
      flipVariations,
      allowedAutoPlacements
    }) : placement2);
  }, []);
  var referenceRect = state.rects.reference;
  var popperRect = state.rects.popper;
  var checksMap = /* @__PURE__ */ new Map();
  var makeFallbackChecks = true;
  var firstFittingPlacement = placements2[0];
  for (var i = 0; i < placements2.length; i++) {
    var placement = placements2[i];
    var _basePlacement = getBasePlacement(placement);
    var isStartVariation = getVariation(placement) === start;
    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;
    var len = isVertical ? "width" : "height";
    var overflow = detectOverflow(state, {
      placement,
      boundary,
      rootBoundary,
      altBoundary,
      padding
    });
    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;
    if (referenceRect[len] > popperRect[len]) {
      mainVariationSide = getOppositePlacement(mainVariationSide);
    }
    var altVariationSide = getOppositePlacement(mainVariationSide);
    var checks = [];
    if (checkMainAxis) {
      checks.push(overflow[_basePlacement] <= 0);
    }
    if (checkAltAxis) {
      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);
    }
    if (checks.every(function(check) {
      return check;
    })) {
      firstFittingPlacement = placement;
      makeFallbackChecks = false;
      break;
    }
    checksMap.set(placement, checks);
  }
  if (makeFallbackChecks) {
    var numberOfChecks = flipVariations ? 3 : 1;
    var _loop = function _loop2(_i2) {
      var fittingPlacement = placements2.find(function(placement2) {
        var checks2 = checksMap.get(placement2);
        if (checks2) {
          return checks2.slice(0, _i2).every(function(check) {
            return check;
          });
        }
      });
      if (fittingPlacement) {
        firstFittingPlacement = fittingPlacement;
        return "break";
      }
    };
    for (var _i = numberOfChecks; _i > 0; _i--) {
      var _ret = _loop(_i);
      if (_ret === "break")
        break;
    }
  }
  if (state.placement !== firstFittingPlacement) {
    state.modifiersData[name]._skip = true;
    state.placement = firstFittingPlacement;
    state.reset = true;
  }
}
var flip_default = {
  name: "flip",
  enabled: true,
  phase: "main",
  fn: flip,
  requiresIfExists: ["offset"],
  data: {
    _skip: false
  }
};

// node_modules/@popperjs/core/lib/modifiers/hide.js
function getSideOffsets(overflow, rect, preventedOffsets) {
  if (preventedOffsets === void 0) {
    preventedOffsets = {
      x: 0,
      y: 0
    };
  }
  return {
    top: overflow.top - rect.height - preventedOffsets.y,
    right: overflow.right - rect.width + preventedOffsets.x,
    bottom: overflow.bottom - rect.height + preventedOffsets.y,
    left: overflow.left - rect.width - preventedOffsets.x
  };
}
function isAnySideFullyClipped(overflow) {
  return [top, right, bottom, left].some(function(side) {
    return overflow[side] >= 0;
  });
}
function hide(_ref) {
  var state = _ref.state, name = _ref.name;
  var referenceRect = state.rects.reference;
  var popperRect = state.rects.popper;
  var preventedOffsets = state.modifiersData.preventOverflow;
  var referenceOverflow = detectOverflow(state, {
    elementContext: "reference"
  });
  var popperAltOverflow = detectOverflow(state, {
    altBoundary: true
  });
  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);
  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);
  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);
  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);
  state.modifiersData[name] = {
    referenceClippingOffsets,
    popperEscapeOffsets,
    isReferenceHidden,
    hasPopperEscaped
  };
  state.attributes.popper = Object.assign({}, state.attributes.popper, {
    "data-popper-reference-hidden": isReferenceHidden,
    "data-popper-escaped": hasPopperEscaped
  });
}
var hide_default = {
  name: "hide",
  enabled: true,
  phase: "main",
  requiresIfExists: ["preventOverflow"],
  fn: hide
};

// node_modules/@popperjs/core/lib/modifiers/offset.js
function distanceAndSkiddingToXY(placement, rects, offset2) {
  var basePlacement = getBasePlacement(placement);
  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;
  var _ref = typeof offset2 === "function" ? offset2(Object.assign({}, rects, {
    placement
  })) : offset2, skidding = _ref[0], distance = _ref[1];
  skidding = skidding || 0;
  distance = (distance || 0) * invertDistance;
  return [left, right].indexOf(basePlacement) >= 0 ? {
    x: distance,
    y: skidding
  } : {
    x: skidding,
    y: distance
  };
}
function offset(_ref2) {
  var state = _ref2.state, options = _ref2.options, name = _ref2.name;
  var _options$offset = options.offset, offset2 = _options$offset === void 0 ? [0, 0] : _options$offset;
  var data = placements.reduce(function(acc, placement) {
    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset2);
    return acc;
  }, {});
  var _data$state$placement = data[state.placement], x = _data$state$placement.x, y = _data$state$placement.y;
  if (state.modifiersData.popperOffsets != null) {
    state.modifiersData.popperOffsets.x += x;
    state.modifiersData.popperOffsets.y += y;
  }
  state.modifiersData[name] = data;
}
var offset_default = {
  name: "offset",
  enabled: true,
  phase: "main",
  requires: ["popperOffsets"],
  fn: offset
};

// node_modules/@popperjs/core/lib/modifiers/popperOffsets.js
function popperOffsets(_ref) {
  var state = _ref.state, name = _ref.name;
  state.modifiersData[name] = computeOffsets({
    reference: state.rects.reference,
    element: state.rects.popper,
    strategy: "absolute",
    placement: state.placement
  });
}
var popperOffsets_default = {
  name: "popperOffsets",
  enabled: true,
  phase: "read",
  fn: popperOffsets,
  data: {}
};

// node_modules/@popperjs/core/lib/utils/getAltAxis.js
function getAltAxis(axis) {
  return axis === "x" ? "y" : "x";
}

// node_modules/@popperjs/core/lib/modifiers/preventOverflow.js
function preventOverflow(_ref) {
  var state = _ref.state, options = _ref.options, name = _ref.name;
  var _options$mainAxis = options.mainAxis, checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis, _options$altAxis = options.altAxis, checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis, boundary = options.boundary, rootBoundary = options.rootBoundary, altBoundary = options.altBoundary, padding = options.padding, _options$tether = options.tether, tether = _options$tether === void 0 ? true : _options$tether, _options$tetherOffset = options.tetherOffset, tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;
  var overflow = detectOverflow(state, {
    boundary,
    rootBoundary,
    padding,
    altBoundary
  });
  var basePlacement = getBasePlacement(state.placement);
  var variation = getVariation(state.placement);
  var isBasePlacement = !variation;
  var mainAxis = getMainAxisFromPlacement(basePlacement);
  var altAxis = getAltAxis(mainAxis);
  var popperOffsets2 = state.modifiersData.popperOffsets;
  var referenceRect = state.rects.reference;
  var popperRect = state.rects.popper;
  var tetherOffsetValue = typeof tetherOffset === "function" ? tetherOffset(Object.assign({}, state.rects, {
    placement: state.placement
  })) : tetherOffset;
  var normalizedTetherOffsetValue = typeof tetherOffsetValue === "number" ? {
    mainAxis: tetherOffsetValue,
    altAxis: tetherOffsetValue
  } : Object.assign({
    mainAxis: 0,
    altAxis: 0
  }, tetherOffsetValue);
  var offsetModifierState = state.modifiersData.offset ? state.modifiersData.offset[state.placement] : null;
  var data = {
    x: 0,
    y: 0
  };
  if (!popperOffsets2) {
    return;
  }
  if (checkMainAxis) {
    var _offsetModifierState$;
    var mainSide = mainAxis === "y" ? top : left;
    var altSide = mainAxis === "y" ? bottom : right;
    var len = mainAxis === "y" ? "height" : "width";
    var offset2 = popperOffsets2[mainAxis];
    var min2 = offset2 + overflow[mainSide];
    var max2 = offset2 - overflow[altSide];
    var additive = tether ? -popperRect[len] / 2 : 0;
    var minLen = variation === start ? referenceRect[len] : popperRect[len];
    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len];
    var arrowElement = state.elements.arrow;
    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {
      width: 0,
      height: 0
    };
    var arrowPaddingObject = state.modifiersData["arrow#persistent"] ? state.modifiersData["arrow#persistent"].padding : getFreshSideObject();
    var arrowPaddingMin = arrowPaddingObject[mainSide];
    var arrowPaddingMax = arrowPaddingObject[altSide];
    var arrowLen = within(0, referenceRect[len], arrowRect[len]);
    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis : minLen - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis;
    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis : maxLen + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis;
    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);
    var clientOffset = arrowOffsetParent ? mainAxis === "y" ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;
    var offsetModifierValue = (_offsetModifierState$ = offsetModifierState == null ? void 0 : offsetModifierState[mainAxis]) != null ? _offsetModifierState$ : 0;
    var tetherMin = offset2 + minOffset - offsetModifierValue - clientOffset;
    var tetherMax = offset2 + maxOffset - offsetModifierValue;
    var preventedOffset = within(tether ? min(min2, tetherMin) : min2, offset2, tether ? max(max2, tetherMax) : max2);
    popperOffsets2[mainAxis] = preventedOffset;
    data[mainAxis] = preventedOffset - offset2;
  }
  if (checkAltAxis) {
    var _offsetModifierState$2;
    var _mainSide = mainAxis === "x" ? top : left;
    var _altSide = mainAxis === "x" ? bottom : right;
    var _offset = popperOffsets2[altAxis];
    var _len = altAxis === "y" ? "height" : "width";
    var _min = _offset + overflow[_mainSide];
    var _max = _offset - overflow[_altSide];
    var isOriginSide = [top, left].indexOf(basePlacement) !== -1;
    var _offsetModifierValue = (_offsetModifierState$2 = offsetModifierState == null ? void 0 : offsetModifierState[altAxis]) != null ? _offsetModifierState$2 : 0;
    var _tetherMin = isOriginSide ? _min : _offset - referenceRect[_len] - popperRect[_len] - _offsetModifierValue + normalizedTetherOffsetValue.altAxis;
    var _tetherMax = isOriginSide ? _offset + referenceRect[_len] + popperRect[_len] - _offsetModifierValue - normalizedTetherOffsetValue.altAxis : _max;
    var _preventedOffset = tether && isOriginSide ? withinMaxClamp(_tetherMin, _offset, _tetherMax) : within(tether ? _tetherMin : _min, _offset, tether ? _tetherMax : _max);
    popperOffsets2[altAxis] = _preventedOffset;
    data[altAxis] = _preventedOffset - _offset;
  }
  state.modifiersData[name] = data;
}
var preventOverflow_default = {
  name: "preventOverflow",
  enabled: true,
  phase: "main",
  fn: preventOverflow,
  requiresIfExists: ["offset"]
};

// node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js
function getHTMLElementScroll(element) {
  return {
    scrollLeft: element.scrollLeft,
    scrollTop: element.scrollTop
  };
}

// node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js
function getNodeScroll(node) {
  if (node === getWindow(node) || !isHTMLElement(node)) {
    return getWindowScroll(node);
  } else {
    return getHTMLElementScroll(node);
  }
}

// node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js
function isElementScaled(element) {
  var rect = element.getBoundingClientRect();
  var scaleX = round(rect.width) / element.offsetWidth || 1;
  var scaleY = round(rect.height) / element.offsetHeight || 1;
  return scaleX !== 1 || scaleY !== 1;
}
function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {
  if (isFixed === void 0) {
    isFixed = false;
  }
  var isOffsetParentAnElement = isHTMLElement(offsetParent);
  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);
  var documentElement = getDocumentElement(offsetParent);
  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled, isFixed);
  var scroll = {
    scrollLeft: 0,
    scrollTop: 0
  };
  var offsets = {
    x: 0,
    y: 0
  };
  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {
    if (getNodeName(offsetParent) !== "body" || // https://github.com/popperjs/popper-core/issues/1078
    isScrollParent(documentElement)) {
      scroll = getNodeScroll(offsetParent);
    }
    if (isHTMLElement(offsetParent)) {
      offsets = getBoundingClientRect(offsetParent, true);
      offsets.x += offsetParent.clientLeft;
      offsets.y += offsetParent.clientTop;
    } else if (documentElement) {
      offsets.x = getWindowScrollBarX(documentElement);
    }
  }
  return {
    x: rect.left + scroll.scrollLeft - offsets.x,
    y: rect.top + scroll.scrollTop - offsets.y,
    width: rect.width,
    height: rect.height
  };
}

// node_modules/@popperjs/core/lib/utils/orderModifiers.js
function order(modifiers) {
  var map = /* @__PURE__ */ new Map();
  var visited = /* @__PURE__ */ new Set();
  var result = [];
  modifiers.forEach(function(modifier) {
    map.set(modifier.name, modifier);
  });
  function sort(modifier) {
    visited.add(modifier.name);
    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);
    requires.forEach(function(dep) {
      if (!visited.has(dep)) {
        var depModifier = map.get(dep);
        if (depModifier) {
          sort(depModifier);
        }
      }
    });
    result.push(modifier);
  }
  modifiers.forEach(function(modifier) {
    if (!visited.has(modifier.name)) {
      sort(modifier);
    }
  });
  return result;
}
function orderModifiers(modifiers) {
  var orderedModifiers = order(modifiers);
  return modifierPhases.reduce(function(acc, phase) {
    return acc.concat(orderedModifiers.filter(function(modifier) {
      return modifier.phase === phase;
    }));
  }, []);
}

// node_modules/@popperjs/core/lib/utils/debounce.js
function debounce(fn2) {
  var pending;
  return function() {
    if (!pending) {
      pending = new Promise(function(resolve) {
        Promise.resolve().then(function() {
          pending = void 0;
          resolve(fn2());
        });
      });
    }
    return pending;
  };
}

// node_modules/@popperjs/core/lib/utils/mergeByName.js
function mergeByName(modifiers) {
  var merged = modifiers.reduce(function(merged2, current) {
    var existing = merged2[current.name];
    merged2[current.name] = existing ? Object.assign({}, existing, current, {
      options: Object.assign({}, existing.options, current.options),
      data: Object.assign({}, existing.data, current.data)
    }) : current;
    return merged2;
  }, {});
  return Object.keys(merged).map(function(key) {
    return merged[key];
  });
}

// node_modules/@popperjs/core/lib/createPopper.js
var DEFAULT_OPTIONS = {
  placement: "bottom",
  modifiers: [],
  strategy: "absolute"
};
function areValidElements() {
  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
    args[_key] = arguments[_key];
  }
  return !args.some(function(element) {
    return !(element && typeof element.getBoundingClientRect === "function");
  });
}
function popperGenerator(generatorOptions) {
  if (generatorOptions === void 0) {
    generatorOptions = {};
  }
  var _generatorOptions = generatorOptions, _generatorOptions$def = _generatorOptions.defaultModifiers, defaultModifiers2 = _generatorOptions$def === void 0 ? [] : _generatorOptions$def, _generatorOptions$def2 = _generatorOptions.defaultOptions, defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;
  return function createPopper2(reference2, popper2, options) {
    if (options === void 0) {
      options = defaultOptions;
    }
    var state = {
      placement: "bottom",
      orderedModifiers: [],
      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),
      modifiersData: {},
      elements: {
        reference: reference2,
        popper: popper2
      },
      attributes: {},
      styles: {}
    };
    var effectCleanupFns = [];
    var isDestroyed = false;
    var instance = {
      state,
      setOptions: function setOptions(setOptionsAction) {
        var options2 = typeof setOptionsAction === "function" ? setOptionsAction(state.options) : setOptionsAction;
        cleanupModifierEffects();
        state.options = Object.assign({}, defaultOptions, state.options, options2);
        state.scrollParents = {
          reference: isElement(reference2) ? listScrollParents(reference2) : reference2.contextElement ? listScrollParents(reference2.contextElement) : [],
          popper: listScrollParents(popper2)
        };
        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers2, state.options.modifiers)));
        state.orderedModifiers = orderedModifiers.filter(function(m) {
          return m.enabled;
        });
        runModifierEffects();
        return instance.update();
      },
      // Sync update – it will always be executed, even if not necessary. This
      // is useful for low frequency updates where sync behavior simplifies the
      // logic.
      // For high frequency updates (e.g. `resize` and `scroll` events), always
      // prefer the async Popper#update method
      forceUpdate: function forceUpdate() {
        if (isDestroyed) {
          return;
        }
        var _state$elements = state.elements, reference3 = _state$elements.reference, popper3 = _state$elements.popper;
        if (!areValidElements(reference3, popper3)) {
          return;
        }
        state.rects = {
          reference: getCompositeRect(reference3, getOffsetParent(popper3), state.options.strategy === "fixed"),
          popper: getLayoutRect(popper3)
        };
        state.reset = false;
        state.placement = state.options.placement;
        state.orderedModifiers.forEach(function(modifier) {
          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);
        });
        for (var index = 0; index < state.orderedModifiers.length; index++) {
          if (state.reset === true) {
            state.reset = false;
            index = -1;
            continue;
          }
          var _state$orderedModifie = state.orderedModifiers[index], fn2 = _state$orderedModifie.fn, _state$orderedModifie2 = _state$orderedModifie.options, _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2, name = _state$orderedModifie.name;
          if (typeof fn2 === "function") {
            state = fn2({
              state,
              options: _options,
              name,
              instance
            }) || state;
          }
        }
      },
      // Async and optimistically optimized update – it will not be executed if
      // not necessary (debounced to run at most once-per-tick)
      update: debounce(function() {
        return new Promise(function(resolve) {
          instance.forceUpdate();
          resolve(state);
        });
      }),
      destroy: function destroy() {
        cleanupModifierEffects();
        isDestroyed = true;
      }
    };
    if (!areValidElements(reference2, popper2)) {
      return instance;
    }
    instance.setOptions(options).then(function(state2) {
      if (!isDestroyed && options.onFirstUpdate) {
        options.onFirstUpdate(state2);
      }
    });
    function runModifierEffects() {
      state.orderedModifiers.forEach(function(_ref) {
        var name = _ref.name, _ref$options = _ref.options, options2 = _ref$options === void 0 ? {} : _ref$options, effect4 = _ref.effect;
        if (typeof effect4 === "function") {
          var cleanupFn = effect4({
            state,
            name,
            instance,
            options: options2
          });
          var noopFn = function noopFn2() {
          };
          effectCleanupFns.push(cleanupFn || noopFn);
        }
      });
    }
    function cleanupModifierEffects() {
      effectCleanupFns.forEach(function(fn2) {
        return fn2();
      });
      effectCleanupFns = [];
    }
    return instance;
  };
}

// node_modules/@popperjs/core/lib/popper.js
var defaultModifiers = [eventListeners_default, popperOffsets_default, computeStyles_default, applyStyles_default, offset_default, flip_default, preventOverflow_default, arrow_default, hide_default];
var createPopper = /* @__PURE__ */ popperGenerator({
  defaultModifiers
});

// suggesters/suggest.ts
var wrapAround = (value, size) => {
  return (value % size + size) % size;
};
var Suggest = class {
  constructor(owner, containerEl, scope) {
    this.owner = owner;
    this.containerEl = containerEl;
    containerEl.on(
      "click",
      ".suggestion-item",
      this.onSuggestionClick.bind(this)
    );
    containerEl.on(
      "mousemove",
      ".suggestion-item",
      this.onSuggestionMouseover.bind(this)
    );
    scope.register([], "ArrowUp", (event) => {
      if (!event.isComposing) {
        this.setSelectedItem(this.selectedItem - 1, true);
        return false;
      }
    });
    scope.register([], "ArrowDown", (event) => {
      if (!event.isComposing) {
        this.setSelectedItem(this.selectedItem + 1, true);
        return false;
      }
    });
    scope.register([], "Enter", (event) => {
      if (!event.isComposing) {
        this.useSelectedItem(event);
        return false;
      }
    });
  }
  onSuggestionClick(event, el) {
    event.preventDefault();
    const item = this.suggestions.indexOf(el);
    this.setSelectedItem(item, false);
    this.useSelectedItem(event);
  }
  onSuggestionMouseover(_event, el) {
    const item = this.suggestions.indexOf(el);
    this.setSelectedItem(item, false);
  }
  setSuggestions(values) {
    this.containerEl.empty();
    const suggestionEls = [];
    values.forEach((value) => {
      const suggestionEl = this.containerEl.createDiv("suggestion-item");
      this.owner.renderSuggestion(value, suggestionEl);
      suggestionEls.push(suggestionEl);
    });
    this.values = values;
    this.suggestions = suggestionEls;
    this.setSelectedItem(0, false);
  }
  useSelectedItem(event) {
    const currentValue = this.values[this.selectedItem];
    if (currentValue) {
      this.owner.selectSuggestion(currentValue, event);
    }
  }
  setSelectedItem(selectedIndex, scrollIntoView) {
    const normalizedIndex = wrapAround(
      selectedIndex,
      this.suggestions.length
    );
    const prevSelectedSuggestion = this.suggestions[this.selectedItem];
    const selectedSuggestion = this.suggestions[normalizedIndex];
    prevSelectedSuggestion == null ? void 0 : prevSelectedSuggestion.removeClass("is-selected");
    selectedSuggestion == null ? void 0 : selectedSuggestion.addClass("is-selected");
    this.selectedItem = normalizedIndex;
    if (scrollIntoView) {
      selectedSuggestion.scrollIntoView(false);
    }
  }
};
var TextInputSuggest = class {
  constructor(inputEl) {
    this.inputEl = inputEl;
    this.scope = new import_obsidian2.Scope();
    this.suggestEl = createDiv("suggestion-container");
    const suggestion = this.suggestEl.createDiv("suggestion");
    this.suggest = new Suggest(this, suggestion, this.scope);
    this.scope.register([], "Escape", this.close.bind(this));
    this.inputEl.addEventListener("input", this.onInputChanged.bind(this));
    this.inputEl.addEventListener("focus", this.onInputChanged.bind(this));
    this.inputEl.addEventListener("blur", this.close.bind(this));
    this.suggestEl.on(
      "mousedown",
      ".suggestion-container",
      (event) => {
        event.preventDefault();
      }
    );
  }
  onInputChanged() {
    const inputStr = this.inputEl.value;
    const suggestions = this.getSuggestions(inputStr);
    if (!suggestions) {
      this.close();
      return;
    }
    if (suggestions.length > 0) {
      this.suggest.setSuggestions(suggestions);
      this.open(app.dom.appContainerEl, this.inputEl);
    } else {
      this.close();
    }
  }
  open(container, inputEl) {
    app.keymap.pushScope(this.scope);
    container.appendChild(this.suggestEl);
    this.popper = createPopper(inputEl, this.suggestEl, {
      placement: "bottom-start",
      modifiers: [
        {
          name: "sameWidth",
          enabled: true,
          fn: ({ state, instance }) => {
            const targetWidth = `${state.rects.reference.width}px`;
            if (state.styles.popper.width === targetWidth) {
              return;
            }
            state.styles.popper.width = targetWidth;
            instance.update().then();
          },
          phase: "beforeWrite",
          requires: ["computeStyles"]
        }
      ]
    });
  }
  close() {
    app.keymap.popScope(this.scope);
    this.suggest.setSuggestions([]);
    if (this.popper)
      this.popper.destroy();
    this.suggestEl.detach();
  }
};

// suggesters/FolderSuggester.ts
var FolderSuggest = class extends TextInputSuggest {
  getSuggestions(inputStr) {
    let normalizedInput = inputStr.toLowerCase();
    const hashIdx = normalizedInput.indexOf("#");
    if (hashIdx < 0) {
      const notes = app.vault.getMarkdownFiles();
      const matchedNotes = [];
      const lowerCaseInputStr = inputStr.toLowerCase();
      notes.forEach((file) => {
        if (file.path.toLowerCase().contains(lowerCaseInputStr)) {
          matchedNotes.push({ type: "File", file, heading: "" });
        }
      });
      return matchedNotes;
    } else {
      const metadata = app.metadataCache.getCache(this.selectedFile.path);
      if (metadata && metadata.headings) {
        const matchedNotes = [];
        const headingSearch = normalizedInput.substring(hashIdx + 1);
        metadata.headings.forEach((h) => {
          if (h.heading.contains(headingSearch)) {
            matchedNotes.push({ type: "Heading", file: this.selectedFile, heading: h.heading });
          }
        });
        return matchedNotes;
      }
      return [];
    }
  }
  renderSuggestion(suggestion, el) {
    switch (suggestion.type) {
      case "File":
        {
          const div = el.createDiv();
          const dotIdx = suggestion.file.name.lastIndexOf(".");
          const noteTitle = dotIdx > 0 ? suggestion.file.name.substring(0, dotIdx) : suggestion.file.name;
          div.createDiv().setText(noteTitle);
          if (suggestion.file.parent && !suggestion.file.parent.isRoot()) {
            div.createDiv({ text: suggestion.file.parent.path, cls: "note-path" });
          }
        }
        ;
        break;
      case "Heading": {
        const div = el.createDiv();
        div.createDiv().setText(suggestion.heading);
      }
    }
  }
  selectSuggestion(suggestion) {
    if (suggestion.type == "File") {
      this.selectedFile = suggestion.file;
      this.inputEl.value = suggestion.file.path;
    } else {
      this.inputEl.value = suggestion.file.path + "#" + suggestion.heading;
    }
    this.inputEl.trigger("input");
    this.close();
  }
};

// ui/ReplaceLinkModal.ts
var ReplaceLinkModal = class extends import_obsidian3.Modal {
  constructor(app2, onSubmit) {
    super(app2);
    this.onSubmit = onSubmit;
  }
  onOpen() {
    const { contentEl } = this;
    contentEl.createEl("h2", { text: "Replace link with" });
    new import_obsidian3.Setting(contentEl).then((setting) => {
      setting.addSearch((search) => {
        new FolderSuggest(search.inputEl);
        search.setPlaceholder("search for note").setValue("").onChange(async (value) => {
          this.notePath = value;
        });
      });
      if (setting.controlEl.lastChild) {
        setting.nameEl.appendChild(setting.controlEl.lastChild);
      }
    });
    new import_obsidian3.Setting(contentEl).addButton((btn) => btn.setButtonText("Replace").setCta().onClick(() => {
      this.close();
      if (this.notePath) {
        this.onSubmit(this.notePath);
      }
    }));
  }
  onClose() {
    const { contentEl } = this;
    contentEl.empty();
  }
};

// IVault.ts
var VaultConfiguration = class {
  constructor(vault) {
    this.vault = vault;
  }
  get useMarkdownLinks() {
    return this.vault.getConfig("useMarkdownLinks");
  }
  get newLinkFormat() {
    return this.vault.getConfig("newLinkFormat");
  }
};

// Vault.ts
var import_obsidian4 = require("obsidian");
var VaultImp = class {
  constructor(app2) {
    this.app = app2;
    this.configuration = new VaultConfiguration(this);
  }
  getFilesInFolder(folder) {
    let folders = [];
    let files = [];
    folders.push(folder);
    while (folders.length > 0) {
      const currentFolder = folders.pop();
      if (!currentFolder) {
        break;
      }
      for (let child of currentFolder.children) {
        if (child instanceof import_obsidian4.TFolder) {
          folders.push(child);
        } else {
          files.push(child);
        }
      }
    }
    return files;
  }
  read(file) {
    return app.vault.read(file);
  }
  modify(file, data, options) {
    return app.vault.modify(file, data, options);
  }
  rename(normalizedPath, normalizedNewPath) {
    return this.app.vault.adapter.rename(normalizedPath, normalizedNewPath);
  }
  createFolder(path) {
    return this.app.vault.createFolder(path);
  }
  getActiveNoteView() {
    return app.workspace.getActiveViewOfType(import_obsidian4.MarkdownView);
  }
  getName() {
    return app.vault.getName();
  }
  exists(path, caseSensitive) {
    return this.app.vault.adapter.exists(path, caseSensitive);
  }
  createNote(path, content) {
    return this.app.vault.create(path, content);
  }
  getBacklinksForFileByPath(file) {
    const _file = typeof file === "string" ? this.app.vault.getAbstractFileByPath(file) : file;
    if (_file) {
      const backlinks = this.app.metadataCache.getBacklinksForFile(_file).data;
      if (backlinks === null) {
        return null;
      }
      const backlinksLinkData = {};
      for (const [sourceFile, linkCaches] of backlinks) {
        const linkDataArray = new Array();
        for (const linkCache of linkCaches) {
          const linkData = LinkData.parse(linkCache.original);
          if (linkData) {
            linkData.position = new Position(linkCache.position.start.offset, linkCache.position.end.offset);
            linkDataArray.push(linkData);
          }
        }
        backlinksLinkData[sourceFile] = linkDataArray;
      }
      return backlinksLinkData;
    }
    return null;
  }
  getRoot() {
    return this.app.vault.getRoot();
  }
  delete(file, force) {
    return this.app.vault.delete(file, force);
  }
  trash(file, system) {
    return this.app.vault.trash(file, system);
  }
  getAbstractFileByPath(path) {
    return this.app.vault.getAbstractFileByPath(path);
  }
  getConfig(setting) {
    return this.app.vault.getConfig(setting);
  }
  getFiles() {
    return this.app.vault.getFiles();
  }
  getMarkdownFiles() {
    return this.app.vault.getMarkdownFiles();
  }
};

// commands/ObsidianProxy.ts
var import_obsidian5 = require("obsidian");
var ObsidianProxy = class {
  constructor(app2, linkTextSuggestContext, settings, uiFactory) {
    this.app = app2;
    this.linkTextSuggestContext = linkTextSuggestContext;
    this.settings = settings;
    this.Vault = new VaultImp(app2);
    this.uiFactory = uiFactory;
  }
  createNotice(message, timeout) {
    return new import_obsidian5.Notice(message, timeout);
  }
  requestUrl(request2) {
    return (0, import_obsidian5.requestUrl)(request2);
  }
  request(req) {
    return (0, import_obsidian5.request)(req);
  }
  clipboardWriteText(text) {
    navigator.clipboard.writeText(text);
  }
  clipboardReadText() {
    return navigator.clipboard.readText();
  }
  linkTextSuggestContextSetLinkData(linkData, titles) {
    this.linkTextSuggestContext.setLinkData(linkData, titles);
  }
  showPromptModal(title, text, buttons, onSubmit) {
    this.uiFactory.createPromptModal(title, text, buttons, onSubmit).open();
  }
  createLink(sourcePath, destination, destinationSubPath, text, dimensions) {
    const useMarkdownLinks = this.Vault.configuration.useMarkdownLinks;
    return useMarkdownLinks ? createMarkdownLink(sourcePath, destination, destinationSubPath, text, dimensions, "none" /* None */) : createWikiLink(sourcePath, destination, destinationSubPath, text, dimensions, "none" /* None */);
  }
  getFileCache(file) {
    return this.app.metadataCache.getFileCache(file);
  }
  getBlock(editor, file) {
    const cursor = editor.getCursor("from");
    const fileCache = this.getFileCache(file);
    let block = ((fileCache == null ? void 0 : fileCache.sections) || []).find((section) => {
      return section.position.start.line <= cursor.line && section.position.end.line >= cursor.line;
    });
    if ((block == null ? void 0 : block.type) === "list") {
      block = ((fileCache == null ? void 0 : fileCache.listItems) || []).find((item) => {
        return item.position.start.line <= cursor.line && item.position.end.line >= cursor.line;
      });
    } else if ((block == null ? void 0 : block.type) === "heading") {
      block = ((fileCache == null ? void 0 : fileCache.headings) || []).find((heading) => {
        return heading.position.start.line === cursor.line;
      });
    }
    return block;
  }
};

// settings.ts
var DEFAULT_SETTINGS = {
  linkReplacements: [],
  titleSeparator: " \u2022 ",
  showPerformanceNotification: false,
  //TODO: remove
  removeLinksFromHeadingsInternalWikilinkWithoutTextReplacement: "Destination",
  deleteUnreferencedLinkTarget: true,
  removeLinksFromHeadingsInternalWikilinkWithoutTextAction: "None" /* None */,
  onConvertToMdlinkAppendMdExtension: false,
  autoselectWordOnCreateLink: true,
  skipFrontmatterInNoteWideCommands: true,
  //feature flags
  ffReplaceLink: false,
  ffExtractSection: false,
  ffWrapNoteInFolder: false,
  ffConvertLinksInFolder: false,
  ffObsidianUrlSupport: false,
  ffHighlightBrokenLinks: false,
  ffSetLinkDestinationFromClipbard: false,
  ffSkipFrontmatterInNoteWideCommands: false,
  ffCopyLinkToObject: false,
  //context menu
  //TODO: fix typo
  contexMenu: {
    editLinkText: true,
    setLinkText: true,
    setLinkTextFromClipboard: true,
    editLinkDestination: true,
    setLinkDestinationFromClipboard: true,
    copyLinkDestination: true,
    unlink: true,
    convertToWikilink: true,
    convertToAutolink: true,
    convertToMakrdownLink: true,
    convertToHtmlLink: false,
    replaceLink: true,
    embedUnembedLink: true,
    deleteLink: true,
    createLink: true,
    createLinkFromClipboard: true,
    convertAllLinksToMdLinks: false,
    convertWikilinkToMdLinks: false,
    convertUrlsToMdlinks: false,
    convertAutolinksToMdlinks: false,
    convertHtmllinksToMdlinks: false,
    extractSection: false,
    wrapNoteInFolder: false,
    copyLinkToClipboard: true,
    copyLinkToHeadingToClipboard: true,
    copyLinkToBlockToClipboard: false,
    cutLinkToClipboard: true
  }
};

// ObsidianLinksSettingTab.ts
var import_obsidian6 = require("obsidian");
var ObsidianLinksSettingTab = class extends import_obsidian6.PluginSettingTab {
  constructor(app2, plugin) {
    super(app2, plugin);
    this.plugin = plugin;
    this.repoUrl = "https://github.com/mii-key/obsidian-links";
  }
  getFullDocUrl(fragment) {
    return this.repoUrl + "?tab=readme-ov-file#" + fragment;
  }
  getFullInsiderDocUrl(filename) {
    return this.repoUrl + "/blob/master/docs/insider/" + filename;
  }
  setSettingHelpLink(setting, helpUrl) {
    const nameEl = setting.settingEl.querySelector(".setting-item-name");
    if (!nameEl) {
      return;
    }
    this.setElementHelpLink(nameEl, helpUrl);
  }
  setElementHelpLink(element, helpUrl) {
    if (!element) {
      return;
    }
    const linkEl = createEl("a", {
      href: helpUrl
    });
    const iconEl = element.createSpan();
    iconEl.addClass("settings-help-icon");
    (0, import_obsidian6.setIcon)(iconEl, "circle-help");
    linkEl.appendChild(iconEl);
    element.appendChild(linkEl);
  }
  display() {
    const { containerEl } = this;
    containerEl.empty();
    containerEl.createEl("h3", { text: "Command settings" });
    const generalHeading = containerEl.createEl("h4", { text: "General" });
    new import_obsidian6.Setting(containerEl).setName("Autoselect upon creating a link").setDesc("Autoselect a word under the cursor when creating a link.").addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.autoselectWordOnCreateLink).onChange(async (value) => {
        this.plugin.settings.autoselectWordOnCreateLink = value;
        await this.plugin.saveSettings();
      });
    });
    const skipFrontmatterInNoteWideCommandsSetting = new import_obsidian6.Setting(containerEl).setName("Skip Frontmatter").setDesc("Skip Frontmatter in note wide commands.").addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.skipFrontmatterInNoteWideCommands).onChange(async (value) => {
        this.plugin.settings.skipFrontmatterInNoteWideCommands = value;
        await this.plugin.saveSettings();
      });
    });
    const toggleskipFrontmatterInNoteWideCommandsSetting = (enabled) => {
      if (enabled) {
        skipFrontmatterInNoteWideCommandsSetting.settingEl.show();
        generalHeading.show();
      } else {
        skipFrontmatterInNoteWideCommandsSetting.settingEl.hide();
        generalHeading.hide();
      }
    };
    toggleskipFrontmatterInNoteWideCommandsSetting(this.plugin.settings.ffSkipFrontmatterInNoteWideCommands);
    const setListTextEl = containerEl.createEl("h4", { text: "Set link text" });
    this.setElementHelpLink(setListTextEl, this.getFullDocUrl("set-link-text"));
    new import_obsidian6.Setting(containerEl).setName("Title separator").setDesc("String used as headings separator in 'Set link text' command.").addText((text) => text.setValue(this.plugin.settings.titleSeparator).onChange(async (value) => {
      this.plugin.settings.titleSeparator = value;
      await this.plugin.saveSettings();
    }));
    containerEl.createEl("h4", { text: "Remove links from headings" });
    new import_obsidian6.Setting(containerEl).setName("Internal wikilink without text").addDropdown((dropDown) => dropDown.addOptions({
      Delete: "Remove",
      ReplaceWithDestination: "Replace with destination",
      ReplaceWithLowestNoteHeading: "Replace with lowest heading"
    }).setValue(InternalWikilinkWithoutTextAction[this.plugin.settings.removeLinksFromHeadingsInternalWikilinkWithoutTextAction]).onChange(async (value) => {
      this.plugin.settings.removeLinksFromHeadingsInternalWikilinkWithoutTextAction = value;
      await this.plugin.saveSettings();
    }));
    containerEl.createEl("h4", { text: "Delete link" });
    new import_obsidian6.Setting(containerEl).setName("Delete unreferenced link target").addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.deleteUnreferencedLinkTarget).onChange(async (value) => {
        this.plugin.settings.deleteUnreferencedLinkTarget = value;
        await this.plugin.saveSettings();
      });
    });
    containerEl.createEl("h4", { text: "Convert to Markdown link" });
    new import_obsidian6.Setting(containerEl).setName("Append .md extension").setDesc("").setClass("setting-item-append-mdextension").addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.onConvertToMdlinkAppendMdExtension).onChange(async (value) => {
        this.plugin.settings.onConvertToMdlinkAppendMdExtension = value;
        await this.plugin.saveSettings();
      });
    });
    const appendMdExtensionDescription = containerEl.querySelector(".setting-item-append-mdextension .setting-item-description");
    if (appendMdExtensionDescription) {
      appendMdExtensionDescription.appendText(" see ");
      appendMdExtensionDescription.appendChild(
        createEl("a", {
          href: "https://github.com/mii-key/obsidian-links?tab=readme-ov-file#convert-wikilink-or-html-link-to-markdown-link",
          text: "docs"
        })
      );
      appendMdExtensionDescription.appendText(".");
      containerEl.createEl("h3", { text: "Context menu" });
      new import_obsidian6.Setting(containerEl).setName("Edit link text").setDesc("").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.contexMenu.editLinkText).onChange(async (value) => {
          this.plugin.settings.contexMenu.editLinkText = value;
          await this.plugin.saveSettings();
        });
      });
      new import_obsidian6.Setting(containerEl).setName("Set link text").setDesc("").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.contexMenu.setLinkText).onChange(async (value) => {
          this.plugin.settings.contexMenu.setLinkText = value;
          await this.plugin.saveSettings();
        });
      });
      new import_obsidian6.Setting(containerEl).setName("Set link text from clipboard").setDesc("").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.contexMenu.setLinkTextFromClipboard).onChange(async (value) => {
          this.plugin.settings.contexMenu.setLinkTextFromClipboard = value;
          await this.plugin.saveSettings();
        });
      });
      new import_obsidian6.Setting(containerEl).setName("Edit link destination").setDesc("").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.contexMenu.editLinkDestination).onChange(async (value) => {
          this.plugin.settings.contexMenu.editLinkDestination = value;
          await this.plugin.saveSettings();
        });
      });
      const setLinkDestinationFromClipboardContextMenuSetting = new import_obsidian6.Setting(containerEl).setName("Set link destination from clipboard").setDesc("").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.contexMenu.setLinkDestinationFromClipboard).onChange(async (value) => {
          this.plugin.settings.contexMenu.setLinkDestinationFromClipboard = value;
          await this.plugin.saveSettings();
        });
      });
      const toggleSetLinkDestinationFromClipboardContextMenuSetting = (enabled) => {
        if (enabled) {
          setLinkDestinationFromClipboardContextMenuSetting.settingEl.show();
        } else {
          setLinkDestinationFromClipboardContextMenuSetting.settingEl.hide();
        }
      };
      toggleSetLinkDestinationFromClipboardContextMenuSetting(this.plugin.settings.ffSetLinkDestinationFromClipbard);
      new import_obsidian6.Setting(containerEl).setName("Copy link").setDesc("").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.contexMenu.copyLinkToClipboard).onChange(async (value) => {
          this.plugin.settings.contexMenu.copyLinkToClipboard = value;
          await this.plugin.saveSettings();
        });
      });
      new import_obsidian6.Setting(containerEl).setName("Cut link").setDesc("").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.contexMenu.cutLinkToClipboard).onChange(async (value) => {
          this.plugin.settings.contexMenu.cutLinkToClipboard = value;
          await this.plugin.saveSettings();
        });
      });
      new import_obsidian6.Setting(containerEl).setName("Copy link destination").setDesc("").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.contexMenu.copyLinkDestination).onChange(async (value) => {
          this.plugin.settings.contexMenu.copyLinkDestination = value;
          await this.plugin.saveSettings();
        });
      });
      const settingCopyLinkToObjectContextMenu = new import_obsidian6.Setting(containerEl).setName("Copy link to element").setDesc("").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.contexMenu.copyLinkToHeadingToClipboard).onChange(async (value) => {
          this.plugin.settings.contexMenu.copyLinkToHeadingToClipboard = value;
          await this.plugin.saveSettings();
        });
      });
      const toggleCopyLinkToObjectContextMenuSetting = (enabled) => {
        if (enabled) {
          settingCopyLinkToObjectContextMenu.settingEl.show();
        } else {
          settingCopyLinkToObjectContextMenu.settingEl.hide();
        }
      };
      toggleCopyLinkToObjectContextMenuSetting(this.plugin.settings.ffCopyLinkToObject);
      new import_obsidian6.Setting(containerEl).setName("Unlink").setDesc("").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.contexMenu.unlink).onChange(async (value) => {
          this.plugin.settings.contexMenu.unlink = value;
          await this.plugin.saveSettings();
        });
      });
      new import_obsidian6.Setting(containerEl).setName("Convert to wikilink").setDesc("").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.contexMenu.convertToWikilink).onChange(async (value) => {
          this.plugin.settings.contexMenu.convertToWikilink = value;
          await this.plugin.saveSettings();
        });
      });
      new import_obsidian6.Setting(containerEl).setName("Convert to autolink").setDesc("").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.contexMenu.convertToAutolink).onChange(async (value) => {
          this.plugin.settings.contexMenu.convertToAutolink = value;
          await this.plugin.saveSettings();
        });
      });
      new import_obsidian6.Setting(containerEl).setName("Convert to markdown link").setDesc("").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.contexMenu.convertToMakrdownLink).onChange(async (value) => {
          this.plugin.settings.contexMenu.convertToMakrdownLink = value;
          await this.plugin.saveSettings();
        });
      });
      new import_obsidian6.Setting(containerEl).setName("Convert to HTML link").setDesc("").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.contexMenu.convertToHtmlLink).onChange(async (value) => {
          this.plugin.settings.contexMenu.convertToHtmlLink = value;
          await this.plugin.saveSettings();
        });
      });
      if (this.plugin.settings.ffReplaceLink) {
        new import_obsidian6.Setting(containerEl).setName("Replace link").setDesc("").addToggle((toggle) => {
          toggle.setValue(this.plugin.settings.contexMenu.replaceLink).onChange(async (value) => {
            this.plugin.settings.contexMenu.replaceLink = value;
            await this.plugin.saveSettings();
          });
        });
      }
      new import_obsidian6.Setting(containerEl).setName("Embed/Unembed").setDesc("").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.contexMenu.embedUnembedLink).onChange(async (value) => {
          this.plugin.settings.contexMenu.embedUnembedLink = value;
          await this.plugin.saveSettings();
        });
      });
      new import_obsidian6.Setting(containerEl).setName("Delete").setDesc("").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.contexMenu.deleteLink).onChange(async (value) => {
          this.plugin.settings.contexMenu.deleteLink = value;
          await this.plugin.saveSettings();
        });
      });
      new import_obsidian6.Setting(containerEl).setName("Create link").setDesc("").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.contexMenu.createLink).onChange(async (value) => {
          this.plugin.settings.contexMenu.createLink = value;
          await this.plugin.saveSettings();
        });
      });
      let settings1 = new import_obsidian6.Setting(containerEl).setName("Create link from clipboard").setDesc("").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.contexMenu.createLinkFromClipboard).onChange(async (value) => {
          this.plugin.settings.contexMenu.createLinkFromClipboard = value;
          await this.plugin.saveSettings();
        });
      });
      const convertAllToMdLinksSettings = new import_obsidian6.Setting(containerEl).setName("Convert all links to Markdown links").setDesc("").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.contexMenu.convertAllLinksToMdLinks).onChange(async (value) => {
          this.plugin.settings.contexMenu.convertAllLinksToMdLinks = value;
          await this.plugin.saveSettings();
        });
      });
      const convertWikilinksToMdLinksSettings = new import_obsidian6.Setting(containerEl).setName("Convert Wikilinks to Markdown links").setDesc("").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.contexMenu.convertWikilinkToMdLinks).onChange(async (value) => {
          this.plugin.settings.contexMenu.convertWikilinkToMdLinks = value;
          await this.plugin.saveSettings();
        });
      });
      const convertUrlsToMdLinksSettings = new import_obsidian6.Setting(containerEl).setName("Convert URLs to Markdown links").setDesc("").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.contexMenu.convertUrlsToMdlinks).onChange(async (value) => {
          this.plugin.settings.contexMenu.convertUrlsToMdlinks = value;
          await this.plugin.saveSettings();
        });
      });
      const convertAutolinksToMdLinksSettings = new import_obsidian6.Setting(containerEl).setName("Convert Autolinks to Markdown links").setDesc("").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.contexMenu.convertAutolinksToMdlinks).onChange(async (value) => {
          this.plugin.settings.contexMenu.convertAutolinksToMdlinks = value;
          await this.plugin.saveSettings();
        });
      });
      const convertHtmllinksToMdLinksSettings = new import_obsidian6.Setting(containerEl).setName("Convert HTML links to Markdown links").setDesc("").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.contexMenu.convertHtmllinksToMdlinks).onChange(async (value) => {
          this.plugin.settings.contexMenu.convertHtmllinksToMdlinks = value;
          await this.plugin.saveSettings();
        });
      });
      const extractSectionSettings = new import_obsidian6.Setting(containerEl).setName("Extract section").setDesc("").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.contexMenu.extractSection).onChange(async (value) => {
          this.plugin.settings.contexMenu.extractSection = value;
          await this.plugin.saveSettings();
        });
      });
      const toggleExtractSection = (enabled) => {
        if (enabled) {
          extractSectionSettings.settingEl.show();
        } else {
          extractSectionSettings.settingEl.hide();
        }
      };
      toggleExtractSection(this.plugin.settings.ffExtractSection);
      containerEl.createEl("h3", { text: "Early access features" });
      const earlyAccessDescription = containerEl.createEl("p");
      earlyAccessDescription.createEl("span", {
        text: "Almost finished features with some "
      });
      earlyAccessDescription.createEl("a", {
        href: "https://github.com/mii-key/obsidian-links/issues",
        text: "bugs"
      });
      earlyAccessDescription.createEl("span", {
        text: " to be fixed."
      });
      containerEl.createEl("h3", { text: "Insider features" });
      const insiderDescription = containerEl.createEl("p");
      insiderDescription.createEl("span", {
        text: "Incomplete features currently under development. Enable these features to "
      });
      insiderDescription.createEl("a", {
        href: "https://github.com/mii-key/obsidian-links/issues",
        text: "provide your input"
      });
      insiderDescription.createEl("span", {
        text: " and influence the direction of development."
      });
      const settingConvertLinksInFolder = new import_obsidian6.Setting(containerEl).setName("Convert links in folder").setDesc("Convert links in a folder").setClass("setting-item--feature-convert-links-in-folder").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.ffConvertLinksInFolder).onChange(async (value) => {
          this.plugin.settings.ffConvertLinksInFolder = value;
          await this.plugin.saveSettings();
        });
      });
      this.setSettingHelpLink(settingConvertLinksInFolder, this.getFullInsiderDocUrl("convert-links-in-folder.md"));
      new import_obsidian6.Setting(containerEl).setName("Obsidian URL support").setDesc("Add support for Obsidian URL").setClass("setting-item-featureObsidianUrl").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.ffObsidianUrlSupport).onChange(async (value) => {
          this.plugin.settings.ffObsidianUrlSupport = value;
          await this.plugin.saveSettings();
        });
      });
      const featureObsidianUrlSettingDesc = containerEl.querySelector(".setting-item-featureObsidianUrl .setting-item-description");
      if (featureObsidianUrlSettingDesc) {
        featureObsidianUrlSettingDesc.appendText(" see ");
        featureObsidianUrlSettingDesc.appendChild(
          createEl("a", {
            href: "https://github.com/mii-key/obsidian-links/blob/master/docs/insider/obsidian-url.md",
            text: "docs"
          })
        );
        featureObsidianUrlSettingDesc.appendText(".");
      }
      new import_obsidian6.Setting(containerEl).setName("Skip Frontmatter").setDesc("Skip Frontmatter in note wide commands").setClass("setting-item-skip-frontmatter-notewide").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.ffSkipFrontmatterInNoteWideCommands).onChange(async (value) => {
          this.plugin.settings.ffSkipFrontmatterInNoteWideCommands = value;
          await this.plugin.saveSettings();
          toggleskipFrontmatterInNoteWideCommandsSetting(value);
        });
      });
      const ffSkipFrontmatterSettingDesc = containerEl.querySelector(".setting-item-skip-frontmatter-notewide .setting-item-description");
      if (ffSkipFrontmatterSettingDesc) {
        ffSkipFrontmatterSettingDesc.appendText(" see ");
        ffSkipFrontmatterSettingDesc.appendChild(
          createEl("a", {
            href: "https://github.com/mii-key/obsidian-links/blob/master/docs/insider/skip-frontmatter.md",
            text: "docs "
          })
        );
        ffSkipFrontmatterSettingDesc.appendText(".");
      }
      new import_obsidian6.Setting(containerEl).setName("Copy link to element").setDesc("Copy link to a heading or a block to the clipboard. ").setClass("setting-item-copy-link-to-object").addToggle((toggle) => {
        toggle.setValue(this.plugin.settings.ffCopyLinkToObject).onChange(async (value) => {
          this.plugin.settings.ffCopyLinkToObject = value;
          toggleCopyLinkToObjectContextMenuSetting(value);
          await this.plugin.saveSettings();
        });
      });
      const feature1SettingDesc = containerEl.querySelector(".setting-item-copy-link-to-object .setting-item-description");
      if (feature1SettingDesc) {
        feature1SettingDesc.appendText(" see ");
        feature1SettingDesc.appendChild(
          createEl("a", {
            href: "https://github.com/mii-key/obsidian-links/blob/master/docs/insider/copy-link-to-element.md",
            text: "docs"
          })
        );
        feature1SettingDesc.appendText(".");
      }
    }
  }
};

// commands/ICommand.ts
var CommandBase = class {
  constructor(isPresentInContextMenu = () => true, isEnabled = () => true) {
    this.isPresentInContextMenu = isPresentInContextMenu;
    this.isEnabled = isEnabled;
  }
};

// commands/UnlinkLinkCommand.ts
var UnlinkLinkCommand = class extends CommandBase {
  constructor(isPresentInContextMenu = () => true, isEnabled = () => true) {
    super(isPresentInContextMenu, isEnabled);
    this.id = "editor-unlink-link";
    this.displayNameCommand = "Unlink";
    this.displayNameContextMenu = "Unlink";
    this.icon = "unlink";
  }
  handler(editor, checking) {
    if (checking && !this.isEnabled()) {
      return false;
    }
    const selection = editor.getSelection();
    if (selection) {
      if (checking) {
        return HasLinks(selection);
      }
      this.removeLinksFromSelection(editor, selection);
    } else {
      const text = editor.getValue();
      const cursorOffset = editor.posToOffset(editor.getCursor("from"));
      const links = findLinks(text, 2 /* Wiki */ | 4 /* Html */ | 1 /* Markdown */, cursorOffset, cursorOffset);
      if (checking) {
        return links.length > 0;
      }
      if (links) {
        this.unlinkLink(links[0], editor);
      }
    }
  }
  removeLinksFromSelection(editor, selection) {
    const unlinkedText = removeLinks(selection);
    editor.replaceSelection(unlinkedText);
  }
  unlinkLink(linkData, editor) {
    let text = linkData.text ? linkData.text.content : "";
    if (linkData.type === 2 /* Wiki */ && !text) {
      text = linkData.destination ? linkData.destination.content : "";
    }
    editor.replaceRange(
      text,
      editor.offsetToPos(linkData.position.start),
      editor.offsetToPos(linkData.position.end)
    );
  }
};

// commands/DeleteLinkCommand.ts
var import_parse_filepath2 = __toESM(require_parse_filepath());

// ui/PromotModal.common.ts
var ButtonInfo = class {
  constructor(text, result, isCta = false, isWarning = false) {
    this.text = text;
    this.result = result;
    this.isCta = isCta;
    this.isWarning = isWarning;
  }
};

// commands/DeleteLinkCommand.ts
var DeleteLinkCommand = class extends CommandBase {
  constructor(obsidianProxy, isPresentInContextMenu = () => true, isEnabled = () => true) {
    super(isPresentInContextMenu, isEnabled);
    this.obsidianProxy = obsidianProxy;
    this.isPresentInContextMenu = () => this.obsidianProxy.settings.contexMenu.deleteLink;
    this.id = "editor-delete-link";
    this.displayNameCommand = "Delete link";
    this.displayNameContextMenu = "Delete";
    this.icon = "trash-2";
  }
  handler(editor, checking) {
    if (checking && !this.isEnabled()) {
      return false;
    }
    const selection = editor.getSelection();
    const text = editor.getValue();
    const cursorOffsetStart = editor.posToOffset(editor.getCursor("from"));
    const cursorOffsetEnd = editor.posToOffset(editor.getCursor("to"));
    const links = findLinks(text, 65535 /* All */, cursorOffsetStart, cursorOffsetEnd);
    if (checking) {
      console.log("'Delete link' command: Availability check.");
      if (selection) {
        console.log("'Delete link' command: Selected text is not supported.");
        return false;
      }
      if ((links == null ? void 0 : links.length) == 0) {
        console.log("'Delete link' command: No links found.");
        return false;
      }
      if ((links == null ? void 0 : links.length) > 1) {
        console.log("'Delete link' command: Multiple links are not supported.");
        return false;
      }
      console.log("'Delete link' command: available.");
      return (links == null ? void 0 : links.length) == 1;
    }
    if ((links == null ? void 0 : links.length) == 1) {
      this.deleteLink(links[0], editor);
    }
  }
  deleteLink(linkData, editor) {
    var _a;
    const destination = (_a = linkData.destination) == null ? void 0 : _a.content;
    try {
      if (this.obsidianProxy.settings.deleteUnreferencedLinkTarget && destination !== void 0 && !isAbsoluteUri(destination) && !isAbsoluteFilePath(destination)) {
        const hashIdx = destination.indexOf("#");
        if (hashIdx == 0) {
          return;
        }
        let filePath = hashIdx > 0 ? destination.substring(0, hashIdx) : destination;
        let file = this.obsidianProxy.Vault.getAbstractFileByPath(filePath);
        if (!file) {
          const path = (0, import_parse_filepath2.default)(filePath);
          let pathExt = path.ext;
          if (pathExt === "") {
            pathExt = ".md";
            filePath += pathExt;
            file = this.obsidianProxy.Vault.getAbstractFileByPath(filePath);
          }
          if (!file) {
            const vaultFiles = pathExt === ".md" ? this.obsidianProxy.Vault.getMarkdownFiles() : this.obsidianProxy.Vault.getFiles();
            const targetFile = vaultFiles.find((x) => x.path.endsWith(filePath));
            if (targetFile) {
              file = targetFile;
            }
          }
        }
        if (!file) {
          return;
        }
        const cache = this.obsidianProxy.Vault.getBacklinksForFileByPath(file);
        if (!cache) {
          return;
        }
        const backlinks = Object.keys(cache);
        if (backlinks.length != 1 || backlinks.length === 1 && cache[backlinks[0]].length > 1) {
          return;
        }
        this.obsidianProxy.showPromptModal(
          "Delete file",
          [
            `The file "${filePath}" is no longer referenced by any note.`,
            "Do you want to delete it?"
          ],
          [
            new ButtonInfo("Yes", "Yes", true, true),
            new ButtonInfo("No", "No")
          ],
          (result) => {
            if (result === "Yes") {
              this.obsidianProxy.Vault.delete(file);
            }
          }
        );
      }
    } finally {
      editor.replaceRange(
        "",
        editor.offsetToPos(linkData.position.start),
        editor.offsetToPos(linkData.position.end)
      );
    }
  }
};

// commands/ConvertToMdlinkCommandBase.ts
var ConvertToMdlinkCommandBase = class extends CommandBase {
  constructor(obsidianProxy, isPresentInContextMenu = () => true, isEnabled = () => true) {
    super(isPresentInContextMenu, isEnabled);
    this.EmailScheme = "mailto:";
    this.obsidianProxy = obsidianProxy;
  }
  //TODO: replace with convertLinkToMarkdownLink1
  async convertLinkToMarkdownLink(linkData, editor, setCursor = true, linkOffset = 0) {
    let text = linkData.text ? linkData.text.content : "";
    let destination = linkData.destination ? linkData.destination.content : "";
    if (linkData.type === 2 /* Wiki */ && !text) {
      text = destination;
    }
    const urlRegEx = /^(http|https):\/\/[^ "]+$/i;
    if ((linkData.type === 8 /* Autolink */ || linkData.type === 16 /* PlainUrl */) && linkData.destination && urlRegEx.test(linkData.destination.content)) {
      const notice = this.obsidianProxy.createNotice("Getting title ...", 0);
      try {
        text = await getPageTitle(new URL(linkData.destination.content), this.getPageText.bind(this));
      } catch (error) {
        this.obsidianProxy.createNotice(error);
      } finally {
        notice.hide();
      }
    }
    let rawLinkText = "";
    if (linkData.type === 8 /* Autolink */ && linkData.destination && RegExPatterns.Email.test(linkData.destination.content)) {
      rawLinkText = `[${text}](${this.EmailScheme}${linkData.destination.content})`;
    } else {
      if (this.obsidianProxy.settings.onConvertToMdlinkAppendMdExtension && linkData.type == 2 /* Wiki */ && !isSectionLink(destination) && !isAbsoluteUri(destination) && !isAbsoluteFilePath(destination)) {
        const extRegEx = /(.*?)(\.([^*"\/\<>:|\?]*?))?(#.*)?$/;
        const match = extRegEx.exec(destination);
        if (match) {
          const [, pathWithName, , ext, hash3] = match;
          if (!ext) {
            destination = `${pathWithName}.md${hash3 ? hash3 : ""}`;
          }
        }
      }
      destination = encodeURI(destination);
      if (destination && linkData.type === 2 /* Wiki */ && destination.indexOf("%20") > 0) {
        destination = `<${destination.replace(/%20/g, " ")}>`;
      }
      const embededSymbol = linkData.embedded ? "!" : "";
      rawLinkText = `${embededSymbol}[${text}](${destination})`;
    }
    editor.replaceRange(
      rawLinkText,
      editor.offsetToPos(linkOffset + linkData.position.start),
      editor.offsetToPos(linkOffset + linkData.position.end)
    );
    if (setCursor) {
      if (text) {
        editor.setCursor(editor.offsetToPos(linkData.position.start + rawLinkText.length));
      } else {
        editor.setCursor(editor.offsetToPos(linkData.position.start + 1));
      }
    }
  }
  //TODO: refactor
  async convertLinkToMarkdownLink1(linkData, textBuffer, setCursor = true, linkOffset = 0) {
    let text = linkData.text ? linkData.text.content : "";
    let destination = linkData.destination ? linkData.destination.content : "";
    if (linkData.type === 2 /* Wiki */ && !text) {
      text = destination;
    }
    const urlRegEx = /^(http|https):\/\/[^ "]+$/i;
    if ((linkData.type === 8 /* Autolink */ || linkData.type === 16 /* PlainUrl */) && linkData.destination && urlRegEx.test(linkData.destination.content)) {
      const notice = this.obsidianProxy.createNotice("Getting title ...", 0);
      try {
        text = await getPageTitle(new URL(linkData.destination.content), this.getPageText.bind(this));
      } catch (error) {
        this.obsidianProxy.createNotice(error);
      } finally {
        notice.hide();
      }
    }
    let rawLinkText = "";
    if (linkData.type === 8 /* Autolink */ && linkData.destination && RegExPatterns.Email.test(linkData.destination.content)) {
      rawLinkText = `[${text}](${this.EmailScheme}${linkData.destination.content})`;
    } else {
      if (this.obsidianProxy.settings.onConvertToMdlinkAppendMdExtension && linkData.type == 2 /* Wiki */ && !isSectionLink(destination) && !isAbsoluteUri(destination) && !isAbsoluteFilePath(destination)) {
        const extRegEx = /(.*?)(\.([^*"\/\<>:|\?]*?))?(#.*)?$/;
        const match = extRegEx.exec(destination);
        if (match) {
          const [, pathWithName, , ext, hash3] = match;
          if (!ext) {
            destination = `${pathWithName}.md${hash3 ? hash3 : ""}`;
          }
        }
      }
      destination = encodeURI(destination);
      if (destination && linkData.type === 2 /* Wiki */ && destination.indexOf("%20") > 0) {
        destination = `<${destination.replace(/%20/g, " ")}>`;
      }
      const embededSymbol = linkData.embedded ? "!" : "";
      rawLinkText = `${embededSymbol}[${text}](${destination})`;
    }
    textBuffer.replaceRange(
      rawLinkText,
      linkOffset + linkData.position.start,
      linkOffset + linkData.position.end
    );
    if (setCursor) {
      if (text) {
        textBuffer.setPosition(linkData.position.start + rawLinkText.length);
      } else {
        textBuffer.setPosition(linkData.position.start + 1);
      }
    }
  }
  async getPageText(url) {
    const response = await this.obsidianProxy.requestUrl({ url: url.toString() });
    if (response.status !== 200) {
      throw new Error(`Failed to request '${url}': ${response.status}`);
    }
    return response.text;
  }
};

// commands/ConvertLinkToMdlinkCommand.ts
var ConvertLinkToMdlinkCommand = class extends ConvertToMdlinkCommandBase {
  constructor(obsidianProxy, isPresentInContextMenu = () => true, isEnabled = () => true, callback = void 0) {
    super(obsidianProxy, isPresentInContextMenu, isEnabled);
    this.id = "editor-convert-link-to-mdlink";
    this.displayNameCommand = "Convert to Markdown link";
    this.displayNameContextMenu = "Convert to Markdown link";
    this.icon = "rotate-cw";
    this.callback = callback;
  }
  handler(editor, checking) {
    var _a;
    if (checking && (!this.isEnabled() || editor.getSelection())) {
      return false;
    }
    const text = editor.getValue();
    const cursorOffset = editor.posToOffset(editor.getCursor("from"));
    const linkData = findLinks(text, ~1 /* Markdown */, cursorOffset, cursorOffset);
    if (checking) {
      return linkData.length != 0;
    }
    if (linkData.length) {
      this.convertLinkToMarkdownLink(linkData[0], editor).then(() => {
        var _a2;
        (_a2 = this.callback) == null ? void 0 : _a2.call(this, null, void 0);
      }).catch((err) => {
        var _a2;
        (_a2 = this.callback) == null ? void 0 : _a2.call(this, err, void 0);
      });
    } else {
      (_a = this.callback) == null ? void 0 : _a.call(this, new Error("link not found"), null);
    }
  }
};

// commands/ConvertLinkToWikilinkCommand.ts
var ConvertLinkToWikilinkCommand = class extends CommandBase {
  constructor(isPresentInContextMenu = () => true, isEnabled = () => true) {
    super(isPresentInContextMenu, isEnabled);
    // TODO: refactor
    this.EmailScheme = "mailto:";
    this.id = "editor-convert-link-to-wikilink";
    this.displayNameCommand = "Convert to Wikilink";
    this.displayNameContextMenu = "Convert to wikilink";
    this.icon = "rotate-cw";
  }
  handler(editor, checking) {
    if (checking && !this.isEnabled()) {
      return false;
    }
    const text = editor.getValue();
    const cursorOffset = editor.posToOffset(editor.getCursor("from"));
    const links = findLinks(text, 1 /* Markdown */, cursorOffset, cursorOffset);
    if (checking) {
      return links.length > 0 && !!links[0].destination && !links[0].destination.content.trim().includes(":");
    }
    if (links.length) {
      this.convertLinkToWikiLink(links[0], editor);
    }
  }
  convertLinkToWikiLink(linkData, editor) {
    const link = linkData.type === 1 /* Markdown */ ? linkData.destination ? decodeURI(linkData.destination.content) : "" : linkData.destination;
    const text = linkData.text ? linkData.text.content !== link ? "|" + linkData.text.content : "" : "";
    const embededSymbol = linkData.embedded ? "!" : "";
    const rawLinkText = `${embededSymbol}[[${link}${text}]]`;
    editor.replaceRange(
      rawLinkText,
      editor.offsetToPos(linkData.position.start),
      editor.offsetToPos(linkData.position.end)
    );
    editor.setCursor(editor.offsetToPos(linkData.position.start + rawLinkText.length));
  }
};

// commands/ConvertLinkToAutolinkCommand.ts
var ConvertLinkToAutolinkCommand = class extends CommandBase {
  constructor(isPresentInContextMenu = () => true, isEnabled = () => true) {
    super(isPresentInContextMenu, isEnabled);
    // TODO: refactor
    this.EmailScheme = "mailto:";
    this.id = "editor-convert-link-to-autolink";
    this.displayNameCommand = "Convert to Autolink";
    this.displayNameContextMenu = "Convert to autolink";
    this.icon = "rotate-cw";
  }
  handler(editor, checking) {
    if (checking && !this.isEnabled()) {
      return false;
    }
    if (checking && editor.getSelection()) {
      return false;
    }
    const text = editor.getValue();
    const cursorOffset = editor.posToOffset(editor.getCursor("from"));
    const links = findLinks(text, 1 /* Markdown */ | 16 /* PlainUrl */, cursorOffset, cursorOffset);
    if (checking) {
      if (links.length === 0 || links[0].destination === void 0) {
        return false;
      }
      switch (links[0].type) {
        case 1 /* Markdown */:
          if (!(RegExPatterns.AbsoluteUri.test(links[0].destination.content) || links[0].destination.content.startsWith(this.EmailScheme))) {
            return false;
          }
          break;
        case 16 /* PlainUrl */:
          if (!RegExPatterns.AbsoluteUri.test(links[0].destination.content)) {
            return false;
          }
          break;
      }
      return true;
    }
    if (links.length === 1) {
      this.convertLinkToAutolink(links[0], editor);
    }
  }
  convertLinkToAutolink(linkData, editor) {
    var _a;
    if (((_a = linkData.destination) == null ? void 0 : _a.content) && (linkData.type === 1 /* Markdown */ || linkData.type === 16 /* PlainUrl */)) {
      let linkContent;
      if (linkData.destination.content.startsWith(this.EmailScheme)) {
        linkContent = `<${linkData.destination.content.substring(this.EmailScheme.length)}>`;
      } else if (RegExPatterns.AbsoluteUri.test(linkData.destination.content)) {
        linkContent = `<${linkData.destination.content}>`;
      }
      if (linkContent) {
        editor.replaceRange(
          linkContent,
          editor.offsetToPos(linkData.position.start),
          editor.offsetToPos(linkData.position.end)
        );
        editor.setCursor(editor.offsetToPos(linkData.position.start + linkContent.length));
      }
    }
  }
};

// commands/CopyLinkDestinationToClipboardCommand.ts
var CopyLinkDestinationToClipboardCommand = class extends CommandBase {
  constructor(obsidianProxy, isPresentInContextMenu = () => true, isEnabled = () => true) {
    super(isPresentInContextMenu, isEnabled);
    this.id = "editor-copy-link-destination-to-clipboard";
    this.displayNameCommand = "Copy link destination";
    this.displayNameContextMenu = "Copy link destination";
    this.icon = "copy";
    this.obdisianProxy = obsidianProxy;
  }
  handler(editor, checking) {
    var _a;
    if (checking && !this.isEnabled()) {
      return false;
    }
    const text = editor.getValue();
    const cursorOffset = editor.posToOffset(editor.getCursor("from"));
    const links = findLinks(text, 2 /* Wiki */ | 1 /* Markdown */ | 4 /* Html */ | 8 /* Autolink */, cursorOffset, cursorOffset);
    if (checking) {
      return !!(links == null ? void 0 : links.length) && !!((_a = links[0]) == null ? void 0 : _a.destination);
    }
    if (links == null ? void 0 : links.length) {
      this.copyLinkUnderCursorToClipboard(links[0]);
    }
  }
  copyLinkUnderCursorToClipboard(linkData) {
    var _a;
    if (linkData == null ? void 0 : linkData.destination) {
      this.obdisianProxy.clipboardWriteText((_a = linkData.destination) == null ? void 0 : _a.content);
      this.obdisianProxy.createNotice("Link destination copied to your clipboard");
    }
  }
};

// commands/RemoveLinksFromHeadingsCommand.ts
var RemoveLinksFromHeadingsCommand = class extends CommandBase {
  constructor(options, isPresentInContextMenu = () => true, isEnabled = () => true) {
    super(isPresentInContextMenu, isEnabled);
    this.options = options;
    this.id = "editor-remove-links-from-headings";
    this.displayNameCommand = "Remove links from headings";
    this.displayNameContextMenu = "Remove links from headings";
    this.icon = "unlink";
  }
  handler(editor, checking) {
    if (checking && !this.isEnabled()) {
      return false;
    }
    const selection = editor.getSelection();
    if (selection) {
      if (checking) {
        return hasLinksInHeadings(selection);
      }
      const result = removeLinksFromHeadings(selection, this.options);
      editor.replaceSelection(result);
    } else {
      const text = editor.getValue();
      if (checking) {
        return !!text && hasLinksInHeadings(text);
      }
      if (text) {
        const result = removeLinksFromHeadings(text, this.options);
        editor.setValue(result);
      }
    }
  }
};

// commands/EditLinkTextCommand.ts
var EditLinkTextCommand = class extends CommandBase {
  constructor(isPresentInContextMenu = () => true, isEnabled = () => true) {
    super(isPresentInContextMenu, isEnabled);
    this.generateLinkTextOnEdit = true;
    this.id = "editor-edit-link-text";
    this.displayNameCommand = "Edit link text";
    this.displayNameContextMenu = "Edit link text";
    this.icon = "text-cursor-input";
  }
  handler(editor, checking) {
    if (checking && !this.isEnabled()) {
      return false;
    }
    const linkData = this.getLink(editor);
    if (checking) {
      return !!linkData && !!linkData.text;
    }
    if (linkData) {
      setTimeout(() => {
        this.editLinkText(linkData, editor);
      }, 500);
    }
  }
  editLinkText(linkData, editor) {
    if (linkData.text) {
      const start2 = linkData.position.start + linkData.text.position.start;
      const end2 = linkData.position.start + linkData.text.position.end;
      editor.setSelection(editor.offsetToPos(start2), editor.offsetToPos(end2));
    } else if (this.generateLinkTextOnEdit) {
    }
  }
  getLink(editor) {
    const text = editor.getValue();
    const cursorOffset = editor.posToOffset(editor.getCursor("from"));
    const links = findLinks(text, 65535 /* All */, cursorOffset, cursorOffset);
    return (links == null ? void 0 : links.length) > 0 ? links[0] : void 0;
  }
};

// commands/SetLinkTextCommand.ts
var SetLinkTextCommand = class extends CommandBase {
  constructor(obsidianProxy, isPresentInContextMenu = () => true, isEnabled = () => true, callback = void 0) {
    super(isPresentInContextMenu, isEnabled);
    this.id = "editor-set-link-text";
    this.displayNameCommand = "Set link text";
    this.displayNameContextMenu = "Set link text";
    this.icon = "text-cursor-input";
    this.obsidianProxy = obsidianProxy;
    this.callback = callback;
  }
  //TODO: refactor
  handler(editor, checking) {
    var _a, _b;
    if (checking && !this.isEnabled()) {
      return false;
    }
    const linkData = this.getLinks(editor);
    const selection = editor.getSelection();
    if (checking) {
      return selection ? !!(linkData == null ? void 0 : linkData.length) && !!linkData.find((x) => {
        var _a2;
        return !x.text && ((_a2 = x.destination) == null ? void 0 : _a2.content) && !isAbsoluteUri(x.destination.content);
      }) : !!(linkData == null ? void 0 : linkData.length) && (linkData[0].type & (1 /* Markdown */ | 2 /* Wiki */)) != 0 && !!((_a = linkData[0].destination) == null ? void 0 : _a.content) && (!linkData[0].text || !((_b = linkData[0]) == null ? void 0 : _b.text.content) || !linkData[0].destination.content.startsWith("#") && linkData[0].destination.content.includes("#"));
    }
    if (linkData == null ? void 0 : linkData.length) {
      setTimeout(() => {
        if (selection) {
          this.setLinksText(linkData, editor).then(() => {
            var _a2;
            (_a2 = this.callback) == null ? void 0 : _a2.call(this, null, void 0);
          }).catch((err) => {
            var _a2;
            return (_a2 = this.callback) == null ? void 0 : _a2.call(this, err, void 0);
          });
        } else {
          this.setLinkText(linkData[0], editor).then(() => {
            var _a2;
            (_a2 = this.callback) == null ? void 0 : _a2.call(this, null, void 0);
          }).catch((err) => {
            var _a2;
            return (_a2 = this.callback) == null ? void 0 : _a2.call(this, err, void 0);
          });
        }
      }, 500);
    }
  }
  async setLinkText(linkData, editor) {
    var _a, _b;
    if (!linkData.destination) {
      return;
    }
    if (linkData.type == 2 /* Wiki */) {
      if (this.showLinkTextSuggestions(linkData, editor)) {
        return;
      }
      const text = linkData.destination.content[0] === "#" ? linkData.destination.content.substring(1) : getFileName((_a = linkData.destination) == null ? void 0 : _a.content);
      let textStart = linkData.position.start + linkData.destination.position.end;
      if (linkData.text) {
        editor.replaceRange("|" + text, editor.offsetToPos(textStart), editor.offsetToPos(linkData.text.content.length + 1));
      } else {
        editor.replaceRange("|" + text, editor.offsetToPos(textStart));
      }
      textStart++;
      editor.setSelection(editor.offsetToPos(textStart), editor.offsetToPos(textStart + text.length));
    } else if (linkData.type == 1 /* Markdown */) {
      const urlRegEx = /^(http|https):\/\/[^ "]+$/i;
      let text = "";
      if (urlRegEx.test(linkData.destination.content)) {
        if (!(linkData.text && linkData.text.content !== "")) {
          const notice = this.obsidianProxy.createNotice("Getting title ...", 0);
          try {
            text = await getPageTitle(new URL(linkData.destination.content), this.getPageText.bind(this));
          } catch (err) {
            this.obsidianProxy.createNotice(err);
          } finally {
            notice.hide();
          }
        }
      } else {
        if (this.showLinkTextSuggestions(linkData, editor)) {
          return;
        }
        text = getFileName(decodeURI((_b = linkData.destination) == null ? void 0 : _b.content));
      }
      const textStart = linkData.position.start + 1;
      editor.setSelection(editor.offsetToPos(textStart));
      editor.replaceSelection(text);
      editor.setSelection(editor.offsetToPos(textStart), editor.offsetToPos(textStart + text.length));
    }
  }
  //TODO: wip
  async setLinksText(linkData, editor) {
    var _a;
    const offset2 = editor.getSelection() ? editor.posToOffset(editor.getCursor("from")) : 0;
    for (let i = linkData.length - 1; i >= 0; i--) {
      const destinationContent = (_a = linkData[i].destination) == null ? void 0 : _a.content;
      if (linkData[i].text || !destinationContent || isAbsoluteUri(destinationContent) || destinationContent.lastIndexOf("#") >= 0) {
        continue;
      }
      const text = getFileName(destinationContent);
      let textStart = offset2 + linkData[i].position.start + linkData[i].destination.position.end;
      editor.replaceRange("|" + text, editor.offsetToPos(textStart));
      textStart++;
    }
  }
  //TODO: refactor
  getLinks(editor) {
    const selection = editor.getSelection();
    if (selection) {
      return findLinks(selection, 2 /* Wiki */);
    } else {
      const text = editor.getValue();
      const cursorOffset = editor.posToOffset(editor.getCursor("from"));
      const links = findLinks(text, 2 /* Wiki */ | 1 /* Markdown */, cursorOffset, cursorOffset);
      return (links == null ? void 0 : links.length) > 0 ? [links[0]] : [];
    }
  }
  showLinkTextSuggestions(linkData, editor) {
    const titles = getLinkTitles(linkData);
    if (titles.length == 0) {
      return false;
    }
    this.obsidianProxy.linkTextSuggestContextSetLinkData(linkData, titles);
    const posLinkEnd = editor.offsetToPos(linkData.position.end);
    editor.setCursor(posLinkEnd);
    editor.replaceRange(" ", posLinkEnd);
    editor.replaceRange("", posLinkEnd, editor.offsetToPos(linkData.position.end + 1));
    return true;
  }
  async getPageText(url) {
    const response = await this.obsidianProxy.requestUrl({ url: url.toString() });
    if (response.status !== 200) {
      throw new Error(`Failed to request '${url}': ${response.status}`);
    }
    return response.text;
  }
};

// commands/EditLinkDestinationCommand.ts
var EditLinkDestinationCommand = class extends CommandBase {
  constructor(isPresentInContextMenu = () => true, isEnabled = () => true) {
    super(isPresentInContextMenu, isEnabled);
    this.generateLinkTextOnEdit = true;
    this.id = "editor-edit-link-destination";
    this.displayNameCommand = "Edit link destination";
    this.displayNameContextMenu = "Edit link destination";
    this.icon = "text-cursor-input";
  }
  handler(editor, checking) {
    if (checking && !this.isEnabled()) {
      return false;
    }
    const linkData = this.getLink(editor);
    if (checking) {
      return !!linkData && (linkData.type & (2 /* Wiki */ | 1 /* Markdown */ | 4 /* Html */ | 8 /* Autolink */)) != 0 && !!linkData.destination;
    }
    if (linkData) {
      setTimeout(() => {
        this.editLinkDestination(linkData, editor);
      }, 500);
    }
  }
  editLinkDestination(linkData, editor) {
    if (linkData.destination) {
      const start2 = linkData.position.start + linkData.destination.position.start;
      const end2 = linkData.position.start + linkData.destination.position.end;
      editor.setSelection(editor.offsetToPos(start2), editor.offsetToPos(end2));
    }
  }
  getLink(editor) {
    const text = editor.getValue();
    const cursorOffsetStart = editor.posToOffset(editor.getCursor("from"));
    const cursorOffsetEnd = editor.posToOffset(editor.getCursor("to"));
    const links = findLinks(text, 65535 /* All */, cursorOffsetStart, cursorOffsetEnd);
    return (links == null ? void 0 : links.length) == 1 ? links[0] : void 0;
  }
};

// editorUtils.ts
function selectWordUnderCursor(editor) {
  const cursorOffset = editor.posToOffset(editor.getCursor("from"));
  const text = editor.getValue();
  const stopChar = /* @__PURE__ */ new Set([" ", "	", "(", ")", "{", "}", "[", "]", ".", ",", "\r", "\n", ":", ";", "\xA0"]);
  if (!stopChar.has(text[cursorOffset]) || !stopChar.has(text[Math.max(cursorOffset - 1, 0)])) {
    let leftIdx = cursorOffset;
    while (leftIdx > 0 && !stopChar.has(text[leftIdx - 1])) {
      leftIdx--;
    }
    let rightIdx = cursorOffset;
    while (rightIdx < text.length && !stopChar.has(text[rightIdx])) {
      rightIdx++;
    }
    if (leftIdx < rightIdx) {
      editor.setSelection(editor.offsetToPos(leftIdx), editor.offsetToPos(rightIdx));
      return text.substring(leftIdx, rightIdx);
    }
  }
  return "";
}

// commands/CreateLinkFromSelectionCommand.ts
var CreateLinkFromSelectionCommand = class extends CommandBase {
  constructor(obsidianProxy, isPresentInContextMenu = () => true, isEnabled = () => true) {
    super(isPresentInContextMenu, isEnabled);
    this.id = "editor-create-link-from-selection";
    this.displayNameCommand = "Create link";
    this.displayNameContextMenu = "Create link";
    this.icon = "link";
    this.obsidianProxy = obsidianProxy;
  }
  handler(editor, checking) {
    if (checking && !this.isEnabled()) {
      return false;
    }
    let selection = editor.getSelection();
    if (checking) {
      return !!selection || this.obsidianProxy.settings.autoselectWordOnCreateLink;
    }
    if (!selection && this.obsidianProxy.settings.autoselectWordOnCreateLink) {
      selection = selectWordUnderCursor(editor);
    }
    const linkStart = editor.posToOffset(editor.getCursor("from"));
    editor.replaceSelection(`[[|${selection}]]`);
    editor.setCursor(editor.offsetToPos(linkStart + 2));
  }
};

// commands/CreateLinkFromClipboardCommand.ts
var CreateLinkFromClipboardCommand = class extends CommandBase {
  constructor(obsidianProxy, isPresentInContextMenu = () => true, isEnabled = () => true, callback = void 0) {
    super(isPresentInContextMenu, isEnabled);
    this.id = "editor-create-link-from-clipboard";
    this.displayNameCommand = "Create link from clipboard";
    this.displayNameContextMenu = "Create link from clipboard";
    this.icon = "link";
    this.obsidianProxy = obsidianProxy;
    this.callback = callback;
  }
  handler(editor, checking) {
    if (checking && !this.isEnabled()) {
      return false;
    }
    if (checking) {
      const noteText = editor.getValue();
      const cursorOffset = editor.posToOffset(editor.getCursor("from"));
      const links = findLinks(noteText, 65535 /* All */, cursorOffset, cursorOffset);
      return (links == null ? void 0 : links.length) === 0;
    }
    (async () => {
      var _a, _b, _c;
      const httpUrlRegEx = /^(http|https):\/\/[^ "]+$/i;
      const clipboardText = await this.obsidianProxy.clipboardReadText();
      const links = findLinks(clipboardText, 65535 /* All */);
      let linkText = "";
      let linkDestination = "";
      if (links.length) {
        const link = links[0];
        if (this.obsidianProxy.settings.ffObsidianUrlSupport && (link.type & 32 /* ObsidianUrl */) === 32 /* ObsidianUrl */) {
          if (link.destination) {
            const url = new URL((_a = link.destination) == null ? void 0 : _a.content);
            if (this.obsidianProxy.Vault.getName() === url.searchParams.get("vault")) {
              const filePath = url.searchParams.get("file");
              if (filePath) {
                linkDestination = decodeURI(filePath);
                linkText = getFileName(linkDestination);
              }
            }
          }
        } else {
          linkDestination = links[0].destination ? links[0].destination.content : "";
        }
      } else {
        linkDestination = clipboardText;
      }
      if (!linkText) {
        linkText = linkDestination;
      }
      let selection = editor.getSelection();
      if (!selection && this.obsidianProxy.settings.autoselectWordOnCreateLink) {
        selection = selectWordUnderCursor(editor);
      }
      let isUrl = false;
      if (selection.length == 0 && httpUrlRegEx.test(linkDestination)) {
        isUrl = true;
        const notice = this.obsidianProxy.createNotice("Getting title ...", 0);
        try {
          linkText = await getPageTitle(new URL(linkDestination), this.getPageText.bind(this));
        } catch (err) {
          this.obsidianProxy.createNotice(err);
          (_b = this.callback) == null ? void 0 : _b.call(this, err, void 0);
          linkText = "";
        } finally {
          notice.hide();
        }
      }
      let posRangeStart = editor.getCursor();
      let posRangeEnd = posRangeStart;
      if (selection.length > 0) {
        posRangeStart = editor.getCursor("from");
        posRangeEnd = editor.getCursor("to");
        linkText = selection;
      }
      const requireAngleBrackets = !isUrl && linkDestination && linkDestination.indexOf(" ") > 0;
      const linkRawText = requireAngleBrackets ? `[${linkText}](<${linkDestination}>)` : `[${linkText}](${linkDestination})`;
      const endOffset = editor.posToOffset(posRangeStart) + linkRawText.length;
      editor.replaceRange(linkRawText, posRangeStart, posRangeEnd);
      if (linkText) {
        editor.setCursor(editor.offsetToPos(endOffset));
      } else {
        editor.setCursor(editor.offsetToPos(editor.posToOffset(posRangeStart) + 1));
      }
      (_c = this.callback) == null ? void 0 : _c.call(this, null, void 0);
    })();
  }
  async getPageText(url) {
    const response = await this.obsidianProxy.requestUrl({ url: url.toString() });
    if (response.status !== 200) {
      throw new Error(`Failed to request '${url}': ${response.status}`);
    }
    return response.text;
  }
};

// commands/ConvertAllLinksToMdlinksCommand.ts
var ConvertAllLinksToMdlinksCommand = class extends ConvertToMdlinkCommandBase {
  constructor(obsidianProxy, isPresentInContextMenu = () => true, isEnabled = () => true, callback = void 0) {
    super(obsidianProxy, isPresentInContextMenu, isEnabled);
    this.isPresentInContextMenu = () => this.obsidianProxy.settings.contexMenu.convertAllLinksToMdLinks;
    this.id = "editor-convert-all-links-to-mdlinks";
    this.displayNameCommand = "Convert all links to Markdown links";
    this.displayNameContextMenu = "Convert all links to Markdown links";
    this.icon = "rotate-cw";
    this.callback = callback;
  }
  handler(editor, checking) {
    if (checking && !this.isEnabled()) {
      return false;
    }
    const selection = editor.getSelection();
    const text = selection || editor.getValue();
    const links = findLinks(text);
    const codeBlocks = findCodeBlocks(text);
    const insideCodeBlock = (pos) => {
      for (const block of codeBlocks) {
        if (pos.start >= block.position.start && pos.end <= block.position.end) {
          return true;
        }
      }
      return false;
    };
    const notMdlinks = links ? links.filter((x) => x.type != 1 /* Markdown */ && !insideCodeBlock(x.position)) : [];
    if (checking) {
      return notMdlinks.length > 0;
    }
    const selectionOffset = selection ? editor.posToOffset(editor.getCursor("from")) : 0;
    (async () => {
      for (let i = notMdlinks.length - 1; i >= 0; i--) {
        const link = notMdlinks[i];
        await this.convertLinkToMarkdownLink(link, editor, false, selectionOffset);
      }
    })().then(() => {
      if (this.callback) {
        this.callback(null, void 0);
      }
    }).catch((err) => {
      if (this.callback) {
        this.callback(err, void 0);
      }
    });
  }
};

// commands/ConvertWikilinksToMdlinksCommand.ts
var ConvertWikilinksToMdlinksCommand = class extends ConvertToMdlinkCommandBase {
  constructor(obsidianProxy, isPresentInContextMenu = () => true, isEnabled = () => true, callback = void 0) {
    super(obsidianProxy, isPresentInContextMenu, isEnabled);
    this.isPresentInContextMenu = () => this.obsidianProxy.settings.contexMenu.convertWikilinkToMdLinks;
    this.id = "editor-convert-wikilinks-to-mdlinks";
    this.displayNameCommand = "Convert Wikilinks to Markdown links";
    this.displayNameContextMenu = "Convert Wikilinks to Markdown links";
    this.icon = "rotate-cw";
    this.callback = callback;
  }
  handler(editor, checking) {
    if (checking && !this.isEnabled()) {
      return false;
    }
    const selection = editor.getSelection();
    let frontmatterToIgnore;
    let text;
    if (selection) {
      text = selection;
    } else {
      text = editor.getValue();
      if (this.obsidianProxy.settings.ffSkipFrontmatterInNoteWideCommands && this.obsidianProxy.settings.skipFrontmatterInNoteWideCommands) {
        frontmatterToIgnore = getFrontmatter(text);
      }
    }
    const links = findLinks(text);
    const wikilinks = links ? links.filter((x) => x.type == 2 /* Wiki */ && (frontmatterToIgnore ? x.position.start > frontmatterToIgnore.position.end : true)) : [];
    if (checking) {
      return wikilinks.length > 0;
    }
    const selectionOffset = selection ? editor.posToOffset(editor.getCursor("from")) : 0;
    (async () => {
      var _a;
      for (let i = wikilinks.length - 1; i >= 0; i--) {
        const link = wikilinks[i];
        if (frontmatterToIgnore && link.position.end < ((_a = frontmatterToIgnore == null ? void 0 : frontmatterToIgnore.position) == null ? void 0 : _a.end)) {
          continue;
        }
        await this.convertLinkToMarkdownLink(link, editor, false, selectionOffset);
      }
    })().then(() => {
      if (this.callback) {
        this.callback(null, void 0);
      }
    }).catch((err) => {
      if (this.callback) {
        this.callback(err, void 0);
      }
    });
  }
};

// EditorTextBuffer.ts
var EditorTextBuffer = class {
  constructor(editor) {
    this.editor = editor;
  }
  getValue() {
    return this.editor.getValue();
  }
  replaceRange(text, from, to) {
    let posTo;
    if (to) {
      posTo = this.editor.offsetToPos(to);
    }
    this.editor.replaceRange(text, this.editor.offsetToPos(from), posTo);
  }
  setPosition(offset2) {
    this.editor.setCursor(this.editor.offsetToPos(offset2));
  }
};

// commands/ConvertAutolinksToMdlinksCommand.ts
var ConvertAutolinksToMdlinksCommand = class extends ConvertToMdlinkCommandBase {
  constructor(obsidianProxy, isPresentInContextMenu = () => true, isEnabled = () => true, callback = void 0) {
    super(obsidianProxy, isPresentInContextMenu, isEnabled);
    this.isPresentInContextMenu = () => this.obsidianProxy.settings.contexMenu.convertAutolinksToMdlinks;
    this.id = "editor-convert-autolinks-to-mdlinks";
    this.displayNameCommand = "Convert Autolinks to Markdown links";
    this.displayNameContextMenu = "Convert Autolinks to Markdown links";
    this.icon = "rotate-cw";
    this.callback = callback;
  }
  handler(editor, checking) {
    if (checking && !this.isEnabled()) {
      return false;
    }
    const selection = editor.getSelection();
    const text = selection || editor.getValue();
    const links = findLinks(text);
    const autolinks = links ? links.filter((x) => x.type == 8 /* Autolink */) : [];
    if (checking) {
      return autolinks.length > 0;
    }
    const selectionOffset = selection ? editor.posToOffset(editor.getCursor("from")) : 0;
    (async () => {
      const textBuffer = new EditorTextBuffer(editor);
      for (let i = autolinks.length - 1; i >= 0; i--) {
        const link = autolinks[i];
        await this.convertLinkToMarkdownLink1(link, textBuffer, false, selectionOffset);
      }
    })().then(() => {
      if (this.callback) {
        this.callback(null, void 0);
      }
    }).catch((err) => {
      if (this.callback) {
        this.callback(err, void 0);
      }
    });
  }
};

// commands/ConvertUrlsToMdlinksCommand.ts
var ConvertUrlsToMdlinksCommand = class extends ConvertToMdlinkCommandBase {
  constructor(obsidianProxy, isPresentInContextMenu = () => true, isEnabled = () => true, callback = void 0) {
    super(obsidianProxy, isPresentInContextMenu, isEnabled);
    this.isPresentInContextMenu = () => this.obsidianProxy.settings.contexMenu.convertUrlsToMdlinks;
    this.id = "editor-convert-urls-to-mdlinks";
    this.displayNameCommand = "Convert URLs to Markdown links";
    this.displayNameContextMenu = "Convert URLs to Markdown links";
    this.icon = "rotate-cw";
    this.callback = callback;
  }
  handler(editor, checking) {
    if (checking && !this.isEnabled()) {
      return false;
    }
    const selection = editor.getSelection();
    const text = selection || editor.getValue();
    const links = findLinks(text);
    const urls = links ? links.filter((x) => x.type == 16 /* PlainUrl */) : [];
    if (checking) {
      return urls.length > 0;
    }
    const selectionOffset = selection ? editor.posToOffset(editor.getCursor("from")) : 0;
    (async () => {
      for (let i = urls.length - 1; i >= 0; i--) {
        const link = urls[i];
        await this.convertLinkToMarkdownLink(link, editor, false, selectionOffset);
      }
    })().then(() => {
      if (this.callback) {
        this.callback(null, void 0);
      }
    }).catch((err) => {
      if (this.callback) {
        this.callback(err, void 0);
      }
    });
  }
};

// commands/ExtractSectionCommand.ts
var ExtractSectionCommand = class extends CommandBase {
  constructor(obsidianProxy, isPresentInContextMenu = () => true, isEnabled = () => true) {
    super(isPresentInContextMenu, isEnabled);
    this.obsidianProxy = obsidianProxy;
    this.id = "editor-extract-section";
    this.displayNameCommand = "Extract section";
    this.displayNameContextMenu = "Extract section";
    this.icon = "split";
    this.isEnabled = () => this.obsidianProxy.settings.ffExtractSection;
    this.isPresentInContextMenu = () => this.obsidianProxy.settings.contexMenu.extractSection;
  }
  handler(editor, checking) {
    var _a, _b;
    const text = editor.getValue();
    if (checking) {
      if (!this.isEnabled() || !text) {
        return false;
      }
      return true;
    }
    if (!text) {
      return;
    }
    let blockStart;
    let blockEnd;
    const cursorOffset = editor.posToOffset(editor.getCursor("from"));
    let found = false;
    blockStart = blockEnd = cursorOffset;
    let headerLevel = 1;
    while (true) {
      blockStart = text.lastIndexOf("# ", blockStart);
      if (blockStart < 0) {
        blockStart = 0;
        break;
      } else {
        while (blockStart >= 0 && text[blockStart] == "#") {
          blockStart--;
          headerLevel++;
        }
        if (blockStart < 0 || text[blockStart] == "\n") {
          blockStart++;
          headerLevel--;
          break;
        }
      }
    }
    if (blockStart < 0) {
      blockStart = 0;
    }
    found = false;
    let idx = blockEnd;
    while (true) {
      blockEnd = text.indexOf("\n" + "#".repeat(headerLevel) + " ", blockEnd);
      if (blockEnd < 0) {
        blockEnd = text.length;
        break;
      } else {
        idx = blockEnd + 1;
        while (idx < text.length && text[idx] == "#") {
          idx++;
        }
        if (idx >= text.length || text[idx] == " ") {
          break;
        }
      }
    }
    if (blockEnd >= text.length) {
      blockEnd = text.length;
    }
    const section = editor.getRange(editor.offsetToPos(blockStart), editor.offsetToPos(blockEnd));
    const currentView = this.obsidianProxy.Vault.getActiveNoteView();
    const currentNoteParentPath = (_b = (_a = currentView == null ? void 0 : currentView.file) == null ? void 0 : _a.parent) == null ? void 0 : _b.path;
    if (!currentNoteParentPath) {
      return;
    }
    const headerMatch = section.match(new RegExp(RegExPatterns.NoteHeading.source, "im"));
    if (headerMatch) {
      const safeFilename = getSafeFilename(headerMatch[1]).trim();
      const noteFullPath = (currentNoteParentPath === "/" ? safeFilename : `${currentNoteParentPath}/${safeFilename}`) + ".md";
      const noteContent = section.substring(headerMatch[0].length + 1);
      (async () => {
        const noteFile = await this.obsidianProxy.Vault.createNote(noteFullPath, noteContent);
        const rawWikilink = `[[${noteFullPath}|${safeFilename}]]`;
        editor.replaceRange(rawWikilink, editor.offsetToPos(blockStart), editor.offsetToPos(blockEnd));
        editor.setCursor(editor.offsetToPos(blockStart + rawWikilink.length));
      })();
    }
  }
};

// commands/ConvertHtmlLinksToMdlinksCommand.ts
var ConvertHtmlLinksToMdlinksCommand = class extends ConvertToMdlinkCommandBase {
  constructor(obsidianProxy, isPresentInContextMenu = () => true, isEnabled = () => true, callback = void 0) {
    super(obsidianProxy, isPresentInContextMenu, isEnabled);
    this.isPresentInContextMenu = () => this.obsidianProxy.settings.contexMenu.convertHtmllinksToMdlinks;
    this.id = "editor-convert-htmllink-to-mdlinks";
    this.displayNameCommand = "Convert HTML links to Markdown links";
    this.displayNameContextMenu = "Convert HTML links to Markdown links";
    this.icon = "rotate-cw";
    this.callback = callback;
  }
  handler(editor, checking) {
    if (checking && !this.isEnabled()) {
      return false;
    }
    const selection = editor.getSelection();
    const text = selection || editor.getValue();
    const links = findLinks(text);
    const urls = links ? links.filter((x) => x.type == 4 /* Html */) : [];
    if (checking) {
      return urls.length > 0;
    }
    const selectionOffset = selection ? editor.posToOffset(editor.getCursor("from")) : 0;
    (async () => {
      for (let i = urls.length - 1; i >= 0; i--) {
        const link = urls[i];
        await this.convertLinkToMarkdownLink(link, editor, false, selectionOffset);
      }
    })().then(() => {
      if (this.callback) {
        this.callback(null, void 0);
      }
    }).catch((err) => {
      if (this.callback) {
        this.callback(err, void 0);
      }
    });
  }
};

// commands/SetLinkTextFromClipboardCommand.ts
var SetLinkTextFromClipboardCommand = class extends ConvertToMdlinkCommandBase {
  constructor(obsidianProxy, isPresentInContextMenu = () => true, isEnabled = () => true, callback = void 0) {
    super(obsidianProxy, isPresentInContextMenu, isEnabled);
    this.isPresentInContextMenu = () => this.obsidianProxy.settings.contexMenu.setLinkTextFromClipboard;
    this.id = "editor-set-link-text-from-clipboard";
    this.displayNameCommand = "Set link text from clipboard";
    this.displayNameContextMenu = "Set link text from clipboard";
    this.icon = "link";
    this.obsidianProxy = obsidianProxy;
    this.callback = callback;
  }
  handler(editor, checking) {
    if (checking && !this.isEnabled()) {
      return false;
    }
    if (checking) {
      const noteText = editor.getValue();
      const cursorOffset = editor.posToOffset(editor.getCursor("from"));
      const links = findLinks(noteText, 1 /* Markdown */ | 2 /* Wiki */ | 16 /* PlainUrl */, cursorOffset, cursorOffset);
      if (!links.length || cursorOffset < links[0].position.start || cursorOffset >= links[0].position.end) {
        return false;
      }
      return true;
    }
    (async () => {
      var _a, _b, _c, _d, _e;
      const noteText = editor.getValue();
      const cursorOffset = editor.posToOffset(editor.getCursor("from"));
      const links = findLinks(noteText, 1 /* Markdown */ | 2 /* Wiki */ | 16 /* PlainUrl */, cursorOffset, cursorOffset);
      if (!links.length || cursorOffset < links[0].position.start || cursorOffset >= links[0].position.end) {
        (_a = this.callback) == null ? void 0 : _a.call(this, null, void 0);
        return;
      }
      const link = links[0];
      const clipboardText = await this.obsidianProxy.clipboardReadText();
      let linkText = clipboardText;
      let textStartOffset;
      let textEndOffset;
      let cursorOffsetCorrection = 0;
      if (link == null ? void 0 : link.text) {
        textStartOffset = link.position.start + link.text.position.start;
        textEndOffset = link.position.start + link.text.position.end;
      } else {
        switch (link == null ? void 0 : link.type) {
          case 2 /* Wiki */:
            textStartOffset = link.position.start + (link.destination ? link.destination.position.end : 2);
            textEndOffset = link.position.start + (link.destination ? link.destination.position.end : 2);
            linkText = "|" + linkText;
            break;
          case 1 /* Markdown */:
            textStartOffset = textEndOffset = link.position.start + (link.embedded ? 2 : 1);
            if (link.imageDimensions) {
              linkText = linkText + "|";
              cursorOffsetCorrection = -1;
            }
            break;
          case 16 /* PlainUrl */:
            {
              const rawLink = `[${linkText}](${(_b = link.destination) == null ? void 0 : _b.content})`;
              editor.replaceRange(rawLink, editor.offsetToPos(link.position.start), editor.offsetToPos(link.position.end));
              editor.setCursor(editor.offsetToPos(link.position.start + linkText.length + 1));
              (_c = this.callback) == null ? void 0 : _c.call(this, null, void 0);
            }
            return;
          default:
            (_d = this.callback) == null ? void 0 : _d.call(this, null, void 0);
            return;
        }
      }
      if (((link == null ? void 0 : link.type) & (1 /* Markdown */ | 2 /* Wiki */)) != 0) {
        editor.replaceRange(linkText, editor.offsetToPos(textStartOffset), editor.offsetToPos(textEndOffset));
        editor.setCursor(editor.offsetToPos(textStartOffset + linkText.length + cursorOffsetCorrection));
        (_e = this.callback) == null ? void 0 : _e.call(this, null, void 0);
      }
    })();
  }
  async getPageText(url) {
    const response = await this.obsidianProxy.requestUrl({ url: url.toString() });
    if (response.status !== 200) {
      throw new Error(`Failed to request '${url}': ${response.status}`);
    }
    return response.text;
  }
};

// commands/WrapNoteInFolderCommand.ts
var WrapNoteInFolderCommand = class extends CommandBase {
  constructor(obsidianProxy, isPresentInContextMenu = () => true, isEnabled = () => true) {
    super(isPresentInContextMenu, isEnabled);
    this.obsidianProxy = obsidianProxy;
    this.id = "note-wrap-in-folder";
    this.displayNameCommand = "Wrap in folder";
    this.displayNameContextMenu = "Wrap in folder";
    this.icon = "folder-input";
    this.isEnabled = () => this.obsidianProxy.settings.ffWrapNoteInFolder;
    this.isPresentInContextMenu = () => this.obsidianProxy.settings.contexMenu.wrapNoteInFolder;
  }
  handler(editor, checking) {
    var _a, _b, _c;
    if (checking) {
      if (!this.isEnabled()) {
        return false;
      }
      return true;
    }
    const currentView = this.obsidianProxy.Vault.getActiveNoteView();
    if (!currentView) {
      return;
    }
    const currentNoteParentPath = (_b = (_a = currentView == null ? void 0 : currentView.file) == null ? void 0 : _a.parent) == null ? void 0 : _b.path;
    const currentNotePath = (_c = currentView.file) == null ? void 0 : _c.path;
    if (!currentNotePath || !currentNoteParentPath) {
      return;
    }
    const matchNoteName = currentNotePath.match(/(.*\/)?(.*)\.md/);
    if (!matchNoteName) {
      return;
    }
    const currentNoteName = matchNoteName[2];
    (async () => {
      const hasSeparator = currentNoteParentPath[currentNoteParentPath.length - 1] === "/" || currentNoteParentPath[currentNoteParentPath.length - 1] == "\\";
      const newParentFolder = `${currentNoteParentPath}${hasSeparator ? "" : "/"}${currentNoteName}`;
      await this.obsidianProxy.Vault.createFolder(newParentFolder);
      await this.obsidianProxy.Vault.rename(currentNotePath, `${newParentFolder}/${currentNoteName}.md`);
    })();
  }
};

// commands/CopyLinkToClipboardCommand.ts
var CopyLinkToClipboardCommand = class extends CommandBase {
  constructor(obsidianProxy, isPresentInContextMenu = () => true, isEnabled = () => true) {
    super(isPresentInContextMenu, isEnabled);
    this.id = "editor-copy-link-to-clipboard";
    this.displayNameCommand = "Copy link";
    this.displayNameContextMenu = "Copy link";
    this.icon = "copy";
    this.obsidianProxy = obsidianProxy;
    this.isPresentInContextMenu = () => this.obsidianProxy.settings.contexMenu.copyLinkToClipboard;
  }
  handler(editor, checking) {
    if (checking && !this.isEnabled()) {
      return false;
    }
    const text = editor.getValue();
    const cursorOffset = editor.posToOffset(editor.getCursor("from"));
    const linkData = findLinks(text, 65535 /* All */, cursorOffset, cursorOffset);
    if (checking) {
      return linkData.length != 0;
    }
    if (linkData) {
      this.copyLinkUnderCursorToClipboard(linkData[0]);
    }
  }
  copyLinkUnderCursorToClipboard(linkData) {
    if (linkData == null ? void 0 : linkData.destination) {
      this.obsidianProxy.clipboardWriteText(linkData.content);
      this.obsidianProxy.createNotice("Link copied to your clipboard");
    }
  }
};

// TextBuffer.ts
var TextBuffer = class {
  constructor(text) {
    this.position = 0;
    this.text = text;
  }
  getValue() {
    return this.text;
  }
  replaceRange(text, from, to) {
    let tmp = this.text.substring(0, from) + text;
    if (to) {
      tmp += this.text.substring(to);
    }
    this.text = tmp;
  }
  setPosition(pos) {
    this.position = pos;
  }
};

// commands/ConvertLinksInFolderCommand.ts
var ConvertLinksInFolderCommand = class extends ConvertToMdlinkCommandBase {
  constructor(obsidianProxy, isPresentInContextMenu = () => false, isEnabled = () => true, callback = void 0) {
    super(obsidianProxy, isPresentInContextMenu, isEnabled);
    this.id = "editor-convert-links-in-folder";
    this.displayNameCommand = "Convert links in folder";
    this.displayNameContextMenu = "Convert links in folder";
    this.icon = "rotate-cw";
    this.callback = callback;
    this.isEnabled = () => this.obsidianProxy.settings.ffConvertLinksInFolder;
  }
  handler(editor, checking) {
    var _a, _b;
    if (checking) {
      if (!this.isEnabled()) {
        return false;
      }
      return true;
    }
    const activeView = this.obsidianProxy.Vault.getActiveNoteView();
    if (!activeView) {
      return;
    }
    const parentFolder = (_a = activeView.file) == null ? void 0 : _a.parent;
    if (!parentFolder) {
      return;
    }
    const files = this.obsidianProxy.Vault.getFilesInFolder(parentFolder);
    if (!files) {
      (_b = this.callback) == null ? void 0 : _b.call(this, null);
      return;
    }
    (async () => {
      var _a2, _b2;
      try {
        for (const file of files) {
          if (file.extension !== "md") {
            continue;
          }
          const content = await this.obsidianProxy.Vault.read(file);
          const codeBlocks = findCodeBlocks(content);
          const insideCodeBlock = (pos) => {
            for (const block of codeBlocks) {
              if (pos.start >= block.position.start && pos.end <= block.position.end) {
                return true;
              }
            }
            return false;
          };
          const links = findLinks(content, 2 /* Wiki */).filter((x) => !insideCodeBlock(x.position));
          if (links.length > 0) {
            const textBuffer = new TextBuffer(content);
            for (let i = links.length - 1; i >= 0; i--) {
              const link = links[i];
              await this.convertLinkToMarkdownLink1(link, textBuffer, false);
            }
            const updatedContent = textBuffer.getValue();
            await this.obsidianProxy.Vault.modify(file, updatedContent);
          }
        }
        this.obsidianProxy.createNotice(`${files.length} notes processed.`);
        (_a2 = this.callback) == null ? void 0 : _a2.call(this, null);
      } catch (err) {
        this.obsidianProxy.createNotice(`Failed: ${err}`);
        console.log(err);
        (_b2 = this.callback) == null ? void 0 : _b2.call(this, err);
      }
    })();
  }
};

// commands/ConvertLinkToHtmllinkCommand.ts
var ConvertLinkToHtmllinkCommand = class extends CommandBase {
  constructor(obsidianProxy, isPresentInContextMenu = () => false, isEnabled = () => true) {
    super(isPresentInContextMenu, isEnabled);
    // TODO: refactor
    this.EmailScheme = "mailto:";
    this.id = "editor-convert-link-to-htmllink";
    this.displayNameCommand = "Convert to HTML link";
    this.displayNameContextMenu = "Convert to HTML link";
    this.icon = "rotate-cw";
    this.obsidianProxy = obsidianProxy;
    this.isPresentInContextMenu = () => this.obsidianProxy.settings.contexMenu.convertToHtmlLink;
  }
  handler(editor, checking) {
    if (checking && !this.isEnabled()) {
      return false;
    }
    if (checking && editor.getSelection()) {
      return false;
    }
    const text = editor.getValue();
    const cursorOffset = editor.posToOffset(editor.getCursor("from"));
    const links = findLinks(text, 2 /* Wiki */, cursorOffset, cursorOffset);
    if (checking) {
      if (links.length === 0 || links[0].destination === void 0) {
        return false;
      }
      return true;
    }
    if (links.length === 1) {
      this.convertLinkToHtmllink(links[0], editor);
    }
  }
  convertLinkToHtmllink(linkData, editor) {
    var _a;
    if (((_a = linkData.destination) == null ? void 0 : _a.content) && linkData.type === 2 /* Wiki */) {
      const linkText = linkData.text ? linkData.text.content : this.generateLinkText(linkData);
      const linkContent = `<a href="${linkData.destination.content}" class="internal-link">${linkText}</a>`;
      editor.replaceRange(
        linkContent,
        editor.offsetToPos(linkData.position.start),
        editor.offsetToPos(linkData.position.end)
      );
      editor.setCursor(editor.offsetToPos(linkData.position.start + linkContent.length));
    }
  }
  generateLinkText(linkData) {
    var _a, _b;
    if (!((_a = linkData.destination) == null ? void 0 : _a.content)) {
      throw new Error("Link destination required");
    }
    const titles = getLinkTitles(linkData);
    if (titles.length > 0) {
      return titles[titles.length - 1];
    }
    return getFileName((_b = linkData.destination) == null ? void 0 : _b.content);
  }
};

// commands/CutLinkToClipboardCommand.ts
var CutLinkToClipboardCommand = class extends CommandBase {
  constructor(obsidianProxy, isPresentInContextMenu = () => true, isEnabled = () => true) {
    super(isPresentInContextMenu, isEnabled);
    this.id = "editor-cut-link-to-clipboard";
    this.displayNameCommand = "Cut link";
    this.displayNameContextMenu = "Cut link";
    this.icon = "scissors";
    this.obsidianProxy = obsidianProxy;
    this.isPresentInContextMenu = () => this.obsidianProxy.settings.contexMenu.copyLinkToClipboard;
  }
  handler(editor, checking) {
    if (checking && !this.isEnabled()) {
      return false;
    }
    const text = editor.getValue();
    const cursorOffset = editor.posToOffset(editor.getCursor("from"));
    const linkData = findLinks(text, 65535 /* All */, cursorOffset, cursorOffset);
    if (checking) {
      return linkData.length != 0;
    }
    if (linkData) {
      this.cutLinkUnderCursorToClipboard(linkData[0], editor);
    }
  }
  cutLinkUnderCursorToClipboard(linkData, editor) {
    if (linkData == null ? void 0 : linkData.destination) {
      this.obsidianProxy.clipboardWriteText(linkData.content);
      editor.replaceRange(
        "",
        editor.offsetToPos(linkData.position.start),
        editor.offsetToPos(linkData.position.end)
      );
      this.obsidianProxy.createNotice("Link cut to your clipboard");
    }
  }
};

// commands/SetLinkDestinationFromClipboardCommand.ts
var SetLinkDestinationFromClipboardCommand = class extends ConvertToMdlinkCommandBase {
  constructor(obsidianProxy, isPresentInContextMenu = () => true, isEnabled = () => true, callback = void 0) {
    super(obsidianProxy, isPresentInContextMenu, isEnabled);
    this.isEnabled = () => this.obsidianProxy.settings.ffSetLinkDestinationFromClipbard;
    this.isPresentInContextMenu = () => this.obsidianProxy.settings.contexMenu.setLinkDestinationFromClipboard;
    this.id = "editor-set-link-destination-from-clipboard";
    this.displayNameCommand = "Set link destination from clipboard";
    this.displayNameContextMenu = "Set link destination from clipboard";
    this.icon = "link";
    this.obsidianProxy = obsidianProxy;
    this.callback = callback;
  }
  handler(editor, checking) {
    if (checking && !this.isEnabled()) {
      return false;
    }
    if (checking) {
      const noteText = editor.getValue();
      const cursorOffset = editor.posToOffset(editor.getCursor("from"));
      const links = findLinks(noteText, 1 /* Markdown */ | 2 /* Wiki */, cursorOffset, cursorOffset);
      if (!links.length || cursorOffset < links[0].position.start || cursorOffset >= links[0].position.end) {
        return false;
      }
      return true;
    }
    (async () => {
      var _a, _b, _c, _d, _e;
      const noteText = editor.getValue();
      const cursorOffset = editor.posToOffset(editor.getCursor("from"));
      const links = findLinks(noteText, 1 /* Markdown */ | 2 /* Wiki */, cursorOffset, cursorOffset);
      if (!links.length || cursorOffset < links[0].position.start || cursorOffset >= links[0].position.end) {
        (_a = this.callback) == null ? void 0 : _a.call(this, null, void 0);
        return;
      }
      const link = links[0];
      const clipboardText = await this.obsidianProxy.clipboardReadText();
      let linkDestination = clipboardText;
      if (this.obsidianProxy.settings.ffObsidianUrlSupport) {
        if (linkDestination.startsWith("obsidian://open?vault=")) {
          const links2 = findLinks(linkDestination, 16 /* PlainUrl */);
          if (links2.length == 1 && links2[0].destination) {
            const url = new URL((_b = links2[0].destination) == null ? void 0 : _b.content);
            if (this.obsidianProxy.Vault.getName() === url.searchParams.get("vault")) {
              const filePath = url.searchParams.get("file");
              if (filePath) {
                linkDestination = filePath + (getFileExtension(filePath) ? "" : ".md");
              }
            }
          }
        }
      }
      let destinationStartOffset;
      let destinationEndOffset;
      if (link == null ? void 0 : link.destination) {
        destinationStartOffset = link.position.start + link.destination.position.start;
        destinationEndOffset = link.position.start + link.destination.position.end;
      } else {
        switch (link == null ? void 0 : link.type) {
          case 2 /* Wiki */:
            destinationStartOffset = link.position.start + 2;
            destinationEndOffset = destinationStartOffset;
            linkDestination += "|";
            break;
          case 1 /* Markdown */:
            destinationStartOffset = destinationEndOffset = link.position.start + (link.text ? link.text.content.length : 0) + (link.embedded ? 2 : 1) + 2;
            break;
          default:
            (_c = this.callback) == null ? void 0 : _c.call(this, null, void 0);
            return;
        }
      }
      if (((link == null ? void 0 : link.type) & (1 /* Markdown */ | 2 /* Wiki */)) != 0) {
        if ((link == null ? void 0 : link.type) === 1 /* Markdown */ && (!((_d = link.destination) == null ? void 0 : _d.content) || !link._destinationInAngleBrackets) && linkDestination.indexOf(" ") >= 0) {
          linkDestination = `<${linkDestination}>`;
        }
        editor.replaceRange(linkDestination, editor.offsetToPos(destinationStartOffset), editor.offsetToPos(destinationEndOffset));
        editor.setCursor(editor.offsetToPos(destinationStartOffset + linkDestination.length));
        (_e = this.callback) == null ? void 0 : _e.call(this, null, void 0);
      }
    })();
  }
  async getPageText(url) {
    const response = await this.obsidianProxy.requestUrl({ url: url.toString() });
    if (response.status !== 200) {
      throw new Error(`Failed to request '${url}': ${response.status}`);
    }
    return response.text;
  }
};

// commands/CopyLinkToObjectToClipboardCommand.ts
var CopyLinkToObjectToClipboardCommand = class extends CommandBase {
  constructor(obsidianProxy, isPresentInContextMenu = () => true, isEnabled = () => true) {
    super(isPresentInContextMenu, isEnabled);
    this.id = "editor-copy-link-to-object-to-clipboard";
    this.displayNameCommand = "Copy link to element";
    this.displayNameContextMenu = "Copy link to element";
    this.icon = "copy";
    this.obsidianProxy = obsidianProxy;
    this.isEnabled = () => this.obsidianProxy.settings.ffCopyLinkToObject;
    this.isPresentInContextMenu = () => this.obsidianProxy.settings.ffCopyLinkToObject && this.obsidianProxy.settings.contexMenu.copyLinkToHeadingToClipboard;
  }
  handler(editor, checking) {
    if (checking && !this.isEnabled()) {
      return false;
    }
    const text = editor.getLine(editor.getCursor("from").line);
    const headingMatch = text.match(new RegExp(RegExPatterns.NoteHeading.source));
    const currentView = this.obsidianProxy.Vault.getActiveNoteView();
    const block = headingMatch ? void 0 : (currentView == null ? void 0 : currentView.file) ? this.obsidianProxy.getBlock(editor, currentView == null ? void 0 : currentView.file) : void 0;
    if (checking) {
      return !!headingMatch || !!block;
    }
    const currentNoteFile = currentView == null ? void 0 : currentView.file;
    if (headingMatch && headingMatch[1] && currentNoteFile) {
      this.copyLinkToHeadingUnderCursorToClipboard(headingMatch[1], currentNoteFile);
    } else if (block && (currentView == null ? void 0 : currentView.file)) {
      this.copyLinkToBlockUnderCursorToClipboard(currentView == null ? void 0 : currentView.file, editor, block);
    }
  }
  copyLinkToHeadingUnderCursorToClipboard(heading, noteFile) {
    const rawLink = this.obsidianProxy.createLink("", noteFile.path, heading, heading);
    this.obsidianProxy.clipboardWriteText(rawLink);
    this.obsidianProxy.createNotice("Link copied to your clipboard");
  }
  copyLinkToBlockUnderCursorToClipboard(file, editor, block) {
    var _a;
    let linkText = void 0;
    const blockFirstLine = editor.getLine(block.position.start.line);
    const links = findLinks(blockFirstLine, 2 /* Wiki */ | 1 /* Markdown */);
    if (links && links.length && links[0].destinationType == DestinationType.Image) {
      linkText = (_a = links[0].text) == null ? void 0 : _a.content;
    }
    if (block.id) {
      this.obsidianProxy.clipboardWriteText(
        this.obsidianProxy.createLink("", file.path, "^" + block.id, linkText)
      );
      this.obsidianProxy.createNotice("Link copied to your clipboard");
      return;
    }
    const sectionEnd = block.position.end;
    const end2 = {
      ch: sectionEnd.col,
      line: sectionEnd.line
    };
    const id = this.generateId();
    editor.replaceRange(`${this.isEolRequired(block) ? "\n\n" : " "}^${id}`, end2);
    this.obsidianProxy.clipboardWriteText(
      this.obsidianProxy.createLink("", file.path, "^" + id, linkText)
    );
    this.obsidianProxy.createNotice("Link copied to your clipboard");
  }
  generateId() {
    return Math.random().toString(36).substring(2, 6);
  }
  isEolRequired(block) {
    const blockType = block.type || "";
    switch (blockType) {
      case "blockquote":
      case "code":
      case "table":
      case "comment":
      case "footnoteDefinition":
        return true;
      default:
        return false;
    }
  }
};

// commands/EmbedLinkCommand.ts
var EmbedLinkCommand = class extends CommandBase {
  constructor(isPresentInContextMenu = () => true, isEnabled = () => true) {
    super(isPresentInContextMenu, isEnabled);
    this.id = "editor-embed-link";
    this.displayNameCommand = "Embed link";
    this.displayNameContextMenu = "Embed";
    this.icon = "file-input";
  }
  handler(editor, checking) {
    if (checking && !this.isEnabled()) {
      return false;
    }
    const text = editor.getValue();
    const cursorOffset = editor.posToOffset(editor.getCursor("from"));
    const links = findLinks(text, 2 /* Wiki */ | 1 /* Markdown */, cursorOffset, cursorOffset);
    if (checking) {
      return (links == null ? void 0 : links.length) > 0 && !links[0].embedded && !!links[0].destination;
    }
    if (links == null ? void 0 : links.length) {
      this.embedLinkUnderCursor(links[0], editor);
    }
  }
  embedLinkUnderCursor(linkData, editor) {
    if (linkData.content && linkData.type & (2 /* Wiki */ | 1 /* Markdown */) && !linkData.embedded) {
      editor.replaceRange(
        "!" + linkData.content,
        editor.offsetToPos(linkData.position.start),
        editor.offsetToPos(linkData.position.end)
      );
    }
  }
};

// commands/UnembedLinkCommand.ts
var UnembedLinkCommand = class extends CommandBase {
  constructor(isPresentInContextMenu = () => true, isEnabled = () => true) {
    super(isPresentInContextMenu, isEnabled);
    this.id = "editor-unembed-link";
    this.displayNameCommand = "Unembed link";
    this.displayNameContextMenu = "Unembed";
    this.icon = "file-output";
  }
  handler(editor, checking) {
    if (checking && !this.isEnabled()) {
      return false;
    }
    const text = editor.getValue();
    const cursorOffset = editor.posToOffset(editor.getCursor("from"));
    const links = findLinks(text, 2 /* Wiki */ | 1 /* Markdown */, cursorOffset, cursorOffset);
    if (checking) {
      return (links == null ? void 0 : links.length) === 1 && links[0].embedded && !!links[0].destination;
    }
    if (links) {
      this.unembedLinkUnderCursor(links[0], editor);
    }
  }
  unembedLinkUnderCursor(linkData, editor) {
    if (linkData.content && linkData.type & (2 /* Wiki */ | 1 /* Markdown */) && linkData.embedded) {
      editor.replaceRange(
        linkData.content.substring(1),
        editor.offsetToPos(linkData.position.start),
        editor.offsetToPos(linkData.position.end)
      );
    }
  }
};

// commands/EmbedUnembedLinkCommand.ts
var EmbedUnembedLinkCommand = class extends CommandBase {
  constructor(isPresentInContextMenu = () => true, isEnabled = () => true) {
    super(isPresentInContextMenu, isEnabled);
    this.id = "editor-embed-unembed-link";
    this.displayNameCommand = "Embed/Unembed link";
    this.displayNameContextMenu = "Embed/Unembed";
    this.icon = "file-output";
    this.embedCommand = new EmbedLinkCommand(isPresentInContextMenu);
    this.unembedCommand = new UnembedLinkCommand(isPresentInContextMenu);
  }
  handler(editor, checking) {
    if (checking && !this.isEnabled()) {
      return false;
    }
    if (this.embedCommand.handler(editor, true)) {
      this.activeCommand = this.embedCommand;
    } else if (this.unembedCommand.handler(editor, true)) {
      this.activeCommand = this.unembedCommand;
    } else {
      this.activeCommand = void 0;
    }
    if (checking) {
      if (this.activeCommand) {
        this.icon = this.activeCommand.icon;
        this.displayNameContextMenu = this.activeCommand.displayNameContextMenu;
        return true;
      }
      return false;
    }
    if (this.activeCommand) {
      this.activeCommand.handler(editor, checking);
    }
  }
};

// commands/Commands.ts
var commands = /* @__PURE__ */ new Map();
function createCommands(obsidianProxy, settings) {
  if (commands.size > 0) {
    return;
  }
  commands.set(UnlinkLinkCommand.name, new UnlinkLinkCommand(() => settings.contexMenu.unlink));
  commands.set(DeleteLinkCommand.name, new DeleteLinkCommand(obsidianProxy));
  commands.set(ConvertLinkToMdlinkCommand.name, new ConvertLinkToMdlinkCommand(obsidianProxy, () => settings.contexMenu.convertToMakrdownLink));
  commands.set(ConvertLinkToWikilinkCommand.name, new ConvertLinkToWikilinkCommand(() => settings.contexMenu.convertToWikilink));
  commands.set(ConvertLinkToHtmllinkCommand.name, new ConvertLinkToHtmllinkCommand(obsidianProxy));
  commands.set(ConvertLinkToAutolinkCommand.name, new ConvertLinkToAutolinkCommand(() => settings.contexMenu.convertToAutolink));
  commands.set(CopyLinkToClipboardCommand.name, new CopyLinkToClipboardCommand(obsidianProxy));
  commands.set(CopyLinkToObjectToClipboardCommand.name, new CopyLinkToObjectToClipboardCommand(obsidianProxy));
  commands.set(CutLinkToClipboardCommand.name, new CutLinkToClipboardCommand(obsidianProxy));
  commands.set(
    CopyLinkDestinationToClipboardCommand.name,
    new CopyLinkDestinationToClipboardCommand(obsidianProxy, () => settings.contexMenu.copyLinkDestination)
  );
  const options = {
    get internalWikilinkWithoutTextAction() {
      return settings.removeLinksFromHeadingsInternalWikilinkWithoutTextAction;
    }
  };
  commands.set(RemoveLinksFromHeadingsCommand.name, new RemoveLinksFromHeadingsCommand(options));
  commands.set(EditLinkTextCommand.name, new EditLinkTextCommand(() => settings.contexMenu.editLinkText));
  commands.set(SetLinkTextCommand.name, new SetLinkTextCommand(obsidianProxy, () => settings.contexMenu.setLinkText));
  commands.set(EditLinkDestinationCommand.name, new EditLinkDestinationCommand(() => settings.contexMenu.editLinkDestination));
  commands.set(CreateLinkFromSelectionCommand.name, new CreateLinkFromSelectionCommand(obsidianProxy, () => settings.contexMenu.createLink));
  commands.set(CreateLinkFromClipboardCommand.name, new CreateLinkFromClipboardCommand(obsidianProxy, () => settings.contexMenu.createLinkFromClipboard));
  commands.set(EmbedUnembedLinkCommand.name, new EmbedUnembedLinkCommand(() => settings.contexMenu.embedUnembedLink));
  commands.set(ConvertAllLinksToMdlinksCommand.name, new ConvertAllLinksToMdlinksCommand(obsidianProxy));
  commands.set(ConvertWikilinksToMdlinksCommand.name, new ConvertWikilinksToMdlinksCommand(obsidianProxy));
  commands.set(ConvertUrlsToMdlinksCommand.name, new ConvertUrlsToMdlinksCommand(obsidianProxy));
  commands.set(ConvertAutolinksToMdlinksCommand.name, new ConvertAutolinksToMdlinksCommand(obsidianProxy));
  commands.set(ConvertHtmlLinksToMdlinksCommand.name, new ConvertHtmlLinksToMdlinksCommand(obsidianProxy));
  commands.set(ExtractSectionCommand.name, new ExtractSectionCommand(obsidianProxy));
  commands.set(SetLinkTextFromClipboardCommand.name, new SetLinkTextFromClipboardCommand(obsidianProxy));
  commands.set(SetLinkDestinationFromClipboardCommand.name, new SetLinkDestinationFromClipboardCommand(obsidianProxy));
  commands.set(WrapNoteInFolderCommand.name, new WrapNoteInFolderCommand(obsidianProxy));
  commands.set(ConvertLinksInFolderCommand.name, new ConvertLinksInFolderCommand(obsidianProxy));
}
function getPaletteCommands(obsidianProxy, settings) {
  createCommands(obsidianProxy, settings);
  return Array.from(commands.values());
}
function getContextMenuCommands(obsidianProxy, settings) {
  createCommands(obsidianProxy, settings);
  const commandNames = [
    null,
    EditLinkTextCommand.name,
    SetLinkTextCommand.name,
    SetLinkTextFromClipboardCommand.name,
    EditLinkDestinationCommand.name,
    SetLinkDestinationFromClipboardCommand.name,
    CopyLinkToClipboardCommand.name,
    CopyLinkToObjectToClipboardCommand.name,
    CutLinkToClipboardCommand.name,
    CopyLinkDestinationToClipboardCommand.name,
    null,
    UnlinkLinkCommand.name,
    null,
    ConvertLinkToWikilinkCommand.name,
    ConvertLinkToAutolinkCommand.name,
    ConvertLinkToMdlinkCommand.name,
    ConvertLinkToHtmllinkCommand.name,
    // UnembedLinkCommand.name,
    // EmbedLinkCommand.name,
    EmbedUnembedLinkCommand.name,
    DeleteLinkCommand.name,
    null,
    CreateLinkFromSelectionCommand.name,
    CreateLinkFromClipboardCommand.name,
    null,
    ConvertAllLinksToMdlinksCommand.name,
    ConvertWikilinksToMdlinksCommand.name,
    ConvertUrlsToMdlinksCommand.name,
    ConvertAutolinksToMdlinksCommand.name,
    ConvertHtmlLinksToMdlinksCommand.name,
    null,
    ExtractSectionCommand.name,
    WrapNoteInFolderCommand.name
  ];
  const contextMenuCommands = [];
  for (const cmdName of commandNames) {
    if (cmdName == null) {
      contextMenuCommands.push(null);
      continue;
    }
    const cmd = commands.get(cmdName);
    if (cmd && cmd.isEnabled() && cmd.isPresentInContextMenu()) {
      contextMenuCommands.push(cmd);
    }
  }
  return contextMenuCommands;
}

// ui/PromptModal.ts
var import_obsidian7 = require("obsidian");
var PromptModal = class extends import_obsidian7.Modal {
  constructor(app2, title, text, buttons, onSubmit) {
    super(app2);
    this.text = text;
    this.title = title;
    this.buttons = buttons;
    this.onSubmit = onSubmit;
  }
  onOpen() {
    const { contentEl } = this;
    contentEl.createDiv({ cls: "modal-title", text: this.title });
    const contentDiv = contentEl.createDiv({ cls: "modal-content" });
    this.text.forEach((t) => {
      contentDiv.createEl("p", { text: t });
    });
    const buttonsContainer = contentEl.createDiv({ cls: "modal-button-container" });
    const buttonsSetting = new import_obsidian7.Setting(buttonsContainer);
    this.buttons.forEach((b) => {
      buttonsSetting.addButton((c) => {
        c.setButtonText(b.text);
        if (b.isCta) {
          c.setCta();
        }
        if (b.isWarning) {
          c.setWarning();
        }
        c.onClick(() => {
          this.close();
          this.onSubmit(b.result);
        });
      });
    });
  }
  onClose() {
    const { contentEl } = this;
    contentEl.empty();
  }
};

// ui/UiFactory.ts
var UiFactory = class {
  constructor(app2) {
    this.app = app2;
  }
  createPromptModal(title, text, buttons, onSubmit) {
    return new PromptModal(this.app, title, text, buttons, onSubmit);
  }
};

// main.ts
var ObsidianLinksPlugin = class extends import_obsidian8.Plugin {
  constructor(app2, manifest) {
    super(app2, manifest);
    this.EmailScheme = "mailto:";
    this.convertHtmlLinksToMdLinks = () => {
      const mdView = this.app.workspace.getActiveViewOfType(import_obsidian8.MarkdownView);
      if (mdView && mdView.getViewData()) {
        const text = mdView.getViewData();
        const result = replaceAllHtmlLinks(text);
        mdView.setViewData(result, false);
      }
    };
    this.linkTextSuggestContext = {
      app: app2,
      titleSeparator: " \u2022 ",
      titles: [],
      provideSuggestions: false,
      setLinkData(linkData, titles) {
        this.linkData = linkData;
        this.titles = titles;
        this.provideSuggestions = true;
      },
      clearLinkData() {
        this.provideSuggestions = false;
        this.linkData = void 0;
        this.titles = [];
      }
    };
  }
  createNotice(message, timeout) {
    return new import_obsidian8.Notice(message, timeout);
  }
  requestUrl(request2) {
    return (0, import_obsidian8.requestUrl)(request2);
  }
  measurePerformance(func) {
    const start2 = (0, import_obsidian8.moment)();
    try {
      func();
    } finally {
      return (0, import_obsidian8.moment)().diff(start2);
    }
  }
  async onload() {
    await this.loadSettings();
    this.obsidianProxy = new ObsidianProxy(this.app, this.linkTextSuggestContext, this.settings, new UiFactory(this.app));
    if (this.settings.removeLinksFromHeadingsInternalWikilinkWithoutTextAction === "None" /* None */) {
      switch (this.settings.removeLinksFromHeadingsInternalWikilinkWithoutTextReplacement) {
        case "Destination":
          this.settings.removeLinksFromHeadingsInternalWikilinkWithoutTextAction = "ReplaceWithDestination" /* ReplaceWithDestination */;
          break;
        case "LowestNoteHeading":
          this.settings.removeLinksFromHeadingsInternalWikilinkWithoutTextAction = "ReplaceWithLowestNoteHeading" /* ReplaceWithLowestNoteHeading */;
          break;
        default:
          this.settings.removeLinksFromHeadingsInternalWikilinkWithoutTextAction = "ReplaceWithDestination" /* ReplaceWithDestination */;
      }
      await this.saveSettings();
    }
    this.addSettingTab(new ObsidianLinksSettingTab(this.app, this));
    this.registerEditorSuggest(new LinkTextSuggest(this.linkTextSuggestContext));
    const commands2 = getPaletteCommands(this.obsidianProxy, this.settings);
    for (let cmd of commands2) {
      this.addCommand({
        id: cmd.id,
        name: cmd.displayNameCommand,
        icon: cmd.icon,
        editorCheckCallback: (checking, editor, ctx) => cmd.handler(editor, checking)
      });
    }
    if (this.settings.ffReplaceLink) {
      this.addCommand({
        id: "editor-replace-external-link-with-internal",
        name: "Replace link",
        icon: "pencil",
        editorCheckCallback: (checking, editor, ctx) => this.replaceExternalLinkUnderCursorHandler(editor, checking)
      });
    }
    if (this.settings.ffReplaceLink) {
      this.registerEvent(
        this.app.workspace.on("file-open", (file) => this.replaceMarkdownTargetsInNote())
      );
      this.registerEvent(
        this.app.vault.on("delete", (file) => this.deleteFileHandler(file))
      );
      this.registerEvent(
        this.app.vault.on("rename", (file, oldPath) => this.renameFileHandler(file, oldPath))
      );
      this.registerEvent(
        this.app.workspace.on("editor-paste", (evt, editor, view) => this.onEditorPaste(evt, editor, view))
      );
      if (this.settings.ffReplaceLink) {
        this.addCommand({
          id: "editor-replace-markdown-targets-in-note",
          name: "#Replace markdown link in notes",
          editorCallback: (editor, view) => this.replaceMarkdownTargetsInNote()
        });
      }
    }
    this.registerEvent(
      this.app.workspace.on("editor-menu", (menu, editor, view) => {
        const linkData = this.getLink(editor);
        let addTopSeparator = function() {
          menu.addSeparator();
          addTopSeparator = function() {
          };
        };
        const commands3 = getContextMenuCommands(this.obsidianProxy, this.settings);
        for (const cmd of commands3) {
          if (cmd == null) {
            addTopSeparator();
          } else {
            if (cmd.handler(editor, true)) {
              menu.addItem((item) => {
                item.setTitle(cmd.displayNameContextMenu).setIcon(cmd.icon).onClick(async () => {
                  cmd.handler(editor, false);
                });
              });
            }
          }
        }
        if (linkData) {
          if (this.settings.ffReplaceLink && this.settings.contexMenu.replaceLink) {
            menu.addItem((item) => {
              item.setTitle("Replace link").setIcon("pencil").onClick(async () => {
                this.replaceExternalLink(linkData, editor);
              });
            });
          }
        }
      })
    );
  }
  onunload() {
  }
  async loadSettings() {
    const loadedSettings = await this.loadData();
    this.settings = Object.assign({}, DEFAULT_SETTINGS, loadedSettings);
    this.settings.contexMenu = Object.assign({}, DEFAULT_SETTINGS.contexMenu, loadedSettings == null ? void 0 : loadedSettings.contexMenu);
    this.linkTextSuggestContext.titleSeparator = this.settings.titleSeparator;
  }
  async saveSettings() {
    await this.saveData(this.settings);
    this.linkTextSuggestContext.titleSeparator = this.settings.titleSeparator;
  }
  getLink(editor) {
    const text = editor.getValue();
    const cursorOffset = editor.posToOffset(editor.getCursor("from"));
    const links = findLinks(text, 65535 /* All */, cursorOffset, cursorOffset);
    return (links == null ? void 0 : links.length) == 1 ? links[0] : void 0;
  }
  replaceExternalLinkUnderCursorHandler(editor, checking) {
    const linkData = this.getLink(editor);
    if (checking) {
      return !!linkData;
    }
    if (linkData) {
      this.replaceExternalLink(linkData, editor);
    }
  }
  //TODO
  replaceExternalLink(linkData, editor) {
    new ReplaceLinkModal(this.app, async (path) => {
      var _a, _b, _c, _d;
      if (path) {
        let target = path;
        if (path.startsWith("[")) {
          const links = findLinks(path, 2 /* Wiki */ | 1 /* Markdown */);
          if (links.length > 0 && ((_a = links[0].destination) == null ? void 0 : _a.content)) {
            target = (_b = links[0].destination) == null ? void 0 : _b.content;
          }
        } else if (path.startsWith("obsidian://")) {
          const links = findLinks(path, 32 /* ObsidianUrl */ | 16 /* PlainUrl */);
          if (links.length > 0 && (links[0].type & 32 /* ObsidianUrl */) === 32 /* ObsidianUrl */ && ((_c = links[0].destination) == null ? void 0 : _c.content)) {
            const url = new URL((_d = links[0].destination) == null ? void 0 : _d.content);
            if (this.obsidianProxy.Vault.getName() === url.searchParams.get("vault")) {
              const filePath = url.searchParams.get("file");
              if (filePath) {
                target = decodeURI(filePath);
              }
            }
          }
        }
        this.settings.linkReplacements.push({
          source: linkData.destination.content,
          target
        });
        await this.saveSettings();
        this.replaceMarkdownTargetsInNote();
      }
    }).open();
  }
  escapeRegex(str) {
    return str.replace(/[/\-\\^$*+?.()|[\]{}]/g, "\\$&");
  }
  replaceMarkdownTargetsInNote() {
    const e = this.measurePerformance(() => {
      const mdView = this.app.workspace.getActiveViewOfType(import_obsidian8.MarkdownView);
      if (mdView && mdView.getViewData()) {
        const text = mdView.getViewData();
        const [result, count] = this.replaceLinksInText(text);
        if (count) {
          mdView.setViewData(result, false);
          new import_obsidian8.Notice(`Links: ${count} items replaced.`);
        }
      }
    });
    if (this.settings.showPerformanceNotification) {
      new import_obsidian8.Notice(`${e} ms`);
    }
  }
  replaceLinksInText(text) {
    let targetText = text;
    let totalCount = 0;
    this.settings.linkReplacements.forEach((e) => {
      const [newText, count] = replaceMarkdownTarget(targetText, e.source, e.target);
      targetText = newText;
      totalCount += count;
    });
    return [targetText, totalCount];
  }
  onEditorPaste(evt, editor, view) {
    var _a;
    const html = (_a = evt.clipboardData) == null ? void 0 : _a.getData("text/html");
    if (html && html.indexOf("<a") > 0) {
      const markdown = (0, import_obsidian8.htmlToMarkdown)(html);
      const [text, count] = this.replaceLinksInText(markdown);
      if (count) {
        evt.preventDefault();
        const fromOffset = editor.posToOffset(editor.getCursor("from"));
        if (editor.getSelection()) {
          editor.replaceSelection(text);
        } else {
          editor.replaceRange(text, editor.getCursor("from"));
        }
        editor.setCursor(editor.offsetToPos(fromOffset + text.length));
      }
    }
  }
  //TOOD: refactor
  processObsidianLink(evt, editor, view) {
    var _a;
    const text = (_a = evt.clipboardData) == null ? void 0 : _a.getData("text/plain");
    if (!(text == null ? void 0 : text.startsWith("obsidian://open?vault="))) {
      return;
    }
    evt.preventDefault();
    const url = new URL(text);
    const targetVaultName = url.searchParams.get("vault");
    const vaultName = this.app.vault.getName();
    let link = text;
    if (targetVaultName === vaultName) {
      const file = url.searchParams.get("file");
      if (file) {
        const destination = decodeURI(file);
        const hashIdx = destination.lastIndexOf("#");
        let text2 = null;
        if (hashIdx >= 0) {
          if (hashIdx + 1 < destination.length - 1) {
            text2 = destination.substring(hashIdx + 1);
          }
        } else {
          text2 = getFileName(destination);
        }
        link = `[[${decodeURI(file)} ${text2 && text2 != destination ? "|" + text2 : ""}]]`;
      }
    }
    const selection = editor.getSelection();
    if (selection.length > 0) {
      editor.replaceSelection(link);
    } else {
      const fromPos = editor.getCursor("from");
      editor.replaceRange(link, fromPos);
      editor.setCursor(editor.offsetToPos(editor.posToOffset(fromPos) + link.length));
    }
  }
  deleteFileHandler(file) {
    const [pathWithoutExtension, success] = removeExtension(file.path);
    if (!success) {
      return;
    }
    const replacements = this.settings.linkReplacements.filter((r) => {
      const hashIdx = r.target.indexOf("#");
      return hashIdx > 0 ? r.target.substring(0, hashIdx) !== pathWithoutExtension : r.target !== pathWithoutExtension;
    });
    this.settings.linkReplacements = replacements;
    this.saveSettings();
  }
  renameFileHandler(file, oldPath) {
    const [oldPathWithoutExtension, success] = removeExtension(oldPath);
    if (!success) {
      return;
    }
    let settingsChanged = false;
    this.settings.linkReplacements.forEach((r) => {
      const hashIdx = r.target.indexOf("#");
      const targetPath = hashIdx > 0 ? r.target.substring(0, hashIdx) : r.target;
      if (targetPath === oldPathWithoutExtension) {
        const [newPathWithoutExtension] = removeExtension(file.path);
        r.target = hashIdx > 0 ? newPathWithoutExtension + r.target.substring(hashIdx) : newPathWithoutExtension;
        settingsChanged = true;
      }
    });
    if (settingsChanged) {
      this.saveSettings();
    }
  }
};
/*! Bundled license information:

is-windows/index.js:
  (*!
   * is-windows <https://github.com/jonschlinkert/is-windows>
   *
   * Copyright © 2015-2018, Jon Schlinkert.
   * Released under the MIT License.
   *)

path-root-regex/index.js:
  (*!
   * path-root-regex <https://github.com/jonschlinkert/path-root-regex>
   *
   * Copyright (c) 2016, Jon Schlinkert.
   * Licensed under the MIT License.
   *)

path-root/index.js:
  (*!
   * path-root <https://github.com/jonschlinkert/path-root>
   *
   * Copyright (c) 2016, Jon Schlinkert.
   * Licensed under the MIT License.
   *)

map-cache/index.js:
  (*!
   * map-cache <https://github.com/jonschlinkert/map-cache>
   *
   * Copyright (c) 2015, Jon Schlinkert.
   * Licensed under the MIT License.
   *)
*/

/* nosourcemap */