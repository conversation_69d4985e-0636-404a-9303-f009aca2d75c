/* Google Calendar Plugin settings */  
  
/* This sets the margin for the navigation container of the calendars. */  
.gcal-nav-container {  
   margin-bottom: 0em !important; /* was 1em */  
   margin-top: 3px !important; /* was 0 */  
}  
  
/* This sets the margin for the date container of the calendars. */  
.gcal-date-container {  
   margin-bottom: 0px !important; /* was 10px */  
}  
  
/* Here I change the appearance of the current time indicator. */  
.gcal-time-display {  
    position: absolute;  
    width: 100% !important; /* was 95%, determines time-line length */  
    height: 3px; /* Determines time-line thickness. */  
    background: #FE8019 !important; /* Determines color of time-line. */  
    overflow: visible;  
    z-index: 2;  
}  
  
/* This sets the margin for the timeline of the calendars. */  
.gcal-timeline {  
    position: relative;  
    display: flex;  
    flex-direction: row;  
    padding-top: 0px !important;  
    flex-shrink: 1000;  
    min-height: 0;  
    margin: -199.2px 0px 0px 0px !important; /* This together with padding-top influence the position of number and lines, make sure they allign. (This alligns if you display time from 7:00 to 23:00) */  
}  
 
/* This is further alignment of the hour numerals. */  
.gcal-hour-text-container {  
    display: flex;  
    flex-direction: column;  
    min-height: 0;  
    overflow: hidden;  
    margin: -208.167px 0px 0px; /* Again tweak the number as you need, it will differ for each setup. */  
}