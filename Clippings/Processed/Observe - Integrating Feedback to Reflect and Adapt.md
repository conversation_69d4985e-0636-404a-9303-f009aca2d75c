---
title: "What are LLMs? - Hugging Face Agents Course"
source: "https://huggingface.co/learn/agents-course/unit1/observations"
author:
published:
created: 2025-03-28
description: "We’re on a journey to advance and democratize artificial intelligence through open source and open science."
tags:
  - "clippings"
---
观察是智能体感知其行动后果的方式。

  
它们提供了关键信息，驱动智能体的思考过程并指导未来的行动。

  
这些信号来自环境——无论是 API 数据、错误信息还是系统日志——它们引导下一轮的思考周期。

| Type of Observation | Example                                           |
| ------------------- | ------------------------------------------------- |
| System Feedback     | 错误消息、成功通知、状态码                                     |
| Data Changes        | 数据库更新、文件系统修改、状态变更                                 |
| Environmental Data  | Sensor readings, system metrics, resource usage   |
| Response Analysis   | API responses, query results, computation outputs |
| Time-based Events   | Deadlines reached, scheduled tasks completed      |

## How Are the Results Appended?

  
执行操作后，框架按以下步骤依次进行：

1. 解析操作以识别要调用的函数及使用的参数。
2. **Execute the action.**
3. **Append the result** as an **Observation**.