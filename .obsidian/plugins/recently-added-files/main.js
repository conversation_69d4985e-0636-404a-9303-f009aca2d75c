/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var we=Object.create;var L=Object.defineProperty;var me=Object.getOwnPropertyDescriptor;var ye=Object.getOwnPropertyNames;var Ee=Object.getPrototypeOf,Fe=Object.prototype.hasOwnProperty;var x=(n,t)=>()=>(t||n((t={exports:{}}).exports,t),t.exports),be=(n,t)=>{for(var e in t)L(n,e,{get:t[e],enumerable:!0})},X=(n,t,e,i)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of ye(t))!Fe.call(n,a)&&a!==e&&L(n,a,{get:()=>t[a],enumerable:!(i=me(t,a))||i.enumerable});return n};var J=(n,t,e)=>(e=n!=null?we(Ee(n)):{},X(t||!n||!n.__esModule?L(e,"default",{value:n,enumerable:!0}):e,n)),Te=n=>X(L({},"__esModule",{value:!0}),n);var Y=x(Z=>{"use strict";Object.defineProperty(Z,"__esModule",{value:!0})});var ee=x(Q=>{"use strict";Object.defineProperty(Q,"__esModule",{value:!0})});var ie=x(te=>{"use strict";Object.defineProperty(te,"__esModule",{value:!0})});var ne=x(p=>{"use strict";var De=p&&p.__createBinding||(Object.create?function(n,t,e,i){i===void 0&&(i=e);var a=Object.getOwnPropertyDescriptor(t,e);(!a||("get"in a?!t.__esModule:a.writable||a.configurable))&&(a={enumerable:!0,get:function(){return t[e]}}),Object.defineProperty(n,i,a)}:function(n,t,e,i){i===void 0&&(i=e),n[i]=t[e]}),R=p&&p.__exportStar||function(n,t){for(var e in n)e!=="default"&&!Object.prototype.hasOwnProperty.call(t,e)&&De(t,n,e)};Object.defineProperty(p,"__esModule",{value:!0});p.getApiSafe=p.getDefer=p.isPluginEnabled=p.PluginNotEnabledError=p.pluginId=void 0;R(Y(),p);R(ee(),p);R(ie(),p);p.pluginId="obsidian-front-matter-title-plugin";var P=class extends Error{};p.PluginNotEnabledError=P;function Pe(n){var t,e,i;return(i=(e=(t=n==null?void 0:n.plugins)===null||t===void 0?void 0:t.enabledPlugins)===null||e===void 0?void 0:e.has(p.pluginId))!==null&&i!==void 0?i:!1}p.isPluginEnabled=Pe;function ae(n){var t,e,i,a;let s=(e=(t=n==null?void 0:n.plugins)===null||t===void 0?void 0:t.getPlugin(p.pluginId))!==null&&e!==void 0?e:null,r=(a=(i=s==null?void 0:s.getDefer)===null||i===void 0?void 0:i.call(s))!==null&&a!==void 0?a:null;if(r===null)throw new P(`Plugin ${p.pluginId} is not enabled or old version`);return r}p.getDefer=ae;function Le(n){return new V(null,n)}p.getApiSafe=Le;var V=class{constructor(t,e){this.api=t,this.app=e}before(){if(this.api!==null)return;let t=this.getDeffer();if(t===null)return;let e=t.getApi();if(e===null)return t.awaitPlugin().then(()=>{this.api=t.getApi()});this.api=e}getDeffer(){try{return ae(this.app)}catch(t){if(t instanceof P)return null;throw t}}getResolverFactory(){var t,e;return this.before(),(e=(t=this.api)===null||t===void 0?void 0:t.getResolverFactory())!==null&&e!==void 0?e:null}getEventDispatcher(){var t,e;return this.before(),(e=(t=this.api)===null||t===void 0?void 0:t.getEventDispatcher())!==null&&e!==void 0?e:null}getEnabledFeatures(){var t,e;return this.before(),(e=(t=this.api)===null||t===void 0?void 0:t.getEnabledFeatures())!==null&&e!==void 0?e:[]}}});var xe={};be(xe,{default:()=>H});module.exports=Te(xe);var o=require("obsidian"),ge=J(ne());var se={newFiles:[],omittedPaths:[],omittedTags:[],maxLength:100,showExtension:!1,activeFileType:"all",enableFileTypeFilter:!1,sortOrder:"newest"};var M="Recently-Added-Files",le=`<svg width="100" height="100" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="File / File_Add">
<path id="Vector" d="M12 18V15M12 15V12M12 15H9M12 15H15M13 3.00087C12.9045 3 12.7973 3 12.6747 3H8.2002C7.08009 3 6.51962 3 6.0918 3.21799C5.71547 3.40973 5.40973 3.71547 5.21799 4.0918C5 4.51962 5 5.08009 5 6.2002V17.8002C5 18.9203 5 19.4801 5.21799 19.9079C5.40973 20.2842 5.71547 20.5905 6.0918 20.7822C6.51921 21 7.079 21 8.19694 21L15.8031 21C16.921 21 17.48 21 17.9074 20.7822C18.2837 20.5905 18.5905 20.2842 18.7822 19.9079C19 19.4805 19 18.9215 19 17.8036V9.32568C19 9.20296 19 9.09561 18.9991 9M13 3.00087C13.2856 3.00347 13.4663 3.01385 13.6388 3.05526C13.8429 3.10425 14.0379 3.18526 14.2168 3.29492C14.4186 3.41857 14.5918 3.59182 14.9375 3.9375L18.063 7.06298C18.4089 7.40889 18.5809 7.58136 18.7046 7.78319C18.8142 7.96214 18.8953 8.15726 18.9443 8.36133C18.9857 8.53376 18.9963 8.71451 18.9991 9M13 3.00087V5.8C13 6.9201 13 7.47977 13.218 7.90759C13.4097 8.28392 13.7155 8.59048 14.0918 8.78223C14.5192 9 15.079 9 16.1969 9H18.9991M18.9991 9H19.0002" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
</svg>`;var N=require("obsidian"),C=class{constructor({containerEl:t,plugin:e,defaultMaxLength:i}){this.containerEl=t,this.plugin=e,this.defaultMaxLength=i}create(){new N.Setting(this.containerEl).setName("List length").setDesc("Maximum number of filenames to keep in the list.").addText(t=>{var e;t.inputEl.setAttr("type","number"),t.inputEl.setAttr("placeholder",this.defaultMaxLength.toString()),t.setValue(((e=this.plugin.data.maxLength)==null?void 0:e.toString())||"").onChange(this.handleChange.bind(this)),t.inputEl.onblur=this.handleBlur.bind(this)})}handleChange(t){let e=parseInt(t,10);if(!Number.isNaN(e)&&e<=0){new N.Notice("List length must be a positive integer");return}this.plugin.data.maxLength=e}handleBlur(t){let e=t.target,i=parseInt(e.value,10);!isNaN(i)&&i>0&&(this.plugin.data.maxLength=i,this.plugin.pruneLength(),this.plugin.view.redraw())}};var re=require("obsidian"),S=class{constructor({containerEl:t,plugin:e,defaultShowExtension:i}){this.containerEl=t,this.plugin=e,this.defaultShowExtension=i}create(){new re.Setting(this.containerEl).setName("Show File Extensions").setDesc("Enabling this option will display file extensions in the list").addToggle(t=>{var e;t.setValue((e=this.plugin.data.showExtension)!=null?e:this.defaultShowExtension).onChange(async i=>{this.plugin.data.showExtension=i,await this.plugin.saveData(),this.plugin.view.redraw()})})}};var y=class{static getDisplayName(t,e){return t?e&&t.path.split("/").pop()||t.basename:""}static trimExtension(t){return t?t.replace(/\.[^/.]+$/,""):""}};var oe=J(require("crypto")),O=require("obsidian"),B=class{static async generateMD5(t,e){let i=await t.vault.read(e);return oe.createHash("md5").update(i).digest("hex")}},I=class{static async renameWithMD5(t,e){if(!e){new O.Notice("Valid file not found.");return}let i=e.path,a=i.substring(0,i.lastIndexOf("/")),s=e.extension,d=`${await B.generateMD5(t,e)}_MD5.${s}`,u=`${a}/${d}`;try{await t.fileManager.renameFile(e,u),new O.Notice(`The file has been renamed to ${d}`)}catch(f){new O.Notice(`An error occurred while renaming the file: ${f}`)}}};var m="newly-added-files";var de={IMAGE:["png","jpg","jpeg","gif","bmp","svg","webp"],VIDEO:["mp4","webm","mov","avi","mkv","m4v"],MARKDOWN:["md","markdown"],PDF:["pdf"],CANVAS:["canvas"]};var pe=require("obsidian"),A=class{constructor({containerEl:t,plugin:e}){this.containerEl=t,this.plugin=e}create(){new pe.Setting(this.containerEl).setName("Default file type filter").setDesc("Select the default file type to filter in the list").addDropdown(t=>{t.addOption("all","All files").addOption("md","Markdown").addOption("pdf","PDF").addOption("image","Images").addOption("video","Videos").addOption("canvas","Canvas").addOption("other","Other Files").setValue(this.plugin.data.activeFileType).onChange(async e=>{this.plugin.data.activeFileType=e,await this.plugin.saveData(),this.plugin.view.redraw()})})}};var ce=require("obsidian"),_=class{constructor({containerEl:t,plugin:e}){this.containerEl=t,this.plugin=e}create(){new ce.Setting(this.containerEl).setName("Enable File Type Filter").setDesc("When enabled, you can filter and display recent files by file type.").addToggle(t=>{t.setValue(this.plugin.data.enableFileTypeFilter).onChange(async e=>{this.plugin.data.enableFileTypeFilter=e,await this.plugin.saveData(),this.plugin.view.redraw()})})}};var c=class{static isFileType(t,e){if(!t)return!1;let i=t.toLowerCase();return this.extensionTypeCache.has(e)||this.extensionTypeCache.set(e,new Set(de[e])),this.extensionTypeCache.get(e).has(i)}static canPreview(t){return t?this.isFileType(t,"IMAGE")||this.isFileType(t,"PDF"):!1}};c.extensionTypeCache=new Map;var ue=require("obsidian"),k=class{constructor({containerEl:t,plugin:e}){this.containerEl=t,this.plugin=e}create(){new ue.Setting(this.containerEl).setName("File Sort Order").setDesc("Choose how files are sorted in the list").addDropdown(t=>{t.addOption("newest","Newest first").addOption("oldest","Oldest first").addOption("az","By name A-Z").addOption("za","By name Z-A").setValue(this.plugin.data.sortOrder).onChange(async e=>{this.plugin.data.sortOrder=e,await this.plugin.saveData(),this.plugin.view.redraw()})})}};var j=class extends o.ItemView{constructor(e,i,a){super(e);this.previewTimeout=null;this.currentHoverElement=null;this.redraw=()=>{var f;let e=this.app.workspace.getActiveFile(),i=createDiv({cls:"nav-folder mod-root"});this.createFilterDropdown(i),this.createSearchBox(i);let a=i.createDiv({cls:"nav-folder-children"}),s=(0,ge.getApiSafe)(this.app),d=s&&s.getEnabledFeatures().contains("explorer")?(f=s.getResolverFactory())==null?void 0:f.createResolver("explorer"):null,u=this.filterFilesByType(this.data.newFiles);switch(this.data.sortOrder){case"newest":break;case"oldest":u=[...u].reverse();break;case"az":u=[...u].sort((l,v)=>l.basename.toLowerCase().localeCompare(v.basename.toLowerCase()));break;case"za":u=[...u].sort((l,v)=>v.basename.toLowerCase().localeCompare(l.basename.toLowerCase()));break}u.forEach(l=>{var G,q,U,K;let v=a.createDiv({cls:"tree-item nav-file newly-added-files-file"}),h=v.createDiv({cls:"tree-item-self is-clickable nav-file-title newly-added-files-title"}),E=h.createDiv({cls:"tree-item-icon newly-added-files-icon"}),F=(G=l.path.split(".").pop())==null?void 0:G.toLowerCase(),b="lucide-file";c.isFileType(F,"MARKDOWN")||c.isFileType(F,"PDF")?b="lucide-file-text":c.isFileType(F,"IMAGE")?b="lucide-image":c.isFileType(F,"VIDEO")?b="lucide-video":c.isFileType(F,"CANVAS")&&(b="lucide-layout-dashboard"),(0,o.setIcon)(E,b);let $=h.createDiv({cls:"tree-item-inner nav-file-title-content newly-added-files-title-content"}),ve=d?(U=d.resolve(l.path))!=null?U:y.getDisplayName(l,(q=this.data.showExtension)!=null?q:!1):y.getDisplayName(l,(K=this.data.showExtension)!=null?K:!1);$.setText(ve),(0,o.setTooltip)(v,l.path),e&&l.path===e.path&&h.addClass("is-active"),h.setAttr("draggable","true"),h.addEventListener("dragstart",g=>{if(!(l!=null&&l.path))return;let w=this.app.metadataCache.getFirstLinkpathDest(l.path,"");if(!w)return;let T=this.app.dragManager,D=T.dragFile(g,w);T.onDragStart(g,D)}),h.addEventListener("mouseover",g=>{l!=null&&l.path&&(this.app.workspace.trigger("hover-link",{event:g,source:m,hoverParent:i,targetEl:v,linktext:l.path}),this.currentHoverElement=h)}),h.addEventListener("mouseenter",g=>{c.canPreview(F)&&(this.previewTimeout||(this.previewTimeout=setTimeout(()=>{this.currentHoverElement===h&&this.showFilePreview(l,g)},500)))}),h.addEventListener("mouseleave",()=>{this.previewTimeout&&(clearTimeout(this.previewTimeout),this.previewTimeout=null),this.currentHoverElement=null;let g=document.querySelector(".newly-added-files-preview");g&&g.remove()}),h.addEventListener("contextmenu",g=>{if(!(l!=null&&l.path))return;let w=new o.Menu;w.addItem(D=>D.setSection("action").setTitle("Open in new tab").setIcon("file-plus").onClick(()=>{this.focusFile(l,"tab")})),w.addItem(D=>D.setSection("action").setTitle("Rename with MD5").setIcon("file-signature").onClick(async()=>{let fe=this.app.vault.getFileByPath(l.path);await I.renameWithMD5(this.app,fe)}));let T=this.app.vault.getAbstractFileByPath(l==null?void 0:l.path);T&&(this.app.workspace.trigger("file-menu",w,T,"link-context-menu"),w.showAtPosition({x:g.clientX,y:g.clientY}))}),h.addEventListener("click",g=>{if(!l)return;let w=o.Keymap.isModEvent(g);this.focusFile(l,w?"tab":!1)}),$.addEventListener("mousedown",g=>{l&&g.button===1&&(g.preventDefault(),this.focusFile(l,"tab"))});let W=h.createDiv({cls:"newly-added-files-file-delete menu-item-icon"});(0,o.setIcon)(W,"lucide-x"),W.addEventListener("click",async g=>{g.stopPropagation(),await this.removeFile(l),this.redraw()})}),this.contentEl.setChildrenInPlace([i])};this.removeFile=async e=>{this.data.newFiles=this.data.newFiles.filter(i=>i.path!==e.path),await this.plugin.saveData()};this.focusFile=(e,i)=>{let a=this.app.vault.getFiles().find(s=>s.path===e.path);a?this.app.workspace.getLeaf(i).openFile(a):(new o.Notice("Cannot find a file with that name"),this.data.newFiles=this.data.newFiles.filter(s=>s.path!==e.path),this.plugin.saveData(),this.redraw())};this.plugin=i,this.data=a}async onOpen(){this.redraw()}getViewType(){return m}getDisplayText(){return"Recently Added Files"}getIcon(){return M}onPaneMenu(e){e.addItem(i=>{i.setTitle("Clear list").setIcon(M).onClick(async()=>{this.data.newFiles=[],await this.plugin.saveData(),this.redraw()})}).addItem(i=>{i.setTitle("Close").setIcon("cross").onClick(()=>{this.app.workspace.detachLeavesOfType(m)})})}load(){super.load()}createSearchBox(e){let i=e.createDiv({cls:"nav-folder-title newly-added-files-search"}),a=i.createEl("input",{cls:"newly-added-files-search-input",attr:{type:"text",placeholder:"Search files..."}}),s=i.createDiv({cls:"newly-added-files-search-clear"});(0,o.setIcon)(s,"lucide-x"),s.style.display="none",a.addEventListener("input",r=>{let d=r.target.value.toLowerCase();this.contentEl.querySelectorAll(".newly-added-files-file").forEach(f=>{var v,h;(((h=(v=f.querySelector(".newly-added-files-title-content"))==null?void 0:v.textContent)==null?void 0:h.toLowerCase())||"").includes(d)?f.style.display="":f.style.display="none"}),s.style.display=d?"":"none"}),s.addEventListener("click",()=>{a.value="";let r=new Event("input");a.dispatchEvent(r)})}createFilterDropdown(e){if(!this.data.enableFileTypeFilter)return;let i=e.createDiv({cls:"nav-folder-title newly-added-files-filter"});i.createSpan({cls:"newly-added-files-filter-label",text:"Filter:"});let a=i.createEl("select",{cls:"dropdown"});[{value:"all",label:"All files",icon:"lucide-files"},{value:"md",label:"Markdown",icon:"lucide-file-text"},{value:"pdf",label:"PDF",icon:"lucide-file-text"},{value:"image",label:"Images",icon:"lucide-image"},{value:"video",label:"Videos",icon:"lucide-video"},{value:"canvas",label:"Canvas",icon:"lucide-layout-dashboard"},{value:"other",label:"Other Files",icon:"lucide-file"}].forEach(r=>{let d=a.createEl("option",{text:r.label,value:r.value});r.value===this.data.activeFileType&&(d.selected=!0)}),a.addEventListener("change",async r=>{let d=r.target;this.data.activeFileType=d.value,await this.plugin.saveData(),this.redraw()})}filterFilesByType(e){return!this.data.enableFileTypeFilter||this.data.activeFileType==="all"?e:e.filter(i=>{var s;let a=(s=i.path.split(".").pop())==null?void 0:s.toLowerCase();switch(this.data.activeFileType){case"md":return c.isFileType(a,"MARKDOWN");case"pdf":return c.isFileType(a,"PDF");case"image":return c.isFileType(a,"IMAGE");case"video":return c.isFileType(a,"VIDEO");case"canvas":return c.isFileType(a,"CANVAS");case"other":return!c.isFileType(a,"MARKDOWN")&&!c.isFileType(a,"PDF")&&!c.isFileType(a,"IMAGE")&&!c.isFileType(a,"VIDEO")&&!c.isFileType(a,"CANVAS");default:return!0}})}async showFilePreview(e,i){var f;let a=document.querySelector(".newly-added-files-preview");a&&a.remove();let s=this.app.vault.getFileByPath(e.path);if(!s)return;let r=document.createElement("div");r.className="newly-added-files-preview";let d=i.target.getBoundingClientRect();r.style.top=`${d.bottom+10}px`,r.style.left=`${d.left}px`;let u=(f=e.path.split(".").pop())==null?void 0:f.toLowerCase();if(c.isFileType(u,"IMAGE"))try{let l=await this.app.vault.readBinary(s),v=new Blob([l]),h=URL.createObjectURL(v),E=document.createElement("img");E.src=h,E.className="newly-added-files-preview-img",r.appendChild(E),E.onload=()=>URL.revokeObjectURL(h)}catch(l){console.error("\u9884\u89C8\u5931\u8D25:",l);return}else c.isFileType(u,"PDF")&&(r.innerHTML='<div class="newly-added-files-preview-pdf">PDF \u9884\u89C8</div>');document.body.appendChild(r)}},H=class extends o.Plugin{constructor(){super(...arguments);this.isInitialized=!1;this.pruneOmittedFiles=async()=>{this.data.newFiles=this.data.newFiles.filter(this.shouldAddFile),await this.saveData(),this.view&&this.view.redraw()};this.pruneLength=async()=>{let e=this.data.newFiles.length-(this.data.maxLength||100);e>0&&this.data.newFiles.splice(this.data.newFiles.length-e,e),await this.saveData(),this.view&&this.view.redraw()};this.shouldAddFile=e=>{var r,d;if(!e||!e.path)return!1;let i=this.data.omittedPaths.filter(u=>u.length>0),a=u=>{try{return new RegExp(u).test(e.path)}catch(f){return console.error("New Files: Invalid regex pattern: "+u),!1}};if(i.some(a))return!1;let s=this.app.vault.getFileByPath(e.path);if(s){let u=this.data.omittedTags.filter(v=>v.length>0),f=((d=(r=this.app.metadataCache.getFileCache(s))==null?void 0:r.frontmatter)==null?void 0:d.tags)||[],l=v=>u.includes(v);if(f.some(l))return!1}return!0};this.handleRename=async(e,i)=>{if(!this.isInitialized)return;let a=this.data.newFiles.find(s=>s.path===i);a&&(a.path=e.path,a.basename=y.trimExtension(e.name),await this.saveData(),this.view&&this.view.redraw())};this.handleDelete=async e=>{if(!this.isInitialized)return;let i=this.data.newFiles.length;this.data.newFiles=this.data.newFiles.filter(a=>a.path!==e.path),i!==this.data.newFiles.length&&(await this.saveData(),this.view&&this.view.redraw())};this.handleNewFile=async e=>{if(!this.isInitialized)return;this.data.newFiles.find(a=>a.path===e.path)||(this.data.newFiles.unshift({path:e.path,basename:y.trimExtension(e.name)}),await this.saveData(),await this.pruneLength(),this.view&&this.view.redraw())}}async onload(){console.log("New Files: Loading plugin v"+this.manifest.version),await this.loadData(),(0,o.addIcon)(M,le),this.registerView(m,e=>this.view=new j(e,this,this.data)),this.addCommand({id:"files-list",name:"Open",callback:async()=>{let e;[e]=this.app.workspace.getLeavesOfType(m),e||(e=this.app.workspace.getLeftLeaf(!1),await(e==null?void 0:e.setViewState({type:m}))),e&&this.app.workspace.revealLeaf(e)}}),this.registerHoverLinkSource(m,{display:"Recently Added Files",defaultMod:!0}),this.app.workspace.onLayoutReady(()=>{this.isInitialized=!0,this.registerFileEvents()}),this.addSettingTab(new z(this.app,this))}registerFileEvents(){this.registerEvent(this.app.vault.on("rename",this.handleRename)),this.registerEvent(this.app.vault.on("delete",this.handleDelete)),this.registerEvent(this.app.vault.on("create",e=>{e instanceof o.TFile&&this.handleNewFile(e)})),console.log("NewFilesPlugin: File event listeners registered")}onunload(){}async loadData(){this.data=Object.assign(se,await super.loadData())}async saveData(){await super.saveData(this.data)}async onExternalSettingsChange(){await this.loadData(),await this.pruneLength(),await this.pruneOmittedFiles(),this.view&&this.view.redraw()}onUserEnable(){this.app.workspace.ensureSideLeaf(m,"left",{reveal:!0})}},z=class extends o.PluginSettingTab{constructor(t,e){super(t,e),this.plugin=e}display(){let{containerEl:t}=this;t.empty();let e=document.createDocumentFragment(),i=document.createElement("a");i.href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_Expressions#writing_a_regular_expression_pattern",i.text="MDN - Regular expressions",e.append("RegExp patterns to ignore. One pattern per line. See "),e.append(i),e.append(" for help."),new o.Setting(t).setName("Omitted pathname patterns").setDesc(e).addTextArea(s=>{s.inputEl.setAttr("rows",6),s.setPlaceholder(`^daily/
\\.png$
foobar.*baz`).setValue(this.plugin.data.omittedPaths.join(`
`)),s.inputEl.onblur=r=>{let d=r.target.value;this.plugin.data.omittedPaths=d.split(`
`),this.plugin.pruneOmittedFiles(),this.plugin.view.redraw()}});let a=document.createDocumentFragment();a.append("Frontmatter tags patterns to ignore. One pattern per line"),new o.Setting(t).setName("Omitted frontmatter tags").setDesc(a).addTextArea(s=>{s.inputEl.setAttr("rows",6),s.setPlaceholder(`daily
ignore`).setValue(this.plugin.data.omittedTags.join(`
`)),s.inputEl.onblur=r=>{let d=r.target.value;this.plugin.data.omittedTags=d.split(`
`),this.plugin.pruneOmittedFiles(),this.plugin.view.redraw()}}),new C({containerEl:t,plugin:this.plugin,defaultMaxLength:100}).create(),new S({containerEl:t,plugin:this.plugin,defaultShowExtension:!1}).create(),new _({containerEl:t,plugin:this.plugin}).create(),new A({containerEl:t,plugin:this.plugin}).create(),new k({containerEl:t,plugin:this.plugin}).create()}};

/* nosourcemap */