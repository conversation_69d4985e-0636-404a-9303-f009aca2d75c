---
title: "Dummy Agent Library - Hugging Face Agents Course"
source: "https://huggingface.co/learn/agents-course/unit1/dummy-agent-library"
author:
published:
created: 2025-03-28
description: "We’re on a journey to advance and democratize artificial intelligence through open source and open science."
tags:
  - "clippings"
---
## Serverless API

在 Hugging Face 生态系统中，有一项便捷功能名为 Serverless API，它能让你轻松对众多模型进行推理，无需安装或部署。

import os
from huggingface\_hub import InferenceClient

\## You need a token from https://hf.co/settings/tokens, ensure that you select 'read' as the token type. If you run this on Google Colab, you can set it up in the "settings" tab under "secrets". Make sure to call it "HF\_TOKEN"
os.environ\["HF\_TOKEN"\]="hf\_xxxxxxxxxxxxxx"

client = InferenceClient("meta-llama/Llama-3.2-3B-Instruct")
\# if the outputs for next cells are wrong, the free model may be overloaded. You can also use this public endpoint that contains Llama-3.2-3B-Instruct
\# client = InferenceClient("https://jc26mwg228mkj8dw.us-east-1.aws.endpoints.huggingface.cloud")

output = client.text\_generation(
    "The capital of France is",
    max\_new\_tokens=100,
)

print(output)

如LLM部分所示，若仅执行解码操作，模型仅在预测到 EOS 标记时才会停止，而此处未出现这种情况，因为这是一个对话（聊天）模型，且我们未应用其期望的聊天模板。

如果我们现在添加与所使用的 Llama-3.2-3B-Instruct 模型相关的特殊标记，行为将发生变化，此时它会生成预期的 EOS。

prompt="""<|begin\_of\_text|><|start\_header\_id|>user<|end\_header\_id|>
The capital of France is<|eot\_id|><|start\_header\_id|>assistant<|end\_header\_id|>"""
output = client.text\_generation(
    prompt,
    max\_new\_tokens=100,
)

print(output)

output:

The capital of France is Paris.

使用“chat”方法是应用聊天模板更为便捷可靠的方式：

output = client.chat.completions.create(
    messages=\[
        {"role": "user", "content": "The capital of France is"},
    \],
    stream=False,
    max\_tokens=1024,
)
print(output.choices\[0\].message.content)

output:

Paris.

## Dummy Agent

在前面的章节中，我们了解到代理库的核心在于向系统提示中添加信息。

这个系统提示比我们之前看到的要复杂一些，但它已经包含：

1. **Information about the tools**
2. **Cycle instructions** (Thought → Action → Observation)  
	循环指令（思考 → 行动 → 观察）

Answer the following questions as best you can. You have access to the following tools:

get\_weather: Get the current weather in a given location

The way you use the tools is by specifying a json blob.
Specifically, this json should have an \`action\` key (with the name of the tool to use) and an \`action\_input\` key (with the input to the tool going here).

The only values that should be in the "action" field are:
get\_weather: Get the current weather in a given location, args: {"location": {"type": "string"}}
example use : 

{{
  "action": "get\_weather",
  "action\_input": {"location": "New York"}
}}

ALWAYS use the following format:

Question: the input question you must answer
Thought: you should always think about one action to take. Only one action at a time in this format:
Action:

$JSON\_BLOB (inside markdown cell)

Observation: the result of the action. This Observation is unique, complete, and the source of truth.
... (this Thought/Action/Observation can repeat N times, you should take several steps when needed. The $JSON\_BLOB must be formatted as markdown and only use a SINGLE action at a time.)

You must always end your output with the following format:

Thought: I now know the final answer
Final Answer: the final answer to the original input question

Now begin! Reminder to ALWAYS use the exact characters \`Final Answer:\` when you provide a definitive answer.

由于我们正在运行“text\_generation”方法，需要手动应用提示：

prompt=f"""<|begin\_of\_text|><|start\_header\_id|>system<|end\_header\_id|>
{SYSTEM\_PROMPT}
<|eot\_id|><|start\_header\_id|>user<|end\_header\_id|>
What's the weather in London ?
<|eot\_id|><|start\_header\_id|>assistant<|end\_header\_id|>
"""

我们也可以这样做，这正是 `chat` 方法内部所发生的情况：

messages=\[
    {"role": "system", "content": SYSTEM\_PROMPT},
    {"role": "user", "content": "What's the weather in London ?"},
    \]
from transformers import AutoTokenizer
tokenizer = AutoTokenizer.from\_pretrained("meta-llama/Llama-3.2-3B-Instruct")

tokenizer.apply\_chat\_template(messages, tokenize=False,add\_generation\_prompt=True)

答案是由模型臆想出来的。我们需要停下来实际执行函数！现在让我们停在“观察”这一步，这样就不会臆想出实际的函数响应了。

output = client.text\_generation(
    prompt,
    max\_new\_tokens=200,
    stop=\["Observation:"\] \# Let's stop before any actual function is called
)

print(output)

output:

Thought: I will check the weather in London.
Action:
\`\`\`
{
  "action": "get\_weather",
  "action\_input": {"location": "London"}
}
\`\`\`
Observation:

好多了！现在我们来创建一个模拟的获取天气函数。在实际情况下，你可能会调用一个 API。

\# Dummy function
def get\_weather(location):
    return f"the weather in {location} is sunny with low temperatures. \\n"

get\_weather('London')

让我们将基础提示、函数执行前的完成内容以及函数结果作为观察结果连接起来，并继续生成。

new\_prompt = prompt + output + get\_weather('London')
final\_output = client.text\_generation(
    new\_prompt,
    max\_new\_tokens=200,
)

print(final\_output)

Output:

Final Answer: The weather in London is sunny with low temperatures.