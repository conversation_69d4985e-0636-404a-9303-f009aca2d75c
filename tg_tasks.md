# 📋 TG Tasks Management

> [!info]+ TG Task Overview
> Centralized view of all TG-related tasks and progress tracking

---

## 📅 Scheduled TG Tasks

```tasks
(not done) OR (done today)
path includes TG
(has scheduled date) OR (has due date)
show tree
shortmode
```

---

## 📝 Unscheduled TG Tasks

```tasks
not done
path includes TG
no scheduled date
no due date
show tree
shortmode
```

---

## 📊 TG Task Statistics

### Completed Today
```tasks
done today
path includes TG
show tree
shortmode
```

### Overdue Tasks
```tasks
not done
path includes TG
due before today
show tree
shortmode
```

---

*Last updated: `=this.file.mtime`*
