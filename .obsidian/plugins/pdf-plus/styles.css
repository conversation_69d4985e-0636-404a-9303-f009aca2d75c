/* @settings

name: PDF++
id: pdf-plus
settings:
    - 
        id: pdf-highlight
        title: Highlights
        type: heading
        level: 2
    -
        id: pdf-plus-highlight-opacity
        title: Highlight opacity
        type: variable-number-slider
        min: 0
        max: 1
        step: 0.01
        default: 0.2
    -
        id: pdf-plus-highlight-padding-vertical-em
        title: Highlight padding (top & bottom)
        description: Boldness of highlights (em)
        type: variable-number-slider
        min: 0
        max: 1
        step: 0.01
        default: 0.05
        format: em
    -
        id: pdf-plus-highlight-padding-horizontal-em
        title: Highlight padding (left & right)
        description: Boldness of highlights (em)
        type: variable-number-slider
        min: 0
        max: 1
        step: 0.01
        default: 0.05
        format: em
    - 
        id: pdf-rect
        title: Rectanglular selection
        type: heading
        level: 2
    -
        id: pdf-plus-rect-highlight-opacity
        title: Rectangle highlight opacity
        type: variable-number-slider
        min: 0
        max: 1
        step: 0.01
        default: 1
    -
        id: pdf-plus-rect-highlight-border-width
        title: Rectangle highlight border width (px)
        type: variable-number-slider
        min: 1
        max: 10
        step: 1
        default: 2
        format: px
    -
        id: hover-popover
        title: Hover popovers
        type: heading
        level: 2
    -
        id: pdf-plus-backlink-popover
        title: Backlink popovers
        description: Styles for popovers displayed when hovering over backlinked highlights or rectangular selections in PDF viewer
        type: heading
        level: 3
    -
        id: pdf-plus-backlink-popover-width
        title: Backlink popover width (px)
        type: variable-number-slider
        min: 100
        max: 1000
        step: 10
        default: 450
        format: px
    -
        id: pdf-plus-backlink-popover-height
        title: Backlink popover height (px)
        type: variable-number-slider
        min: 100
        max: 1000
        step: 10
        default: 400
        format: px
    -
        id: pdf-plus-pdf-link-like-popover
        title: PDF internal link popovers
        description: Styles for popovers displayed when hovering over internal links, outline items or thumbnails in PDF viewer
        type: heading
        level: 3
    -
        id: pdf-plus-pdf-link-like-popover-width
        title: PDF internal link popover width (px)
        type: variable-number-slider
        min: 100
        max: 1000
        step: 10
        default: 450
        format: px
    -
        id: pdf-plus-pdf-link-like-popover-height
        title: PDF internal link popover height (px)
        type: variable-number-slider
        min: 100
        max: 1000
        step: 10
        default: 400
        format: px
    - 
        id: pdf-toolbar
        title: PDF toolbars
        type: heading
        level: 2
    - 
        id: hide-pdf-embed-toolbar
        title: Hide toolbar in PDF embeds with a page specified
        type: class-toggle
        default: true
    - 
        id: hide-pdf-toolbar-in-hover-editor
        title: Hide PDF toolbar in Hover Editor
        type: class-toggle
        default: true
    -
        id: pdf-sidebar
        title: PDF sidebars
        type: heading
        level: 2
    -
        id: pdf-plus-sidebar-width
        title: Sidebar width (px)
        type: variable-number-slider
        min: 100
        max: 1000
        step: 10
        default: 140
        format: px
    -
        id: pdf-plus-vim
        title: Vim keybindings
        type: heading
        level: 2
    -
        id: pdf-plus-vim-hin
        title: Hint mode
        type: heading
        level: 3
    - 
        id: pdf-plus-vim-hint-inverted
        title: Inverted color scheme
        type: class-toggle
        default: false
*/

:root {
    --pdf-plus-highlight-padding-default-em: 0.05em;
}

.hide-pdf-embed-toolbar .pdf-embed[src*="#"] .pdf-toolbar,
.hide-pdf-embed-toolbar .popover.hover-popover.hover-editor .pdf-embed[src*="#"] .pdf-toolbar {
    display: none;
}

.hide-pdf-toolbar-in-hover-editor .popover.hover-popover.hover-editor .view-content>.pdf-toolbar {
    display: none !important;
}

/* When hovering over a highlighted text in PDF viewer, highlight the corresponding item in backlink pane */
.backlink-pane .search-result-file-match.hovered-backlink,
.backlink-pane .search-result-file-matches:has(.better-search-views-tree) .better-search-views-file-match.hovered-backlink:not(:hover) {
    background-color: var(--text-selection);
}

.setting-item.no-border,
.pdf-plus-settings.vertical-tab-content .setting-item.no-border {
    border-top: none;

    &.small-padding {
        padding-top: 0;
    }
}

.setting-item-control input.error {
    border-color: var(--background-modifier-error);
}

.setting-item-description.error {
    color: var(--background-modifier-error);
}

.is-mobile,
.is-tablet {
    .pdf-plus-color-palette .pdf-plus-color-palette-item-inner {
        width: calc(var(--swatch-width) * 0.85);
        height: calc(var(--swatch-width) * 0.85);
    }
}

.pdf-plus-color-palette {
    user-select: none;
    -webkit-user-select: none;

    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;

    .pdf-plus-color-palette-item {
        /* Avoid text selections to be cleared when tapping on a color palette item on the mobile app */
        /* https://github.com/RyotaUshio/obsidian-pdf-plus/issues/169 */
        user-select: none;

        .pdf-plus-color-palette-item-inner {
            width: var(--swatch-width);
            height: var(--swatch-width);
            border-radius: 50%;
            border: var(--input-border-width) solid var(--background-modifier-border);
        }
    }

    .pdf-plus-color-palette-status-container {
        padding: var(--size-2-2) var(--size-2-3);
        color: var(--text-muted);
        font-size: var(--font-ui-small);
        text-wrap: nowrap;
    }
}

.menu .menu-item.pdf-plus-color-menu-item {
    padding-left: 0;

    .pdf-plus-color-indicator {
        border-radius: 50%;
        border-width: 0;
        height: var(--size-4-3);
        width: var(--size-4-3);
    }
}

.pdf-toolbar .clickable-icon.is-disabled {
    background-color: inherit;

    &>svg {
        color: var(--text-faint);
    }
}

/* .pdf-page-input, */
.pdf-zoom-level-input {
    width: 6ch;
    text-align: right;
    font-variant-numeric: tabular-nums;
}

.pdf-zoom-level-percent {
    white-space: nowrap;
    margin-right: var(--size-4-1);
    font-size: var(--font-ui-small);
    font-variant-numeric: tabular-nums;
}

.pdf-plus-settings.vertical-tab-content {
    --pdf-plus-settings-header-height: var(--size-4-12);

    padding-top: 0;
    padding-left: 0;
    padding-right: 0;
}

.pdf-plus-settings.vertical-tab-content .header-container {
    position: sticky;
    top: 0;
    z-index: 10;
    height: var(--pdf-plus-settings-header-height);
    line-height: var(--pdf-plus-settings-header-height);
    text-align: center;
    background-color: var(--background-secondary);
    border-bottom: 1px solid var(--divider-color);
    padding: 0 var(--size-4-4);

    overflow-x: scroll;
    overflow-y: hidden;
    white-space: nowrap;

    display: flex;
    justify-content: space-between;
    align-items: center;

    .header {
        line-height: normal;

        .header-title {
            display: none;
        }
    }
}

.pdf-plus-settings.vertical-tab-content .content {
    padding-top: var(--size-4-8);
    padding-bottom: var(--size-4-16);
    padding-left: var(--size-4-12);
    padding-right: var(--size-4-12);
}

.pdf-plus-settings.vertical-tab-content .spacer {
    height: var(--pdf-plus-settings-header-height);
}

.pdf-plus-settings.vertical-tab-content .top-note {
    min-height: var(--pdf-plus-settings-header-height);
    color: var(--text-muted);
    font-size: var(--font-ui-smaller);
}

.pdf-plus-settings .setting-item-description,
.pdf-plus-modal .setting-item-description {
    &>p:first-child {
        margin-top: 0;
    }

    &>p:last-child {
        margin-bottom: 0;
    }
}

.pdf-plus-settings .ignore-split-setting.setting-item {
    padding-top: 0;
}

.annotationLayer .popupContent {
    &>p:first-child {
        margin-top: 0;
    }

    &>p:last-child {
        margin-bottom: 0;
    }
}

.pdf-plus-backlink-highlight-layer {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 2;
    transform-origin: 0 0;
    pointer-events: none;
}

.pdf-plus-backlink-highlight-layer .pdf-plus-backlink {
    position: absolute;
    pointer-events: auto;
}

.pdf-plus-backlink-highlight-layer .pdf-plus-backlink.pdf-plus-backlink-selection {
    box-sizing: content-box;
    cursor: text;
}

.pdf-plus-backlink-highlight-layer .rect-highlight {
    background-color: rgb(var(--text-highlight-bg-rgb));
    border-radius: var(--radius-m);
    opacity: 0.2;
}

body:not(.pdf-plus-backlink-selection-underline) .pdf-plus-backlink-highlight-layer .pdf-plus-backlink.pdf-plus-backlink-selection {
    background-color: rgb(from var(--pdf-plus-color) r g b / var(--pdf-plus-highlight-opacity, 0.2));
    padding: var(--pdf-plus-highlight-padding-vertical-em, var(--pdf-plus-highlight-padding-default-em)) var(--pdf-plus-highlight-padding-horizontal-em, var(--pdf-plus-highlight-padding-default-em));
    margin: calc(var(--pdf-plus-highlight-padding-vertical-em, var(--pdf-plus-highlight-padding-default-em)) * -1) calc(var(--pdf-plus-highlight-padding-horizontal-em, var(--pdf-plus-highlight-padding-default-em)) * -1);
    border-radius: 0.1em;
}

body.pdf-plus-backlink-selection-underline {
    .pdf-plus-backlink-highlight-layer .pdf-plus-backlink.pdf-plus-backlink-selection {
        padding: 0;
        margin: 0;
        opacity: 1.0;
        border-radius: 0;
    }

    .pdf-plus-backlink-highlight-layer[data-main-rotation="0"] .pdf-plus-backlink.pdf-plus-backlink-selection {
        border-bottom: 0.1em solid var(--pdf-plus-color);
    }

    .pdf-plus-backlink-highlight-layer[data-main-rotation="90"] .pdf-plus-backlink.pdf-plus-backlink-selection {
        border-right: 0.1em solid var(--pdf-plus-color);
    }

    .pdf-plus-backlink-highlight-layer[data-main-rotation="180"] .pdf-plus-backlink.pdf-plus-backlink-selection {
        border-top: 0.1em solid var(--pdf-plus-color);
    }

    .pdf-plus-backlink-highlight-layer[data-main-rotation="270"] .pdf-plus-backlink.pdf-plus-backlink-selection {
        border-left: 0.1em solid var(--pdf-plus-color);
    }
}

.pdf-plus-backlink-highlight-layer .pdf-plus-backlink.pdf-plus-backlink-fit-r {
    border: dashed rgb(from var(--pdf-plus-rect-color) r g b / var(--pdf-plus-rect-highlight-opacity, 1)) var(--pdf-plus-rect-highlight-border-width, 2px);
}

.pdf-plus-backlink-icon {
    position: absolute;
    --icon-size: 100%;
}

[data-main-rotation="90"] .pdf-plus-backlink-icon {
    transform: rotate(270deg);
}

[data-main-rotation="180"] .pdf-plus-backlink-icon {
    transform: rotate(180deg);
}

[data-main-rotation="270"] .pdf-plus-backlink-icon {
    transform: rotate(90deg);
}

.pdf-plus-annotation-edit-modal {
    .desc {
        margin-bottom: var(--size-4-4);
    }

    .preview-container {
        background: var(--background-modifier-form-field);
        border: var(--input-border-width) solid var(--background-modifier-border);
        border-radius: var(--input-radius);
        padding: var(--size-4-1) var(--size-4-2);
        text-align: left;

        &>p:first-child {
            margin-top: 0;
        }

        &>p:last-child {
            margin-bottom: 0;
        }
    }

    /* Arrange two children of .setting-item, namely .setting-item-info and .setting-item-contrl, vertically */
    .setting-item:last-child:has(textarea) {
        display: flex;
        flex-direction: column;
        justify-content: left;
        align-items: flex-start;

        .setting-item-control {
            width: 100%;
            padding-top: var(--size-4-2);
        }
    }
}

.popupWrapper {
    --pdf-popup-width: 310px;
}

.pdf-plus-annotation-icon-container {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;

    margin-right: calc(var(--size-4-1) * -1);
    margin-left: calc(var(--size-2-1) * -1);

    .clickable-icon {
        margin-right: 0;
        margin-left: 0;
    }
}

.pdf-plus-draggable .popup {
    cursor: default;
}

#pdf-plus-funding {
    display: flex;
    flex-direction: column;
    justify-content: left;
    align-items: flex-end;

    .setting-item-control {
        padding-top: var(--size-4-4);
    }
}

#pdf-plus-funding-icon-info-container {
    display: flex;
    flex-direction: row;
    /* justify-content: left; */
    align-items: center;
}

#pdf-plus-funding-icon {
    margin-right: var(--size-4-4);
}

.page-label-range:first-of-type {
    margin-top: var(--size-4-4);
}

.page-label-range:not(:first-of-type) {
    margin-top: var(--size-4-9);
}

.pdf-plus-page-label-modal {
    z-index: var(--he-popover-layer-inactive, var(--layer-popover));

    .page-labels-loading {
        color: var(--text-muted);
        text-align: center;
        margin: var(--size-4-4);
    }
}

.pdf-plus-restore-default-modal {
    user-select: text;
}

.pdf-content-container {
    --sidebar-width: var(--pdf-plus-sidebar-width, 140px);
}

body {
    --container-pdf-cropped-width: var(--line-width);
    --container-pdf-cropped-max-width: var(--max-width);
}

.internal-embed.pdf-cropped-embed {
    width: var(--container-pdf-cropped-width);
    max-width: var(--container-pdf-cropped-max-width);

    img {
        cursor: text !important;
        max-width: 100%;
    }
}

.popover.hover-popover>.pdf-cropped-embed img {
    max-height: 100%;
    max-width: 100%;
    height: auto;
}

.pdf-plus-selecting * {
    cursor: crosshair !important;

    .textLayer {
        user-select: none;
    }
}

.pdf-container .pdf-plus-select-box {
    position: absolute;
    z-index: 1000;
    border: dashed var(--background-modifier-border) 2px;
    background-color: hsla(var(--interactive-accent-hsl), 0.15);
}

/* From Obsidian's app.css (.annotationLayer .mod-focused /  .annotationLayer .boundingRect)*/
.pdf-plus-annotation-bounding-rect {
    background-color: rgba(var(--text-highlight-bg-rgb), 0.1);
    border-radius: var(--radius-s);
    box-shadow: var(--shadow-s);
    box-sizing: content-box;
    margin: calc(var(--size-4-2) * -1);
    border: var(--size-4-1) solid rgba(var(--text-highlight-bg-rgb), 0.8);
    padding: var(--size-4-1);
    z-index: 0;
    /* Avoid preventing annotation click */
    position: absolute;
    pointer-events: none;
}

.popover.hover-popover.pdf-plus-backlink-popover {
    --popover-width: var(--pdf-plus-backlink-popover-width, 450px);
    --popover-height: var(--pdf-plus-backlink-popover-height, 400px);
}

.popover.hover-popover.pdf-plus-pdf-link-like-popover {
    --popover-pdf-width: var(--pdf-plus-pdf-link-like-popover-width, 450px);
    --popover-pdf-height: var(--pdf-plus-pdf-link-like-popover-height, 400px);
}

.popover.hover-popover.pdf-plus-bib-popover {
    --popover-width: 400px;
    --pdf-plus-bib-metadata-font-size: var(--font-ui-small);

    .pdf-plus-bib {
        padding: var(--size-4-3);
        font-size: var(--font-ui-medium);

        /* Make text inside citation hover selectable (https://github.com/RyotaUshio/obsidian-pdf-plus/issues/252) */
        -moz-user-select: text;
        -webkit-user-select: text;
        user-select: text;

        .bib-title {
            font-weight: bold;
            padding-bottom: var(--size-4-2);
        }

        .bib-author-year {
            color: var(--text-muted);
            text-align: left;
            font-size: var(--pdf-plus-bib-metadata-font-size);
        }

        .bib-container-title {
            color: var(--text-muted);
            font-style: italic;
            text-align: left;
            font-size: var(--pdf-plus-bib-metadata-font-size);
        }

        .button-container {
            margin-top: 1em;
            display: flex;
            justify-content: space-between;
            gap: var(--size-4-2);
            flex-wrap: wrap;
        }
    }
}

.pdf-plus-vim-command {
    border-top: 1px solid var(--background-modifier-border);
    /* height: var(--size-4-8); */
    font-family: monospace;

    input {
        background: transparent;
        border: none;
        outline: none;
        font-family: monospace;
        white-space: pre;
        width: 90%;
    }
}

.page.pdf-plus-vim-hint-mode [data-pdf-plus-vim-hint]::after {
    content: attr(data-pdf-plus-vim-hint);
    color: var(--pdf-plus-vim-hint-color);
    text-transform: uppercase;
    font-size: var(--font-ui-medium);
    font-weight: bold;
    background-color: var(--pdf-plus-vim-hint-background-color);
    border: var(--size-2-1) solid hsl(var(--accent-h), var(--accent-s), var(--accent-l));
    border-radius: 10%;
    padding: 0 var(--size-2-2);
    position: relative;
    left: 0;
    top: 0;
    z-index: 9999;
}

body {
    --pdf-plus-vim-hint-color: var(--text-normal);
    --pdf-plus-vim-hint-background-color: var(--background-primary);
}

body.pdf-plus-vim-hint-inverted {
    --pdf-plus-vim-hint-color: var(--text-on-accent);
    --pdf-plus-vim-hint-background-color: hsl(var(--accent-h), var(--accent-s), var(--accent-l));
}
