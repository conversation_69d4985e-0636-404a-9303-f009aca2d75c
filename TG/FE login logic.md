## FE login logic
1. if `!isAuthenticated` 
	- login
		- invitation
		- organization: get from url search or `idToken?.metadata?.last_login_org`
		- redirect_uri
2. if `isAuthenticated && !idToken`
	- fetchIdToken
		- if `!idToken?.org_id`
			- if `idToken?.metadata?.last_login_org`
				- login(idToken?.metadata?.last_login_org)
			- else
				- fetch orgs from `/users/me/orgs`
					- if has org: login with it
					- else: create a log and login with it
3. if  `idToken`
	- set idToken to cookie and sessionStorage
	- if `idToken.org_id`
		- fetch `/users/me/orgs` and setCurrentOrg
		- fetch `/users/me` and setUserInfo
		- fetch a specific url
			- if got 401, call `login`