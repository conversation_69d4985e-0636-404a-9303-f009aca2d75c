---
title: Introduction to Identity and Access Management (IAM)
source: https://auth0.com/docs/get-started/identity-fundamentals/identity-and-access-management#authentication-and-authorization-standards
author:
  - "[[auth0]]"
published: 
created: 2025-03-21
description: Basic overview of the computer software field of identity and access management, written for those new to the space
tags:
  - clippings
---
### Identity providers

身份提供者创建、维护和管理身份信息，可以向其他应用程序提供身份验证服务。例如，谷歌账户是一个身份提供者。它们存储账户信息，例如您的用户名、全名、职位和电子邮件地址。Slate 在线杂志允许您使用谷歌（或另一个身份提供者）登录，而不是重新输入和存储您的信息。

身份提供者不会将您的身份验证凭据与依赖它们的应用程序共享。以 Slate 为例，它永远不会看到您的 Google 密码。Google 只让 Slate 知道您已经证明了您的身份。

其他身份提供者包括社交媒体（如 Facebook 或 LinkedIn）、企业（如微软 Active Directory）和法律身份提供者（如瑞典 BankID）。

### Authentication factors

| Factor type | Examples |
| --- | --- |
| Knowledge (something you know) | Pin, password |
| Possession (something you have) | Mobile phone, encryption key device |
| Inherence (something you are) | Fingerprint, facial recognition, iris scan |

IAM 系统需要一个或多个身份验证因素来验证身份。

### Authentication and authorization standards

Authentication and authorization standards are open specifications and protocols that provide guidance on how to:  
身份验证和授权标准是开放的规范和协议，它们提供了关于如何的指导：

- Design IAM systems to manage identity
- Move personal data securely
- Decide who can access resources