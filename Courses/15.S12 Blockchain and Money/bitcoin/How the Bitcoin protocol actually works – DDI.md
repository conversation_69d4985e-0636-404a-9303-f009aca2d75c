---
title: "How the Bitcoin protocol actually works – DDI"
source: "https://michaelnielsen.org/ddi/how-the-bitcoin-protocol-actually-works/"
author:
published:
created: 2025-01-25
description:
tags:
  - "clippings"
---
Many thousands of articles have been written purporting to explain Bitcoin, the online, peer-to-peer currency. Most of those articles give a hand-wavy account of the underlying cryptographic protocol, omitting many details. Even those articles which delve deeper often gloss over crucial points. My aim in this post is to explain the major ideas behind the Bitcoin protocol in a clear, easily comprehensible way. We’ll start from first principles, build up to a broad theoretical understanding of how the protocol works, and then dig down into the nitty-gritty, examining the raw data in a Bitcoin transaction.Many thousands of articles have been written purporting to explain Bitcoin, the online, peer-to-peer currency. Most of those articles give a hand-wavy account of the underlying cryptographic protocol, omitting many details. Even those articles which delve deeper often gloss over crucial points. My aim in this post is to explain the major ideas behind the Bitcoin protocol in a clear, easily comprehensible way. We’ll start from first principles, build up to a broad theoretical understanding of how the protocol works, and then dig down into the nitty-gritty, examining the raw data in a Bitcoin transaction.成千上万的文章都声称要解释比特币，这种在线的对等货币。大多数此类文章对底层加密协议的描述都很笼统，省略了很多细节。即使是那些深入探讨的文章也常常掩盖关键要点。我在这篇文章中的目的是以清晰、易于理解的方式解释比特币协议背后的主要思想。我们将从基本原理开始，建立对该协议如何工作的广泛理论理解，然后深入研究细节，检查比特币交易中的原始数据。

Understanding the protocol in this detailed way is hard work. It is tempting instead to take Bitcoin as given, and to engage in speculation about how to get rich with Bitcoin, whether Bitcoin is a bubble, whether Bitcoin might one day mean the end of taxation, and so on. That’s fun, but severely limits your understanding. Understanding the details of the Bitcoin protocol opens up otherwise inaccessible vistas. In particular, it’s the basis for understanding Bitcoin’s built-in scripting language, which makes it possible to use Bitcoin to create new types of financial instruments, such as [smart contracts](http://szabo.best.vwh.net/formalize.html). New financial instruments can, in turn, be used to create new markets and to enable new forms of collective human behaviour. Talk about fun!Understanding the protocol in this detailed way is hard work. It is tempting instead to take Bitcoin as given, and to engage in speculation about how to get rich with Bitcoin, whether Bitcoin is a bubble, whether Bitcoin might one day mean the end of taxation, and so on. That’s fun, but severely limits your understanding. Understanding the details of the Bitcoin protocol opens up otherwise inaccessible vistas. In particular, it’s the basis for understanding Bitcoin’s built-in scripting language, which makes it possible to use Bitcoin to create new types of financial instruments, such as [smart contracts](http://szabo.best.vwh.net/formalize.html). New financial instruments can, in turn, be used to create new markets and to enable new forms of collective human behaviour. Talk about fun!以这种详细的方式理解协议是一项艰巨的工作。相反，人们很容易把比特币视为既定事实，并进行关于如何通过比特币致富、比特币是否是泡沫、比特币是否有一天可能意味着税收的终结等等的猜测。这很有趣，但会严重限制你的理解。理解比特币协议的细节会开辟出原本无法触及的视野。特别是，它是理解比特币内置脚本语言的基础，这使得使用比特币创建新型金融工具成为可能，例如“智能合约”。反过来，新的金融工具可以用来创建新的市场，并实现新的人类集体行为形式。这才叫有趣呢！

I’ll describe Bitcoin scripting and concepts such as smart contracts in future posts. This post concentrates on explaining the nuts-and-bolts of the Bitcoin protocol. To understand the post, you need to be comfortable with [public key cryptography](http://en.wikipedia.org/wiki/Public-key_cryptography), and with the closely related idea of [digital signatures](https://en.wikipedia.org/wiki/Digital_signature). I’ll also assume you’re familiar with [cryptographic hashing](https://en.wikipedia.org/wiki/Cryptographic_hash_function). None of this is especially difficult. The basic ideas can be taught in freshman university mathematics or computer science classes. The ideas are beautiful, so if you’re not familiar with them, I recommend taking a few hours to get familiar.I’ll describe Bitcoin scripting and concepts such as smart contracts in future posts. This post concentrates on explaining the nuts-and-bolts of the Bitcoin protocol. To understand the post, you need to be comfortable with [public key cryptography](http://en.wikipedia.org/wiki/Public-key_cryptography), and with the closely related idea of [digital signatures](https://en.wikipedia.org/wiki/Digital_signature). I’ll also assume you’re familiar with [cryptographic hashing](https://en.wikipedia.org/wiki/Cryptographic_hash_function). None of this is especially difficult. The basic ideas can be taught in freshman university mathematics or computer science classes. The ideas are beautiful, so if you’re not familiar with them, I recommend taking a few hours to get familiar.我将在未来的文章中描述比特币脚本以及智能合约等概念。本文集中解释比特币协议的具体细节。要理解本文，你需要熟悉公钥密码学，以及与之密切相关的数字签名的概念。我还假定你熟悉加密哈希。这些都不是特别困难。基本概念可以在大学一年级的数学或计算机科学课程中教授。这些概念很美妙，所以如果你不熟悉它们，我建议花几个小时来熟悉一下。

It may seem surprising that Bitcoin’s basis is cryptography. Isn’t Bitcoin a currency, not a way of sending secret messages? In fact, the problems Bitcoin needs to solve are largely about securing transactions — making sure people can’t steal from one another, or impersonate one another, and so on. In the world of atoms we achieve security with devices such as locks, safes, signatures, and bank vaults. In the world of bits we achieve this kind of security with cryptography. And that’s why Bitcoin is at heart a cryptographic protocol.It may seem surprising that Bitcoin’s basis is cryptography. Isn’t Bitcoin a currency, not a way of sending secret messages? In fact, the problems Bitcoin needs to solve are largely about securing transactions — making sure people can’t steal from one another, or impersonate one another, and so on. In the world of atoms we achieve security with devices such as locks, safes, signatures, and bank vaults. In the world of bits we achieve this kind of security with cryptography. And that’s why Bitcoin is at heart a cryptographic protocol.比特币的基础是密码学，这可能看起来令人惊讶。比特币不是一种货币吗，而不是一种发送秘密信息的方式？事实上，比特币需要解决的问题主要是关于确保交易安全——确保人们不能相互偷窃或冒充他人等等。在原子世界中，我们用锁、保险箱、签名和银行金库等设备来实现安全。在比特世界中，我们用密码学来实现这种安全。这就是为什么比特币本质上是一种密码协议。

My strategy in the post is to build Bitcoin up in stages. I’ll begin by explaining a very simple digital currency, based on ideas that are almost obvious. We’ll call that currency *Infocoin*, to distinguish it from Bitcoin. Of course, our first version of Infocoin will have many deficiencies, and so we’ll go through several iterations of Infocoin, with each iteration introducing just one or two simple new ideas. After several such iterations, we’ll arrive at the full Bitcoin protocol. We will have reinvented Bitcoin!My strategy in the post is to build Bitcoin up in stages. I’ll begin by explaining a very simple digital currency, based on ideas that are almost obvious. We’ll call that currency *Infocoin*, to distinguish it from Bitcoin. Of course, our first version of Infocoin will have many deficiencies, and so we’ll go through several iterations of Infocoin, with each iteration introducing just one or two simple new ideas. After several such iterations, we’ll arrive at the full Bitcoin protocol. We will have reinvented Bitcoin!我的这篇文章中的策略是分阶段构建比特币。我将首先解释一种非常简单的数字货币，它基于几乎显而易见的想法。我们将这种货币称为*信息币*，以区别于比特币。当然，我们的第一个版本的信息币会有很多不足之处，所以我们将经历信息币的几个迭代版本，每个迭代版本只引入一两个简单的新想法。经过几次这样的迭代后，我们将得到完整的比特币协议。我们将重新发明比特币！

This strategy is slower than if I explained the entire Bitcoin protocol in one shot. But while you can understand the mechanics of Bitcoin through such a one-shot explanation, it would be difficult to understand *why* Bitcoin is designed the way it is. The advantage of the slower iterative explanation is that it gives us a much sharper understanding of each element of Bitcoin.This strategy is slower than if I explained the entire Bitcoin protocol in one shot. But while you can understand the mechanics of Bitcoin through such a one-shot explanation, it would be difficult to understand *why* Bitcoin is designed the way it is. The advantage of the slower iterative explanation is that it gives us a much sharper understanding of each element of Bitcoin.这种策略比我一次性解释整个比特币协议要慢。但是，虽然你可以通过这样的一次性解释理解比特币的机制，但很难理解比特币为什么要这样设计。较慢的迭代解释的优势在于，它让我们对比特币的每个元素有更清晰的理解。

Finally, I should mention that I’m a relative newcomer to Bitcoin. I’ve been following it loosely since 2011 (and cryptocurrencies since the late 1990s), but only got seriously into the details of the Bitcoin protocol earlier this year. So I’d certainly appreciate corrections of any misapprehensions on my part. Also in the post I’ve included a number of “problems for the author” – notes to myself about questions that came up during the writing. You may find these interesting, but you can also skip them entirely without losing track of the main text.Finally, I should mention that I’m a relative newcomer to Bitcoin. I’ve been following it loosely since 2011 (and cryptocurrencies since the late 1990s), but only got seriously into the details of the Bitcoin protocol earlier this year. So I’d certainly appreciate corrections of any misapprehensions on my part. Also in the post I’ve included a number of “problems for the author” – notes to myself about questions that came up during the writing. You may find these interesting, but you can also skip them entirely without losing track of the main text.最后，我应该提到，我是比特币领域的相对新手。我从 2011 年起就一直在松散地关注它（并且自 20 世纪 90 年代末以来就一直在关注加密货币），但直到今年早些时候才认真研究比特币协议的细节。所以，如果我有任何误解，我肯定会感谢大家的纠正。此外，在这篇文章中，我还列出了一些“作者的问题”——这是我在写作过程中想到的问题的笔记。你可能会觉得这些很有趣，但你也可以完全跳过它们，而不会失去对主要文本的理解。

### First steps: a signed letter of intentFirst steps: a signed letter of intent第一步：一份签署的意向书。

So how can we design a digital currency? So how can we design a digital currency? 那么我们如何设计一种数字货币呢？

On the face of it, a digital currency sounds impossible. Suppose some person – let’s call her Alice – has some digital money which she wants to spend. If Alice can use a string of bits as money, how can we prevent her from using the same bit string over and over, thus minting an infinite supply of money? Or, if we can somehow solve that problem, how can we prevent someone else forging such a string of bits, and using that to steal from Alice?On the face of it, a digital currency sounds impossible. Suppose some person – let’s call her Alice – has some digital money which she wants to spend. If Alice can use a string of bits as money, how can we prevent her from using the same bit string over and over, thus minting an infinite supply of money? Or, if we can somehow solve that problem, how can we prevent someone else forging such a string of bits, and using that to steal from Alice?表面上看，数字货币似乎是不可能存在的。假设某人——我们称她为爱丽丝——有一些数字货币想要花掉。如果爱丽丝可以使用一串比特作为货币，我们如何防止她一遍又一遍地使用相同的比特串，从而制造出无限的货币供应呢？或者，如果我们能以某种方式解决这个问题，我们又如何防止其他人伪造这样一串比特，并利用它来窃取爱丽丝的财产呢？

These are just two of the many problems that must be overcome in order to use information as money.These are just two of the many problems that must be overcome in order to use information as money.这些只是为了将信息用作货币而必须克服的众多问题中的两个。

As a first version of Infocoin, let’s find a way that Alice can use a string of bits as a (very primitive and incomplete) form of money, in a way that gives her at least some protection against forgery. Suppose Alice wants to give another person, Bob, an infocoin. To do this, Alice writes down the message “I, Alice, am giving Bob one infocoin”. She then digitally signs the message using a private cryptographic key, and announces the signed string of bits to the entire world.As a first version of Infocoin, let’s find a way that Alice can use a string of bits as a (very primitive and incomplete) form of money, in a way that gives her at least some protection against forgery. Suppose Alice wants to give another person, Bob, an infocoin. To do this, Alice writes down the message “I, Alice, am giving Bob one infocoin”. She then digitally signs the message using a private cryptographic key, and announces the signed string of bits to the entire world.作为 Infocoin 的第一个版本，让我们找到一种方法，使爱丽丝可以使用一串比特作为一种（非常原始且不完整的）货币形式，并且这种方式至少能为她提供一定程度的防伪造保护。假设爱丽丝想给另一个人鲍勃一个 Infocoin。为此，爱丽丝写下“我，爱丽丝，正在给鲍勃一个 Infocoin”这句话。然后，她使用私钥对这条消息进行数字签名，并向全世界公布这个经过签名的比特串。

(By the way, I’m using capitalized “Infocoin” to refer to the protocol and general concept, and lowercase “infocoin” to refer to specific denominations of the currency. A similar useage is common, though not universal, in the Bitcoin world.)(By the way, I’m using capitalized “Infocoin” to refer to the protocol and general concept, and lowercase “infocoin” to refer to specific denominations of the currency. A similar useage is common, though not universal, in the Bitcoin world.)（顺便说一下，我使用大写的“Infocoin”来指代协议和一般概念，使用小写的“infocoin”来指代特定面额的货币。在比特币世界中，类似的用法很常见，尽管不是普遍的。）

This isn’t terribly impressive as a prototype digital currency! But it does have some virtues. Anyone in the world (including Bob) can use Alice’s public key to verify that Alice really was the person who signed the message “I, Alice, am giving Bob one infocoin”. No-one else could have created that bit string, and so Alice can’t turn around and say “No, I didn’t mean to give Bob an infocoin”. So the protocol establishes that Alice truly intends to give Bob one infocoin. The same fact – no-one else could compose such a signed message – also gives Alice some limited protection from forgery. Of course, *after* Alice has published her message it’s possible for other people to duplicate the message, so in that sense forgery is possible. But it’s not possible from scratch. These two properties – establishment of intent on Alice’s part, and the limited protection from forgery – are genuinely notable features of this protocol.This isn’t terribly impressive as a prototype digital currency! But it does have some virtues. Anyone in the world (including Bob) can use Alice’s public key to verify that Alice really was the person who signed the message “I, Alice, am giving Bob one infocoin”. No-one else could have created that bit string, and so Alice can’t turn around and say “No, I didn’t mean to give Bob an infocoin”. So the protocol establishes that Alice truly intends to give Bob one infocoin. The same fact – no-one else could compose such a signed message – also gives Alice some limited protection from forgery. Of course, *after* Alice has published her message it’s possible for other people to duplicate the message, so in that sense forgery is possible. But it’s not possible from scratch. These two properties – establishment of intent on Alice’s part, and the limited protection from forgery – are genuinely notable features of this protocol.作为一种原型数字货币，这并不令人印象深刻！但它确实有一些优点。世界上任何人（包括鲍勃）都可以使用爱丽丝的公钥来验证爱丽丝确实是签署了“我，爱丽丝，给鲍勃一个信息币”这条消息的人。没有其他人能够创建那个位字符串，所以爱丽丝不能反悔说“不，我不是想给鲍勃一个信息币”。因此，该协议确定爱丽丝确实有意给鲍勃一个信息币。同样的事实——没有其他人能够撰写这样一条签名消息——也给爱丽丝提供了一些有限的防伪保护。当然，在爱丽丝发布了她的消息之后，其他人有可能复制这条消息，所以从这个意义上说，伪造是可能的。但从零开始是不可能的。这两个特性——确定爱丽丝的意图，以及有限的防伪保护——是这个协议真正值得注意的特点。

I haven’t (quite) said exactly what digital money *is* in this protocol. To make this explicit: it’s just the message itself, i.e., the string of bits representing the digitally signed message “I, Alice, am giving Bob one infocoin”. Later protocols will be similar, in that all our forms of digital money will be just more and more elaborate messages \[1\].I haven’t (quite) said exactly what digital money *is* in this protocol. To make this explicit: it’s just the message itself, i.e., the string of bits representing the digitally signed message “I, Alice, am giving Bob one infocoin”. Later protocols will be similar, in that all our forms of digital money will be just more and more elaborate messages \[1\].在这个协议中，我并没有（完全）确切说明数字货币到底是什么。明确一下：它只是消息本身，即代表数字签名消息“我，爱丽丝，正在给鲍勃一个信息币”的比特字符串。后续的协议也会类似，因为我们所有形式的数字货币都将只是越来越复杂的消息\[1\]。

### Using serial numbers to make coins uniquely identifiableUsing serial numbers to make coins uniquely identifiable使用序列号使硬币具有唯一可识别性。

A problem with the first version of Infocoin is that Alice could keep sending Bob the same signed message over and over. Suppose Bob receives ten copies of the signed message “I, Alice, am giving Bob one infocoin”. Does that mean Alice sent Bob ten *different* infocoins? Was her message accidentally duplicated? Perhaps she was trying to trick Bob into believing that she had given him ten different infocoins, when the message only proves to the world that she intends to transfer one infocoin.A problem with the first version of Infocoin is that Alice could keep sending Bob the same signed message over and over. Suppose Bob receives ten copies of the signed message “I, Alice, am giving Bob one infocoin”. Does that mean Alice sent Bob ten *different* infocoins? Was her message accidentally duplicated? Perhaps she was trying to trick Bob into believing that she had given him ten different infocoins, when the message only proves to the world that she intends to transfer one infocoin.Infocoin 第一版的一个问题是，Alice 可以一遍又一遍地向 Bob 发送相同的签名消息。假设 Bob 收到十份签名消息“我，Alice，正在给 Bob 一个 Infocoin”。这是否意味着 Alice 给 Bob 发送了十个不同的 Infocoin？她的消息是意外重复了吗？也许她试图欺骗 Bob，让他相信她给了他十个不同的 Infocoin，而该消息只是向世界证明她打算转移一个 Infocoin。

What we’d like is a way of making infocoins unique. They need a label or serial number. Alice would sign the message “I, Alice, am giving Bob one infocoin, with serial number 8740348”. Then, later, Alice could sign the message “I, Alice, am giving Bob one infocoin, with serial number 8770431”, and Bob (and everyone else) would know that a different infocoin was being transferred.What we’d like is a way of making infocoins unique. They need a label or serial number. Alice would sign the message “I, Alice, am giving Bob one infocoin, with serial number 8740348”. Then, later, Alice could sign the message “I, Alice, am giving Bob one infocoin, with serial number 8770431”, and Bob (and everyone else) would know that a different infocoin was being transferred.我们想要的是一种使信息币独一无二的方法。它们需要一个标签或序列号。爱丽丝会在消息“我，爱丽丝，给鲍勃一个信息币，序列号为 8740348”上签名。然后，稍后，爱丽丝可以在消息“我，爱丽丝，给鲍勃一个信息币，序列号为 8770431”上签名，鲍勃（和其他所有人）就会知道正在转移的是一个不同的信息币。

To make this scheme work we need a trusted source of serial numbers for the infocoins. One way to create such a source is to introduce a *bank*. This bank would provide serial numbers for infocoins, keep track of who has which infocoins, and verify that transactions really are legitimate,To make this scheme work we need a trusted source of serial numbers for the infocoins. One way to create such a source is to introduce a *bank*. This bank would provide serial numbers for infocoins, keep track of who has which infocoins, and verify that transactions really are legitimate,为了使这个方案奏效，我们需要一个可靠的信息币序列号来源。创建这样一个来源的一种方法是引入一家“银行”。这家银行将为信息币提供序列号，跟踪谁拥有哪些信息币，并验证交易是否真的合法。

In more detail, let’s suppose Alice goes into the bank, and says “I want to withdraw one infocoin from my account”. The bank reduces her account balance by one infocoin, and assigns her a new, never-before used serial number, let’s say 1234567. Then, when Alice wants to transfer her infocoin to Bob, she signs the message “I, Alice, am giving Bob one infocoin, with serial number 1234567”. But Bob doesn’t just accept the infocoin. Instead, he contacts the bank, and verifies that: (a) the infocoin with that serial number belongs to Alice; and (b) Alice hasn’t already spent the infocoin. If both those things are true, then Bob tells the bank he wants to accept the infocoin, and the bank updates their records to show that the infocoin with that serial number is now in Bob’s possession, and no longer belongs to Alice.In more detail, let’s suppose Alice goes into the bank, and says “I want to withdraw one infocoin from my account”. The bank reduces her account balance by one infocoin, and assigns her a new, never-before used serial number, let’s say 1234567. Then, when Alice wants to transfer her infocoin to Bob, she signs the message “I, Alice, am giving Bob one infocoin, with serial number 1234567”. But Bob doesn’t just accept the infocoin. Instead, he contacts the bank, and verifies that: (a) the infocoin with that serial number belongs to Alice; and (b) Alice hasn’t already spent the infocoin. If both those things are true, then Bob tells the bank he wants to accept the infocoin, and the bank updates their records to show that the infocoin with that serial number is now in Bob’s possession, and no longer belongs to Alice.更详细地说，假设爱丽丝走进银行，说：“我想从我的账户中取出一个信息币。”银行将她的账户余额减少一个信息币，并分配给她一个新的、从未使用过的序列号，比如说 1234567。然后，当爱丽丝想把她的信息币转给鲍勃时，她在消息“我，爱丽丝，正在给鲍勃一个信息币，序列号为 1234567”上签名。但是鲍勃并不只是接受这个信息币。相反，他联系银行，并核实：（a）那个序列号的信息币属于爱丽丝；以及（b）爱丽丝还没有花掉这个信息币。如果这两件事都是真的，那么鲍勃告诉银行他想接受这个信息币，银行更新他们的记录，以表明那个序列号的信息币现在在鲍勃的手中，不再属于爱丽丝。

### Making everyone collectively the bankMaking everyone collectively the bank让每个人共同成为银行。

This last solution looks pretty promising. However, it turns out that we can do something much more ambitious. We can eliminate the bank entirely from the protocol. This changes the nature of the currency considerably. It means that there is no longer any single organization in charge of the currency. And when you think about the enormous power a central bank has – control over the money supply – that’s a pretty huge change.This last solution looks pretty promising. However, it turns out that we can do something much more ambitious. We can eliminate the bank entirely from the protocol. This changes the nature of the currency considerably. It means that there is no longer any single organization in charge of the currency. And when you think about the enormous power a central bank has – control over the money supply – that’s a pretty huge change.最后这个解决方案看起来很有希望。然而，事实证明我们可以做一些更有雄心的事情。我们可以完全将银行从协议中剔除。这极大地改变了货币的性质。这意味着不再有任何单一组织负责这种货币。当你想到中央银行所拥有的巨大权力——对货币供应的控制——这是一个相当巨大的变化。

The idea is to make it so *everyone* (collectively) is the bank. In particular, we’ll assume that everyone using Infocoin keeps a complete record of which infocoins belong to which person. You can think of this as a shared public ledger showing all Infocoin transactions. We’ll call this ledger the *block chain*, since that’s what the complete record will be called in Bitcoin, once we get to it.The idea is to make it so *everyone* (collectively) is the bank. In particular, we’ll assume that everyone using Infocoin keeps a complete record of which infocoins belong to which person. You can think of this as a shared public ledger showing all Infocoin transactions. We’ll call this ledger the *block chain*, since that’s what the complete record will be called in Bitcoin, once we get to it.这个想法是让每个人（共同地）都成为银行。特别是，我们假设每个使用信息币的人都完整地记录着哪些信息币属于哪些人。你可以把这看作是一个显示所有信息币交易的共享公共账本。我们将这个账本称为区块链，因为一旦我们讲到比特币时，完整的记录就会被称为区块链。

Now, suppose Alice wants to transfer an infocoin to Bob. She signs the message “I, Alice, am giving Bob one infocoin, with serial number 1234567”, and gives the signed message to Bob. Bob can use his copy of the block chain to check that, indeed, the infocoin is Alice’s to give. If that checks out then he broadcasts both Alice’s message and his acceptance of the transaction to the entire network, and everyone updates their copy of the block chain.Now, suppose Alice wants to transfer an infocoin to Bob. She signs the message “I, Alice, am giving Bob one infocoin, with serial number 1234567”, and gives the signed message to Bob. Bob can use his copy of the block chain to check that, indeed, the infocoin is Alice’s to give. If that checks out then he broadcasts both Alice’s message and his acceptance of the transaction to the entire network, and everyone updates their copy of the block chain.现在，假设爱丽丝想给鲍勃转一个信息币。她在消息“我，爱丽丝，给鲍勃一个信息币，序列号为 1234567”上签名，然后把签过名的消息给鲍勃。鲍勃可以用他那份区块链副本检查，确认这个信息币确实是爱丽丝有权给予的。如果检查通过，那么他向整个网络广播爱丽丝的消息和他对这笔交易的接受，并且每个人都更新他们那份区块链副本。

We still have the “where do serial number come from” problem, but that turns out to be pretty easy to solve, and so I will defer it to later, in the discussion of Bitcoin. A more challenging problem is that this protocol allows Alice to cheat by double spending her infocoin. She sends the signed message “I, Alice, am giving Bob one infocoin, with serial number 1234567″ to Bob, and the message”I, Alice, am giving Charlie one infocoin, with \[the same\] serial number 1234567” to Charlie. Both Bob and Charlie use their copy of the block chain to verify that the infocoin is Alice’s to spend. Provided they do this verification at nearly the same time (before they’ve had a chance to hear from one another), both will find that, yes, the block chain shows the coin belongs to Alice. And so they will both accept the transaction, and also broadcast their acceptance of the transaction. Now there’s a problem. How should other people update their block chains? There may be no easy way to achieve a consistent shared ledger of transactions. And even if everyone can agree on a consistent way to update their block chains, there is still the problem that either Bob or Charlie will be cheated.We still have the “where do serial number come from” problem, but that turns out to be pretty easy to solve, and so I will defer it to later, in the discussion of Bitcoin. A more challenging problem is that this protocol allows Alice to cheat by double spending her infocoin. She sends the signed message “I, Alice, am giving Bob one infocoin, with serial number 1234567″ to Bob, and the message”I, Alice, am giving Charlie one infocoin, with \[the same\] serial number 1234567” to Charlie. Both Bob and Charlie use their copy of the block chain to verify that the infocoin is Alice’s to spend. Provided they do this verification at nearly the same time (before they’ve had a chance to hear from one another), both will find that, yes, the block chain shows the coin belongs to Alice. And so they will both accept the transaction, and also broadcast their acceptance of the transaction. Now there’s a problem. How should other people update their block chains? There may be no easy way to achieve a consistent shared ledger of transactions. And even if everyone can agree on a consistent way to update their block chains, there is still the problem that either Bob or Charlie will be cheated.我们仍然有“序列号从哪里来”的问题，但事实证明这个问题很容易解决，所以我将在后面讨论比特币时再提及它。一个更具挑战性的问题是，这个协议允许爱丽丝通过双重花费她的信息币来作弊。她将签名消息“我，爱丽丝，给鲍勃一个信息币，序列号为 1234567”发送给鲍勃，并将消息“我，爱丽丝，给查理一个信息币，序列号为\[相同的\]1234567”发送给查理。鲍勃和查理都使用他们的区块链副本来验证这个信息币是爱丽丝可以花费的。如果他们几乎在同一时间进行这种验证（在他们有机会相互交流之前），他们都会发现，是的，区块链显示这个币属于爱丽丝。因此他们都会接受这笔交易，并广播他们对这笔交易的接受。现在有一个问题。其他人应该如何更新他们的区块链呢？可能没有简单的方法来实现一致的交易共享账本。即使每个人都能就一致的更新区块链的方法达成一致，仍然存在鲍勃或查理将被欺骗的问题。

At first glance double spending seems difficult for Alice to pull off. After all, if Alice sends the message first to Bob, then Bob can verify the message, and tell everyone else in the network (including Charlie) to update their block chain. Once that has happened, Charlie would no longer be fooled by Alice. So there is most likely only a brief period of time in which Alice can double spend. However, it’s obviously undesirable to have any such a period of time. Worse, there are techniques Alice could use to make that period longer. She could, for example, use network traffic analysis to find times when Bob and Charlie are likely to have a lot of latency in communication. Or perhaps she could do something to deliberately disrupt their communications. If she can slow communication even a little that makes her task of double spending much easier.At first glance double spending seems difficult for Alice to pull off. After all, if Alice sends the message first to Bob, then Bob can verify the message, and tell everyone else in the network (including Charlie) to update their block chain. Once that has happened, Charlie would no longer be fooled by Alice. So there is most likely only a brief period of time in which Alice can double spend. However, it’s obviously undesirable to have any such a period of time. Worse, there are techniques Alice could use to make that period longer. She could, for example, use network traffic analysis to find times when Bob and Charlie are likely to have a lot of latency in communication. Or perhaps she could do something to deliberately disrupt their communications. If she can slow communication even a little that makes her task of double spending much easier.乍一看，爱丽丝似乎很难进行双重支付。毕竟，如果爱丽丝先将消息发送给鲍勃，那么鲍勃可以验证该消息，并告诉网络中的其他所有人（包括查理）更新他们的区块链。一旦发生这种情况，查理就不会再被爱丽丝欺骗了。所以爱丽丝很可能只有很短的一段时间可以进行双重支付。然而，有这样一段时间显然是不可取的。更糟糕的是，爱丽丝可以使用一些技术来延长这段时间。例如，她可以使用网络流量分析来找到鲍勃和查理之间通信可能有很大延迟的时候。或者她可能会采取一些措施故意破坏他们的通信。如果她能稍微减缓通信速度，那么她进行双重支付的任务就会容易得多。

How can we address the problem of double spending? The obvious solution is that when Alice sends Bob an infocoin, Bob shouldn’t try to verify the transaction alone. Rather, he should broadcast the possible transaction to the entire network of Infocoin users, and ask them to help determine whether the transaction is legitimate. If they collectively decide that the transaction is okay, then Bob can accept the infocoin, and everyone will update their block chain. This type of protocol can help prevent double spending, since if Alice tries to spend her infocoin with both Bob and Charlie, other people on the network will notice, and network users will tell both Bob and Charlie that there is a problem with the transaction, and the transaction shouldn’t go through.How can we address the problem of double spending? The obvious solution is that when Alice sends Bob an infocoin, Bob shouldn’t try to verify the transaction alone. Rather, he should broadcast the possible transaction to the entire network of Infocoin users, and ask them to help determine whether the transaction is legitimate. If they collectively decide that the transaction is okay, then Bob can accept the infocoin, and everyone will update their block chain. This type of protocol can help prevent double spending, since if Alice tries to spend her infocoin with both Bob and Charlie, other people on the network will notice, and network users will tell both Bob and Charlie that there is a problem with the transaction, and the transaction shouldn’t go through.我们如何解决双重支付问题呢？显而易见的解决方案是，当爱丽丝向鲍勃发送信息币时，鲍勃不应该单独尝试验证交易。相反，他应该向整个信息币用户网络广播可能的交易，并请求他们帮助确定交易是否合法。如果他们共同决定交易没问题，那么鲍勃可以接受信息币，并且每个人都会更新他们的区块链。这种类型的协议可以帮助防止双重支付，因为如果爱丽丝试图将她的信息币同时支付给鲍勃和查理，网络上的其他人会注意到，网络用户会告诉鲍勃和查理交易存在问题，并且交易不应该进行。

In more detail, let’s suppose Alice wants to give Bob an infocoin. As before, she signs the message “I, Alice, am giving Bob one infocoin, with serial number 1234567”, and gives the signed message to Bob. Also as before, Bob does a sanity check, using his copy of the block chain to check that, indeed, the coin currently belongs to Alice. But at that point the protocol is modified. Bob doesn’t just go ahead and accept the transaction. Instead, he broadcasts Alice’s message to the entire network. Other members of the network check to see whether Alice owns that infocoin. If so, they broadcast the message “Yes, Alice owns infocoin 1234567, it can now be transferred to Bob.” Once enough people have broadcast that message, everyone updates their block chain to show that infocoin 1234567 now belongs to Bob, and the transaction is complete.In more detail, let’s suppose Alice wants to give Bob an infocoin. As before, she signs the message “I, Alice, am giving Bob one infocoin, with serial number 1234567”, and gives the signed message to Bob. Also as before, Bob does a sanity check, using his copy of the block chain to check that, indeed, the coin currently belongs to Alice. But at that point the protocol is modified. Bob doesn’t just go ahead and accept the transaction. Instead, he broadcasts Alice’s message to the entire network. Other members of the network check to see whether Alice owns that infocoin. If so, they broadcast the message “Yes, Alice owns infocoin 1234567, it can now be transferred to Bob.” Once enough people have broadcast that message, everyone updates their block chain to show that infocoin 1234567 now belongs to Bob, and the transaction is complete.更详细地说，假设爱丽丝想给鲍勃一个信息币。和之前一样，她在消息“我，爱丽丝，正在给鲍勃一个序列号为 1234567 的信息币”上签名，并将签名后的消息交给鲍勃。同样，鲍勃进行合理性检查，使用他的区块链副本检查该硬币当前确实属于爱丽丝。但在这一点上，协议被修改了。鲍勃不只是继续并接受交易。相反，他将爱丽丝的消息广播到整个网络。网络中的其他成员检查爱丽丝是否拥有那个信息币。如果是，他们广播消息“是的，爱丽丝拥有信息币 1234567，现在它可以转移给鲍勃。”一旦有足够多的人广播了该消息，每个人都更新他们的区块链以显示信息币 1234567 现在属于鲍勃，交易就完成了。

This protocol has many imprecise elements at present. For instance, what does it mean to say “once enough people have broadcast that message”? What exactly does “enough” mean here? It can’t mean everyone in the network, since we don’t *a priori* know who is on the Infocoin network. For the same reason, it can’t mean some fixed fraction of users in the network. We won’t try to make these ideas precise right now. Instead, in the next section I’ll point out a serious problem with the approach as described. Fixing that problem will at the same time have the pleasant side effect of making the ideas above much more precise.This protocol has many imprecise elements at present. For instance, what does it mean to say “once enough people have broadcast that message”? What exactly does “enough” mean here? It can’t mean everyone in the network, since we don’t *a priori* know who is on the Infocoin network. For the same reason, it can’t mean some fixed fraction of users in the network. We won’t try to make these ideas precise right now. Instead, in the next section I’ll point out a serious problem with the approach as described. Fixing that problem will at the same time have the pleasant side effect of making the ideas above much more precise.该协议目前有许多不精确的元素。例如，说“一旦有足够多的人广播了那条消息”是什么意思呢？这里的“足够多”到底是什么意思？它不可能意味着网络中的每个人，因为我们事先并不知道谁在信息币网络上。出于同样的原因，它也不可能意味着网络中用户的某个固定比例。我们现在不会试图使这些概念精确化。相反，在下一节中，我将指出所描述的方法中的一个严重问题。解决那个问题的同时，会产生一个令人愉快的副作用，即使上述概念更加精确。

### Proof-of-workProof-of-work 工作量证明

Suppose Alice wants to double spend in the network-based protocol I just described. She could do this by taking over the Infocoin network. Let’s suppose she uses an automated system to set up a large number of separate identities, let’s say a billion, on the Infocoin network. As before, she tries to double spend the same infocoin with both Bob and Charlie. But when Bob and Charlie ask the network to validate their respective transactions, Alice’s sock puppet identities swamp the network, announcing to Bob that they’ve validated his transaction, and to Charlie that they’ve validated his transaction, possibly fooling one or both into accepting the transaction.Suppose Alice wants to double spend in the network-based protocol I just described. She could do this by taking over the Infocoin network. Let’s suppose she uses an automated system to set up a large number of separate identities, let’s say a billion, on the Infocoin network. As before, she tries to double spend the same infocoin with both Bob and Charlie. But when Bob and Charlie ask the network to validate their respective transactions, Alice’s sock puppet identities swamp the network, announcing to Bob that they’ve validated his transaction, and to Charlie that they’ve validated his transaction, possibly fooling one or both into accepting the transaction.假设爱丽丝想在我刚刚描述的基于网络的协议中进行双重支付。她可以通过接管信息币网络来做到这一点。假设她使用一个自动化系统在信息币网络上设置大量独立的身份，比如说十亿个。和之前一样，她试图用同一个信息币对鲍勃和查理进行双重支付。但是当鲍勃和查理要求网络验证他们各自的交易时，爱丽丝的傀儡身份淹没了网络，向鲍勃宣布他们已经验证了他的交易，向查理宣布他们已经验证了他的交易，这可能会欺骗其中一个或两个人接受交易。

There’s a clever way of avoiding this problem, using an idea known as *proof-of-work*. The idea is counterintuitive and involves a combination of two ideas: (1) to (artificially) make it *computationally costly* for network users to validate transactions; and (2) to *reward* them for trying to help validate transactions. The reward is used so that people on the network will try to help validate transactions, even though that’s now been made a computationally costly process. The benefit of making it costly to validate transactions is that validation can no longer be influenced by the number of network identities someone controls, but only by the total computational power they can bring to bear on validation. As we’ll see, with some clever design we can make it so a cheater would need enormous computational resources to cheat, making it impractical.There’s a clever way of avoiding this problem, using an idea known as *proof-of-work*. The idea is counterintuitive and involves a combination of two ideas: (1) to (artificially) make it *computationally costly* for network users to validate transactions; and (2) to *reward* them for trying to help validate transactions. The reward is used so that people on the network will try to help validate transactions, even though that’s now been made a computationally costly process. The benefit of making it costly to validate transactions is that validation can no longer be influenced by the number of network identities someone controls, but only by the total computational power they can bring to bear on validation. As we’ll see, with some clever design we can make it so a cheater would need enormous computational resources to cheat, making it impractical.有一种巧妙的方法可以避免这个问题，使用一种被称为“工作量证明”的概念。这个想法违反直觉，涉及两个概念的结合：（1）（人为地）使网络用户验证交易“计算成本高昂”；（2）“奖励”他们尝试帮助验证交易。使用奖励是为了让网络上的人们尝试帮助验证交易，即使现在这已成为一个计算成本高昂的过程。使验证交易成本高昂的好处是，验证不再受某人控制的网络身份数量的影响，而仅受他们可用于验证的总计算能力的影响。正如我们将看到的，通过一些巧妙的设计，我们可以使作弊者需要巨大的计算资源才能作弊，从而使其变得不切实际。

That’s the gist of proof-of-work. But to really understand proof-of-work, we need to go through the details.That’s the gist of proof-of-work. But to really understand proof-of-work, we need to go through the details.这就是工作量证明的要点。但要真正理解工作量证明，我们需要了解细节。

Suppose Alice broadcasts to the network the news that “I, Alice, am giving Bob one infocoin, with serial number 1234567”. Suppose Alice broadcasts to the network the news that “I, Alice, am giving Bob one infocoin, with serial number 1234567”. 假设爱丽丝向网络广播消息：“我，爱丽丝，正在给鲍勃一个信息币，序列号为 1234567”。

As other people on the network hear that message, each adds it to a queue of pending transactions that they’ve been told about, but which haven’t yet been approved by the network. For instance, another network user named David might have the following queue of pending transactions:As other people on the network hear that message, each adds it to a queue of pending transactions that they’ve been told about, but which haven’t yet been approved by the network. For instance, another network user named David might have the following queue of pending transactions:当网络上的其他人听到该消息时，每个人都会将其添加到他们已被告知但尚未得到网络批准的待处理交易队列中。例如，另一个名为大卫的网络用户可能有以下待处理交易队列：

I, Tom, am giving Sue one infocoin, with serial number 1201174.I, Tom, am giving Sue one infocoin, with serial number 1201174.我，汤姆，正在给苏一个序列号为 1201174 的信息币。

I, Sydney, am giving Cynthia one infocoin, with serial number 1295618.I, Sydney, am giving Cynthia one infocoin, with serial number 1295618.我，悉尼，正在给辛西娅一个序列号为 1295618 的信息币。

I, Alice, am giving Bob one infocoin, with serial number 1234567.I, Alice, am giving Bob one infocoin, with serial number 1234567.我，爱丽丝，正在给鲍勃一个序列号为 1234567 的信息币。

David checks his copy of the block chain, and can see that each transaction is valid. He would like to help out by broadcasting news of that validity to the entire network.David checks his copy of the block chain, and can see that each transaction is valid. He would like to help out by broadcasting news of that validity to the entire network.大卫检查了他的区块链副本，可以看到每笔交易都是有效的。他想通过向整个网络广播该有效性的消息来提供帮助。

However, before doing that, as part of the validation protocol David is required to solve a hard computational puzzle – the proof-of-work. Without the solution to that puzzle, the rest of the network won’t accept his validation of the transaction.However, before doing that, as part of the validation protocol David is required to solve a hard computational puzzle – the proof-of-work. Without the solution to that puzzle, the rest of the network won’t accept his validation of the transaction.然而，在那之前，作为验证协议的一部分，大卫被要求解决一个困难的计算难题——工作量证明。如果没有这个难题的解决方案，网络的其他部分将不会接受他对交易的验证。

What puzzle does David need to solve? To explain that, let ![h](https://s0.wp.com/latex.php?latex=h&bg=ffffff&fg=000000&s=0 "h") be a fixed hash function known by everyone in the network – it’s built into the protocol. Bitcoin uses the well-known [SHA-256](https://en.wikipedia.org/wiki/SHA-2) hash function, but any cryptographically secure hash function will do. Let’s give David’s queue of pending transactions a label, ![l](https://s0.wp.com/latex.php?latex=l&bg=ffffff&fg=000000&s=0 "l"), just so it’s got a name we can refer to. Suppose David appends a number ![x](https://s0.wp.com/latex.php?latex=x&bg=ffffff&fg=000000&s=0 "x") (called the *nonce*) to ![l](https://s0.wp.com/latex.php?latex=l&bg=ffffff&fg=000000&s=0 "l") and hashes the combination. For example, if we use ![l = ](https://s0.wp.com/latex.php?latex=l+%3D+&bg=ffffff&fg=000000&s=0 "l = ") “Hello, world!” (obviously this is not a list of transactions, just a string used for illustrative purposes) and the nonce ![x = 0](https://s0.wp.com/latex.php?latex=x+%3D+0&bg=ffffff&fg=000000&s=0 "x = 0") [then](https://en.bitcoin.it/wiki/Proof_of_work) (output is in hexadecimal) What puzzle does David need to solve? To explain that, let be a fixed hash function known by everyone in the network – it’s built into the protocol. Bitcoin uses the well-known [SHA-256](https://en.wikipedia.org/wiki/SHA-2) hash function, but any cryptographically secure hash function will do. Let’s give David’s queue of pending transactions a label, , just so it’s got a name we can refer to. Suppose David appends a number (called the *nonce*) to and hashes the combination. For example, if we use “Hello, world!” (obviously this is not a list of transactions, just a string used for illustrative purposes) and the nonce [then](https://en.bitcoin.it/wiki/Proof_of_work) (output is in hexadecimal) 大卫需要解决什么难题？为了解释这一点，设存在一个网络中每个人都知道的固定哈希函数——它被内置在协议中。比特币使用著名的 SHA-256 哈希函数，但任何密码学安全的哈希函数都可以。让我们给大卫的待处理交易队列一个标签，设为 T，这样它就有了一个我们可以引用的名称。假设大卫在 T 后附加一个数字 n（称为“随机数”）并对组合进行哈希。例如，如果我们使用“Hello, world!”（显然这不是一个交易列表，只是一个用于说明目的的字符串）和随机数，那么（输出为十六进制）。

```
h("Hello, world!0") = 
  1312af178c253f84028d480a6adc1e25e81caa44c749ec81976192e2ec934c64
h("Hello, world!0") = 
  1312af178c253f84028d480a6adc1e25e81caa44c749ec81976192e2ec934c64
h("Hello, world!0") = 1312af178c253f84028d480a6adc1e25e81caa44c749ec81976192e2ec934c64
```

The puzzle David has to solve – the proof-of-work – is to find a nonce ![x](https://s0.wp.com/latex.php?latex=x&bg=ffffff&fg=000000&s=0 "x") such that when we append ![x](https://s0.wp.com/latex.php?latex=x&bg=ffffff&fg=000000&s=0 "x") to ![l](https://s0.wp.com/latex.php?latex=l&bg=ffffff&fg=000000&s=0 "l") and hash the combination the output hash begins with a long run of zeroes. The puzzle can be made more or less difficult by varying the number of zeroes required to solve the puzzle. A relatively simple proof-of-work puzzle might require just three or four zeroes at the start of the hash, while a more difficult proof-of-work puzzle might require a much longer run of zeros, say 15 consecutive zeroes. In either case, the above attempt to find a suitable nonce, with ![x = 0](https://s0.wp.com/latex.php?latex=x+%3D+0&bg=ffffff&fg=000000&s=0 "x = 0"), is a failure, since the output doesn’t begin with any zeroes at all. Trying ![x = 1](https://s0.wp.com/latex.php?latex=x+%3D+1&bg=ffffff&fg=000000&s=0 "x = 1") doesn’t work either: The puzzle David has to solve – the proof-of-work – is to find a nonce such that when we append to and hash the combination the output hash begins with a long run of zeroes. The puzzle can be made more or less difficult by varying the number of zeroes required to solve the puzzle. A relatively simple proof-of-work puzzle might require just three or four zeroes at the start of the hash, while a more difficult proof-of-work puzzle might require a much longer run of zeros, say 15 consecutive zeroes. In either case, the above attempt to find a suitable nonce, with , is a failure, since the output doesn’t begin with any zeroes at all. Trying doesn’t work either: 大卫必须解决的难题——工作量证明——是找到一个随机数，当我们将其附加到某个值后并对组合进行哈希运算时，输出的哈希值以一长串零开头。通过改变解决难题所需的零的数量，可以使难题变得更容易或更难。一个相对简单的工作量证明难题可能只需要哈希值开头有三四个零，而一个更难的工作量证明难题可能需要更长的连续零串，比如连续 15 个零。在这两种情况下，上述尝试找到合适的随机数（n=1）的方法都失败了，因为输出根本不以任何零开头。尝试 n=2 也不起作用。

```
h("Hello, world!1") = 
  e9afc424b79e4f6ab42d99c81156d3a17228d6e1eef4139be78e948a9332a7d8
h("Hello, world!1") = 
  e9afc424b79e4f6ab42d99c81156d3a17228d6e1eef4139be78e948a9332a7d8
h("Hello, world!1") = e9afc424b79e4f6ab42d99c81156d3a17228d6e1eef4139be78e948a9332a7d8
```

We can keep trying different values for the nonce, ![x = 2, 3,\ldots](https://s0.wp.com/latex.php?latex=x+%3D+2%2C+3%2C%5Cldots&bg=ffffff&fg=000000&s=0 "x = 2, 3,\ldots"). Finally, at ![x = 4250](https://s0.wp.com/latex.php?latex=x+%3D+4250&bg=ffffff&fg=000000&s=0 "x = 4250") we obtain: We can keep trying different values for the nonce, . Finally, at we obtain: We can keep trying different values for the nonce,. Finally, at we obtain: 我们可以不断尝试不同的随机数值，最后在（此处缺少内容）我们得到：（此处缺少内容）。

```
h("Hello, world!4250") = 
  0000c3af42fc31103f1fdc0151fa747ff87349a4714df7cc52ea464e12dcd4e9
h("Hello, world!4250") = 
  0000c3af42fc31103f1fdc0151fa747ff87349a4714df7cc52ea464e12dcd4e9
h("Hello, world!4250") = 0000c3af42fc31103f1fdc0151fa747ff87349a4714df7cc52ea464e12dcd4e9
```

This nonce gives us a string of four zeroes at the beginning of the output of the hash. This will be enough to solve a simple proof-of-work puzzle, but not enough to solve a more difficult proof-of-work puzzle. This nonce gives us a string of four zeroes at the beginning of the output of the hash. This will be enough to solve a simple proof-of-work puzzle, but not enough to solve a more difficult proof-of-work puzzle.这个随机数在哈希输出的开头给我们一个由四个零组成的字符串。这足以解决一个简单的工作量证明难题，但不足以解决一个更困难的工作量证明难题。

What makes this puzzle hard to solve is the fact that the output from a cryptographic hash function behaves like a random number: change the input even a tiny bit and the output from the hash function changes completely, in a way that’s hard to predict. So if we want the output hash value to begin with 10 zeroes, say, then David will need, on average, to try ![16^{10} \approx 10^{12}](https://s0.wp.com/latex.php?latex=16%5E%7B10%7D+%5Capprox+10%5E%7B12%7D&bg=ffffff&fg=000000&s=0 "16^{10} \approx 10^{12}") different values for ![x](https://s0.wp.com/latex.php?latex=x&bg=ffffff&fg=000000&s=0 "x") before he finds a suitable nonce. That’s a pretty challenging task, requiring lots of computational power.What makes this puzzle hard to solve is the fact that the output from a cryptographic hash function behaves like a random number: change the input even a tiny bit and the output from the hash function changes completely, in a way that’s hard to predict. So if we want the output hash value to begin with 10 zeroes, say, then David will need, on average, to try different values for before he finds a suitable nonce. That’s a pretty challenging task, requiring lots of computational power.使这个谜题难以解决的是，加密哈希函数的输出表现得像一个随机数：即使对输入进行微小的更改，哈希函数的输出也会完全改变，而且这种改变难以预测。因此，如果我们希望输出的哈希值以 10 个零开头，那么大卫平均需要尝试不同的输入值才能找到合适的随机数。这是一项非常具有挑战性的任务，需要大量的计算能力。

Obviously, it’s possible to make this puzzle more or less difficult to solve by requiring more or fewer zeroes in the output from the hash function. In fact, the Bitcoin protocol gets quite a fine level of control over the difficulty of the puzzle, by using a slight variation on the proof-of-work puzzle described above. Instead of requiring leading zeroes, the Bitcoin proof-of-work puzzle requires the hash of a block’s header to be lower than or equal to a number known as the [target](https://en.bitcoin.it/wiki/Target). This target is automatically adjusted to ensure that a Bitcoin block takes, on average, about ten minutes to validate. Obviously, it’s possible to make this puzzle more or less difficult to solve by requiring more or fewer zeroes in the output from the hash function. In fact, the Bitcoin protocol gets quite a fine level of control over the difficulty of the puzzle, by using a slight variation on the proof-of-work puzzle described above. Instead of requiring leading zeroes, the Bitcoin proof-of-work puzzle requires the hash of a block’s header to be lower than or equal to a number known as the [target](https://en.bitcoin.it/wiki/Target). This target is automatically adjusted to ensure that a Bitcoin block takes, on average, about ten minutes to validate. 显然，通过要求哈希函数的输出中有更多或更少的零，可以使这个谜题或多或少难以解决。事实上，比特币协议通过使用上述工作量证明谜题的一个细微变体，对谜题的难度进行了相当精细的控制。比特币的工作量证明谜题不是要求前导零，而是要求一个区块头的哈希值小于或等于一个被称为“目标值”的数字。这个目标值会自动调整，以确保验证一个比特币区块平均需要大约十分钟。

(In practice there is a sizeable randomness in how long it takes to validate a block – sometimes a new block is validated in just a minute or two, other times it may take 20 minutes or even longer. It’s straightforward to modify the Bitcoin protocol so that the time to validation is much more sharply peaked around ten minutes. Instead of solving a single puzzle, we can require that multiple puzzles be solved; with some careful design it is possible to considerably reduce the variance in the time to validate a block of transactions.)(In practice there is a sizeable randomness in how long it takes to validate a block – sometimes a new block is validated in just a minute or two, other times it may take 20 minutes or even longer. It’s straightforward to modify the Bitcoin protocol so that the time to validation is much more sharply peaked around ten minutes. Instead of solving a single puzzle, we can require that multiple puzzles be solved; with some careful design it is possible to considerably reduce the variance in the time to validate a block of transactions.)（在实践中，验证一个区块所需的时间存在相当大的随机性——有时一个新的区块在一两分钟内就得到验证，而其他时候可能需要 20 分钟甚至更长时间。对比特币协议进行修改，使验证时间更集中在十分钟左右是很容易的。我们可以要求解决多个谜题，而不是只解决一个谜题；通过一些精心设计，可以大大减少验证一批交易的区块所需时间的方差。）

Alright, let’s suppose David is lucky and finds a suitable nonce, ![x](https://s0.wp.com/latex.php?latex=x&bg=ffffff&fg=000000&s=0 "x"). Celebration! (He’ll be rewarded for finding the nonce, as described below). He broadcasts the block of transactions he’s approving to the network, together with the value for ![x](https://s0.wp.com/latex.php?latex=x&bg=ffffff&fg=000000&s=0 "x"). Other participants in the Infocoin network can verify that ![x](https://s0.wp.com/latex.php?latex=x&bg=ffffff&fg=000000&s=0 "x") is a valid solution to the proof-of-work puzzle. And they then update their block chains to include the new block of transactions.Alright, let’s suppose David is lucky and finds a suitable nonce, . Celebration! (He’ll be rewarded for finding the nonce, as described below). He broadcasts the block of transactions he’s approving to the network, together with the value for . Other participants in the Infocoin network can verify that is a valid solution to the proof-of-work puzzle. And they then update their block chains to include the new block of transactions.好的，让我们假设大卫很幸运，找到了一个合适的随机数。太棒了！（如下面所述，他会因为找到这个随机数而获得奖励）。他将自己批准的交易块以及随机数的值广播到网络中。信息币网络中的其他参与者可以验证这个随机数是工作量证明难题的有效解决方案。然后，他们更新自己的区块链以包含这个新的交易块。

For the proof-of-work idea to have any chance of succeeding, network users need an incentive to help validate transactions. Without such an incentive, they have no reason to expend valuable computational power, merely to help validate other people’s transactions. And if network users are not willing to expend that power, then the whole system won’t work. The solution to this problem is to reward people who help validate transactions. In particular, suppose we reward whoever successfully validates a block of transactions by crediting them with some infocoins. Provided the infocoin reward is large enough that will give them an incentive to participate in validation.For the proof-of-work idea to have any chance of succeeding, network users need an incentive to help validate transactions. Without such an incentive, they have no reason to expend valuable computational power, merely to help validate other people’s transactions. And if network users are not willing to expend that power, then the whole system won’t work. The solution to this problem is to reward people who help validate transactions. In particular, suppose we reward whoever successfully validates a block of transactions by crediting them with some infocoins. Provided the infocoin reward is large enough that will give them an incentive to participate in validation.对于工作量证明的理念要想有任何成功的机会，网络用户需要有一个激励来帮助验证交易。如果没有这样的激励，他们就没有理由消耗宝贵的计算能力，仅仅是为了帮助验证其他人的交易。如果网络用户不愿意消耗这种能力，那么整个系统就无法工作。这个问题的解决方案是奖励那些帮助验证交易的人。特别是，假设我们通过给成功验证一批交易的人奖励一些信息币来奖励他们。如果信息币奖励足够大，这将给他们一个参与验证的激励。

In the Bitcoin protocol, this validation process is called *mining*. For each block of transactions validated, the successful miner receives a bitcoin reward. Initially, this was set to be a 50 bitcoin reward. But for every 210,000 validated blocks (roughly, once every four years) the reward halves. This has happened just once, to date, and so the current reward for mining a block is 25 bitcoins. This halving in the rate will continue every four years until the year 2140 CE. At that point, the reward for mining will drop below ![10^{-8}](https://s0.wp.com/latex.php?latex=10%5E%7B-8%7D&bg=ffffff&fg=000000&s=0 "10^{-8}") bitcoins per block. ![10^{-8}](https://s0.wp.com/latex.php?latex=10%5E%7B-8%7D&bg=ffffff&fg=000000&s=0 "10^{-8}") bitcoins is actually the minimal unit of Bitcoin, and is known as a *satoshi*. So in 2140 CE the total supply of bitcoins will cease to increase. However, that won’t eliminate the incentive to help validate transactions. Bitcoin also makes it possible to set aside some currency in a transaction as a *transaction fee*, which goes to the miner who helps validate it. In the early days of Bitcoin transaction fees were mostly set to zero, but as Bitcoin has gained in popularity, transaction fees have gradually risen, and are now a substantial additional incentive on top of the 25 bitcoin reward for mining a block.In the Bitcoin protocol, this validation process is called *mining*. For each block of transactions validated, the successful miner receives a bitcoin reward. Initially, this was set to be a 50 bitcoin reward. But for every 210,000 validated blocks (roughly, once every four years) the reward halves. This has happened just once, to date, and so the current reward for mining a block is 25 bitcoins. This halving in the rate will continue every four years until the year 2140 CE. At that point, the reward for mining will drop below bitcoins per block. bitcoins is actually the minimal unit of Bitcoin, and is known as a *satoshi*. So in 2140 CE the total supply of bitcoins will cease to increase. However, that won’t eliminate the incentive to help validate transactions. Bitcoin also makes it possible to set aside some currency in a transaction as a *transaction fee*, which goes to the miner who helps validate it. In the early days of Bitcoin transaction fees were mostly set to zero, but as Bitcoin has gained in popularity, transaction fees have gradually risen, and are now a substantial additional incentive on top of the 25 bitcoin reward for mining a block.在比特币协议中，这个验证过程被称为“挖矿”。对于每一个经过验证的交易区块，成功的矿工都会获得比特币奖励。最初，这个奖励被设定为 50 个比特币。但是每经过 210,000 个经过验证的区块（大约每四年一次），奖励就会减半。到目前为止，这种情况只发生过一次，所以目前挖掘一个区块的奖励是 25 个比特币。这种奖励减半的速度将每四年持续一次，直到公元 2140 年。到那时，挖掘一个区块的奖励将降至不足一个比特币。一个比特币实际上是比特币的最小单位，被称为“聪”。因此，在公元 2140 年，比特币的总供应量将停止增加。然而，这并不会消除帮助验证交易的激励。比特币还可以在交易中预留一些货币作为“交易费”，这笔费用会支付给帮助验证交易的矿工。在比特币早期，交易费用大多被设定为零，但随着比特币的普及，交易费用逐渐上升，现在除了挖掘一个区块的 25 个比特币奖励之外，还是一个相当大的额外激励。

You can think of proof-of-work as a competition to approve transactions. Each entry in the competition costs a little bit of computing power. A miner’s chance of winning the competition is (roughly, and with some caveats) equal to the proportion of the total computing power that they control. So, for instance, if a miner controls one percent of the computing power being used to validate Bitcoin transactions, then they have roughly a one percent chance of winning the competition. So provided a lot of computing power is being brought to bear on the competition, a dishonest miner is likely to have only a relatively small chance to corrupt the validation process, unless they expend a huge amount of computing resources.You can think of proof-of-work as a competition to approve transactions. Each entry in the competition costs a little bit of computing power. A miner’s chance of winning the competition is (roughly, and with some caveats) equal to the proportion of the total computing power that they control. So, for instance, if a miner controls one percent of the computing power being used to validate Bitcoin transactions, then they have roughly a one percent chance of winning the competition. So provided a lot of computing power is being brought to bear on the competition, a dishonest miner is likely to have only a relatively small chance to corrupt the validation process, unless they expend a huge amount of computing resources.你可以将工作量证明视为批准交易的一种竞争。参与竞争的每一项都需要消耗一点计算能力。矿工赢得竞争的机会（大致上且有一些注意事项）等于他们所控制的总计算能力的比例。因此，例如，如果一个矿工控制了用于验证比特币交易的计算能力的百分之一，那么他们大约有百分之一的机会赢得竞争。所以，只要有大量的计算能力被用于这场竞争，不诚实的矿工很可能只有相对较小的机会破坏验证过程，除非他们消耗大量的计算资源。

Of course, while it’s encouraging that a dishonest party has only a relatively small chance to corrupt the block chain, that’s not enough to give us confidence in the currency. In particular, we haven’t yet conclusively addressed the issue of double spending.Of course, while it’s encouraging that a dishonest party has only a relatively small chance to corrupt the block chain, that’s not enough to give us confidence in the currency. In particular, we haven’t yet conclusively addressed the issue of double spending.当然，虽然不诚实的一方只有相对较小的机会破坏区块链这一点令人鼓舞，但这还不足以让我们对这种货币充满信心。特别是，我们还没有最终解决双重支付的问题。

I’ll analyse double spending shortly. Before doing that, I want to fill in an important detail in the description of Infocoin. We’d ideally like the Infocoin network to agree upon the *order* in which transactions have occurred. If we don’t have such an ordering then at any given moment it may not be clear who owns which infocoins. To help do this we’ll require that new blocks always include a pointer to the last block validated in the chain, in addition to the list of transactions in the block. (The pointer is actually just a hash of the previous block). So typically the block chain is just a linear chain of blocks of transactions, one after the other, with later blocks each containing a pointer to the immediately prior block:I’ll analyse double spending shortly. Before doing that, I want to fill in an important detail in the description of Infocoin. We’d ideally like the Infocoin network to agree upon the *order* in which transactions have occurred. If we don’t have such an ordering then at any given moment it may not be clear who owns which infocoins. To help do this we’ll require that new blocks always include a pointer to the last block validated in the chain, in addition to the list of transactions in the block. (The pointer is actually just a hash of the previous block). So typically the block chain is just a linear chain of blocks of transactions, one after the other, with later blocks each containing a pointer to the immediately prior block:我很快就会分析双重支付问题。在进行分析之前，我想在 Infocoin 的描述中补充一个重要细节。我们理想情况下希望 Infocoin 网络就交易发生的*顺序*达成一致。如果我们没有这样的顺序，那么在任何给定时刻，可能不清楚谁拥有哪些 Infocoin。为了实现这一点，我们将要求新的区块除了包含区块中的交易列表之外，还始终包含一个指向链中最后一个已验证区块的指针。（该指针实际上只是上一个区块的哈希值）。因此，通常情况下，区块链只是一个由交易区块组成的线性链，一个接一个，后面的区块每个都包含一个指向前一个区块的指针。

![](https://michaelnielsen.org/ddi/wp-content/uploads/2013/12/block_chain.png)

Occasionally, a fork will appear in the block chain. This can happen, for instance, if by chance two miners happen to validate a block of transactions near-simultaneously – both broadcast their newly-validated block out to the network, and some people update their block chain one way, and others update their block chain the other way:Occasionally, a fork will appear in the block chain. This can happen, for instance, if by chance two miners happen to validate a block of transactions near-simultaneously – both broadcast their newly-validated block out to the network, and some people update their block chain one way, and others update their block chain the other way:偶尔，区块链中会出现分叉。例如，如果两个矿工碰巧几乎同时验证了一个交易块——他们都将新验证的块广播到网络中，一些人以一种方式更新他们的区块链，而另一些人以另一种方式更新他们的区块链，就可能会发生这种情况。

![](https://michaelnielsen.org/ddi/wp-content/uploads/2013/12/block_chain_fork.png)

This causes exactly the problem we’re trying to avoid – it’s no longer clear in what order transactions have occurred, and it may not be clear who owns which infocoins. Fortunately, there’s a simple idea that can be used to remove any forks. The rule is this: if a fork occurs, people on the network keep track of both forks. But at any given time, miners only work to extend whichever fork is longest in their copy of the block chain. This causes exactly the problem we’re trying to avoid – it’s no longer clear in what order transactions have occurred, and it may not be clear who owns which infocoins. Fortunately, there’s a simple idea that can be used to remove any forks. The rule is this: if a fork occurs, people on the network keep track of both forks. But at any given time, miners only work to extend whichever fork is longest in their copy of the block chain. 这恰恰造成了我们试图避免的问题——现在不再清楚交易发生的顺序，也可能不清楚谁拥有哪些信息币。幸运的是，有一个简单的想法可以用来消除任何分叉。规则是这样的：如果发生分叉，网络上的人们会跟踪两个分叉。但在任何给定时间，矿工只致力于在他们的区块链副本中扩展最长的那个分叉。

Suppose, for example, that we have a fork in which some miners receive block A first, and some miners receive block B first. Those miners who receive block A first will continue mining along that fork, while the others will mine along fork B. Let’s suppose that the miners working on fork B are the next to successfully mine a block:Suppose, for example, that we have a fork in which some miners receive block A first, and some miners receive block B first. Those miners who receive block A first will continue mining along that fork, while the others will mine along fork B. Let’s suppose that the miners working on fork B are the next to successfully mine a block:例如，假设我们有一个分叉，一些矿工首先收到区块 A，而一些矿工首先收到区块 B。那些首先收到区块 A 的矿工将沿着该分叉继续挖矿，而其他矿工将沿着分叉 B 进行挖矿。让我们假设在分叉 B 上工作的矿工是下一个成功挖出一个区块的人。

![](https://michaelnielsen.org/ddi/wp-content/uploads/2013/12/block_chain_extended.png)

After they receive news that this has happened, the miners working on fork A will notice that fork B is now longer, and will switch to working on that fork. Presto, in short order work on fork A will cease, and everyone will be working on the same linear chain, and block A can be ignored. Of course, any still-pending transactions in A will still be pending in the queues of the miners working on fork B, and so all transactions will eventually be validated.After they receive news that this has happened, the miners working on fork A will notice that fork B is now longer, and will switch to working on that fork. Presto, in short order work on fork A will cease, and everyone will be working on the same linear chain, and block A can be ignored. Of course, any still-pending transactions in A will still be pending in the queues of the miners working on fork B, and so all transactions will eventually be validated.在他们收到这件事已经发生的消息后，在分叉 A 上工作的矿工们会注意到分叉 B 现在更长了，并且会切换到在那个分叉上工作。很快，在分叉 A 上的工作将停止，每个人都将在同一条线性链上工作，并且可以忽略区块 A。当然，A 中任何仍在等待处理的交易仍将在分叉 B 上工作的矿工的队列中等待处理，因此所有交易最终都将得到验证。

Likewise, it may be that the miners working on fork A are the first to extend their fork. In that case work on fork B will quickly cease, and again we have a single linear chain.Likewise, it may be that the miners working on fork A are the first to extend their fork. In that case work on fork B will quickly cease, and again we have a single linear chain.同样，可能在分叉 A 上工作的矿工是第一个扩展其分叉的。在这种情况下，在分叉 B 上的工作将很快停止，并且我们再次拥有一个单一的线性链。

No matter what the outcome, this process ensures that the block chain has an agreed-upon time ordering of the blocks. In Bitcoin proper, a transaction is not considered confirmed until: (1) it is part of a block in the longest fork, and (2) at least 5 blocks follow it in the longest fork. In this case we say that the transaction has “6 confirmations”. This gives the network time to come to an agreed-upon the ordering of the blocks. We’ll also use this strategy for Infocoin.No matter what the outcome, this process ensures that the block chain has an agreed-upon time ordering of the blocks. In Bitcoin proper, a transaction is not considered confirmed until: (1) it is part of a block in the longest fork, and (2) at least 5 blocks follow it in the longest fork. In this case we say that the transaction has “6 confirmations”. This gives the network time to come to an agreed-upon the ordering of the blocks. We’ll also use this strategy for Infocoin.无论结果如何，这个过程都能确保区块链对区块有一个一致认可的时间顺序。在真正的比特币中，一笔交易在以下情况才被认为是已确认的：（1）它是最长分叉中的一个区块的一部分；（2）在最长分叉中至少有 5 个区块在它之后。在这种情况下，我们说这笔交易有“6 次确认”。这给网络时间来就区块的顺序达成一致。我们也将对信息币使用这个策略。

With the time-ordering now understood, let’s return to think about what happens if a dishonest party tries to double spend. Suppose Alice tries to double spend with Bob and Charlie. One possible approach is for her to try to validate a block that includes both transactions. Assuming she has one percent of the computing power, she will occasionally get lucky and validate the block by solving the proof-of-work. Unfortunately for Alice, the double spending will be immediately spotted by other people in the Infocoin network and rejected, despite solving the proof-of-work problem. So that’s not something we need to worry about.With the time-ordering now understood, let’s return to think about what happens if a dishonest party tries to double spend. Suppose Alice tries to double spend with Bob and Charlie. One possible approach is for her to try to validate a block that includes both transactions. Assuming she has one percent of the computing power, she will occasionally get lucky and validate the block by solving the proof-of-work. Unfortunately for Alice, the double spending will be immediately spotted by other people in the Infocoin network and rejected, despite solving the proof-of-work problem. So that’s not something we need to worry about.现在已经理解了时间顺序，让我们回到思考如果一个不诚实的一方试图双重花费会发生什么。假设爱丽丝试图与鲍勃和查理进行双重花费。一种可能的方法是她尝试验证一个包含这两个交易的区块。假设她拥有百分之一的计算能力，她偶尔会幸运地通过解决工作量证明来验证这个区块。但对爱丽丝来说不幸的是，尽管解决了工作量证明问题，双重花费会立即被信息币网络中的其他人发现并拒绝。所以这不是我们需要担心的事情。

A more serious problem occurs if she broadcasts two separate transactions in which she spends the same infocoin with Bob and Charlie, respectively. She might, for example, broadcast one transaction to a subset of the miners, and the other transaction to another set of miners, hoping to get both transactions validated in this way. Fortunately, in this case, as we’ve seen, the network will eventually confirm one of these transactions, but not both. So, for instance, Bob’s transaction might ultimately be confirmed, in which case Bob can go ahead confidently. Meanwhile, Charlie will see that his transaction has not been confirmed, and so will decline Alice’s offer. So this isn’t a problem either. In fact, knowing that this will be the case, there is little reason for Alice to try this in the first place.A more serious problem occurs if she broadcasts two separate transactions in which she spends the same infocoin with Bob and Charlie, respectively. She might, for example, broadcast one transaction to a subset of the miners, and the other transaction to another set of miners, hoping to get both transactions validated in this way. Fortunately, in this case, as we’ve seen, the network will eventually confirm one of these transactions, but not both. So, for instance, Bob’s transaction might ultimately be confirmed, in which case Bob can go ahead confidently. Meanwhile, Charlie will see that his transaction has not been confirmed, and so will decline Alice’s offer. So this isn’t a problem either. In fact, knowing that this will be the case, there is little reason for Alice to try this in the first place.如果她分别向鲍勃和查理广播两笔独立的交易，在交易中她把同一枚信息币花给他们，就会出现一个更严重的问题。例如，她可能向一部分矿工广播一笔交易，向另一部分矿工广播另一笔交易，希望以这种方式让两笔交易都得到验证。幸运的是，在这种情况下，正如我们所见，网络最终会确认其中一笔交易，但不会同时确认两笔。所以，例如，鲍勃的交易最终可能会得到确认，在这种情况下，鲍勃可以放心地进行交易。同时，查理会看到他的交易没有得到确认，所以会拒绝爱丽丝的提议。所以这也不是问题。事实上，知道情况会是这样，爱丽丝一开始就没有什么理由尝试这样做。

An important variant on double spending is if Alice = Bob, i.e., Alice tries to spend a coin with Charlie which she is also “spending” with herself (i.e., giving back to herself). This sounds like it ought to be easy to detect and deal with, but, of course, it’s easy on a network to set up multiple identities associated with the same person or organization, so this possibility needs to be considered. In this case, Alice’s strategy is to wait until Charlie accepts the infocoin, which happens after the transaction has been confirmed 6 times in the longest chain. She will then attempt to fork the chain before the transaction with Charlie, adding a block which includes a transaction in which she pays herself:An important variant on double spending is if Alice = Bob, i.e., Alice tries to spend a coin with Charlie which she is also “spending” with herself (i.e., giving back to herself). This sounds like it ought to be easy to detect and deal with, but, of course, it’s easy on a network to set up multiple identities associated with the same person or organization, so this possibility needs to be considered. In this case, Alice’s strategy is to wait until Charlie accepts the infocoin, which happens after the transaction has been confirmed 6 times in the longest chain. She will then attempt to fork the chain before the transaction with Charlie, adding a block which includes a transaction in which she pays herself:双重支出的一个重要变体是如果爱丽丝等于鲍勃，即爱丽丝试图与查理花费一枚硬币，而她也在与自己“花费”（即把硬币还给自己）。这听起来应该很容易被检测和处理，但当然，在网络上很容易为同一个人或组织设置多个身份，所以这种可能性需要被考虑。在这种情况下，爱丽丝的策略是等到查理接受信息币，这发生在交易在最长链中被确认六次之后。然后她将尝试在与查理的交易之前分叉链，添加一个包含她向自己付款的交易的区块。

![](https://michaelnielsen.org/ddi/wp-content/uploads/2013/12/block_chain_cheating.png)

Unfortunately for Alice, it’s now very difficult for her to catch up with the longer fork. Other miners won’t want to help her out, since they’ll be working on the longer fork. And unless Alice is able to solve the proof-of-work at least as fast as everyone else in the network combined – roughly, that means controlling more than fifty percent of the computing power – then she will just keep falling further and further behind. Of course, she might get lucky. We can, for example, imagine a scenario in which Alice controls one percent of the computing power, but happens to get lucky and finds six extra blocks in a row, before the rest of the network has found any extra blocks. In this case, she might be able to get ahead, and get control of the block chain. But this particular event will occur with probability ![1/100^6 = 10^{-12}](https://s0.wp.com/latex.php?latex=1%2F100%5E6+%3D+10%5E%7B-12%7D&bg=ffffff&fg=000000&s=0 "1/100^6 = 10^{-12}"). A more general analysis along these lines shows that Alice’s probability of ever catching up is infinitesimal, unless she is able to solve proof-of-work puzzles at a rate approaching all other miners combined.Unfortunately for Alice, it’s now very difficult for her to catch up with the longer fork. Other miners won’t want to help her out, since they’ll be working on the longer fork. And unless Alice is able to solve the proof-of-work at least as fast as everyone else in the network combined – roughly, that means controlling more than fifty percent of the computing power – then she will just keep falling further and further behind. Of course, she might get lucky. We can, for example, imagine a scenario in which Alice controls one percent of the computing power, but happens to get lucky and finds six extra blocks in a row, before the rest of the network has found any extra blocks. In this case, she might be able to get ahead, and get control of the block chain. But this particular event will occur with probability . A more general analysis along these lines shows that Alice’s probability of ever catching up is infinitesimal, unless she is able to solve proof-of-work puzzles at a rate approaching all other miners combined.不幸的是，对于爱丽丝来说，现在要赶上更长的分支非常困难。其他矿工不会想帮助她，因为他们将在更长的分支上工作。除非爱丽丝能够至少以与网络中其他所有人加起来一样快的速度解决工作量证明问题——大致来说，这意味着控制超过 50%的计算能力——否则她只会越来越落后。当然，她可能会走运。例如，我们可以想象这样一种情况：爱丽丝控制着 1%的计算能力，但碰巧走运，在网络中的其他人找到任何额外的区块之前连续找到了六个额外的区块。在这种情况下，她可能会领先并获得对区块链的控制权。但这种特定事件发生的概率为……。沿着这些思路进行更一般的分析表明，爱丽丝追上的概率极小，除非她能够以接近所有其他矿工总和的速度解决工作量证明难题。

Of course, this is not a rigorous security analysis showing that Alice cannot double spend. It’s merely an informal plausibility argument. The [original paper](http://bitcoin.org/bitcoin.pdf) introducing Bitcoin did not, in fact, contain a rigorous security analysis, only informal arguments along the lines I’ve presented here. The security community is still analysing Bitcoin, and trying to understand possible vulnerabilities. You can see some of this research [listed here](https://en.bitcoin.it/wiki/Research), and I mention a few related problems in the “Problems for the author” below. At this point I think it’s fair to say that the jury is still out on how secure Bitcoin is.Of course, this is not a rigorous security analysis showing that Alice cannot double spend. It’s merely an informal plausibility argument. The [original paper](http://bitcoin.org/bitcoin.pdf) introducing Bitcoin did not, in fact, contain a rigorous security analysis, only informal arguments along the lines I’ve presented here. The security community is still analysing Bitcoin, and trying to understand possible vulnerabilities. You can see some of this research [listed here](https://en.bitcoin.it/wiki/Research), and I mention a few related problems in the “Problems for the author” below. At this point I think it’s fair to say that the jury is still out on how secure Bitcoin is.当然，这并不是一个严格的安全性分析，表明爱丽丝不能双重花费。这仅仅是一个非正式的合理性论证。引入比特币的原始论文实际上并没有包含严格的安全性分析，只有我在这里提出的那种非正式的论证。安全社区仍在分析比特币，并试图了解可能的漏洞。你可以在这里看到一些这项研究，在下面的“作者的问题”中，我提到了一些相关的问题。在这一点上，我认为可以公平地说，比特币的安全性如何尚无定论。

The proof-of-work and mining ideas give rise to many questions. How much reward is enough to persuade people to mine? How does the change in supply of infocoins affect the Infocoin economy? Will Infocoin mining end up concentrated in the hands of a few, or many? If it’s just a few, doesn’t that endanger the security of the system? Presumably transaction fees will eventually equilibriate – won’t this introduce an unwanted source of friction, and make small transactions less desirable? These are all great questions, but beyond the scope of this post. I may come back to the questions (in the context of Bitcoin) in a future post. For now, we’ll stick to our focus on understanding how the Bitcoin protocol works.The proof-of-work and mining ideas give rise to many questions. How much reward is enough to persuade people to mine? How does the change in supply of infocoins affect the Infocoin economy? Will Infocoin mining end up concentrated in the hands of a few, or many? If it’s just a few, doesn’t that endanger the security of the system? Presumably transaction fees will eventually equilibriate – won’t this introduce an unwanted source of friction, and make small transactions less desirable? These are all great questions, but beyond the scope of this post. I may come back to the questions (in the context of Bitcoin) in a future post. For now, we’ll stick to our focus on understanding how the Bitcoin protocol works.工作量证明和挖矿的概念引发了许多问题。多少奖励才足以说服人们去挖矿呢？信息币的供应量变化会如何影响信息币经济呢？信息币挖矿最终会集中在少数人手中还是多数人手中呢？如果只是少数人，这难道不会危及系统的安全吗？大概交易费用最终会达到平衡——但这难道不会引入一个不受欢迎的摩擦源，并使小额交易变得不那么受欢迎吗？这些都是很好的问题，但超出了本文的范围。我可能会在未来的一篇文章中回到这些问题（在比特币的背景下）。目前，我们将继续专注于理解比特币协议是如何工作的。

### Problems for the authorProblems for the author 作者面临的问题

- I don’t understand why double spending can’t be prevented in a simpler manner using [two-phase commit](http://en.wikipedia.org/wiki/Two-phase_commit_protocol). Suppose Alice tries to double spend an infocoin with both Bob and Charlie. The idea is that Bob and Charlie would each broadcast their respective messages to the Infocoin network, along with a request: “Should I accept this?” They’d then wait some period – perhaps ten minutes – to hear any naysayers who could prove that Alice was trying to double spend. If no such nays are heard (and provided there are no signs of attempts to disrupt the network), they’d then accept the transaction. This protocol needs to be hardened against network attacks, but it seems to me to be the core of a good alternate idea. How well does this work? What drawbacks and advantages does it have compared to the full Bitcoin protocol? I don’t understand why double spending can’t be prevented in a simpler manner using [two-phase commit](http://en.wikipedia.org/wiki/Two-phase_commit_protocol). Suppose Alice tries to double spend an infocoin with both Bob and Charlie. The idea is that Bob and Charlie would each broadcast their respective messages to the Infocoin network, along with a request: “Should I accept this?” They’d then wait some period – perhaps ten minutes – to hear any naysayers who could prove that Alice was trying to double spend. If no such nays are heard (and provided there are no signs of attempts to disrupt the network), they’d then accept the transaction. This protocol needs to be hardened against network attacks, but it seems to me to be the core of a good alternate idea. How well does this work? What drawbacks and advantages does it have compared to the full Bitcoin protocol? 我不明白为什么不能用更简单的方式使用两阶段提交来防止双重支付。假设爱丽丝试图向鲍勃和查理双重支付一枚信息币。这个想法是，鲍勃和查理各自将他们的消息广播到信息币网络，并附上一个请求：“我应该接受这个吗？”然后他们会等待一段时间——也许十分钟——以听取任何能证明爱丽丝试图双重支付的反对者的意见。如果没有听到这样的反对意见（并且如果没有网络中断的迹象），他们就会接受这笔交易。这个协议需要针对网络攻击进行强化，但在我看来，这似乎是一个很好的替代方案的核心。这个方法效果如何？与完整的比特币协议相比，它有哪些缺点和优点？
- Early in the section I mentioned that there is a natural way of reducing the variance in time required to validate a block of transactions. If that variance is reduced too much, then it creates an interesting attack possibility. Suppose Alice tries to fork the chain in such a way that: (a) one fork starts with a block in which Alice pays herself, while the other fork starts with a block in which Alice pays Bob; (b) both blocks are announced nearly simultaneously, so roughly half the miners will attempt to mine each fork; (c) Alice uses her mining power to try to keep the forks of roughly equal length, mining whichever fork is shorter – this is ordinarily hard to pull off, but becomes significantly easier if the standard deviation of the time-to-validation is much shorter than the network latency; (d) after 5 blocks have been mined on both forks, Alice throws her mining power into making it more likely that Charles’s transaction is confirmed; and (e) after confirmation of Charles’s transaction, she then throws her computational power into the other fork, and attempts to regain the lead. This balancing strategy will have only a small chance of success. But while the probability is small, it will certainly be much larger than in the standard protocol, with high variance in the time to validate a block. Is there a way of avoiding this problem? Early in the section I mentioned that there is a natural way of reducing the variance in time required to validate a block of transactions. If that variance is reduced too much, then it creates an interesting attack possibility. Suppose Alice tries to fork the chain in such a way that: (a) one fork starts with a block in which Alice pays herself, while the other fork starts with a block in which Alice pays Bob; (b) both blocks are announced nearly simultaneously, so roughly half the miners will attempt to mine each fork; (c) Alice uses her mining power to try to keep the forks of roughly equal length, mining whichever fork is shorter – this is ordinarily hard to pull off, but becomes significantly easier if the standard deviation of the time-to-validation is much shorter than the network latency; (d) after 5 blocks have been mined on both forks, Alice throws her mining power into making it more likely that Charles’s transaction is confirmed; and (e) after confirmation of Charles’s transaction, she then throws her computational power into the other fork, and attempts to regain the lead. This balancing strategy will have only a small chance of success. But while the probability is small, it will certainly be much larger than in the standard protocol, with high variance in the time to validate a block. Is there a way of avoiding this problem? 在本节的开头，我提到有一种自然的方法可以减少验证一批交易所需时间的方差。如果这个方差被过度减小，那么就会产生一种有趣的攻击可能性。假设爱丽丝试图以这样一种方式分叉区块链：（a）一个分叉以爱丽丝向自己付款的区块开始，而另一个分叉以爱丽丝向鲍勃付款的区块开始；（b）两个区块几乎同时被宣布，所以大约一半的矿工将尝试挖掘每个分叉；（c）爱丽丝使用她的挖矿能力试图保持两个分叉大致相等的长度，挖掘较短的那个分叉——这通常很难做到，但如果验证时间的标准差远小于网络延迟，就会变得容易得多；（d）在两个分叉上都挖掘了 5 个区块后，爱丽丝将她的挖矿能力投入到使查尔斯的交易更有可能被确认的工作中；（e）在查尔斯的交易确认后，她然后将她的计算能力投入到另一个分叉中，并试图重新获得领先地位。这种平衡策略成功的机会很小。但是，虽然概率很小，但它肯定会比在标准协议中高得多，在标准协议中，验证一个区块的时间方差很大。有什么方法可以避免这个问题吗？
- Suppose Bitcoin mining software always explored nonces starting with ![x = 0](https://s0.wp.com/latex.php?latex=x+%3D+0&bg=ffffff&fg=000000&s=0 "x = 0"), then ![x = 1, x = 2,\ldots](https://s0.wp.com/latex.php?latex=x+%3D+1%2C+x+%3D+2%2C%5Cldots&bg=ffffff&fg=000000&s=0 "x = 1, x = 2,\ldots"). If this is done by all (or even just a substantial fraction) of Bitcoin miners then it creates a vulnerability. Namely, it’s possible for someone to improve their odds of solving the proof-of-work merely by starting with some other (much larger) nonce. More generally, it may be possible for attackers to exploit any systematic patterns in the way miners explore the space of nonces. More generally still, in the analysis of this section I have implicitly assumed a kind of symmetry between different miners. In practice, there will be asymmetries and a thorough security analysis will need to account for those asymmetries. Suppose Bitcoin mining software always explored nonces starting with , then . If this is done by all (or even just a substantial fraction) of Bitcoin miners then it creates a vulnerability. Namely, it’s possible for someone to improve their odds of solving the proof-of-work merely by starting with some other (much larger) nonce. More generally, it may be possible for attackers to exploit any systematic patterns in the way miners explore the space of nonces. More generally still, in the analysis of this section I have implicitly assumed a kind of symmetry between different miners. In practice, there will be asymmetries and a thorough security analysis will need to account for those asymmetries. 假设比特币挖矿软件总是从某个特定值开始探索随机数，然后是另一个特定值。如果所有（或者只是很大一部分）比特币矿工都这样做，那么这就会产生一个漏洞。也就是说，有人仅仅通过从其他（大得多的）随机数开始，就有可能提高他们解决工作量证明的几率。更一般地说，攻击者可能会利用矿工探索随机数空间的任何系统性模式。更广泛地说，在本节的分析中，我隐含地假设了不同矿工之间的一种对称性。实际上，会存在不对称性，彻底的安全分析需要考虑这些不对称性。

### BitcoinBitcoin 比特币

Let’s move away from Infocoin, and describe the actual Bitcoin protocol. There are a few new ideas here, but with one exception (discussed below) they’re mostly obvious modifications to Infocoin.Let’s move away from Infocoin, and describe the actual Bitcoin protocol. There are a few new ideas here, but with one exception (discussed below) they’re mostly obvious modifications to Infocoin.让我们远离信息币，来描述一下实际的比特币协议。这里有一些新的想法，但除了一个（下面会讨论）之外，它们大多是对信息币的明显修改。

To use Bitcoin in practice, you first install a [wallet](http://bitcoin.org/en/choose-your-wallet) program on your computer. To give you a sense of what that means, here’s a screenshot of a wallet called [Multbit](https://multibit.org/). You can see the Bitcoin balance on the left — 0.06555555 Bitcoins, or about 70 dollars at the exchange rate on the day I took this screenshot — and on the right two recent transactions, which deposited those 0.06555555 Bitcoins:To use Bitcoin in practice, you first install a [wallet](http://bitcoin.org/en/choose-your-wallet) program on your computer. To give you a sense of what that means, here’s a screenshot of a wallet called [Multbit](https://multibit.org/). You can see the Bitcoin balance on the left — 0.06555555 Bitcoins, or about 70 dollars at the exchange rate on the day I took this screenshot — and on the right two recent transactions, which deposited those 0.06555555 Bitcoins:在实际使用比特币时，你首先要在电脑上安装一个[钱包](http://bitcoin.org/en/choose-your-wallet)程序。为了让你了解这意味着什么，这里有一个名为[Multbit](https://multibit.org/)的钱包的屏幕截图。你可以在左边看到比特币余额——0.06555555 比特币，以我截取此屏幕截图当天的汇率计算约为 70 美元——在右边可以看到两笔最近的交易，正是这两笔交易存入了那 0.06555555 比特币。

![](https://michaelnielsen.org/ddi/wp-content/uploads/2013/12/wallet_transaction.jpg)

Suppose you’re a merchant who has set up an online store, and you’ve decided to allow people to pay using Bitcoin. What you do is tell your wallet program to generate a *Bitcoin address*. In response, it will generate a public / private key pair, and then hash the public key to form your Bitcoin address:Suppose you’re a merchant who has set up an online store, and you’ve decided to allow people to pay using Bitcoin. What you do is tell your wallet program to generate a *Bitcoin address*. In response, it will generate a public / private key pair, and then hash the public key to form your Bitcoin address:假设你是一位开设了在线商店的商家，并且决定允许人们使用比特币进行支付。你要做的是告诉钱包程序生成一个*比特币地址*。作为回应，它将生成一个公钥/私钥对，然后对公钥进行哈希以形成你的比特币地址：

![](https://michaelnielsen.org/ddi/wp-content/uploads/2013/12/bitcoin_address.jpg)

You then send your Bitcoin address to the person who wants to buy from you. You could do this in email, or even put the address up publicly on a webpage. This is safe, since the address is merely a hash of your public key, which can safely be known by the world anyway. (I’ll return later to the question of why the Bitcoin address is a hash, and not just the public key.)You then send your Bitcoin address to the person who wants to buy from you. You could do this in email, or even put the address up publicly on a webpage. This is safe, since the address is merely a hash of your public key, which can safely be known by the world anyway. (I’ll return later to the question of why the Bitcoin address is a hash, and not just the public key.)然后你将你的比特币地址发送给想要从你这里购买的人。你可以通过电子邮件来做这件事，甚至可以将地址公开放在网页上。这是安全的，因为该地址只是你的公钥的哈希值，而公钥无论如何都可以被全世界安全地知晓。（稍后我会回到为什么比特币地址是一个哈希值而不仅仅是公钥这个问题上。）

The person who is going to pay you then generates a *transaction*. Let’s take a look at the data from an [actual transaction](http://blockexplorer.com/tx/7c402505be883276b833d57168a048cfdf306a926484c0b58930f53d89d036f9) transferring ![0.31900000](https://s0.wp.com/latex.php?latex=0.31900000&bg=ffffff&fg=000000&s=0 "0.31900000") bitcoins. What’s shown below is very nearly the raw data. It’s changed in three ways: (1) the data has been deserialized; (2) line numbers have been added, for ease of reference; and (3) I’ve abbreviated various hashes and public keys, just putting in the first six hexadecimal digits of each, when in reality they are much longer. Here’s the data: The person who is going to pay you then generates a *transaction*. Let’s take a look at the data from an [actual transaction](http://blockexplorer.com/tx/7c402505be883276b833d57168a048cfdf306a926484c0b58930f53d89d036f9) transferring bitcoins. What’s shown below is very nearly the raw data. It’s changed in three ways: (1) the data has been deserialized; (2) line numbers have been added, for ease of reference; and (3) I’ve abbreviated various hashes and public keys, just putting in the first six hexadecimal digits of each, when in reality they are much longer. Here’s the data: 将要向你付款的人随后会生成一笔*交易*。让我们看一下一笔[实际交易](http://blockexplorer.com/tx/7c402505be883276b833d57168a048cfdf306a926484c0b58930f53d89d036f9)中比特币转账的数据。下面显示的几乎是原始数据。它在三个方面进行了更改：（1）数据已反序列化；（2）为了便于参考，添加了行号；（3）我对各种哈希值和公钥进行了缩写，只给出了每个的前六个十六进制数字，而实际上它们要长得多。以下是数据：

```
1.  {"hash":"7c4025...",
2.  "ver":1,
3.  "vin_sz":1,
4.  "vout_sz":1,
5.  "lock_time":0,
6.  "size":224,
7.  "in":[
8.    {"prev_out":
9.      {"hash":"2007ae...",
10.      "n":0},
11.    "scriptSig":"304502... 042b2d..."}],
12. "out":[
13.   {"value":"0.31900000",
14.    "scriptPubKey":"OP_DUP OP_HASH160 a7db6f OP_EQUALVERIFY OP_CHECKSIG"}]}
1.  {"hash":"7c4025...",
2.  "ver":1,
3.  "vin_sz":1,
4.  "vout_sz":1,
5.  "lock_time":0,
6.  "size":224,
7.  "in":[
8.    {"prev_out":
9.      {"hash":"2007ae...",
10.      "n":0},
11.    "scriptSig":"304502... 042b2d..."}],
12. "out":[
13.   {"value":"0.31900000",
14.    "scriptPubKey":"OP_DUP OP_HASH160 a7db6f OP_EQUALVERIFY OP_CHECKSIG"}]}
1.  {"hash":"7c4025...",2.  "ver":1,3.  "vin_sz":1,4.  "vout_sz":1,5.  "lock_time":0,6.  "size":224,7.  "in":[8.    {"prev_out":9.      {"hash":"2007ae...",10.      "n":0},11.    "scriptSig":"304502... 042b2d..."}],12. "out":[13.   {"value":"0.31900000",14.    "scriptPubKey":"OP_DUP OP_HASH160 a7db6f OP_EQUALVERIFY OP_CHECKSIG"}]}
```

Let’s go through this, line by line.Let’s go through this, line by line.“让我们一行一行地过一遍这个。”

Line 1 contains the hash of the remainder of the transaction, 7c4025..., expressed in hexadecimal. This is used as an identifier for the transaction.Line 1 contains the hash of the remainder of the transaction, 7c4025..., expressed in hexadecimal. This is used as an identifier for the transaction.第 1 行包含交易剩余部分的哈希值，7c4025……，以十六进制表示。这用作交易的标识符。

Line 2 tells us that this is a transaction in version 1 of the Bitcoin protocol.Line 2 tells us that this is a transaction in version 1 of the Bitcoin protocol.第 2 行告诉我们，这是比特币协议第 1 版中的一笔交易。

Lines 3 and 4 tell us that the transaction has one input and one output, respectively. I’ll talk below about transactions with more inputs and outputs, and why that’s useful. Lines 3 and 4 tell us that the transaction has one input and one output, respectively. I’ll talk below about transactions with more inputs and outputs, and why that’s useful. 第 3 行和第 4 行告诉我们，该交易分别有一个输入和一个输出。下面我将讨论具有更多输入和输出的交易，以及为什么这很有用。

Line 5 contains the value for lock\_time, which can be used to control when a transaction is finalized. For most Bitcoin transactions being carried out today the lock\_time is set to 0, which means the transaction is finalized immediately.Line 5 contains the value for lock\_time, which can be used to control when a transaction is finalized. For most Bitcoin transactions being carried out today the lock\_time is set to 0, which means the transaction is finalized immediately.第 5 行包含 lock\_time 的值，它可用于控制交易何时最终确定。对于当今正在进行的大多数比特币交易，lock\_time 被设置为 0，这意味着交易立即最终确定。

Line 6 tells us the size (in bytes) of the transaction. Note that it’s not the monetary amount being transferred! That comes later.Line 6 tells us the size (in bytes) of the transaction. Note that it’s not the monetary amount being transferred! That comes later.第 6 行告诉我们交易的大小（以字节为单位）。请注意，这不是正在转移的货币金额！货币金额稍后给出。

Lines 7 through 11 define the input to the transaction. In particular, lines 8 through 10 tell us that the input is to be taken from the output from an earlier transaction, with the given hash, which is expressed in hexadecimal as 2007ae.... The n=0 tells us it’s to be the first output from that transaction; we’ll see soon how multiple outputs (and inputs) from a transaction work, so don’t worry too much about this for now. Line 11 contains the signature of the person sending the money, 304502..., followed by a space, and then the corresponding public key, 04b2d.... Again, these are both in hexadecimal.Lines 7 through 11 define the input to the transaction. In particular, lines 8 through 10 tell us that the input is to be taken from the output from an earlier transaction, with the given hash, which is expressed in hexadecimal as 2007ae.... The n=0 tells us it’s to be the first output from that transaction; we’ll see soon how multiple outputs (and inputs) from a transaction work, so don’t worry too much about this for now. Line 11 contains the signature of the person sending the money, 304502..., followed by a space, and then the corresponding public key, 04b2d.... Again, these are both in hexadecimal.第 7 行到第 11 行定义了交易的输入。特别是，第 8 行到第 10 行告诉我们，输入将从具有给定哈希值的早期交易的输出中获取，该哈希值以十六进制表示为 2007ae……。n = 0 告诉我们它将是该交易的第一个输出；我们很快就会看到一个交易中的多个输出（和输入）是如何工作的，所以目前不要对此过于担心。第 11 行包含发送资金的人的签名，304502……，后面跟着一个空格，然后是相应的公钥，04b2d……。同样，这些都是十六进制的。

One thing to note about the input is that there’s nothing explicitly specifying how many bitcoins from the previous transaction should be spent in this transaction. In fact, *all* the bitcoins from the n=0th output of the previous transaction are spent. So, for example, if the n=0th output of the earlier transaction was 2 bitcoins, then 2 bitcoins will be spent in this transaction. This seems like an inconvenient restriction – like trying to buy bread with a 20 dollar note, and not being able to break the note down. The solution, of course, is to have a mechanism for providing change. This can be done using transactions with multiple inputs and outputs, which we’ll discuss in the next section.One thing to note about the input is that there’s nothing explicitly specifying how many bitcoins from the previous transaction should be spent in this transaction. In fact, *all* the bitcoins from the n=0th output of the previous transaction are spent. So, for example, if the n=0th output of the earlier transaction was 2 bitcoins, then 2 bitcoins will be spent in this transaction. This seems like an inconvenient restriction – like trying to buy bread with a 20 dollar note, and not being able to break the note down. The solution, of course, is to have a mechanism for providing change. This can be done using transactions with multiple inputs and outputs, which we’ll discuss in the next section.关于输入需要注意的一点是，没有明确指定在本次交易中应该花费前一笔交易中的多少比特币。实际上，前一笔交易的第 n = 0 个输出中的所有比特币都被花费了。因此，例如，如果前一笔交易的第 n = 0 个输出是 2 个比特币，那么在本次交易中将花费 2 个比特币。这似乎是一个不方便的限制——就像试图用一张 20 美元的钞票买面包，却无法把钞票破开。当然，解决方案是有一种提供找零的机制。这可以通过使用具有多个输入和输出的交易来实现，我们将在下一节中讨论。

Lines 12 through 14 define the output from the transaction. In particular, line 13 tells us the value of the output, 0.319 bitcoins. Line 14 is somewhat complicated. The main thing to note is that the string a7db6f... is the Bitcoin address of the intended recipient of the funds (written in hexadecimal). In fact, Line 14 is actually an expression in Bitcoin’s scripting language. I’m not going to describe that language in detail in this post, the important thing to take away now is just that a7db6f... is the Bitcoin address.Lines 12 through 14 define the output from the transaction. In particular, line 13 tells us the value of the output, 0.319 bitcoins. Line 14 is somewhat complicated. The main thing to note is that the string a7db6f... is the Bitcoin address of the intended recipient of the funds (written in hexadecimal). In fact, Line 14 is actually an expression in Bitcoin’s scripting language. I’m not going to describe that language in detail in this post, the important thing to take away now is just that a7db6f... is the Bitcoin address.第 12 行到第 14 行定义了交易的输出。特别是，第 13 行告诉我们输出的值为 0.319 比特币。第 14 行有点复杂。主要要注意的是，字符串 a7db6f……是资金预期接收者的比特币地址（以十六进制表示）。事实上，第 14 行实际上是比特币脚本语言中的一个表达式。在这篇文章中我不会详细描述那种语言，现在要记住的重要一点是 a7db6f……是比特币地址。

You can now see, by the way, how Bitcoin addresses the question I swept under the rug in the last section: where do Bitcoin serial numbers come from? In fact, the role of the serial number is played by transaction hashes. In the transaction above, for example, the recipient is receiving 0.319 Bitcoins, which come out of the first output of an earlier transaction with hash 2007ae... (line 9). If you go and look in the block chain for that transaction, you’d see that its output comes from a still earlier transaction. And so on.You can now see, by the way, how Bitcoin addresses the question I swept under the rug in the last section: where do Bitcoin serial numbers come from? In fact, the role of the serial number is played by transaction hashes. In the transaction above, for example, the recipient is receiving 0.319 Bitcoins, which come out of the first output of an earlier transaction with hash 2007ae... (line 9). If you go and look in the block chain for that transaction, you’d see that its output comes from a still earlier transaction. And so on.现在你可以看到，顺便说一下，比特币是如何解决我在上一节中搁置的问题的：比特币序列号从何而来？事实上，序列号的作用是由交易哈希值来扮演的。例如，在上述交易中，接收方收到 0.319 个比特币，这些比特币来自具有哈希值 2007ae……的早期交易的第一个输出（第 9 行）。如果你去区块链中查找该交易，你会看到它的输出来自一个更早的交易。依此类推。

There are two clever things about using transaction hashes instead of serial numbers. First, in Bitcoin there’s not really any separate, persistent “coins” at all, just a long series of transactions in the block chain. It’s a clever idea to realize that you don’t need persistent coins, and can just get by with a ledger of transactions. Second, by operating in this way we remove the need for any central authority issuing serial numbers. Instead, the serial numbers can be self-generated, merely by hashing the transaction.There are two clever things about using transaction hashes instead of serial numbers. First, in Bitcoin there’s not really any separate, persistent “coins” at all, just a long series of transactions in the block chain. It’s a clever idea to realize that you don’t need persistent coins, and can just get by with a ledger of transactions. Second, by operating in this way we remove the need for any central authority issuing serial numbers. Instead, the serial numbers can be self-generated, merely by hashing the transaction.使用交易哈希而不是序列号有两个巧妙之处。首先，在比特币中实际上根本没有任何单独的、持久的“硬币”，只有区块链中的一长串交易。意识到你不需要持久的硬币，并且可以仅通过交易分类账来应付，这是一个聪明的想法。其次，通过这种方式操作，我们消除了任何中央机构发布序列号的需要。相反，序列号可以通过对交易进行哈希运算来自行生成。

In fact, it’s possible to keep following the chain of transactions further back in history. Ultimately, this process must terminate. This can happen in one of two ways. The first possibilitty is that you’ll arrive at the very first Bitcoin transaction, contained in the so-called [Genesis block](https://en.bitcoin.it/wiki/Genesis_block). This is a special transaction, having no inputs, but a 50 Bitcoin output. In other words, this transaction establishes an initial money supply. The Genesis block is treated separately by Bitcoin clients, and I won’t get into the details here, although it’s along similar lines to the transaction above. You can see the deserialized raw data [here](http://blockexplorer.com/rawblock/000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f), and read about the Genesis block [here](https://en.bitcoin.it/wiki/Genesis_block).In fact, it’s possible to keep following the chain of transactions further back in history. Ultimately, this process must terminate. This can happen in one of two ways. The first possibilitty is that you’ll arrive at the very first Bitcoin transaction, contained in the so-called [Genesis block](https://en.bitcoin.it/wiki/Genesis_block). This is a special transaction, having no inputs, but a 50 Bitcoin output. In other words, this transaction establishes an initial money supply. The Genesis block is treated separately by Bitcoin clients, and I won’t get into the details here, although it’s along similar lines to the transaction above. You can see the deserialized raw data [here](http://blockexplorer.com/rawblock/000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f), and read about the Genesis block [here](https://en.bitcoin.it/wiki/Genesis_block).事实上，有可能在历史中进一步追溯交易链。最终，这个过程必须终止。这可以通过两种方式之一发生。第一种可能性是，你会到达第一个比特币交易，包含在所谓的“创世区块”中。这是一个特殊的交易，没有输入，但有一个 50 比特币的输出。换句话说，这个交易建立了初始货币供应。创世区块由比特币客户端单独处理，我在这里不会深入细节，尽管它与上述交易类似。你可以在这里看到反序列化的原始数据，在这里阅读关于创世区块的内容。

The second possibility when you follow a chain of transactions back in time is that eventually you’ll arrive at a so-called *coinbase transaction*. With the exception of the Genesis block, every block of transactions in the block chain starts with a special coinbase transaction. This is the transaction rewarding the miner who validated that block of transactions. It uses a similar but not identical format to the transaction above. I won’t go through the format in detail, but if you want to see an example, see [here](http://blockexplorer.com/rawtx/c3facb1e90fdbaf0ee59e342a00e1c82588af138784fabad7398eb9dab3a0e5a). You can read a little more about coinbase transactions [here](https://en.bitcoin.it/wiki/Protocol_specification#Transaction_Verification).The second possibility when you follow a chain of transactions back in time is that eventually you’ll arrive at a so-called *coinbase transaction*. With the exception of the Genesis block, every block of transactions in the block chain starts with a special coinbase transaction. This is the transaction rewarding the miner who validated that block of transactions. It uses a similar but not identical format to the transaction above. I won’t go through the format in detail, but if you want to see an example, see [here](http://blockexplorer.com/rawtx/c3facb1e90fdbaf0ee59e342a00e1c82588af138784fabad7398eb9dab3a0e5a). You can read a little more about coinbase transactions [here](https://en.bitcoin.it/wiki/Protocol_specification#Transaction_Verification).当你沿着交易链追溯时间时，第二种可能性是最终你会到达一个所谓的“coinbase 交易”。除了创世区块之外，区块链中的每个交易区块都以一个特殊的 coinbase 交易开始。这是对验证该交易区块的矿工的奖励交易。它使用与上述交易类似但不完全相同的格式。我不会详细介绍其格式，但如果你想查看一个示例，请点击“这里”。你可以在这里阅读更多关于 coinbase 交易的信息。

Something I haven’t been precise about above is what exactly is being signed by the digital signature in line 11. The obvious thing to do is for the payer to sign the whole transaction (apart from the transaction hash, which, of course, must be generated later). Currently, this is *not* what is done – some pieces of the transaction are omitted. This makes some pieces of the transaction [malleable](https://en.bitcoin.it/wiki/Transaction_Malleability), i.e., they can be changed later. However, this malleability does not include the amounts being paid out, senders and recipients, which can’t be changed later. I must admit I haven’t dug down into the details here. I gather that this malleability is under discussion in the Bitcoin developer community, and there are efforts afoot to reduce or eliminate this malleability.Something I haven’t been precise about above is what exactly is being signed by the digital signature in line 11. The obvious thing to do is for the payer to sign the whole transaction (apart from the transaction hash, which, of course, must be generated later). Currently, this is *not* what is done – some pieces of the transaction are omitted. This makes some pieces of the transaction [malleable](https://en.bitcoin.it/wiki/Transaction_Malleability), i.e., they can be changed later. However, this malleability does not include the amounts being paid out, senders and recipients, which can’t be changed later. I must admit I haven’t dug down into the details here. I gather that this malleability is under discussion in the Bitcoin developer community, and there are efforts afoot to reduce or eliminate this malleability.上面我没有精确说明的一点是，第 11 行中的数字签名到底对什么进行签名。显而易见的做法是让付款人对整个交易进行签名（交易哈希除外，当然，交易哈希必须在后面生成）。目前，情况并非如此——交易的某些部分被省略了。这使得交易的某些部分具有“可塑性”，即它们可以在以后被更改。然而，这种可塑性不包括支付金额、发送方和接收方，这些在以后不能被更改。我必须承认，我在这里还没有深入研究细节。我了解到，比特币开发者社区正在讨论这种可塑性，并且正在努力减少或消除这种可塑性。

### Transactions with multiple inputs and outputsTransactions with multiple inputs and outputs具有多个输入和输出的交易。

In the last section I described how a transaction with a single input and a single output works. In practice, it’s often extremely convenient to create Bitcoin transactions with multiple inputs or multiple outputs. I’ll talk below about why this can be useful. But first let’s take a look at the data from an [actual transaction](http://blockexplorer.com/tx/99383066a5140b35b93e8f84ef1d40fd720cc201d2aa51915b6c33616587b94f): In the last section I described how a transaction with a single input and a single output works. In practice, it’s often extremely convenient to create Bitcoin transactions with multiple inputs or multiple outputs. I’ll talk below about why this can be useful. But first let’s take a look at the data from an [actual transaction](http://blockexplorer.com/tx/99383066a5140b35b93e8f84ef1d40fd720cc201d2aa51915b6c33616587b94f): 在最后一部分中，我描述了具有单个输入和单个输出的交易是如何运作的。实际上，创建具有多个输入或多个输出的比特币交易通常非常方便。下面我将谈谈为什么这可能是有用的。但首先让我们看一下来自一个[实际交易](http://blockexplorer.com/tx/99383066a5140b35b93e8f84ef1d40fd720cc201d2aa51915b6c33616587b94f)的数据：

```
1. {"hash":"993830...",
2. "ver":1,
3. "vin_sz":3,
4.  "vout_sz":2,
5.  "lock_time":0,
6.  "size":552,
7.  "in":[
8.    {"prev_out":{
9.      "hash":"3beabc...",
10.        "n":0},
11.     "scriptSig":"304402... 04c7d2..."},
12.    {"prev_out":{
13.        "hash":"fdae9b...",
14.        "n":0},
15.      "scriptSig":"304502... 026e15..."},
16.    {"prev_out":{
17.        "hash":"20c86b...",
18.        "n":1},
19.      "scriptSig":"304402... 038a52..."}],
20.  "out":[
21.    {"value":"0.01068000",
22.      "scriptPubKey":"OP_DUP OP_HASH160 e8c306... OP_EQUALVERIFY OP_CHECKSIG"},
23.    {"value":"4.00000000",
24.      "scriptPubKey":"OP_DUP OP_HASH160 d644e3... OP_EQUALVERIFY OP_CHECKSIG"}]}
1. {"hash":"993830...",
2. "ver":1,
3. "vin_sz":3,
4.  "vout_sz":2,
5.  "lock_time":0,
6.  "size":552,
7.  "in":[
8.    {"prev_out":{
9.      "hash":"3beabc...",
10.        "n":0},
11.     "scriptSig":"304402... 04c7d2..."},
12.    {"prev_out":{
13.        "hash":"fdae9b...",
14.        "n":0},
15.      "scriptSig":"304502... 026e15..."},
16.    {"prev_out":{
17.        "hash":"20c86b...",
18.        "n":1},
19.      "scriptSig":"304402... 038a52..."}],
20.  "out":[
21.    {"value":"0.01068000",
22.      "scriptPubKey":"OP_DUP OP_HASH160 e8c306... OP_EQUALVERIFY OP_CHECKSIG"},
23.    {"value":"4.00000000",
24.      "scriptPubKey":"OP_DUP OP_HASH160 d644e3... OP_EQUALVERIFY OP_CHECKSIG"}]}
1. {"hash":"993830...",2. "ver":1,3. "vin_sz":3,4.  "vout_sz":2,5.  "lock_time":0,6.  "size":552,7.  "in":[8.    {"prev_out":{9.      "hash":"3beabc...",10.        "n":0},11.     "scriptSig":"304402... 04c7d2..."},12.    {"prev_out":{13.        "hash":"fdae9b...",14.        "n":0},15.      "scriptSig":"304502... 026e15..."},16.    {"prev_out":{17.        "hash":"20c86b...",18.        "n":1},19.      "scriptSig":"304402... 038a52..."}],20.  "out":[21.    {"value":"0.01068000",22.      "scriptPubKey":"OP_DUP OP_HASH160 e8c306... OP_EQUALVERIFY OP_CHECKSIG"},23.    {"value":"4.00000000",24.      "scriptPubKey":"OP_DUP OP_HASH160 d644e3... OP_EQUALVERIFY OP_CHECKSIG"}]}
```

Let’s go through the data, line by line. It’s very similar to the single-input-single-output transaction, so I’ll do this pretty quickly. Let’s go through the data, line by line. It’s very similar to the single-input-single-output transaction, so I’ll do this pretty quickly.让我们逐行查看数据。它与单输入单输出事务非常相似，所以我会很快完成这个任务。

Line 1 contains the hash of the remainder of the transaction. This is used as an identifier for the transaction.Line 1 contains the hash of the remainder of the transaction. This is used as an identifier for the transaction.第 1 行包含交易剩余部分的哈希值。这用作交易的标识符。

Line 2 tells us that this is a transaction in version 1 of the Bitcoin protocol.Line 2 tells us that this is a transaction in version 1 of the Bitcoin protocol.第 2 行告诉我们，这是比特币协议第 1 版中的一笔交易。

Lines 3 and 4 tell us that the transaction has three inputs and two outputs, respectively.Lines 3 and 4 tell us that the transaction has three inputs and two outputs, respectively.第3行和第4行告诉我们，该交易分别有三个输入和两个输出。

Line 5 contains the lock\_time. As in the single-input-single-output case this is set to 0, which means the transaction is finalized immediately.Line 5 contains the lock\_time. As in the single-input-single-output case this is set to 0, which means the transaction is finalized immediately.“Line 5 包含锁定期时间。与单输入单输出的情况一样，这里设置为 0，这意味着交易立即完成。”

Line 6 tells us the size of the transaction in bytes.Line 6 tells us the size of the transaction in bytes.第 6 行告诉我们交易的大小（以字节为单位）。

Lines 7 through 19 define a list of the inputs to the transaction. Each corresponds to an output from a previous Bitcoin transaction.Lines 7 through 19 define a list of the inputs to the transaction. Each corresponds to an output from a previous Bitcoin transaction.第 7 行到第 19 行定义了一个交易输入列表。每个输入对应于先前比特币交易的一个输出。

The first input is defined in lines 8 through 11. The first input is defined in lines 8 through 11. “第一个输入在第 8 行到第 11 行中定义。”

In particular, lines 8 through 10 tell us that the input is to be taken from the n=0th output from the transaction with hash 3beabc.... Line 11 contains the signature, followed by a space, and then the public key of the person sending the bitcoins.In particular, lines 8 through 10 tell us that the input is to be taken from the n=0th output from the transaction with hash 3beabc.... Line 11 contains the signature, followed by a space, and then the public key of the person sending the bitcoins.特别是，第 8 至 10 行告诉我们，输入将从哈希值为 3beabc……的交易的第 n = 0 个输出中获取。第 11 行包含签名，后面跟一个空格，然后是发送比特币的人的公钥。

Lines 12 through 15 define the second input, with a similar format to lines 8 through 11. And lines 16 through 19 define the third input.Lines 12 through 15 define the second input, with a similar format to lines 8 through 11. And lines 16 through 19 define the third input.第 12 行至第 15 行定义了第二个输入，其格式与第 8 行至第 11 行类似。第 16 行至第 19 行定义了第三个输入。

Lines 20 through 24 define a list containing the two outputs from the transaction.Lines 20 through 24 define a list containing the two outputs from the transaction.第 20 行到第 24 行定义了一个包含交易的两个输出的列表。

The first output is defined in lines 21 and 22. Line 21 tells us the value of the output, 0.01068000 bitcoins. As before, line 22 is an expression in Bitcoin’s scripting language. The main thing to take away here is that the string e8c30622... is the Bitcoin address of the intended recipient of the funds.The first output is defined in lines 21 and 22. Line 21 tells us the value of the output, 0.01068000 bitcoins. As before, line 22 is an expression in Bitcoin’s scripting language. The main thing to take away here is that the string e8c30622... is the Bitcoin address of the intended recipient of the funds.第一个输出在第 21 行和第 22 行中定义。第 21 行告诉我们输出的值为 0.01068000 比特币。和之前一样，第 22 行是比特币脚本语言中的一个表达式。这里要记住的主要一点是，字符串 e8c30622……是资金接收方的比特币地址。

The second output is defined lines 23 and 24, with a similar format to the first output.The second output is defined lines 23 and 24, with a similar format to the first output.第二个输出在第 23 和 24 行定义，格式与第一个输出类似。

One apparent oddity in this description is that although each output has a Bitcoin value associated to it, the inputs do not. Of course, the values of the respective inputs can be found by consulting the corresponding outputs in earlier transactions. In a standard Bitcoin transaction, the sum of all the inputs in the transaction must be at least as much as the sum of all the outputs. (The only exception to this principle is the Genesis block, and in coinbase transactions, both of which add to the overall Bitcoin supply.) If the inputs sum up to more than the outputs, then the excess is used as a *transaction fee*. This is paid to whichever miner successfully validates the block which the current transaction is a part of.One apparent oddity in this description is that although each output has a Bitcoin value associated to it, the inputs do not. Of course, the values of the respective inputs can be found by consulting the corresponding outputs in earlier transactions. In a standard Bitcoin transaction, the sum of all the inputs in the transaction must be at least as much as the sum of all the outputs. (The only exception to this principle is the Genesis block, and in coinbase transactions, both of which add to the overall Bitcoin supply.) If the inputs sum up to more than the outputs, then the excess is used as a *transaction fee*. This is paid to whichever miner successfully validates the block which the current transaction is a part of.这段描述中的一个明显奇怪之处在于，尽管每个输出都有一个与之相关的比特币价值，但输入却没有。当然，各个输入的价值可以通过查询早期交易中的相应输出找到。在标准的比特币交易中，交易中的所有输入总和必须至少与所有输出总和一样多。（这个原则的唯一例外是创世区块和币基交易，这两者都会增加比特币的总供应量。）如果输入总和超过输出总和，那么超出部分将用作“交易费”。这笔费用支付给成功验证当前交易所在区块的矿工。

That’s all there is to multiple-input-multiple-output transactions! They’re a pretty simple variation on single-input-single-output-transactions.That’s all there is to multiple-input-multiple-output transactions! They’re a pretty simple variation on single-input-single-output-transactions.这就是多输入多输出交易的全部内容！它们是单输入单输出交易的一个非常简单的变体。

One nice application of multiple-input-multiple-output transactions is the idea of *change*. Suppose, for example, that I want to send you 0.15 bitcoins. I can do so by spending money from a previous transaction in which I received 0.2 bitcoins. Of course, I don’t want to send you the entire 0.2 bitcoins. The solution is to send you 0.15 bitcoins, and to send 0.05 bitcoins to a Bitcoin address which I own. Those 0.05 bitcoins are the change. Of course, it differs a little from the change you might receive in a store, since change in this case is what you pay yourself. But the broad idea is similar.One nice application of multiple-input-multiple-output transactions is the idea of *change*. Suppose, for example, that I want to send you 0.15 bitcoins. I can do so by spending money from a previous transaction in which I received 0.2 bitcoins. Of course, I don’t want to send you the entire 0.2 bitcoins. The solution is to send you 0.15 bitcoins, and to send 0.05 bitcoins to a Bitcoin address which I own. Those 0.05 bitcoins are the change. Of course, it differs a little from the change you might receive in a store, since change in this case is what you pay yourself. But the broad idea is similar.多输入多输出交易的一个很好的应用是“找零”的概念。例如，假设我想给你发送 0.15 个比特币。我可以通过花费我之前收到 0.2 个比特币的交易中的钱来做到这一点。当然，我不想把整个 0.2 个比特币都发给你。解决方案是给你发送 0.15 个比特币，并将 0.05 个比特币发送到我自己拥有的一个比特币地址。那 0.05 个比特币就是找零。当然，这与你在商店可能收到的找零略有不同，因为在这种情况下，找零是你付给自己的钱。但大致的概念是相似的。

### ConclusionConclusion 结论

That completes a basic description of the main ideas behind Bitcoin. Of course, I’ve omitted many details – this isn’t a formal specification. But I have described the main ideas behind the most common use cases for Bitcoin.That completes a basic description of the main ideas behind Bitcoin. Of course, I’ve omitted many details – this isn’t a formal specification. But I have described the main ideas behind the most common use cases for Bitcoin.这就完成了对比特币背后主要思想的基本描述。当然，我省略了很多细节——这不是一个正式的规范说明。但我已经描述了比特币最常见用例背后的主要思想。

While the rules of Bitcoin are simple and easy to understand, that doesn’t mean that it’s easy to understand all the consequences of the rules. There is vastly more that could be said about Bitcoin, and I’ll investigate some of these issues in future posts.While the rules of Bitcoin are simple and easy to understand, that doesn’t mean that it’s easy to understand all the consequences of the rules. There is vastly more that could be said about Bitcoin, and I’ll investigate some of these issues in future posts.虽然比特币的规则简单易懂，但这并不意味着很容易理解这些规则的所有后果。关于比特币还有很多可以说的，我将在未来的文章中探讨其中的一些问题。

For now, though, I’ll wrap up by addressing a few loose ends.For now, though, I’ll wrap up by addressing a few loose ends.不过，现在我将通过处理一些未解决的问题来结束。

**How anonymous is Bitcoin?** Many people claim that Bitcoin can be used anonymously. This claim has led to the formation of marketplaces such as [Silk Road](http://en.wikipedia.org/wiki/Silk_Road_\(marketplace\)) (and various successors), which specialize in illegal goods. However, the claim that Bitcoin is anonymous is a myth. The block chain is public, meaning that it’s possible for anyone to see every Bitcoin transaction ever. Although Bitcoin addresses aren’t immediately associated to real-world identities, computer scientists have done a [great deal of work](http://scholar.google.com/scholar?q=de-anonymization) figuring out how to de-anonymize “anonymous” social networks. The block chain is a marvellous target for these techniques. I will be extremely surprised if the great majority of Bitcoin users are not identified with relatively high confidence and ease in the near future. The confidence won’t be high enough to achieve convictions, but will be high enough to identify likely targets. Furthermore, identification will be retrospective, meaning that someone who bought drugs on Silk Road in 2011 will still be identifiable on the basis of the block chain in, say, 2020. These de-anonymization techniques are well known to computer scientists, and, one presumes, therefore to the NSA. I would not be at all surprised if the NSA and other agencies have already de-anonymized many users. It is, in fact, ironic that Bitcoin is often touted as anonymous. It’s not. Bitcoin is, instead, perhaps the most open and transparent financial instrument the world has ever seen.**How anonymous is Bitcoin?** Many people claim that Bitcoin can be used anonymously. This claim has led to the formation of marketplaces such as [Silk Road](http://en.wikipedia.org/wiki/Silk_Road_\(marketplace\)) (and various successors), which specialize in illegal goods. However, the claim that Bitcoin is anonymous is a myth. The block chain is public, meaning that it’s possible for anyone to see every Bitcoin transaction ever. Although Bitcoin addresses aren’t immediately associated to real-world identities, computer scientists have done a [great deal of work](http://scholar.google.com/scholar?q=de-anonymization) figuring out how to de-anonymize “anonymous” social networks. The block chain is a marvellous target for these techniques. I will be extremely surprised if the great majority of Bitcoin users are not identified with relatively high confidence and ease in the near future. The confidence won’t be high enough to achieve convictions, but will be high enough to identify likely targets. Furthermore, identification will be retrospective, meaning that someone who bought drugs on Silk Road in 2011 will still be identifiable on the basis of the block chain in, say, 2020. These de-anonymization techniques are well known to computer scientists, and, one presumes, therefore to the NSA. I would not be at all surprised if the NSA and other agencies have already de-anonymized many users. It is, in fact, ironic that Bitcoin is often touted as anonymous. It’s not. Bitcoin is, instead, perhaps the most open and transparent financial instrument the world has ever seen.\*\*《比特币的匿名性究竟如何？》\*\* 许多人声称比特币可以匿名使用。这种说法导致了诸如\*\*《丝绸之路》\*\*（及其各种后续平台）等专门从事非法商品交易的市场的形成。然而，比特币是匿名的这种说法是一个神话。区块链是公开的，这意味着任何人都可以看到有史以来的每一笔比特币交易。虽然比特币地址并非立即与现实世界的身份相关联，但计算机科学家们已经做了\*\*《大量工作》\*\*来弄清楚如何对“匿名”社交网络进行去匿名化。区块链是这些技术的一个绝佳目标。如果在不久的将来，绝大多数比特币用户不能相对容易且有较高把握地被识别出来，我会非常惊讶。这种把握还不足以实现定罪，但足以确定可能的目标。此外，识别将是追溯性的，这意味着 2011 年在丝绸之路购买毒品的人在比如说 2020 年仍然可以根据区块链被识别出来。这些去匿名化技术为计算机科学家所熟知，并且可以推测，美国国家安全局（NSA）也知道。如果美国国家安全局和其他机构已经对许多用户进行了去匿名化，我一点也不会感到惊讶。事实上，具有讽刺意味的是，比特币经常被吹捧为匿名的。但它不是。相反，比特币可能是世界上有史以来最开放和透明的金融工具。

**Can you get rich with Bitcoin?** Well, maybe. Tim O’Reilly [once said](http://radar.oreilly.com/2006/05/my-commencement-speech-at-sims.html): “Money is like gas in the car – you need to pay attention or you’ll end up on the side of the road – but a well-lived life is not a tour of gas stations!” Much of the interest in Bitcoin comes from people whose life mission seems to be to find a *really big* gas station. I must admit I find this perplexing. What is, I believe, much more interesting and enjoyable is to think of Bitcoin and other cryptocurrencies as a way of enabling new forms of collective behaviour. That’s intellectually fascinating, offers marvellous creative possibilities, is socially valuable, and may just also put some money in the bank. But if money in the bank is your primary concern, then I believe that other strategies are much more likely to succeed.**Can you get rich with Bitcoin?** Well, maybe. Tim O’Reilly [once said](http://radar.oreilly.com/2006/05/my-commencement-speech-at-sims.html): “Money is like gas in the car – you need to pay attention or you’ll end up on the side of the road – but a well-lived life is not a tour of gas stations!” Much of the interest in Bitcoin comes from people whose life mission seems to be to find a *really big* gas station. I must admit I find this perplexing. What is, I believe, much more interesting and enjoyable is to think of Bitcoin and other cryptocurrencies as a way of enabling new forms of collective behaviour. That’s intellectually fascinating, offers marvellous creative possibilities, is socially valuable, and may just also put some money in the bank. But if money in the bank is your primary concern, then I believe that other strategies are much more likely to succeed.\*\*《你能靠比特币致富吗？》\*\* 嗯，也许吧。蒂姆·奥莱利曾经说过：“金钱就像汽车里的汽油——你需要留意，否则你会最终停在路边——但美好生活不是对加油站的巡游！”对比特币的大部分兴趣来自那些人生使命似乎是找到一个\*\*《非常大的》\*\*加油站的人。我必须承认，我觉得这很令人困惑。我认为，更有趣和令人愉快的是将比特币和其他加密货币视为一种实现新形式集体行为的方式。这在智力上很吸引人，提供了奇妙的创造性可能性，具有社会价值，并且可能也会在银行里存一些钱。但是，如果银行里的钱是你的首要关注点，那么我相信其他策略更有可能成功。

**Details I’ve omitted:** Although this post has described the main ideas behind Bitcoin, there are many details I haven’t mentioned. One is a nice space-saving trick used by the protocol, based on a data structure known as a [Merkle tree](http://en.wikipedia.org/wiki/Merkle_tree). It’s a detail, but a splendid detail, and worth checking out if fun data structures are your thing. You can get an overview in the [original Bitcoin paper](http://bitcoin.org/bitcoin.pdf). Second, I’ve said little about the [Bitcoin network](https://en.bitcoin.it/wiki/Network) – questions like how the network deals with denial of service attacks, how nodes [join and leave the network](https://en.bitcoin.it/wiki/Satoshi_Client_Node_Discovery), and so on. This is a fascinating topic, but it’s also something of a mess of details, and so I’ve omitted it. You can read more about it at some of the links above.**Details I’ve omitted:** Although this post has described the main ideas behind Bitcoin, there are many details I haven’t mentioned. One is a nice space-saving trick used by the protocol, based on a data structure known as a [Merkle tree](http://en.wikipedia.org/wiki/Merkle_tree). It’s a detail, but a splendid detail, and worth checking out if fun data structures are your thing. You can get an overview in the [original Bitcoin paper](http://bitcoin.org/bitcoin.pdf). Second, I’ve said little about the [Bitcoin network](https://en.bitcoin.it/wiki/Network) – questions like how the network deals with denial of service attacks, how nodes [join and leave the network](https://en.bitcoin.it/wiki/Satoshi_Client_Node_Discovery), and so on. This is a fascinating topic, but it’s also something of a mess of details, and so I’ve omitted it. You can read more about it at some of the links above.**我省略的细节：**尽管这篇文章已经描述了比特币背后的主要思想，但还有很多细节我没有提到。其中一个是协议使用的一种很好的节省空间的技巧，基于一种被称为[默克尔树](http://en.wikipedia.org/wiki/Merkle_tree)的数据结构。这是一个细节，但也是一个很棒的细节，如果有趣的数据结构是你的菜，那么值得去了解一下。你可以在[比特币原始论文](http://bitcoin.org/bitcoin.pdf)中获得概述。第二，我对比特币网络说得很少——比如网络如何处理拒绝服务攻击，节点[如何加入和离开网络](https://en.bitcoin.it/wiki/Satoshi_Client_Node_Discovery)等等问题。这是一个引人入胜的话题，但也是一堆细节，所以我把它省略了。你可以在上面的一些链接中阅读更多相关内容。

**Bitcoin scripting:** In this post I’ve explained Bitcoin as a form of digital, online money. But this is only a small part of a much bigger and more interesting story. As we’ve seen, every Bitcoin transaction is associated to a script in the Bitcoin programming language. The scripts we’ve seen in this post describe simple transactions like “Alice gave Bob 10 bitcoins”. But the scripting language can also be used to express far more complicated transactions. To put it another way, Bitcoin is *programmable money*. In later posts I will explain the scripting system, and how it is possible to use Bitcoin scripting as a platform to experiment with all sorts of amazing financial instruments.**Bitcoin scripting:** In this post I’ve explained Bitcoin as a form of digital, online money. But this is only a small part of a much bigger and more interesting story. As we’ve seen, every Bitcoin transaction is associated to a script in the Bitcoin programming language. The scripts we’ve seen in this post describe simple transactions like “Alice gave Bob 10 bitcoins”. But the scripting language can also be used to express far more complicated transactions. To put it another way, Bitcoin is *programmable money*. In later posts I will explain the scripting system, and how it is possible to use Bitcoin scripting as a platform to experiment with all sorts of amazing financial instruments.\*\*《比特币脚本》：\*\*在这篇文章中，我已经将比特币解释为一种数字在线货币形式。但这只是一个更大、更有趣的故事的一小部分。正如我们所看到的，每一笔比特币交易都与比特币编程语言中的一个脚本相关联。我们在这篇文章中看到的脚本描述了简单的交易，比如“爱丽丝给了鲍勃 10 个比特币”。但是这种脚本语言也可以用来表达更加复杂的交易。换句话说，比特币是\*\*可编程货币\*\*。在以后的文章中，我将解释脚本系统，以及如何将比特币脚本用作一个平台来试验各种令人惊叹的金融工具。

*Thanks for reading. Enjoy the essay? You can tip me with Bitcoin (!) at address: **********************************. You may also enjoy the [first chapter](http://neuralnetworksanddeeplearning.com/chap1.html) of my forthcoming book on neural networks and deep learning, and may wish to [follow me on Twitter](https://twitter.com/michael_nielsen).Thanks for reading. Enjoy the essay? You can tip me with Bitcoin (!) at address: **********************************. You may also enjoy the [first chapter](http://neuralnetworksanddeeplearning.com/chap1.html) of my forthcoming book on neural networks and deep learning, and may wish to [follow me on Twitter](https://twitter.com/michael_nielsen).感谢阅读。喜欢这篇文章吗？你可以用比特币（！）向我打赏，地址是：**********************************。你可能也会喜欢我即将出版的关于神经网络和深度学习的书的第一章，并且可能希望在推特上关注我。*

### FootnoteFootnote 脚注

\[1\] In the United States the question “Is money a form of speech?” is an important legal question, because of the protection afforded speech under the US Constitution. In my (legally uninformed) opinion digital money may make this issue more complicated. As we’ll see, the Bitcoin protocol is really a way of standing up before the rest of the world (or at least the rest of the Bitcoin network) and avowing “I’m going to give such-and-such a number of bitcoins to so-and-so a person” in a way that’s extremely difficult to repudiate. At least naively, it looks more like speech than exchanging copper coins, say.\[1\] In the United States the question “Is money a form of speech?” is an important legal question, because of the protection afforded speech under the US Constitution. In my (legally uninformed) opinion digital money may make this issue more complicated. As we’ll see, the Bitcoin protocol is really a way of standing up before the rest of the world (or at least the rest of the Bitcoin network) and avowing “I’m going to give such-and-such a number of bitcoins to so-and-so a person” in a way that’s extremely difficult to repudiate. At least naively, it looks more like speech than exchanging copper coins, say.在美国，“金钱是一种言论形式吗？”这个问题是一个重要的法律问题，因为美国宪法对言论提供了保护。在我（法律知识匮乏）的观点中，数字货币可能会使这个问题更加复杂。正如我们将看到的，比特币协议实际上是一种在世界其他地方（或者至少是比特币网络的其他部分）面前表明“我要把这么多数量的比特币给某某人”的方式，这种方式极难被否认。至少从表面上看，它比交换铜币更像是言论。