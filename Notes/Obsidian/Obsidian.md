## Tasks
## Source
- [Home - Obsidian Help](https://help.obsidian.md/Home)
## Plugins
- [charts](https://charts.phib.ro/Meta/Charts/Chart+from+Table)
- [tasks](https://publish.obsidian.md/tasks/Introduction)
	- [About Queries - Tasks User Guide - Obsidian Publish](https://publish.obsidian.md/tasks/Queries/About+Queries)
- [remainder](https://uphy.github.io/obsidian-reminder/)
- [Home - Breadcrumbs - Obsidian Publish](https://publish.obsidian.md/breadcrumbs-docs/Home)
- Style Settings: for theme author
- Highlightr
-  themes
	- AnupPuccin + style settings
- local rest api
	- [Local Rest API for Obsidian: Interactive API Documentation](https://coddingtonbear.github.io/obsidian-local-rest-api/)
- obsidian clipper
	- [Understanding Templates · coddingtonbear/obsidian-web Wiki · GitHub](https://github.com/coddingtonbear/obsidian-web/wiki/Understanding-Templates)
## Style
- create css snippets
- use selectors: `body`, `.theme-dark`, `:root`
- re-define [CSS variables ](https://docs.obsidian.md/Reference/CSS+variables/CSS+variables)
- [a sample](https://github.com/obsidianmd/obsidian-sample-theme/blob/master/theme.css)
## Formats
- [Callout](https://help.obsidian.md/Editing+and+formatting/Callouts)
> [!tip]- callout 
> test

## Notes
### Block identifier
- `[[2023-01-01#^37066d]]`
-  by adding a blank space followed by the identifier, for example `^quote-of-the-day`, at the end of a block
### Shortcuts
- Next tab: `⌃`+`⇥` 
- 按纯文本粘贴：即 Ctrl+Shift+V
## Ref
- [My Obsidian setup after a month of learning and adjustments. : r/ObsidianMD](https://www.reddit.com/r/ObsidianMD/comments/12kfo7j/my_obsidian_setup_after_a_month_of_learning_and/)

- [Creating a Life OS Dashboard Setup in Obsidian for 2024(Part 1) - Prakash Joshi Pax](https://prakashjoshipax.com/obsidian-dashboard-setup/)