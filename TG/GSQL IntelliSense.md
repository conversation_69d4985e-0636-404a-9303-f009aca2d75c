## Ref
- [mycli](https://www.mycli.net/)
- [GitHub - and<PERSON><PERSON>/sqlparse: A non-validating SQL parser module for Python](https://github.com/and<PERSON><PERSON>/sqlparse)
- [sqls](https://github.com/sqls-server/sqls)
- [c# - A New and Full Implementation of Generic Intellisense - Stack Overflow](https://stackoverflow.com/questions/9556026/a-new-and-full-implementation-of-generic-intellisense)
- [How to Implement Autocomplete | Developer Sam Blog](https://developersam.com/blog/2020/01/09/implement-autocomplete)
- codemirror
	- https://discuss.codemirror.net/t/schemacompletionsource-example/5196/99
	- [GitHub - codemirror/lang-sql: SQL language support for the CodeMirror code editor](https://github.com/codemirror/lang-sql)
## Key Features of IntelliSense
1. **Code Completion:** Suggests possible completions for partially typed words.
2. **Parameter Info:** Displays information about function parameters.
3. **Quick Info:** Shows information about the symbol under the cursor.
4. **Error Checking:** Highlights syntax and semantic errors in real-time.
5. **Code Snippets:** Inserts commonly used code patterns.
