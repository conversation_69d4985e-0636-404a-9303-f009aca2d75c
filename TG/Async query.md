## Ref
- [Fetching Title#rv30](https://docs.tigergraph.com/tigergraph-server/3.9/api/built-in-endpoints#_abort_a_query)
- [Fetching Title#6kn2](https://graphsql.atlassian.net/wiki/spaces/GRAP/pages/1358267028/Asynchronous+Query+Execution+Mode+-+Design+Details+and+Implementation+Note)
## Tasks
- [x] GUS: restpp proxy forward async header
- [x] GUS: restpp proxy forward requests to all nodes when there node = 'ALL'
- [x] GST: set async header when run installed query
- [x] handle query status
	- “success”: query result
	- “timeout”, “aborted”: show error, stop polling
	- “running”: schedule next poll
- [x] handle query result: display it
- [x] run query with params
- [x] GUS: use go routine and submit code reivew
```
http://localhost:4201/api/restpp/abortquery/ldbc_snb?requestid=16777229.RESTPP_1_1.1727171569050.N&node=ALL
```
## API
```bash
curl -H "Authorization: Bearer aDMhMCIW0URZZUUZBcDiE3Bpw0sbFIlx" -H "GSQL-ASYNC:true" "http://localhost:14240/restpp/query/Social/test"

# query status
curl -H "Authorization: Bearer aDMhMCIW0URZZUUZBcDiE3Bpw0sbFIlx"  "http://localhost:14240/restpp/query_status?requestid=16908291.RESTPP_1_1.1726818849783.N"

# query result
curl -H "Authorization: Bearer aDMhMCIW0URZZUUZBcDiE3Bpw0sbFIlx"  "http://localhost:14240/restpp/query_result?requestid=16908291.RESTPP_1_1.1726818849783.N"
```
## response
- run query
```json
{"error":false,"message":"The query is successfully submitted. Please check query status using the request id.","results":{"error":false,"message":"The query is successfully submitted. Please check query status using the request id.","request_id":"16908302.RESTPP_1_1.1727076396016.N"}}
```
- gus: query status
```json
{"error":false,"message":"OK","results":[[{"elapsedTime":4,"expirationTime":"2024-09-23 08:35:17.343","requestid":"16842878.RESTPP_1_1.1727080501343.N","startTime":"2024-09-23 08:35:01.343","status":"success","url":"query/Social/test?","user":""}]]}

{"error":false,"message":"OK","results":[[{"\"test works!\"":"test works!"}]]}
```
- query result
```json
{"version":{"edition":"enterprise","api":"v2","schema":0},"error":false,"message":"","results":[{"\"test works!\"":"test works!"}]}
```
- abort query
```json
{"error":false,"message":"OK","results":[[{"aborted_queries":[{"requestid":"196649.RESTPP_1_1.1727258554395.N","url":"query/ldbc_snb/tg_pagerank?"}]}]]}
```
## Design
- run query
	- gst
		- add async header when running query
	- gus
		- should forward this header in restpp proxy
- check query status, get query result, abort query
	- gst
		- call restpp endpoint with request_id
	- gus
		- forward to all restpp nodes
	