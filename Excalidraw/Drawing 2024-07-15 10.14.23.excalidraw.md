---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'


# Excalidraw Data
## Text Elements
tempFiles:
    * isTemp
    * fileId
    * queryName ^4qiZyX5A

Edit query:
    - create a temp file
    - add to active files
    - set temp file id to currentFileId
 ^RQN1HP31

Save:
    - if no fileId, create file first, assign temp file's fileId, set active file's sourceCode and content
    - else save file with fileId ^Kxe1Mnxd

Is a file unsave?
    - if no fileId, unsaved
    - else compare activeFile's sourceCode and content ^5FWvExBV

currentFileId: temp file id or backend file id ^DPCgMD1d

test cases:
    1. file item actions
      * select
      * rename
      * save
      * delete ^SMsnLANj

%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebTiAdho6IIR9BA4oZm4AbXAwUDAi6HhxdCgsKGSiyEYWdi40AA4ARn5iutZOADlOMW4WgBYAZhGeFoSEtvzIQg5iLG4IXBbq

4sJmABFUiuJuADMCMPbZvYlBgEdCAC0YAA0AVgBBNch9wnx8AGVYYKXBDyvCDMKCkNgAawQAHUSOpuHwZsDQRCED8YH8JACSECwX5JBxwtk0NMahA2HBcNg1DABgAGWknZYcZQY1AMxGYbjOB4PJraFoPHijYYJB6MmloZzDHhxJqDelNBJNACctIAbDwxYiQWDIQBhNj4NikJYAYhaCAtFqBmkp4OUuPmBqNJokoOszApgUyQIosMkAx4yu0tKm

ysGDxaqsGQYSjMkCEIymk3Ca7NJYQQZ1QavDKoeMeVjIdwjgAEliETUDkALqM/bkdIV7gcISfRmO4gE5hV1vtxGaYTzACiwXSmSrtcZQjgxFwuwGCXGtOGkbDyrjiKIHHBLbb+EZRuwkOzh3wx0R+04UC+hCMZRaavr14AYrh9B8JagScUKpgqm6aRwC+HzhMgAA6HCoNBqAAFSoBsAAqQGQTBcGoO8wQVqhMHwRcQhMDA3TvkgHaUMh/5LBU+jA

aB8g4dB8FIShUG4RhoHYaxjGoPhhHEekQJ/lATxEMojToME+xVIydRQOYBAiUm4nQOSQJ6JkuBzEwzZoH2B6IsaSZzAQFEAeUQEgcE9FcehzE0Qx6GYQgnFoXhBGkERJFArgQhQGwABK4R3mUoIEYeWkABKJsmZktPEDz5AAvu0hS/qUVGVECnQNPCPCbqS2U9H0D5TKuyrKsMtI/rM8yLBIuDDECGzbMEC5oGeF6ktiEj+QAit0LQRQACsMqz1h

83y/GUwKGtijI6iiMLEHCaAIhmyKQmirIzYCHbCMm3ZVtVZIUlSsB0umxTWCyZSXZAnKSjygz8mqVUPBqWqkl+zgTLS/LjMMIoquqmrzRtCDOsaZpWpapEDnaJZCE6hpQ265AcJ6uDetJiJ+stAZoJVwzxKNrQCkKIyivG0UpsS9LzQgWYDJGaqs5qx2I+Wla5HWl6Ns52Z6XtSNdoSe79qSg4i6OaQZFkPPTrO85M8SS4tCukbKq9BbhTu4v6aS

R4ngcRwIM+mS3veAxPper7vp+AyMkJSzDo4UA8e5MAQTZzioNggTK6guCoNRcDscEDm+7gxDECHbBB9gcmMOH4SR6gYTu6HKcIbHfl+yI2OWQLkE4uRmUSK7age4R3tob7/shBUQch0BKdp9Hufx5SScICnzBpxnLc0dnJBx/npCFxxxAl07lSKWJSySTjBVMHJ7jz8pflwGp16aQSpA6agQsGaQRkcCZ5foJX7u8R5tcwfXAdN8HWdOe3Mdj93h

DJ05/c++nCBM6tycjnMe2AC5yyLpxbyvkApBStmgUKZstyRRprFeKSUUqIlgIgDKlEZJMC6OJeU+UOiEIaL0Dg/RiRqjlDwV6aooyMjmAsB66BcAPCalsHYKsMKm2YdmCAABpTAloACyHBMB7HGp8La00sTSO1ODJaK1eBg11KiKa/xZqKNJLiA6YtiSMnJJSakF1GTXVZHdCAbDuSMO0IMCq4ZhhNEFKzCq4ouSDD5EDd69IZTeOVLydRKJIaun

QOaWG1pGS2mPIjZGLoqLo0xtjX0/pUy0j5DKVcUxJgJGFE0amSZaY5msZmbMLQWg8AjDwOUDwiyIk5hWScvNSQNhIofY+eihyix7PrGJPSZbjnlmgKciIZxzjat+NWGsgwakGMdbcu5dL7kPGwY8vCOrILadeS2D4bY7MyG+D8+AvzHWdhIL4uBGD32gr7Qg+wj7xychWagfsn69xAe8FgUA3mhFYGJIeYcnIAHJmAp1eQA92X8f6gTBenYQogIZ

sAWEHeYftrxyzTkEMI6drmfNAqgP06gIW6OKOQCgpklhXJuWnB5TzSVvIboHL5hAfl/J7EpIFKd4UvOIG8weMKCXBHhYIEQYgDSousLHdSFRMjYvPL3Zg+Ls7EskKSwSc9RLKSXllVe8l8AbyoqpRksq97aUFqsk+Z8L6UUufi25qB7mPI4M8qeTKPnZ2+SCDlAKoKvzheCvlArAEJx7jy8FYqkWSt7tKjFmQsX/xxUqlVIC1UaosbAwKrAEEh1I

GFFBBIorFPQZqTB+RUqQBwdNZ2BD6icAGOVOtRDKHUN4KNB4CRXqVOYbVNhywmhcJaoAzZ/DETdXQA8F8UJ6DDkwAAIQAGpAkwpNdE8idFAgWpCFRBM1FKI0XI7Ru1ET6PxIY78xjTpmLptYyxt1GS2J5MGKmiIvxqmJvQlcuYO0xlpEKT6xRt3IsSRISJMMbQI07GEpJHovRyzSfjbgIwfHlSVP+/JlNAOQATCW7gr0Ga8IFGqRxeVCzFlxFzFp

9Z+adKtd0kWh1+kDkGWOOW1HxlKymRMZcq5pS0hjIsuYyyj70eKEbUdirzY3mCtbaTxyHZGOwZfCAZZwXBxAUjZVjAAD8dKXVuqwvy1AWn8XTyTYqjFNEsaxsTt/BARdRWIolSi2N6LZUIbIpSlTanm6aYxvivT/96WusZSZgLjBzN11QMmqz8Gw32cc5G5zyKpXucxT6We/4jUSF1XWteCltXGu3qa3eWkD6WolsUQy/hbVmVU+p7OpndP6YZcG

8L2mswKtxXoazgQEuMCSwi8VqW3Myoy8vK6Wb4EhXzds8TqC8PEgwUUZKFbx2CM2ENPUygxGbBaGSqt6VAL4I5FyehcUhQJG/S0YYtDxgkc8ZKIMcVrsoeI44nkcpGR41UaMZ6tDJiDAWfKD6RSYrwjvcyKxIT9Qo3CRAcDsNINxOg/D2DGN4OZcvBNI9mJN2w+hOk1ahO8foAUTifa56+lKdJCYs6Zz6aNMo80hWfMOmVYNusPtSxcAvkp4xi9X

SgOM2zDwWkgpWi8iaMMZtOU0Abjl0VKhD4Hj/ZBzL5h3DWqSc6sUKWI42MTmY6SCZysKkzNXOzQUgxdYieF5ACTp4x2kguegcBE9IFT0QcAwlo9jSoFiZCdFIC5qnrLna93ECjne+5aH2OAeg8ZFjvHlduzZM0Pk/bU5jtlPZaK7lhAUk9WkAK4agv5QTWIjNeVujVXIA1eMvgKlEgPeTyMz74e8fUCJ7tMnkeh3ljTZzbNgthtFsQ+W2W1bWCuq

CK+GI5gHAAAyTxugACtBLHfMqd0kT7O3aASBVGXTQmi5iDIDJ735XHaGl4+O7LjFTPp+8T6ZaptBqmfUMSqrNQdrWKLhpPt+OLhYtDg+geqEujmBjDNEvDKjj0jBmjHBjZtjm0rjlovjieutBoruvCKThgeTgTqelTkxrTtVteudLehRqWKzqMq0sUO0k2Jzr2qwrzhFALvMKQaJvXsCKLtwMqBMK0GmK4kruJEMKQrUOQsrm2uLowlGN/thhAM1

Dws7lJixtLEbiMtwVzpAGbtxpbgWF2rSB4oWvbmJo7ussbO1C7r+CphUCCH7KEGBA5HFCPNRAlpwH/GhNxGEMEInA5NxN6CRAEehJ1iEfBAsDrqXN5pHtAOEO7HgGENZGhK4aHu4V/J4eEQAn4VAFkUEekFkWETZNxJESOmnhbBnt+AcgwXbCcmcllsJBXhAHloiLJAajlpXiVtXmVvvHXjoWSKfLVs3nYfEY4UkY6qkX7ukXZpkcUaEakP4XMfB

PkQgIUfilkaURUDAn5NmpUUgrrAgMWkAZdglDPutq7tvnEbvmQvWuJEGIoYVBwK2vslrF2p2lMCwXVOwrSEOiodwPsRtksC0AAFIXBQgaBiL0ArroHrrHph7YGLSv7/4CDgxk47TwnkokEXrHT043rfhM6kj3rcDWK2I/7xBaxP5X7OAvbxCCaUkQFw6gYRIwFwySxQYIFQHlDJJY6TaQC/Z7qaz8hoa0LqgkYgzg4lIEbah8HEgCjXbqyVQNKkh

NLcx0E0Yc4m6YmC407aEDIaGyzG5qmcaTJEYGETBTBSnj56wrI8FO4mxqGHIya5qPhZ51G56u4qauEdzNwEgUDpxRBNx5yBB6CkC5y+5WSQRxBEoJhQTMpyTMjVweR/IfzBy+lx6EpxqDxVx5xCqQTEzRkZB4qMAeqNyxqoABz4ApzREt7oBekplHwIB+kgiBxBkIAhlhld50SRnaAFmxlPxzDKCJkwDJmxypmNnpnBBoqxxZmZxdx2aMB5k9kUA

xlFlmzvKlnNwVlVkNEdHNFF68kMD6rrxNFbw7waS17MHWpDE1kQB1mjkNlNkBm9ytntkTmpwcBRnLmFlxkDlDkjk+njkBqTmZmhrZlzk9yLm9mrklmBzBxblOTbFwIj7/FzYHFHElInHlpFCVolC4InYHmPHcCdqiHPEDBNDi7hgCiDASFKE871R6i/E67ZgAlz5LAYwACaQiIit40JsiBB6Jg+wGuBJODJmisJmBGJkAZ6XBOJFBjOUON0xJj6X

IX2H+AheUcop+4wJhX0ZFcUna9CMuj4AmVShSoliBzJUSrJ+u7JIsFl0A3JKBB5/JkOcUqowwqoVUgwJGcoLiEpZk8ohG2YkwkYMoTazONBqp1Y9BbwtGl5DGnBQu5hEABuxAQy7GbOpuXGppvGauZGq4dumpFhGyqheubw6ezp1R5VRy2e9Ree9WbA6gTAIcoQ4IyRD8fskg10vczWWYRc2QXmN5jVCYpALVzAbVjq9cXVzIPVEWfVdEmq+eSki

8+5JeZeu5p5pW55vR8V1WgxTeQ1TVo1UQ417VdynV3VHWZm/ViFuxuaLFC2RaaCAwK2YAa22FgJEgQgTQ86AAqhcLdjSE7JcbWmdpKC0ORdoNKKRjyC4t4gKGZbpeDddofqqKzCKFUqzBLooS5c0P5ZDqAYpWgGUuDPZUjrAWyfAXZZyQ5cgakjImuttBToTsJfugiZtPxczcQXiDJVeqYpQfidYiqRxm0nFUVbRawfVJsBwb0r2MleUkRdLsqK4

pfq0VIeJLLmrbcaRWgFMBLnlFVJrV1NriOsxaheoYbgaVoQ7hAHoTlQqYKLdsYYoUsuLXadYQ6bYbEdWSpuUU6fsq6YppevVbuS0SvKXu0SeVXqSDXjteLY3ufMMd7ZmjsTNihWPo9Ycc9VPqcW9bPusIIpIEMJIPoE0GwGqFvnhTvgebYvQg8CGLDWKbQuVOGLbm+lyJVHFKfp/iqHDcYZ/i/khnjYiIASUiAYiEScTYTmTSySjvaGjkybTZjk5

bxYzRulgUBsokifgeJYQRvVJViTqbJfzfJdQTOLQdFeqUweLSwl8csMODLVwTbQrcSPKGfoDNKEbTcUQgMMdI8TragA8PSAkPKLdkqesCbVMg9ZAKlelYabqcaebouLleMKfmfoVTaf0e7Xwp7VWr7YNfg7bBUZVYHTnmQXg0tQvIXsXvlpHctW6NHcULHRavHftYnTebdWnYgubVaVnUtt+K9e9QUJ9egJsFFOoHKGWJXTWplMpY9NRR/pUh+ur

EA+TDRV+BRYftdqqEuMYYDN5YPaoojQAdnbwApTDuZTTeTdZTA7ZQkqjFyXTZ5jjnxbvQJVulvUPWzZvYepzUQQxgYkfXzQzuYhFefVFWMqLRqZg58f2rgAAOKP1JU8Ev3X4eVaXA5f2SG3HcBZOHna3FTwidqMIfpBJa7DpQM8P66sZW1y08F20W65UeWoMu3CZu2WG67zYUP1Y+3J1EP+1yb9MKZkPB0emUM6qrW0PHn0OdFnlRAXmsM2pJ09M

p1IV7FVOO4T4YWCPgCtLLBwBwA/DKzcCpTQAJgCS5Z4btAMCECNnzp2PEBk37DPMvNrAQBt5yxljUQ/AaIz1WVvMfOZBfNpD3NU32MI7ujL3035DvPR5QDAv6CWRr1wlkqwue5AvfOeOqL/5ovYwIs/Moholc1MNwsIv+SH1HTXOAvwvUQADycloTJL6LNLaQL4tRQdP4uLnz1EbLxDAdML1LCLpkodkzArpLmLWMwkE8bAX50cmpXLGLaQw48wT

w0rsrgiWMYIVA1zzA/sho+Adw/BkYh+EN9ILiS4sYgGwIernw7FyGMu8QrimTj4SoWsBjMLRgbABgJzrRBABExJh+ritCgmAhaDIoPAWFkAgr1E5L2pVYEAnYbzDoJAey8rybjznJOF86hogipoeoyo+b+bL4/OjIgUygbYWMZow4mw1b1bxbEAkbCrN44M9Lcknh8rcAgQZgwgzA8Tp8xAqbMTMLjBCAgUtUp8zIPrpIGQuAmgwQZtGdUbRA283

Di7EA58Fzeaa7wgUA24o+CAjbdg6+bZWQXw58cAYirmyrs7879pYQ4A71EAv8JziUIAiUQAA
```
%%