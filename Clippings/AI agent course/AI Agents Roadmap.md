[Announcing our SQL course Visit →](https://roadmap.sh/courses/sql)

<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="-181 -170 1100 3640" style="font-family: balsamiq" version="1.1"><path d="M502.98246683384076,144.3447875361903 C547.904578234333,144.3447875361903 547.904578234333,144.3447875361903 592.8266896348252,144.3447875361903" fill="none" stroke="#2b78e4" stroke-width="3.5" data-edge-id="xy-edge__VPI89s-m885r2YrXjYxddz2-Ynvi5skgC85lgm96V_p42y1" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0.8 8"></path><path d="M502.98246683384076,197.3447875361903 C547.904578234333,197.3447875361903 547.904578234333,197.3447875361903 592.8266896348252,197.3447875361903" fill="none" stroke="#2b78e4" stroke-width="3.5" data-edge-id="xy-edge__McREk2zHOlIrqbGSKbX-Jz2-Ynvi5skgC85lgm96V_p42y1" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0.8 8"></path><path d="M502.98246683384076,250.3447875361903 C547.904578234333,250.3447875361903 547.904578234333,250.3447875361903 592.8266896348252,250.3447875361903" fill="none" stroke="#2b78e4" stroke-width="3.5" data-edge-id="xy-edge__QtTwecLdvQa8pgELJ6i80z2-Ynvi5skgC85lgm96V_p42y1" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0.8 8"></path><path d="M367.98246683384076,274.8447875361903 C367.98246683384076,312.2630869535194 367.98246683384076,312.2630869535194 367.98246683384076,349.6813863708485" fill="none" stroke="#2b78e4" stroke-width="3.5" data-edge-id="xy-edge__QtTwecLdvQa8pgELJ6i80x2-eeU25xBGXtFFisDckfn31w1" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0"></path><path d="M473.48246683384076,375.6813863708485 C528.0838805640459,375.6813863708485 528.0838805640459,375.6813863708485 582.685294294251,375.6813863708485" fill="none" stroke="#2b78e4" stroke-width="3.5" data-edge-id="xy-edge__eeU25xBGXtFFisDckfn31z2-ZF5_5Y5zqa75Ov22JACX6y2" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0"></path><path d="M367.98246683384076,-0.9683088092860999 C367.98246683384076,35.68944241140143 367.98246683384076,35.68944241140143 367.98246683384076,72.34719363208896" fill="none" stroke="#2b78e4" stroke-width="3.5" data-edge-id="xy-edge__Ji35JsKgUQXN4DJGEgTACx2-imzXXZPt8wNny1oe3-MeNw1" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0"></path><path d="M367.86750909294227,608.7837601979952 C367.86750909294227,577.480394128641 367.86750909294227,577.480394128641 367.86750909294227,546.1770280592868" fill="none" stroke="#2b78e4" stroke-width="3.5" data-edge-id="xy-edge__X8W7EWSJxYiY50sEs38fow2-tJYmEDDwK0LtEux-kwp9Bx1" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0.8 8"></path><path d="M236.86750909294227,635.2837601979952 C185.43771407727417,635.2837601979952 185.43771407727417,635.2837601979952 134.00791906160603,635.2837601979952" fill="none" stroke="#2b78e4" stroke-width="3.5" data-edge-id="xy-edge__X8W7EWSJxYiY50sEs38foy2-7-EqPC6VDTiFn8DfM7V63z2" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0.8 8"></path><path d="M8.007919061606032,608.7837601979952 C8.007919061606032,578.549880547738 8.007919061606032,578.549880547738 8.007919061606032,548.3160008974808" fill="none" stroke="#2b78e4" stroke-width="3.5" data-edge-id="xy-edge__7-EqPC6VDTiFn8DfM7V63w2-B8dzg61TGaknuruBgkEJdx1" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0.8 8"></path><path d="M8.007919061606032,661.7837601979952 C8.007919061606032,723.5893115614331 8.007919061606032,723.5893115614331 8.007919061606032,785.3948629248711" fill="none" stroke="#2b78e4" stroke-width="3.5" data-edge-id="xy-edge__7-EqPC6VDTiFn8DfM7V63x2-THJQQuiCmKGayd6EdcX9ow1" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0"></path><path d="M8.007919061606032,841.3948629248711 C8.007919061606032,895.6615246102635 8.007919061606032,895.6615246102635 8.007919061606032,949.928186295656" fill="none" stroke="#2b78e4" stroke-width="3.5" data-edge-id="xy-edge__THJQQuiCmKGayd6EdcX9ox2-aFZAm44nP5NefX_9TpT0Aw1" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0"></path><path d="M139.50791906160603,1029.428186295656 C194.93771407727417,1029.428186295656 194.93771407727417,1029.428186295656 250.36750909294227,1029.428186295656" fill="none" stroke="#2b78e4" stroke-width="3.5" data-edge-id="xy-edge__2zsOUWJQ8e7wnoHmq1icGz2-Eih4eybuYB3C2So8K0AT3y2" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0"></path><path d="M367.86750909294227,1002.928186295656 C367.86750909294227,982.9219319941346 367.86750909294227,982.9219319941346 367.86750909294227,962.9156776926133" fill="none" stroke="#2b78e4" stroke-width="3.5" data-edge-id="xy-edge__Eih4eybuYB3C2So8K0AT3w2-ZJTrun3jK3zBGOTm1jdMIx2" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0.8 8"></path><path d="M582.80929781038,1166.5676406496057 C531.8384034516612,1166.5676406496057 531.8384034516612,1166.5676406496057 480.86750909294227,1166.5676406496057" fill="none" stroke="#2b78e4" stroke-width="3.5" data-edge-id="xy-edge__fBn8SCyMuWS7fRLMJCgSyy2-tyjeFUjjTGFVXRLOe1Oh1z2" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0"></path><path d="M254.86750909294227,1166.5676406496057 C206.81098926748808,1166.5676406496057 206.81098926748808,1166.5676406496057 158.7544694420339,1166.5676406496057" fill="none" stroke="#2b78e4" stroke-width="3.5" data-edge-id="xy-edge__tyjeFUjjTGFVXRLOe1Oh1y2-Y8EqzFx3qxtrSh7bWbbV8z1" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0"></path><path d="M10.754469442033894,1193.0676406496057 C10.754469442033894,1218.6793217117315 10.754469442033894,1218.6793217117315 10.754469442033894,1244.2910027738571" fill="none" stroke="#2b78e4" stroke-width="3.5" data-edge-id="xy-edge__Y8EqzFx3qxtrSh7bWbbV8x2-v-zVQKH6XEMCI4Np-Q3Jow1" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0.8 8"></path><path d="M366.76555040271785,1301.8505644697545 C366.76555040271785,1337.2286894619242 366.76555040271785,1337.2286894619242 366.76555040271785,1372.6068144540936" fill="none" stroke="#2b78e4" stroke-width="3.5" data-edge-id="xy-edge__nlVToD-pmbBV2ikgrCQiOx2-qakbxB8xe7Y8gejC5cZnKw1" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0"></path><path d="M377.98246683384076,1659.5297261117416 C377.98246683384076,1685.3852307777984 378.35088070462893,1685.3852307777984 378.35088070462893,1711.2407354438553" fill="none" stroke="#2b78e4" stroke-width="3.5" data-edge-id="xy-edge__1B0IqRNYdtbHDi1jHSXuIx2-K2ueUkBcV5ASMhHUUZH-Aw2" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0"></path><path d="M10.754469442033894,1760.2407354438553 C10.754469442033894,1781.4174418640623 10.754469442033894,1781.4174418640623 10.754469442033894,1802.5941482842695" fill="none" stroke="#2b78e4" stroke-width="3.5" data-edge-id="xy-edge__1NXIN-Hbjl5rPy_mqxQYWx2-qLO_DcIM2OY_i8njSIt1qw1" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0"></path><path d="M163.30693053066472,2142.410569685858 C226.39153778440564,2142.410569685858 226.39153778440564,2142.410569685858 289.47614503814657,2142.410569685858" fill="none" stroke="#2b78e4" stroke-width="3.5" data-edge-id="xy-edge__T2vksVGCRajcwy_WRe2Yfz2-SU8HwGaDiRCho5I_pBl3Ny1" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0"></path><path d="M397.47614503814657,2165.910569685858 C397.47614503814657,2191.9651622652827 397.47614503814657,2191.9651622652827 397.47614503814657,2218.019754844708" fill="none" stroke="#2b78e4" stroke-width="3.5" data-edge-id="xy-edge__SU8HwGaDiRCho5I_pBl3Nx2--MLEtjuRq9y1HpZeKJCtsw1" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0"></path><path d="M749.80929781038,2388.870720452855 C749.80929781038,2421.0417717064865 749.80929781038,2421.0417717064865 749.80929781038,2453.212822960118" fill="none" stroke="#2b78e4" stroke-width="3.5" data-edge-id="xy-edge__7c-FMXWsMGv95vxnUydFix2-US6T5dXM8IY9V2qZnTOFWw1" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0"></path><path d="M749.80929781038,2654.0335978441904 C749.80929781038,2674.9787690157054 749.80929781038,2674.9787690157054 749.80929781038,2695.92394018722" fill="none" stroke="#2b78e4" stroke-width="3.5" data-edge-id="xy-edge__MeOebqpbLZkPbj0iWRjP2x2-aafZxtjxiwzJH1lwHBODiw1" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0"></path><path d="M749.80929781038,2744.92394018722 C749.80929781038,2761.3910134085345 749.80929781038,2761.3910134085345 749.80929781038,2777.8580866298494" fill="none" stroke="#2b78e4" stroke-width="3.5" data-edge-id="xy-edge__aafZxtjxiwzJH1lwHBODix2-1EZFbDHA5J5_5BPMLMxXbw1" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0.8 8"></path><g data-node-id="3GO9CZQSSxM1eRSBz9sWR" data-type="horizontal"><line x1="448.47614503814657" y1="3332.5001335516636" x2="605.4761450381466" y2="3332.5001335516636" style="stroke-linecap: round; stroke-width: 3.75; stroke: #2B78E4; stroke-dasharray: 0;"></line></g><g data-node-id="9MVi8gN_TNI515-5til_s" data-type="vertical"><line x1="749" y1="3056.5001335516636" x2="749" y2="3111.5001335516636" style="stroke-linecap: round; stroke-width: 3.75; stroke: #2B78E4; stroke-dasharray: 0;"></line></g><g data-node-id="h2-93F69l8wKXkvT-iFC4" data-type="section"><rect x="-133.65832024152124" y="2819.5899357924472" width="303.3" height="285.3" rx="5" fill="#ffffff" stroke="#000000" stroke-width="2.7"></rect></g><g data-node-id="ucZ3vn5WgFOfa8Wpb0Hgk" data-type="section"><rect x="-133.65832024152124" y="2413.697193632089" width="303.3" height="417.3" rx="5" fill="#ffffff" stroke="#000000" stroke-width="2.7"></rect></g><g data-node-id="JuxzWFeM_vwgn4pj0wxfY" data-type="horizontal"><line x1="528.9761450381466" y1="2720.42394018722" x2="600.9761450381466" y2="2720.42394018722" style="stroke-linecap: round; stroke-width: 3.75; stroke: #2B78E4; stroke-dasharray: 0;"></line></g><g data-node-id="R18O3kbMoSEicZbLgUP7s" data-type="section"><rect x="266.3261450381466" y="2696.27394018722" width="262.3" height="307.3" rx="5" fill="#ffffff" stroke="#000000" stroke-width="2.7"></rect></g><g data-node-id="MeOebqpbLZkPbj0iWRjP2" data-type="section"><rect x="601.65929781038" y="2482.3835978441903" width="296.3" height="170.3" rx="5" fill="transparent" stroke="black" stroke-width="2.7"></rect></g><g data-node-id="-MLEtjuRq9y1HpZeKJCts" data-type="section" data-parent-id="SU8HwGaDiRCho5I_pBl3N"><rect x="266.3261450381466" y="2219.369754844708" width="262.3" height="431.3" rx="5" fill="#ffffff" stroke="#000000" stroke-width="2.7"></rect></g><g data-node-id="NI3gC3eiPXDkirpD7sk10" data-type="horizontal"><line x1="162.26555040271785" y1="2033.1355435052951" x2="294.26555040271785" y2="2033.1355435052951" style="stroke-linecap: round; stroke-width: 3.75; stroke: #2B78E4; stroke-dasharray: 0;"></line></g><g data-node-id="u8n0Lh3rJrqnRyc0KCjdr" data-type="vertical"><line x1="784.80929781038" y1="1851.6702232480886" x2="784.80929781038" y2="1989.6702232480886" style="stroke-linecap: round; stroke-width: 3.75; stroke: #2B78E4; stroke-dasharray: 0;"></line></g><g data-node-id="0nEbIdUFlhBp7Pn9lzZDG" data-type="vertical"><line x1="719.8290815371215" y1="1851.6702232480886" x2="719.8290815371215" y2="1925.6702232480886" style="stroke-linecap: round; stroke-width: 3.75; stroke: #2B78E4; stroke-dasharray: 0;"></line></g><g data-node-id="qLO_DcIM2OY_i8njSIt1q" data-type="section" data-parent-id="1NXIN-Hbjl5rPy_mqxQYW"><rect x="-102.39553055796611" y="1803.9441482842694" width="226.3" height="168.3" rx="5" fill="#ffffff" stroke="#000000" stroke-width="2.7"></rect></g><g data-node-id="PebDvL0Oyzh9mna-AbiaO" data-type="horizontal"><line x1="118.85088070462893" y1="1735.7407354438553" x2="233.85088070462893" y2="1735.7407354438553" style="stroke-linecap: round; stroke-width: 3.5; stroke: #2B78E4; stroke-dasharray: 0;"></line></g><g data-node-id="K2ueUkBcV5ASMhHUUZH-A" data-type="section" data-parent-id="1B0IqRNYdtbHDi1jHSXuI"><rect x="235.20088070462893" y="1712.5907354438552" width="286.3" height="163.3" rx="5" fill="#ffffff" stroke="#000000" stroke-width="2.7"></rect></g><g data-node-id="0WHI9bT9SJnNfuBkY1oU1" data-type="section"><rect x="637.4488844716816" y="1989.985543505295" width="252.3" height="204.3" rx="5" fill="#ffffff" stroke="#000000" stroke-width="2.7"></rect></g><g data-node-id="iw9O2qcCgvICJc6HvpUYf" data-type="section"><rect x="249.11555040271784" y="1413.5518556690076" width="235.3" height="146.3" rx="5" fill="#ffffff" stroke="#000000" stroke-width="2.7"></rect></g><g data-node-id="v-zVQKH6XEMCI4Np-Q3Jo" data-type="section" data-parent-id="Y8EqzFx3qxtrSh7bWbbV8"><rect x="-135.8955305579661" y="1245.641002773857" width="293.3" height="371.3" rx="5" fill="#ffffff" stroke="#000000" stroke-width="2.7"></rect></g><g data-node-id="c58fcsthBlVfzfr-MHdpF" data-type="vertical"><line x1="367.98246683384076" y1="-136.01175492596462" x2="367.98246683384076" y2="-70.01175492596462" style="stroke-linecap: round; stroke-width: 3.5; stroke: #2B78E4; stroke-dasharray: 0.8 8;"></line></g><g data-node-id="Ji35JsKgUQXN4DJGEgTAC" data-type="title" data-title="AI Agents"><text x="367.98246683384076" y="-34.9683088092861" text-anchor="middle" dominant-baseline="middle" font-size="28" fill="black"><tspan>AI Agents</tspan></text></g> <g data-node-id="kpF15oUmlUmk1qVGEBB7Y" data-type="paragraph"><rect x="518.3442414053277" y="-135.7115199492601" width="355" height="140.5" rx="5" fill="#FFFFFf" stroke="#000000" stroke-width="2.5"></rect><text fill="black"><tspan x="535.5942414053277" y="-106.9615199492601" dy="0" text-anchor="start" dominant-baseline="middle" font-size="17">Find the detailed version of this roadmap</tspan> <tspan x="535.5942414053277" y="-106.9615199492601" dy="25.5" text-anchor="start" dominant-baseline="middle" font-size="17">along with other similar roadmaps</tspan></text></g> <g data-link="https://roadmap.sh" data-node-id="if9eTna5NRTMStVwpSwDP" data-type="button"><rect x="530.9442414053277" y="-53.753402024556614" width="327.3" height="46.3" rx="5" fill="#4136D6" stroke="#4136D6" stroke-width="2.7"></rect><text x="694.5942414053277" y="-28.453402024556617" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#ffffff"><tspan>roadmap.sh</tspan></text></g> <g data-node-id="0vLaVNJaJSHZ_bHli6Qzs" data-type="paragraph"><rect x="41.232758882202006" y="3245.2664384242175" width="412" height="118.5" rx="5" fill="WHITe" stroke="#000000" stroke-width="2.5"></rect><text fill="black"><tspan x="245.982758882202" y="3274.0164384242175" dy="0" text-anchor="middle" dominant-baseline="middle" font-size="17">Visit the following relevant tracks</tspan></text></g> <g data-node-id="xi0QAi4kXm3-IFKgopnOP" data-type="vertical"><line x1="245.98275888220198" y1="3365.0164384242175" x2="245.98275888220198" y2="3450.0164384242175" style="stroke-linecap: round; stroke-width: 3.5; stroke: #2B78E4; stroke-dasharray: 0.8 8;"></line></g><g data-link="https://roadmap.sh/ai-engineer" data-node-id="NrTAfxHCksgqSkhnrO4kF" data-type="button"><rect x="58.33275888220195" y="3302.090989142758" width="147.3" height="46.3" rx="5" fill="#4136D6" stroke="#4136D6" stroke-width="2.7"></rect><text x="131.98275888220195" y="3327.3909891427584" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#FFFFFf"><tspan>AI Engineer</tspan></text></g> <g data-node-id="Wm2VPi1Tn_Y7dqPowny69" data-type="linksgroup"><rect x="-159.60487988355524" y="-148.30280636791105" width="329.3" height="224.3" rx="5" fill="white" stroke="black" stroke-width="2.7"></rect><text x="-136.95487988355524" y="-120.65280636791104" text-anchor="left" dominant-baseline="middle" font-size="16" fill="black"><tspan>Related Roadmaps</tspan></text> <g data-node-id="29I4Uf1Z74ihhM4cpfaP7" data-type="link-item" data-parent-id="Wm2VPi1Tn_Y7dqPowny69" data-link="https://roadmap.sh/ai-engineer"><circle cx="-128.45487988355524" cy="-82.15280636791104" r="9.5" fill="#6b7280" zindex="1" id="icon-link"></circle><path d="M-132.45487988355524 -82.15280636791104L-129.95487988355524 -79.15280636791104 -124.95487988355524 -84.15280636791104" fill="none" stroke="#fff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path><text x="-110.95487988355524" y="-80.65280636791104" text-anchor="left" dominant-baseline="middle" font-size="16" fill="black"><tspan>AI Engineer Roadmap</tspan></text></g> <g data-node-id="9IzkW8kJ-4TiqEuoeyteP" data-type="link-item" data-parent-id="Wm2VPi1Tn_Y7dqPowny69" data-link="https://roadmap.sh/ai-data-scientist"><circle cx="-128.45487988355524" cy="-52.15280636791104" r="9.5" fill="#6b7280" zindex="1" id="icon-link"></circle><path d="M-132.45487988355524 -52.15280636791104L-129.95487988355524 -49.15280636791104 -124.95487988355524 -54.15280636791104" fill="none" stroke="#fff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path><text x="-110.95487988355524" y="-50.65280636791104" text-anchor="left" dominant-baseline="middle" font-size="16" fill="black"><tspan>AI and Data Scientist Roadmap</tspan></text></g> <g data-node-id="tvB9Bt9JTO98FlPqvPZGl" data-type="link-item" data-parent-id="Wm2VPi1Tn_Y7dqPowny69" data-link="https://roadmap.sh/mlops"><circle cx="-128.45487988355524" cy="-22.15280636791104" r="9.5" fill="#6b7280" zindex="1" id="icon-link"></circle><path d="M-132.45487988355524 -22.15280636791104L-129.95487988355524 -19.15280636791104 -124.95487988355524 -24.15280636791104" fill="none" stroke="#fff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path><text x="-110.95487988355524" y="-20.65280636791104" text-anchor="left" dominant-baseline="middle" font-size="16" fill="black"><tspan>MLOps Roadmap</tspan></text></g> <g data-node-id="usMWqZBoyrbkxGv-YHziv" data-type="link-item" data-parent-id="Wm2VPi1Tn_Y7dqPowny69" data-link="https://roadmap.sh/ai-red-teaming"><circle cx="-128.45487988355524" cy="7.84719363208896" r="9.5" fill="#6b7280" zindex="1" id="icon-link"></circle><path d="M-132.45487988355524 7.84719363208896L-129.95487988355524 10.84719363208896 -124.95487988355524 5.84719363208896" fill="none" stroke="#fff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path><text x="-110.95487988355524" y="9.34719363208896" text-anchor="left" dominant-baseline="middle" font-size="16" fill="black"><tspan>AI Red Teaming Roadmap</tspan></text></g> <g data-node-id="BOm0mlohoZzmcooxNYPhr" data-type="link-item" data-parent-id="Wm2VPi1Tn_Y7dqPowny69" data-link="https://roadmap.sh/prompt-engineering"><circle cx="-128.45487988355524" cy="37.84719363208896" r="9.5" fill="#6b7280" zindex="1" id="icon-link"></circle><path d="M-132.45487988355524 37.84719363208896L-129.95487988355524 40.84719363208896 -124.95487988355524 35.84719363208896" fill="none" stroke="#fff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path><text x="-110.95487988355524" y="39.34719363208896" text-anchor="left" dominant-baseline="middle" font-size="16" fill="black"><tspan>Prompt Engineering Roadmap</tspan></text></g></g> <g data-node-id="VPI89s-m885r2YrXjYxdd" data-type="topic" data-title="Basic Backend Development"><rect x="234.33246683384075" y="121.19478753619029" width="267.3" height="46.3" rx="5" fill="#fdff00" stroke="black" stroke-width="2.7" style="--hover-color: #d6d700"></rect><text x="367.98246683384076" y="146.4947875361903" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Basic Backend Development</tspan></text></g> <g data-node-id="McREk2zHOlIrqbGSKbX-J" data-type="topic" data-title="Git and Terminal Usage"><rect x="234.33246683384075" y="174.1947875361903" width="267.3" height="46.3" rx="5" fill="#fdff00" stroke="black" stroke-width="2.7" style="--hover-color: #d6d700"></rect><text x="367.98246683384076" y="199.4947875361903" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Git and Terminal Usage</tspan></text></g> <g data-node-id="QtTwecLdvQa8pgELJ6i80" data-type="topic" data-title="REST API Knowledge"><rect x="234.33246683384075" y="227.1947875361903" width="267.3" height="46.3" rx="5" fill="#fdff00" stroke="black" stroke-width="2.7" style="--hover-color: #d6d700"></rect><text x="367.98246683384076" y="252.4947875361903" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>REST API Knowledge</tspan></text></g> <g data-link="https://roadmap.sh/backend?r=backend-beginner" data-node-id="amfuroaEeP1sqhvPN-jgO" data-type="button"><rect x="594.1766896348253" y="121.19478753619029" width="269.3" height="46.3" rx="5" fill="#4036d6" stroke="#4036d6" stroke-width="2.7"></rect><text x="728.8266896348252" y="146.4947875361903" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#ffffff"><tspan>Backend Beginner Roadmap</tspan></text></g> <g data-link="https://roadmap.sh/git-github?r=git-github-beginner" data-node-id="kA1ggkT-2KI0cEdd3rIcM" data-type="button"><rect x="594.1766896348253" y="174.1947875361903" width="269.3" height="46.3" rx="5" fill="#4036d6" stroke="#4036d6" stroke-width="2.7"></rect><text x="728.8266896348252" y="199.4947875361903" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#ffffff"><tspan>Git and GitHub Roadmap</tspan></text></g> <g data-link="https://roadmap.sh/api-design" data-node-id="Ynvi5skgC85lgm96V_p42" data-type="button"><rect x="594.1766896348253" y="227.1947875361903" width="269.3" height="46.3" rx="5" fill="#4036d6" stroke="#4036d6" stroke-width="2.7"></rect><text x="728.8266896348252" y="252.4947875361903" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#ffffff"><tspan>API Design Roadmap</tspan></text></g> <g data-node-id="imzXXZPt8wNny1oe3-MeN" data-type="label" data-parent-id="Ji35JsKgUQXN4DJGEgTAC"><text x="276.48246683384076" y="97.34719363208896" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>Learn the Pre-requisites</tspan></text></g> <g data-node-id="eeU25xBGXtFFisDckfn31" data-type="paragraph"><rect x="263.73246683384076" y="350.9313863708485" width="211" height="49.5" rx="5" fill="transparent" stroke="transparent" stroke-width="2.5"></rect><text fill="#000000"><tspan x="367.98246683384076" y="375.6813863708485" dy="0" text-anchor="middle" dominant-baseline="middle" font-size="20">LLM Fundamentals</tspan></text></g> <g data-node-id="ZF5_5Y5zqa75Ov22JACX6" data-type="topic" data-title="Transformer Models and LLMs" data-parent-id="eeU25xBGXtFFisDckfn31" data-parent-title="LLM Fundamentals"><rect x="584.035294294251" y="352.5313863708485" width="281.3" height="46.3" rx="5" fill="#fdff00" stroke="black" stroke-width="2.7" style="--hover-color: #d6d700"></rect><text x="724.685294294251" y="377.83138637084846" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Transformer Models and LLMs</tspan></text></g> <g data-node-id="fDFxKOTLw6O41OGVmRuyy" data-type="section"><rect x="584.035294294251" y="398.63863122380326" width="281.3" height="216.3" rx="5" fill="#ffffff" stroke="#000000" stroke-width="2.7"></rect></g><g data-node-id="JBorUqfR1T7kFzwHfdJm4" data-type="label"><text x="663.185294294251" y="431.2063076021101" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>Model Mechanis</tspan></text></g> <g data-node-id="GAjuWyJl9CI1nqXBp6XCf" data-type="subtopic" data-title="Tokenization"><rect x="596.535294294251" y="450.5563076021101" width="256.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="724.685294294251" y="475.85630760211006" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Tokenization</tspan></text></g> <g data-node-id="dyn1LSioema-Bf9lLTgUZ" data-type="subtopic" data-title="Context Windows"><rect x="596.535294294251" y="503.55630760211017" width="256.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="724.685294294251" y="528.8563076021102" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Context Windows</tspan></text></g> <g data-node-id="1fiWPBV99E2YncqdCgUw2" data-type="subtopic" data-title="Token Based Pricing"><rect x="596.535294294251" y="556.5563076021101" width="256.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="724.685294294251" y="581.8563076021101" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Token Based Pricing</tspan></text></g> <g data-node-id="W0402lPcZoaTL1OMTED6o" data-type="section"><rect x="583.035294294251" y="615.0469577367049" width="282.3" height="330.3" rx="5" fill="#ffffff" stroke="#000000" stroke-width="2.7"></rect></g><g data-node-id="DUOWFOfqpppC9lZZR2_y2" data-type="label"><text x="649.185294294251" y="647.6146341150118" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>Generation Controls</tspan></text></g> <g data-node-id="L1zL1GzqjSAjF06pIIXhy" data-type="subtopic" data-title="Temperature"><rect x="596.535294294251" y="666.9646341150118" width="133.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="663.185294294251" y="692.2646341150117" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Temperature</tspan></text></g> <g data-node-id="z_N-Y0zGkv8_qHPuVtimL" data-type="subtopic" data-title="Frequency Penalty"><rect x="596.535294294251" y="719.9646341150118" width="256.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="724.685294294251" y="745.2646341150117" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Frequency Penalty</tspan></text></g> <g data-node-id="Vd8ycw8pW-ZKvg5WYFtoh" data-type="subtopic" data-title="Presence Penalty"><rect x="596.535294294251" y="772.9646341150118" width="256.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="724.685294294251" y="798.2646341150117" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Presence Penalty</tspan></text></g> <g data-node-id="icbp1NjurQfdM0dHnz6v2" data-type="subtopic" data-title="Top-p"><rect x="737.535294294251" y="666.9646341150118" width="114.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="794.685294294251" y="692.2646341150117" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Top-p</tspan></text></g> <g data-node-id="K0G-Lw069jXUJwZqHtybd" data-type="subtopic" data-title="Stopping Criteria"><rect x="596.535294294251" y="825.9646341150118" width="257.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="725.185294294251" y="851.2646341150117" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Stopping Criteria</tspan></text></g> <g data-node-id="DSJAhQhc1dQmBHQ8ZkTau" data-type="topic" data-title="Open Weight Models"><rect x="238.21750909294227" y="437.52702805928686" width="259.3" height="50.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="367.86750909294227" y="464.8270280592868" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Open Weight Models</tspan></text></g> <g data-node-id="tJYmEDDwK0LtEux-kwp9B" data-type="topic" data-title="Closed Weight Models" data-parent-id="X8W7EWSJxYiY50sEs38fo" data-parent-title="Model Families and Licences"><rect x="238.21750909294227" y="494.52702805928686" width="259.3" height="50.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="367.86750909294227" y="521.8270280592868" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Closed Weight Models</tspan></text></g> <g data-node-id="S1KLwzUZastfV9daGbYTk" data-type="horizontal"><line x1="483.685294294251" y1="635.2837601979952" x2="583.685294294251" y2="635.2837601979952" style="stroke-linecap: round; stroke-width: 3.75; stroke: #2B78E4; stroke-dasharray: 0;"></line></g><g data-node-id="X8W7EWSJxYiY50sEs38fo" data-type="paragraph"><rect x="238.11750909294227" y="610.0337601979952" width="262" height="50.5" rx="5" fill="#ffffff" stroke="#000000" stroke-width="2.5"></rect><text fill="#000000"><tspan x="367.86750909294227" y="635.2837601979952" dy="0" text-anchor="middle" dominant-baseline="middle" font-size="17">Model Families and Licences</tspan></text></g> <g data-node-id="i2NE6haX9-7mdoV5LQ3Ah" data-type="topic" data-title="Streamed vs Unstreamed Responses"><rect x="-156.64208093839397" y="211.6660008974808" width="329.3" height="50.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="8.007919061606032" y="238.96600089748082" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Streamed vs Unstreamed Responses</tspan></text></g> <g data-node-id="N3yZfUxphxjiupqGpyaS9" data-type="topic" data-title="Reasoning vs Standard Models"><rect x="-156.64208093839397" y="268.66600089748084" width="329.3" height="50.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="8.007919061606032" y="295.9660008974808" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Reasoning vs Standard Models</tspan></text></g> <g data-node-id="5OW_6o286mj470ElFyJ_5" data-type="topic" data-title="Fine-tuning vs Prompt Engineering"><rect x="-156.64208093839397" y="325.66600089748084" width="329.3" height="50.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="8.007919061606032" y="352.9660008974808" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Fine-tuning vs Prompt Engineering</tspan></text></g> <g data-node-id="UIm54UmICKgep6s8Itcyv" data-type="topic" data-title="Embeddings and Vector Search"><rect x="-156.64208093839397" y="382.66600089748084" width="329.3" height="50.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="8.007919061606032" y="409.9660008974808" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Embeddings and Vector Search</tspan></text></g> <g data-node-id="qwVQOwBTLA2yUgRISzC8k" data-type="topic" data-title="Understand the Basics of RAG"><rect x="-156.64208093839397" y="439.66600089748084" width="329.3" height="50.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="8.007919061606032" y="466.9660008974808" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Understand the Basics of RAG</tspan></text></g> <g data-node-id="7-EqPC6VDTiFn8DfM7V63" data-type="paragraph"><rect x="-116.74208093839397" y="610.0337601979952" width="252" height="50.5" rx="5" fill="#ffffff" stroke="#000000" stroke-width="2.5"></rect><text fill="#000000"><tspan x="8.007919061606032" y="635.2837601979952" dy="0" text-anchor="middle" dominant-baseline="middle" font-size="17">Understand the Basics</tspan></text></g> <g data-node-id="B8dzg61TGaknuruBgkEJd" data-type="topic" data-title="Pricing of Common Models" data-parent-id="7-EqPC6VDTiFn8DfM7V63" data-parent-title="Understand the Basics"><rect x="-156.64208093839397" y="496.6660008974808" width="329.3" height="50.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="8.007919061606032" y="523.9660008974807" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Pricing of Common Models</tspan></text></g> <g data-node-id="THJQQuiCmKGayd6EdcX9o" data-type="paragraph"><rect x="-118.74208093839397" y="786.6448629248711" width="256" height="53.5" rx="5" fill="transparent" stroke="transparent" stroke-width="2.5"></rect><text fill="#000000"><tspan x="8.007919061606032" y="813.3948629248711" dy="0" text-anchor="middle" dominant-baseline="middle" font-size="20">AI Agents 101</tspan></text></g> <g data-node-id="aFZAm44nP5NefX_9TpT0A" data-type="subtopic" data-title="What are AI Agents?" data-parent-id="THJQQuiCmKGayd6EdcX9o" data-parent-title="AI Agents 101"><rect x="-122.14208093839397" y="951.278186295656" width="260.3" height="48.3" rx="5" fill="#fdff00" stroke="black" stroke-width="2.7" style="--hover-color: #d6d700"></rect><text x="8.007919061606032" y="977.578186295656" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>What are AI Agents?</tspan></text></g><g data-node-id="2zsOUWJQ8e7wnoHmq1icG" data-type="subtopic" data-title="What are Tools?"><rect x="-122.14208093839397" y="1006.278186295656" width="260.3" height="46.3" rx="5" fill="#fdff00" stroke="black" stroke-width="2.7" style="--hover-color: #d6d700"></rect> <text x="8.007919061606032" y="1031.578186295656" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>What are Tools?</tspan></text></g><g data-node-id="Eih4eybuYB3C2So8K0AT3" data-type="topic" data-title="Agent Loop" data-parent-id="2zsOUWJQ8e7wnoHmq1icG" data-parent-title="What are Tools?"><rect x="251.71750909294227" y="1004.278186295656" width="232.3" height="50.3" rx="5" fill="#fdff00" stroke="black" stroke-width="2.7" style="--hover-color: #d6d700"></rect> <text x="367.86750909294227" y="1031.578186295656" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Agent Loop</tspan></text></g> <g data-node-id="LU76AhCYDjxdBhpMQ4eMU" data-type="subtopic" data-title="Perception / User Input"><rect x="250.21750909294227" y="756.2656776926133" width="235.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="367.86750909294227" y="781.5656776926132" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Perception / User Input</tspan></text></g> <g data-node-id="ycPRgRYR4lEBQr_xxHKnM" data-type="subtopic" data-title="Reason and Plan"><rect x="250.21750909294227" y="809.2656776926133" width="235.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="367.86750909294227" y="834.5656776926132" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Reason and Plan</tspan></text></g> <g data-node-id="sHYd4KsKlmw5Im3nQ19W8" data-type="subtopic" data-title="Acting / Tool Invocation"><rect x="250.21750909294227" y="862.2656776926133" width="235.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="367.86750909294227" y="887.5656776926132" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Acting / Tool Invocation</tspan></text></g> <g data-node-id="ZJTrun3jK3zBGOTm1jdMI" data-type="subtopic" data-title="Observation &amp; Reflection" data-parent-id="Eih4eybuYB3C2So8K0AT3" data-parent-title="Agent Loop"><rect x="250.21750909294227" y="915.2656776926133" width="235.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="367.86750909294227" y="940.5656776926132" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Observation &amp; Reflection</tspan></text></g> <g data-node-id="wb8zEmsAX7VyNRPe-DHxw" data-type="label"><text x="222.99392181540966" y="785.4156776926133" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>1</tspan></text></g> <g data-node-id="BkFgVFOtSkuzO0s9EJ3tc" data-type="label"><text x="221.49392181540966" y="838.4156776926133" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>2</tspan></text></g> <g data-node-id="kLZToxQ1iv0m5Po1TyLPw" data-type="label"><text x="221.49392181540966" y="944.4156776926133" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>4</tspan></text></g> <g data-node-id="fBn8SCyMuWS7fRLMJCgSy" data-type="section"><rect x="584.15929781038" y="1003.9176406496057" width="281.3" height="325.3" rx="5" fill="#ffffff" stroke="#000000" stroke-width="2.7"></rect></g><g data-node-id="TehqgcNFmS2Iy2TFrAz_r" data-type="label"><text x="653.30929781038" y="1036.4853170279125" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>Example Usecases</tspan></text></g> <g data-node-id="PPdAutqJF5G60Eg9lYBND" data-type="subtopic" data-title="Personal assistant"><rect x="596.65929781038" y="1055.8353170279124" width="256.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="724.80929781038" y="1081.1353170279126" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Personal assistant</tspan></text></g> <g data-node-id="PK8w31GlvtmAuU92sHaqr" data-type="subtopic" data-title="Code generation"><rect x="596.65929781038" y="1108.8353170279124" width="256.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="724.80929781038" y="1134.1353170279126" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Code generation</tspan></text></g> <g data-node-id="wKYEaPWNsR30TIpHaxSsq" data-type="subtopic" data-title="Data analysis"><rect x="596.65929781038" y="1161.8353170279124" width="256.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="724.80929781038" y="1187.1353170279126" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Data analysis</tspan></text></g> <g data-node-id="5oLc-235bvKhApxzYFkEc" data-type="subtopic" data-title="Web Scraping / Crawling"><rect x="596.65929781038" y="1214.8353170279124" width="256.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="724.80929781038" y="1240.1353170279126" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Web Scraping / Crawling</tspan></text></g> <g data-node-id="ok8vN7VtCgyef5x6aoQaL" data-type="subtopic" data-title="NPC / Game AI"><rect x="596.65929781038" y="1267.8353170279124" width="256.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="724.80929781038" y="1293.1353170279126" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>NPC / Game AI</tspan></text></g> <g data-node-id="szfDDmz2gnjR2nL6oQIRT" data-type="horizontal"><line x1="483.48246683384076" y1="1030.4853170279125" x2="582.4824668338408" y2="1030.4853170279125" style="stroke-linecap: round; stroke-width: 3.75; stroke: #2B78E4; stroke-dasharray: 0;"></line></g><g data-node-id="vVshXAzK203UzQkgk2HrP" data-type="label"><text x="219.99392181540966" y="891.4156776926133" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>3</tspan></text></g> <g data-node-id="Bn_BkthrVX_vOuwQzvPZa" data-type="subtopic" data-title="Max Length"><rect x="596.535294294251" y="879.9646341150118" width="257.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="725.185294294251" y="905.2646341150117" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Max Length</tspan></text></g> <g data-node-id="tyjeFUjjTGFVXRLOe1Oh1" data-type="paragraph"><rect x="256.11750909294227" y="1139.8176406496057" width="226" height="53.5" rx="5" fill="transparent" stroke="transparent" stroke-width="2.5"></rect><text fill="#000000"><tspan x="367.86750909294227" y="1166.5676406496057" dy="0" text-anchor="middle" dominant-baseline="middle" font-size="20">Prompt Engineering</tspan></text></g> <g data-node-id="Y8EqzFx3qxtrSh7bWbbV8" data-type="topic" data-title="What is Prompt Engineering" data-parent-id="tyjeFUjjTGFVXRLOe1Oh1" data-parent-title="Prompt Engineering"><rect x="-135.8955305579661" y="1141.4176406496056" width="293.3" height="50.3" rx="5" fill="#fdff00" stroke="black" stroke-width="2.7" style="--hover-color: #d6d700"></rect><text x="10.754469442033894" y="1168.7176406496058" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>What is Prompt Engineering</tspan></text></g> <g data-node-id="qFKFM2qNPEN7EoD0V-1SM" data-type="subtopic" data-title="Be specific in what you want"><rect x="-124.71066608698446" y="1294.2005644697545" width="272.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="11.439333913015545" y="1319.5005644697546" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Be specific in what you want</tspan></text></g> <g data-node-id="6I42CoeWX-kkFXTKAY7rw" data-type="subtopic" data-title="Provide additional context"><rect x="-124.71066608698446" y="1347.2005644697545" width="272.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="11.439333913015545" y="1372.5005644697546" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Provide additional context</tspan></text></g> <g data-node-id="sUwdtOX550tSdceaeFPmF" data-type="subtopic" data-title="Use relevant technical terms"><rect x="-124.71066608698446" y="1400.2005644697545" width="272.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="11.439333913015545" y="1425.5005644697546" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Use relevant technical terms</tspan></text></g> <g data-node-id="yulzE4ZNLhXOgHhG7BtZQ" data-type="subtopic" data-title="Use Examples in your Prompt"><rect x="-124.71066608698446" y="1453.2005644697545" width="272.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="11.439333913015545" y="1478.5005644697546" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Use Examples in your Prompt</tspan></text></g> <g data-node-id="noTuUFnHSBzn7GKG9UZEi" data-type="subtopic" data-title="Iterate and Test your Prompts"><rect x="-124.71066608698446" y="1506.2005644697545" width="272.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="11.439333913015545" y="1531.5005644697546" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Iterate and Test your Prompts</tspan></text></g> <g data-node-id="wwHHlEoPAx0TLxbtY6nMA" data-type="subtopic" data-title="Specify Length, format etc"><rect x="-124.71066608698446" y="1559.2005644697545" width="272.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="11.439333913015545" y="1584.5005644697546" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Specify Length, format etc</tspan></text></g> <g data-node-id="QWpwDrwySbmdDYJaR3UhQ" data-type="label"><text x="-74.49208093839397" y="1279.8505644697545" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>Writing Good Prompts</tspan></text></g> <g data-link="https://roadmap.sh/prompt-engineering" data-node-id="wCOn2Lb_RlEKkVvdulRXj" data-type="button"><rect x="-135.8955305579661" y="1616.3797261117415" width="293.3" height="46.3" rx="5" fill="#4036d6" stroke="#4036d6" stroke-width="2.7"></rect><text x="10.754469442033894" y="1641.6797261117417" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#ffffff"><tspan>Prompt Engineering Roadmap</tspan></text></g> <g data-node-id="nlVToD-pmbBV2ikgrCQiO" data-type="paragraph"><rect x="254.51555040271785" y="1247.1005644697545" width="227" height="53.5" rx="5" fill="transparent" stroke="transparent" stroke-width="2.5"></rect><text fill="#000000"><tspan x="366.76555040271785" y="1273.8505644697545" dy="0" text-anchor="middle" dominant-baseline="middle" font-size="20">Tools / Actions</tspan></text></g> <g data-node-id="R1zdRA3UqlLOjfSw9Swx_" data-type="horizontal"><line x1="158.7544694420339" y1="1273.8505644697545" x2="264.7544694420339" y2="1273.8505644697545" style="stroke-linecap: round; stroke-width: 3.75; stroke: #2B78E4; stroke-dasharray: 0;"></line></g><g data-node-id="qakbxB8xe7Y8gejC5cZnK" data-type="topic" data-title="Tool Definition" data-parent-id="nlVToD-pmbBV2ikgrCQiO" data-parent-title="Tools / Actions"><rect x="249.11555040271784" y="1373.9568144540935" width="235.3" height="46.3" rx="5" fill="#fdff00" stroke="black" stroke-width="2.7" style="--hover-color: #d6d700"></rect><text x="366.76555040271785" y="1399.2568144540937" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Tool Definition</tspan></text></g> <g data-node-id="VpAF2EGz8HtnE-Xt3hzpf" data-type="label"><text x="276.26555040271785" y="1457.5238284556976" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>Name and Description</tspan></text></g> <g data-node-id="0MUgaZi5vHHnhYYiXsIdX" data-type="label"><text x="276.26555040271785" y="1482.5238284556976" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>Input / Output Schema</tspan></text></g> <g data-node-id="ehSLDOPK-mndL9CXF60s5" data-type="label"><text x="276.26555040271785" y="1507.5238284556976" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>Error Handling</tspan></text></g> <g data-node-id="5Tu6zdcNbotuhVAwTYeh_" data-type="label"><text x="276.26555040271785" y="1532.5238284556976" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>Usage Examples</tspan></text></g> <g data-node-id="gAGw6nEhIY9O5yYmfHTmv" data-type="section"><rect x="588.5475456690336" y="1376.4620497334347" width="280.3" height="374.3" rx="5" fill="#ffffff" stroke="#000000" stroke-width="2.7"></rect></g><g data-node-id="7AhPsyz0KfJkbn5THLOvb" data-type="label"><text x="661.1975456690336" y="1409.0297261117416" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>Examples of Tools</tspan></text></g> <g data-node-id="kBtqT8AduLoYDWopj-V9_" data-type="subtopic" data-title="Web Search"><rect x="600.0475456690336" y="1428.3797261117415" width="256.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="728.1975456690336" y="1453.6797261117417" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Web Search</tspan></text></g> <g data-node-id="mS0EVCkWuPN_GkVPng4A2" data-type="subtopic" data-title="Code Execution / REPL"><rect x="600.0475456690336" y="1481.3797261117415" width="256.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="728.1975456690336" y="1506.6797261117417" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Code Execution / REPL</tspan></text></g> <g data-node-id="sV1BnA2-qBnXoKpUn-8Ub" data-type="subtopic" data-title="Database Queries"><rect x="600.0475456690336" y="1534.3797261117415" width="256.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="728.1975456690336" y="1559.6797261117417" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Database Queries</tspan></text></g> <g data-node-id="52qxjZILV-X1isup6dazC" data-type="subtopic" data-title="API Requests"><rect x="600.0475456690336" y="1587.3797261117415" width="256.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="728.1975456690336" y="1612.6797261117417" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>API Requests</tspan></text></g> <g data-node-id="qaNr5I-NQPnfrRH7ynGTl" data-type="subtopic" data-title="Email / Slack / SMS"><rect x="600.0475456690336" y="1640.3797261117415" width="256.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="728.1975456690336" y="1665.6797261117417" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Email / Slack / SMS</tspan></text></g> <g data-node-id="BoJqZvdGam4cd6G6yK2IV" data-type="subtopic" data-title="File System Access"><rect x="600.0475456690336" y="1693.3797261117415" width="256.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="728.1975456690336" y="1718.6797261117417" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>File System Access</tspan></text></g> <g data-node-id="9hKOwIyy5JP5IX_J2a2So" data-type="horizontal"><line x1="483.76555040271785" y1="1397.1068144540936" x2="587.7655504027178" y2="1397.1068144540936" style="stroke-linecap: round; stroke-width: 3.75; stroke: #2B78E4; stroke-dasharray: 0;"></line></g><g data-node-id="uMTuTzTcwMBwttBoHuEtr" data-type="paragraph"><rect x="289.60088070462893" y="1898.650354280238" width="180" height="50.5" rx="5" fill="transparent" stroke="transparent" stroke-width="2.5"></rect><text fill="#000000"><tspan x="378.35088070462893" y="1923.900354280238" dy="0" text-anchor="middle" dominant-baseline="middle" font-size="20">Agent Memory</tspan></text></g> <g data-node-id="Rjuzmgb1AHnm18qNwbRhQ" data-type="horizontal"><line x1="441.1975456690336" y1="1635.0297261117416" x2="587.1975456690336" y2="1635.0297261117416" style="stroke-linecap: round; stroke-width: 3.75; stroke: #2B78E4; stroke-dasharray: 0;"></line></g><g data-node-id="TBH_DZTAfR8Daoh-njNFC" data-type="topic" data-title="What is Agent Memory?"><rect x="634.15929781038" y="1814.4710935853252" width="231.3" height="46.3" rx="5" fill="#fdff00" stroke="black" stroke-width="2.7" style="--hover-color: #d6d700"></rect><text x="749.80929781038" y="1839.7710935853254" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>What is Agent Memory?</tspan></text></g><g data-node-id="M3U6RfIqaiut2nuOibY8W" data-type="topic" data-title="Short Term  Memory"><rect x="648.4488844716816" y="2001.8757670010978" width="230.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect> <text x="763.5988844716816" y="2027.175767001098" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Short Term Memory</tspan></text></g> <g data-node-id="Ue633fz6Xu2wa2-KOAtdP" data-type="topic" data-title="Long Term Memory"><rect x="648.9488844716816" y="2093.8757670010978" width="229.3" height="51.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="763.5988844716816" y="2121.675767001098" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Long Term Memory</tspan></text></g> <g data-node-id="UcfPT2eIDT8peU0P8mere" data-type="label"><text x="712.0988844716816" y="2079.525767001098" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>Within Prompt</tspan></text></g> <g data-node-id="gLACXg46Uxaa1bbL8B8K1" data-type="label"><text x="664.0988844716816" y="2176.525767001098" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>Vector DB / SQL / Custom</tspan></text></g> <g data-node-id="zx2EMzU0tWyKo73Ebope9" data-type="horizontal"><line x1="532.0988844716816" y1="2033.1355435052951" x2="636.0988844716816" y2="2033.1355435052951" style="stroke-linecap: round; stroke-width: 3.75; stroke: #2B78E4; stroke-dasharray: 0;"></line></g><g data-node-id="EfCCNqLMJpWKKtamUa5gK" data-type="topic" data-title="Episodic vs Semantic Memory"><rect x="280.8324668338408" y="2009.985543505295" width="274.3" height="46.3" rx="5" fill="#fdff00" stroke="black" stroke-width="2.7" style="--hover-color: #d6d700"></rect><text x="417.98246683384076" y="2035.2855435052952" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Episodic vs Semantic Memory</tspan></text></g> <g data-node-id="T2vksVGCRajcwy_WRe2Yf" data-type="section"><rect x="-135.3430694693353" y="2005.7605696858577" width="297.3" height="273.3" rx="5" fill="#ffffff" stroke="#000000" stroke-width="2.7"></rect></g><g data-node-id="CEZcNlJeX6Gb4F1bZNS_X" data-type="label"><text x="-63.193069469335285" y="2038.328246064165" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>Maintaining Memory</tspan></text></g> <g data-node-id="wkS4yOJ3JdZQE_yBID8K7" data-type="subtopic" data-title="RAG and Vector Databases"><rect x="-124.34306946933529" y="2057.678246064165" width="275.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="13.306930530664715" y="2082.978246064165" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>RAG and Vector Databases</tspan></text></g> <g data-node-id="QJqXHV8VHPTnfYfmKPzW7" data-type="subtopic" data-title="User Profile Storage"><rect x="-124.34306946933529" y="2110.678246064165" width="275.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="13.306930530664715" y="2135.978246064165" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>User Profile Storage</tspan></text></g> <g data-node-id="jTDC19BTWCqxqMizrIJHr" data-type="subtopic" data-title="Summarization / Compression"><rect x="-124.34306946933529" y="2163.678246064165" width="275.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="13.306930530664715" y="2188.978246064165" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Summarization / Compression</tspan></text></g> <g data-node-id="m-97m7SI0XpBnhEE8-_1S" data-type="subtopic" data-title="Forgetting / Aging Strategies"><rect x="-124.34306946933529" y="2216.678246064165" width="275.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="13.306930530664715" y="2241.978246064165" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Forgetting / Aging Strategies</tspan></text></g> <g data-node-id="SU8HwGaDiRCho5I_pBl3N" data-type="paragraph"><rect x="290.72614503814657" y="2120.160569685858" width="216" height="44.5" rx="5" fill="transparent" stroke="transparent" stroke-width="2.5"></rect><text fill="#000000"><tspan x="397.47614503814657" y="2142.410569685858" dy="0" text-anchor="middle" dominant-baseline="middle" font-size="20">Agent Architectures</tspan></text></g> <g data-node-id="53xDks6JQ33fHMa3XcuCd" data-type="topic" data-title="ReAct (Reason + Act)"><rect x="283.3261450381466" y="2322.7670226331693" width="228.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="397.47614503814657" y="2348.0670226331695" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>ReAct (Reason + Act)</tspan></text></g> <g data-node-id="1B0IqRNYdtbHDi1jHSXuI" data-type="topic" data-title="Model Context Protocol (MCP)"><rect x="236.33246683384075" y="1611.8797261117415" width="283.3" height="46.3" rx="5" fill="#fdff00" stroke="black" stroke-width="2.7" style="--hover-color: #d6d700"></rect><text x="377.98246683384076" y="1637.1797261117417" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Model Context Protocol (MCP)</tspan></text></g> <g data-node-id="9FryAIrWRHh8YlzKX3et5" data-type="subtopic" data-title="MCP Hosts"><rect x="245.32212622056713" y="1765.4710935853252" width="130.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="310.47212622056713" y="1790.7710935853254" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>MCP Hosts</tspan></text></g> <g data-node-id="CGVstUxVXLJcYZrwk3iNQ" data-type="subtopic" data-title="MCP Client"><rect x="383.33246683384084" y="1765.4710935853252" width="129.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="447.9824668338408" y="1790.7710935853254" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>MCP Client</tspan></text></g> <g data-node-id="yv_-87FVM7WKn5iv6LW9q" data-type="subtopic" data-title="MCP Servers"><rect x="244.32212622056713" y="1819.35" width="268.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="378.47212622056713" y="1844.65" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>MCP Servers</tspan></text></g> <g data-node-id="gAPRLPQFVHuB9rBW5-IRG" data-type="label"><text x="310.35088070462893" y="1746.2407354438553" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>Core Components</tspan></text></g> <g data-node-id="1NXIN-Hbjl5rPy_mqxQYW" data-type="topic" data-title="Creating MCP Servers"><rect x="-102.39553055796611" y="1712.5907354438552" width="226.3" height="46.3" rx="5" fill="#fdff00" stroke="black" stroke-width="2.7" style="--hover-color: #d6d700"></rect><text x="10.754469442033894" y="1737.8907354438554" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Creating MCP Servers</tspan></text></g> <g data-node-id="CppUUpbDy6j0_wjd94qgO" data-type="label"><text x="-61.745530557966106" y="1838.6702232480886" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>Deployment Modes</tspan></text></g> <g data-node-id="iBtJp24F_kJE3YlBsW60s" data-type="subtopic" data-title="Local Desktop"><rect x="-89.89553055796611" y="1859.9663326459772" width="201.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="10.754469442033894" y="1885.2663326459774" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Local Desktop</tspan></text></g> <g data-node-id="dHNMX3_t1KSDdAWqgdJXv" data-type="subtopic" data-title="Remote / Cloud"><rect x="-89.89553055796611" y="1912.9663326459772" width="201.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="10.754469442033894" y="1938.2663326459774" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Remote / Cloud</tspan></text></g> <g data-node-id="8ym1jwJfLUnj4uimn5HFR" data-type="horizontal"><line x1="125.2544694420339" y1="1923.900354280238" x2="290.2544694420339" y2="1923.900354280238" style="stroke-linecap: round; stroke-width: 3.75; stroke: #2B78E4; stroke-dasharray: 0;"></line></g><g data-node-id="t1SqlX1PhIL2cTc0h4fXQ" data-type="horizontal"><line x1="467.3290815371215" y1="1923.900354280238" x2="720.3290815371215" y2="1923.900354280238" style="stroke-linecap: round; stroke-width: 3.75; stroke: #2B78E4; stroke-dasharray: 0;"></line></g><g data-node-id="qwdh5pkBbrF8LKPxbZp4F" data-type="topic" data-title="Chain of Thought (CoT)"><rect x="283.3261450381466" y="2375.7670226331693" width="228.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="397.47614503814657" y="2401.0670226331695" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Chain of Thought (CoT)</tspan></text></g> <g data-node-id="cW8O4vLLKEG-Q0dE8E5Zp" data-type="topic" data-title="RAG Agent"><rect x="283.3261450381466" y="2269.7670226331693" width="228.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="397.47614503814657" y="2295.0670226331695" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>RAG Agent</tspan></text></g> <g data-node-id="QKXjaGohaNwhuqqwb-z6b" data-type="label"><text x="311.97614503814657" y="2251.4170226331694" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>Common Architectures</tspan></text></g> <g data-node-id="5KAj1kjfDUY7xSPJIC2T8" data-type="label"><text x="294.47614503814657" y="2459.550271236641" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>Other Architecture Patterns</tspan></text></g> <g data-node-id="6YLCMWzystao6byCYCTPO" data-type="topic" data-title="Planner Executor"><rect x="283.3261450381466" y="2479.8274309459325" width="228.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="397.47614503814657" y="2505.1274309459327" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Planner Executor</tspan></text></g> <g data-node-id="Ep8RoZSy_Iq_zWXlGQLZo" data-type="topic" data-title="DAG Agents"><rect x="283.3261450381466" y="2532.8274309459325" width="228.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="397.47614503814657" y="2558.1274309459327" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>DAG Agents</tspan></text></g> <g data-node-id="Nmy1PoB32DcWZnPM8l8jT" data-type="topic" data-title="Tree-of-Thought"><rect x="282.3261450381466" y="2585.8274309459325" width="229.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="396.97614503814657" y="2611.1274309459327" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Tree-of-Thought</tspan></text></g> <g data-node-id="yaxVwWPeb7mHPZrXaNusP" data-type="horizontal"><line x1="527.9761450381466" y1="2270.410569685858" x2="750.9761450381466" y2="2270.410569685858" style="stroke-linecap: round; stroke-width: 3.75; stroke: #2B78E4; stroke-dasharray: 0;"></line></g><g data-node-id="7c-FMXWsMGv95vxnUydFi" data-type="paragraph"><rect x="643.05929781038" y="2343.120720452855" width="216" height="44.5" rx="5" fill="transparent" stroke="transparent" stroke-width="2.5"></rect><text fill="#000000"><tspan x="749.80929781038" y="2365.370720452855" dy="0" text-anchor="middle" dominant-baseline="middle" font-size="20">Building Agents</tspan></text></g> <g data-node-id="_w42GbQza-Qu1EDLbA83S" data-type="vertical"><line x1="749.80929781038" y1="2270.410569685858" x2="749.80929781038" y2="2332.410569685858" style="stroke-linecap: round; stroke-width: 3.75; stroke: #2B78E4; stroke-dasharray: 0;"></line></g><g data-node-id="US6T5dXM8IY9V2qZnTOFW" data-type="topic" data-title="Manual (from scratch)" data-parent-id="7c-FMXWsMGv95vxnUydFi" data-parent-title="Building Agents"><rect x="601.65929781038" y="2454.562822960118" width="296.3" height="46.3" rx="5" fill="#fdff00" stroke="black" stroke-width="2.7" style="--hover-color: #d6d700"></rect><text x="749.80929781038" y="2479.862822960118" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Manual (from scratch)</tspan></text></g> <g data-node-id="nHkXoJFoyDHcO3E4FhsGK" data-type="label"><text x="634.86544847935" y="2539.322480617088" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>Direct LLM API calls</tspan></text></g> <g data-node-id="Ox0SIl1qDY1MdNEa3Zcdh" data-type="label"><text x="634.86544847935" y="2569.322480617088" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>Implementing the agent loop</tspan></text></g> <g data-node-id="mVoZRlCcJcGCVRnGHbsWD" data-type="label"><text x="634.86544847935" y="2599.322480617088" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>Parsing model output</tspan></text></g> <g data-node-id="PUGICZOvB0H0EWwwUlusF" data-type="label"><text x="634.86544847935" y="2629.322480617088" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>Error &amp; Rate-limit handling</tspan></text></g> <g data-node-id="aafZxtjxiwzJH1lwHBODi" data-type="topic" data-title="LLM Native &quot;Function Calling&quot;" data-parent-id="MeOebqpbLZkPbj0iWRjP2"><rect x="601.65929781038" y="2697.27394018722" width="296.3" height="46.3" rx="5" fill="#fdff00" stroke="black" stroke-width="2.7" style="--hover-color: #d6d700"></rect><text x="749.80929781038" y="2722.57394018722" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>LLM Native "Function Calling"</tspan></text></g> <g data-node-id="AQtxTTxmBpfl8BMgJbGzc" data-type="subtopic" data-title="OpenAI Functions Calling" data-parent-id="aafZxtjxiwzJH1lwHBODi" data-parent-title="LLM Native &quot;Function Calling&quot;"><rect x="601.65929781038" y="2779.2080866298493" width="296.3" height="50.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="749.80929781038" y="2806.5080866298495" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>OpenAI Functions Calling</tspan></text></g> <g data-node-id="_iIsBJTVS6OBf_dsdmbVO" data-type="subtopic" data-title="Gemini Function Calling"><rect x="601.65929781038" y="2893.2080866298493" width="296.3" height="50.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="749.80929781038" y="2920.5080866298495" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Gemini Function Calling</tspan></text></g> <g data-node-id="37GBFVZ2J2d5r8bd1ViHq" data-type="subtopic" data-title="OpenAI Assistant API"><rect x="601.65929781038" y="2836.2080866298493" width="296.3" height="50.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="749.80929781038" y="2863.5080866298495" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>OpenAI Assistant API</tspan></text></g> <g data-node-id="VVFomJWwR-bvPRsiLmbiV" data-type="label"><text x="293.97614503814657" y="2739.92394018722" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>Building Using Frameworks</tspan></text></g> <g data-node-id="Ka6VpCEnqABvwiF9vba7t" data-type="subtopic" data-title="Langchain"><rect x="278.3261450381466" y="2765.27394018722" width="108.3" height="52.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="332.47614503814657" y="2793.57394018722" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Langchain</tspan></text></g> <g data-node-id="iEHF-Jm3ck-Iu85EbCoDi" data-type="subtopic" data-title="LlamaIndex"><rect x="394.8261450381466" y="2765.27394018722" width="121.3" height="52.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="455.47614503814657" y="2793.57394018722" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>LlamaIndex</tspan></text></g> <g data-node-id="XS-FsvtrXGZ8DPrwOsnlI" data-type="subtopic" data-title="Haystack"><rect x="278.3261450381466" y="2824.27394018722" width="108.3" height="52.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="332.47614503814657" y="2852.57394018722" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Haystack</tspan></text></g> <g data-node-id="7YtnQ9-KIvGPSpDzEDexl" data-type="subtopic" data-title="AutoGen"><rect x="394.8261450381466" y="2823.77394018722" width="121.3" height="52.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="455.47614503814657" y="2852.07394018722" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>AutoGen</tspan></text></g> <g data-node-id="uFPJqgU4qGvZyxTv-osZA" data-type="subtopic" data-title="CrewAI"><rect x="278.3261450381466" y="2883.27394018722" width="237.3" height="48.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="396.97614503814657" y="2909.57394018722" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>CrewAI</tspan></text></g> <g data-node-id="eWxQiBrxIUG2JNcrdfIHS" data-type="subtopic" data-title="Smol Depot"><rect x="278.3261450381466" y="2939.27394018722" width="237.3" height="52.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="396.97614503814657" y="2967.57394018722" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Smol Depot</tspan></text></g> <g data-node-id="1EZFbDHA5J5_5BPMLMxXb" data-type="subtopic" data-title="Anthropic Tool Use"><rect x="601.65929781038" y="2950.2080866298493" width="296.3" height="50.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="749.80929781038" y="2977.5080866298495" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Anthropic Tool Use</tspan></text></g> <g data-node-id="y3Km7Dc3FidoKtF7cnTfR" data-type="label"><text x="-64.7455305579661" y="2447.870720452855" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>Evaluation and Testing</tspan></text></g> <g data-node-id="v8qLnyFRnEumodBYxQSXQ" data-type="subtopic" data-title="Metrics to Track"><rect x="-120.39553055796611" y="2464.900271236641" width="280.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="19.754469442033894" y="2490.200271236641" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Metrics to Track</tspan></text></g> <g data-node-id="qo_O4YAe4-MTP_ZJoXJHR" data-type="subtopic" data-title="Unit Testing for Individual Tools"><rect x="-120.39553055796611" y="2517.900271236641" width="280.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="19.754469442033894" y="2543.200271236641" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Unit Testing for Individual Tools</tspan></text></g> <g data-node-id="P9-SiIda3TSjHsfkI5OUV" data-type="subtopic" data-title="Integration Testing for Flows"><rect x="-120.39553055796611" y="2570.900271236641" width="280.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="19.754469442033894" y="2596.200271236641" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Integration Testing for Flows</tspan></text></g> <g data-node-id="rHxdxN97ZcU7MPl8L1jzN" data-type="subtopic" data-title="Human in the Loop Evaluation"><rect x="-120.39553055796611" y="2623.900271236641" width="280.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="19.754469442033894" y="2649.200271236641" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Human in the Loop Evaluation</tspan></text></g> <g data-node-id="xp7TCTRE9HP60_rGzTUF6" data-type="subtopic" data-title="LangSmith"><rect x="-122.97279103658767" y="2718.77394018722" width="142.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="-51.82279103658766" y="2744.07394018722" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>LangSmith</tspan></text></g> <g data-node-id="YzEDtGEaMaMWVt0W03HRt" data-type="subtopic" data-title="Ragas"><rect x="-122.97279103658767" y="2772.27394018722" width="282.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="18.17720896341234" y="2797.57394018722" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Ragas</tspan></text></g> <g data-node-id="0924QUH1wV7Mp-Xu0FAhF" data-type="subtopic" data-title="DeepEval"><rect x="27.656930530664717" y="2718.77394018722" width="131.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="93.30693053066472" y="2744.07394018722" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>DeepEval</tspan></text></g> <g data-node-id="OxRCEi7olEsHcBZ7oVfLl" data-type="label"><text x="-26.32279103658766" y="2702.322480617088" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>Frameworks</tspan></text></g> <g data-node-id="zs6LM8WEnb0ERWpiaQCgc" data-type="subtopic" data-title="Structured logging &amp; tracing"><rect x="-122.1597055754202" y="2891.8501335516635" width="282.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="18.9902944245798" y="2917.1501335516637" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Structured logging &amp; tracing</tspan></text></g> <g data-node-id="P5D6O9GmDjENsvNStJcEC" data-type="label"><text x="-81.5097055754202" y="2867.434279994293" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>Debugging and Monitoring</tspan></text></g> <g data-node-id="PsMoyCyhXdYIIgTK4mduG" data-type="label"><text x="-50.009705575420185" y="2970.0001335516636" text-anchor="left" dominant-baseline="auto" font-size="17" fill="black"><tspan>Observability Tools</tspan></text></g> <g data-node-id="SS8mGqf9wfrNqenIWvN8Z" data-type="subtopic" data-title="LangSmith"><rect x="-121.38444959728217" y="2989.3501335516635" width="135.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="-53.734449597282165" y="3014.6501335516637" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>LangSmith</tspan></text></g> <g data-node-id="MLxP5N0Vrmwh-kyvNeGXn" data-type="subtopic" data-title="Helicone"><rect x="21.857919061606037" y="2989.3501335516635" width="138.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="91.00791906160603" y="3014.6501335516637" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Helicone</tspan></text></g> <g data-node-id="UoIheaJlShiceafrWALEH" data-type="subtopic" data-title="LangFuse"><rect x="-121.38444959728217" y="3043.3501335516635" width="135.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="-53.734449597282165" y="3068.6501335516637" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>LangFuse</tspan></text></g> <g data-node-id="7UqPXUzqKYXklnB3x-tsv" data-type="subtopic" data-title="openllmetry"><rect x="21.857919061606037" y="3043.3501335516635" width="138.3" height="46.3" rx="5" fill="#ffe599" stroke="black" stroke-width="2.7" style="--hover-color: #f3c950"></rect><text x="91.00791906160603" y="3068.6501335516637" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>openllmetry</tspan></text></g> <g data-node-id="ZczxILvJ8ZVlaNd887lS5" data-type="horizontal"><line x1="169.48246683384076" y1="2720.42394018722" x2="265.48246683384076" y2="2720.42394018722" style="stroke-linecap: round; stroke-width: 3.75; stroke: #2d78e1; stroke-dasharray: 0;"></line></g><g data-node-id="8Fk3qNFO_5UNxJDGDDCc8" data-type="horizontal"><line x1="170.99167975847877" y1="3056.5001335516636" x2="292.99167975847877" y2="3056.5001335516636" style="stroke-linecap: round; stroke-width: 3.5; stroke: #2B78E4; stroke-dasharray: 0;"></line></g><g data-node-id="AZGZKUelTu9A0VbpnsrnN" data-type="paragraph"><rect x="288.72614503814657" y="3035.7501335516636" width="219" height="41.5" rx="5" fill="transparent" stroke="transparent" stroke-width="2.5"></rect><text fill="#000000"><tspan x="396.97614503814657" y="3056.5001335516636" dy="0" text-anchor="middle" dominant-baseline="middle" font-size="20">Security &amp; Ethics</tspan></text></g> <g data-node-id="41n1zuSz7Ur8Zq0GcmQ9y" data-type="horizontal"><line x1="498.86750909294227" y1="3056.5001335516636" x2="750.8675090929423" y2="3056.5001335516636" style="stroke-linecap: round; stroke-width: 3.5; stroke: #2B78E4; stroke-dasharray: 0;"></line></g><g data-node-id="SU2RuicMUo8tiAsQtDI1k" data-type="topic" data-title="Prompt Injection / Jailbreaks"><rect x="603.65929781038" y="3098.3501335516635" width="292.3" height="46.3" rx="5" fill="#fdff00" stroke="black" stroke-width="2.7" style="--hover-color: #d6d700"></rect><text x="749.80929781038" y="3123.6501335516637" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Prompt Injection / Jailbreaks</tspan></text></g> <g data-node-id="UVzLGXG6K7HQVHmw8ZAv2" data-type="topic" data-title="Tool sandboxing / Permissioning"><rect x="603.65929781038" y="3151.1001335516635" width="292.3" height="46.3" rx="5" fill="#fdff00" stroke="black" stroke-width="2.7" style="--hover-color: #d6d700"></rect><text x="749.80929781038" y="3176.4001335516637" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Tool sandboxing / Permissioning</tspan></text></g> <g data-node-id="rdlYBJNNyZUshzsJawME4" data-type="topic" data-title="Data Privacy + PII Redaction"><rect x="605.65929781038" y="3203.8501335516635" width="292.3" height="46.3" rx="5" fill="#fdff00" stroke="black" stroke-width="2.7" style="--hover-color: #d6d700"></rect><text x="751.80929781038" y="3229.1501335516637" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Data Privacy + PII Redaction</tspan></text></g> <g data-node-id="EyLo2j8IQsIK91SKaXkmK" data-type="topic" data-title="Bias &amp; Toxicity Guardrails"><rect x="605.65929781038" y="3256.6001335516635" width="292.3" height="46.3" rx="5" fill="#fdff00" stroke="black" stroke-width="2.7" style="--hover-color: #d6d700"></rect><text x="751.80929781038" y="3281.9001335516637" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Bias &amp; Toxicity Guardrails</tspan></text></g> <g data-node-id="63nsfJFO1BwjLX_ZVaPFC" data-type="topic" data-title="Safety + Red Team Testing"><rect x="605.65929781038" y="3309.3501335516635" width="292.3" height="46.3" rx="5" fill="#fdff00" stroke="black" stroke-width="2.7" style="--hover-color: #d6d700"></rect><text x="751.80929781038" y="3334.6501335516637" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#000000"><tspan>Safety + Red Team Testing</tspan></text></g> <g data-link="https://roadmap.sh/ai-data-scientist" data-node-id="-sFboM4eFUMVq1tlPl-fV" data-type="button"><rect x="214.34392181540966" y="3302.090989142758" width="220.3" height="46.3" rx="5" fill="#4136D6" stroke="#4136D6" stroke-width="2.7"></rect><text x="324.49392181540964" y="3327.3909891427584" text-anchor="middle" dominant-baseline="middle" font-size="17" fill="#FFFFFf"><tspan>AI &amp; Data Scientist</tspan></text></g></svg>

Join the Community

roadmap.sh is the [6th most starred project on GitHub](https://github.com/search?o=desc&q=stars%3A%3E100000&s=stars&type=Repositories) and is visited by hundreds of thousands of developers every month.

Rank 6th out of 28M!

323K

GitHub Stars

[

Star us on GitHub

Help us reach #1](https://github.com/kamranahmedse/developer-roadmap)

+2k every month

37K

Discord Members

[

Join on Discord

Join the community](https://roadmap.sh/discord)

[Roadmaps](https://roadmap.sh/roadmaps) [Best Practices](https://roadmap.sh/best-practices) [Guides](https://roadmap.sh/guides) [Videos](https://roadmap.sh/videos) [FAQs](https://roadmap.sh/about) [YouTube](https://youtube.com/theroadmap?sub_confirmation=1)

[roadmap.sh](https://roadmap.sh/) by [@kamrify](https://x.com/kamrify)

Community created roadmaps, best practices, projects, articles, resources and journeys to help you choose your path and grow in your career.

The top DevOps resource for Kubernetes, cloud-native computing, and large-scale development and deployment.

· ·