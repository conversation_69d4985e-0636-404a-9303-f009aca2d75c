{"newFiles": [{"path": "Daily/Guitar/2025-07-03.md", "basename": "2025-07-03"}, {"path": "Reflection/guitar_daily_log.md", "basename": "guitar_daily_log"}, {"path": "Template/Guitar Practice.md", "basename": "Guitar Practice"}, {"path": "guitar_practice.md", "basename": "guitar_practice"}, {"path": "Courses/Huggingface LLM Course.md", "basename": "Huggingface LLM Course"}, {"path": "Books/门将之死 罗伯特恩克的故事.md", "basename": "门将之死 罗伯特恩克的故事"}, {"path": "News/这是我的30岁生日礼物.md", "basename": "这是我的30岁生日礼物"}, {"path": "News/Felix Hill临终信公开：耗时18个月写完，AI天才的挣扎与告别.md", "basename": "Felix Hill临终信公开：耗时18个月写完，AI天才的挣扎与告别"}, {"path": "News/得了不治之症后，我卖命存了100万留给老婆.md", "basename": "得了不治之症后，我卖命存了100万留给老婆"}, {"path": "News/可能是方大同新专辑的唯一专访!丨真假方大同终于同框丨HOPICO.md", "basename": "可能是方大同新专辑的唯一专访!丨真假方大同终于同框丨HOPICO"}, {"path": "News/倍速人生：97年尘肺夫妻的爱与绝望.md", "basename": "倍速人生：97年尘肺夫妻的爱与绝望"}, {"path": "News/三个小时，我救回了我爸的命 - 小红书.md", "basename": "三个小时，我救回了我爸的命 - 小红书"}, {"path": "News/Faker Living as a pro gamer is like a rollercoaster.md", "basename": "<PERSON>aker Living as a pro gamer is like a rollercoaster"}, {"path": "News/EP 58. 你所不知道的AI产品，哪些正在“闷声赚大钱”？.md", "basename": "EP 58. 你所不知道的AI产品，哪些正在“闷声赚大钱”？"}, {"path": "News/The 100 Most Popular Online Courses (2025 Edition) — Class Central.md", "basename": "The 100 Most Popular Online Courses (2025 Edition) — Class Central"}, {"path": "News/埃及志愿者从埃及进入加沙被阻拦.md", "basename": "埃及志愿者从埃及进入加沙被阻拦"}, {"path": "News/No. 191 - 旅行｜英国的公园、历史、教堂与入室抢劫般的罗曼蒂克@伦敦.md", "basename": "No. 191 - 旅行｜英国的公园、历史、教堂与入室抢劫般的罗曼蒂克@伦敦"}, {"path": "News/MCP vs API：Simplifying AI Agent Integration with External Data.md", "basename": "MCP vs API：Simplifying AI Agent Integration with External Data"}, {"path": "News/Build an LLM Trading Agent with Natural Language & MCP Server.md", "basename": "Build an LLM Trading Agent with Natural Language & MCP Server"}, {"path": "News/避免过度思考，高阶能力就是大脑运转自由.md", "basename": "避免过度思考，高阶能力就是大脑运转自由"}, {"path": "News/世界好像变难了，我们该如何应对？| 对谈绿洲资本张津剑之 AI、人 与 生命力.md", "basename": "世界好像变难了，我们该如何应对？| 对谈绿洲资本张津剑之 AI、人 与 生命力"}, {"path": "News/与张云帆聊《芒格之道》：对知识诚实，是你能给自己最好的礼物.md", "basename": "与张云帆聊《芒格之道》：对知识诚实，是你能给自己最好的礼物"}, {"path": "News/情绪经济：今天你的情绪被变现了吗？.md", "basename": "情绪经济：今天你的情绪被变现了吗？"}, {"path": "Projects/index.md", "basename": "index"}, {"path": "projects_index.md", "basename": "projects_index"}, {"path": "Books/Book.md 13-48-00-119.md", "basename": "Book.md 13-48-00-119"}, {"path": "Template/Book.md", "basename": "Book"}, {"path": "Template/Lecture.md", "basename": "Lecture"}, {"path": "Template/Course.md", "basename": "Course"}, {"path": "Template/Project.md", "basename": "Project"}, {"path": "Template/Eisenhower Matrix.md", "basename": "<PERSON>"}, {"path": "Template/Weekly Review.md", "basename": "Weekly Review"}, {"path": "Template/Cornell Notes.md", "basename": "Cornell Notes"}, {"path": "Template/News.md", "basename": "News"}, {"path": "News/S5E8 鲁豫对话张春.md", "basename": "S5E8 鲁豫对话张春"}, {"path": "assets/xhs1.mov", "basename": "xhs1"}, {"path": "Clippings/DevTools Snippets.md", "basename": "DevTools Snippets"}, {"path": "News/脑科医生不常告诉你的事.md", "basename": "脑科医生不常告诉你的事"}, {"path": "News/吴恩达谈GenAI工程师.md", "basename": "吴恩达谈GenAI工程师"}, {"path": "News/陶哲轩罕见长长长长长访谈：数学、AI和给年轻人的建议.md", "basename": "陶哲轩罕见长长长长长访谈：数学、AI和给年轻人的建议"}, {"path": "News/年轻人不考研，去考公了.md", "basename": "年轻人不考研，去考公了"}, {"path": "assets/Pasted image 20250616160231.png", "basename": "Pasted image 20250616160231"}, {"path": "TG/Access k8s pod to check tg logs.md", "basename": "Access k8s pod to check tg logs"}, {"path": "Notes/Swimming.md", "basename": "Swimming"}, {"path": "Clippings/New! Automate your Systematic Review with Elicit.md", "basename": "New! Automate your Systematic Review with <PERSON><PERSON><PERSON>"}, {"path": "Clippings/Apple 设计大奖 - 2025 年获奖及入围名单 - Apple 开发者.md", "basename": "Apple 设计大奖 - 2025 年获奖及入围名单 - Apple 开发者"}, {"path": "Notes/灵感Ideas.md", "basename": "灵感Ideas"}, {"path": "Programming/AI/AI agents/Pasted image 20250529233741.png", "basename": "Pasted image 20250529233741"}, {"path": "Programming/AI/AI agents/Pasted image 20250529233719.png", "basename": "Pasted image 20250529233719"}, {"path": "Programming/AI/AI agents/Pasted image 20250529233404.png", "basename": "Pasted image 20250529233404"}, {"path": "Notes/事业单位考试和公务员考试的区别.md", "basename": "事业单位考试和公务员考试的区别"}, {"path": "Notes/kaobian.md", "basename": "ka<PERSON>n"}, {"path": "Clippings/AI agent course/AI Agents Roadmap.md", "basename": "AI Agents Roadmap"}, {"path": "Clippings/AI agent course/Fine-Tune your model for function-calling.md", "basename": "Fine-Tune your model for function-calling"}, {"path": "Clippings/AI agent course/什么是函数调用？(What is Function Calling?).md", "basename": "什么是函数调用？(What is Function Calling?)"}, {"path": "Clippings/AI agent course/创建宾客信息检索生成（RAG）工具.md", "basename": "创建宾客信息检索生成（RAG）工具"}, {"path": "Clippings/AI agent course/智能体增强检索生成（Agentic RAG）.md", "basename": "智能体增强检索生成（Agentic RAG）"}, {"path": "Clippings/AI agent course/LangGraph资源.md", "basename": "LangGraph资源"}, {"path": "Clippings/AI agent course/文档分析智能体.md", "basename": "文档分析智能体"}, {"path": "Clippings/AI agent course/构建你的第一个 LangGraph.md", "basename": "构建你的第一个 LangGraph"}, {"path": "Clippings/AI agent course/LangGraph 的核心构建模块.md", "basename": "LangGraph 的核心构建模块"}, {"path": "Clippings/AI agent course/在 LlamaIndex 中创建智能工作流 - Hugging Face Agents Course.md", "basename": "在 LlamaIndex 中创建智能工作流 - Hugging Face Agents Course"}, {"path": "Programming/AI/AI agents/在 LlamaIndex 中创建智能工作流 - Hugging Face Agents Course.md", "basename": "在 LlamaIndex 中创建智能工作流 - Hugging Face Agents Course"}, {"path": "Programming/AI/AI agents/创建多智能体系统.md", "basename": "创建多智能体系统"}, {"path": "Programming/AI/AI agents/三种主要类型的推理智能体.md", "basename": "三种主要类型的推理智能体"}, {"path": "Programming/AI/AI agents/在 LlamaIndex 中使用工具.md", "basename": "在 LlamaIndex 中使用工具"}, {"path": "Programming/AI/AI agents/RAG.md", "basename": "RAG"}, {"path": "Programming/AI/AI agents/LlamaIndex.md", "basename": "LlamaIndex"}, {"path": "Programming/AI/AI agents/使用提示词和 LLM 查询 VectorStoreIndex.md", "basename": "使用提示词和 LLM 查询 VectorStoreIndex"}, {"path": "Programming/AI/AI agents/存储与索引文档.md", "basename": "存储与索引文档"}, {"path": "Programming/AI/AI agents/LlamaIndex数据加载与嵌入.md", "basename": "LlamaIndex数据加载与嵌入"}, {"path": "Programming/AI/AI agents/LlamaIndex QueryEngine.md", "basename": "LlamaIndex QueryEngine"}, {"path": "Programming/AI/AI agents/RAG五个关键阶段.md", "basename": "RAG五个关键阶段"}, {"path": "Programming/AI/AI agents/什么是 RAG.md", "basename": "什么是 RAG"}, {"path": "Clippings/微软一晚上发了50个新东西，要建一个Agent互联的“伊甸园”.md", "basename": "微软一晚上发了50个新东西，要建一个Agent互联的“伊甸园”"}, {"path": "Programming/AI/AI agents/初始执行阶段提供图像.md", "basename": "初始执行阶段提供图像"}, {"path": "Programming/AI/AI agents/提供屏幕截图.md", "basename": "提供屏幕截图"}, {"path": "Programming/AI/AI agents/浏览器自动化工具.md", "basename": "浏览器自动化工具"}, {"path": "Programming/AI/AI agents/How CodeAgent run.md", "basename": "How CodeAgent run"}, {"path": "Programming/AI/AI agents/Pasted image 20250520010622.png", "basename": "Pasted image 20250520010622"}, {"path": "Programming/AI/AI agents/基于 MultiStepAgent 类.md", "basename": "基于 MultiStepAgent 类"}, {"path": "Programming/AI/AI agents/使用 smolagents 构建视觉智能体.md", "basename": "使用 smolagents 构建视觉智能体"}, {"path": "Notes/程序员还有出路吗.md", "basename": "程序员还有出路吗"}, {"path": "excalibrain.md", "basename": "excalibrain"}, {"path": "Programming/AI/AI agents/Example Building Agentic RAG Systems.md", "basename": "Example Building Agentic RAG Systems"}, {"path": "Programming/AI/AI agents/How to write tools using smolagents.md", "basename": "How to write tools using smolagents"}, {"path": "Programming/AI/AI agents/Example Running a Tool Calling Agent.md", "basename": "Example Running a Tool Calling Agent"}, {"path": "Programming/AI/AI agents/Using Python Imports Inside the Agent.md", "basename": "Using Python Imports Inside the Agent"}, {"path": "Programming/AI/AI agents/smolagents.md", "basename": "smolagents"}, {"path": "Programming/AI/AI agents/Example prepare a menu.md", "basename": "Example prepare a menu"}, {"path": "Programming/AI/AI agents/Example deepseek.md", "basename": "Example deepseek"}, {"path": "Programming/AI/AI agents/How do we give tools to an LLM.md", "basename": "How do we give tools to an LLM"}, {"path": "Programming/AI/AI agents/What are AI Tools.md", "basename": "What are AI Tools"}, {"path": "Programming/AI/AI agents/Tools.md", "basename": "Tools"}, {"path": "Programming/AI/AI agents/Example get_travel_duration.md", "basename": "Example get_travel_duration"}, {"path": "Programming/AI/AI agents/What are agents.md", "basename": "What are agents"}, {"path": "Programming/AI/AI agents/Code Agents.md", "basename": "Code Agents"}, {"path": "Programming/AI/AI agents/Types of Agent Actions.md", "basename": "Types of Agent Actions"}, {"path": "Programming/AI/AI agents/Multi-Agent Systems.md", "basename": "Multi-Agent Systems"}, {"path": "Programming/AI/AI agents/What's LLM.md", "basename": "What's LLM"}], "omittedPaths": [], "omittedTags": [], "maxLength": 100, "showExtension": false, "activeFileType": "all", "enableFileTypeFilter": false, "sortOrder": "newest"}