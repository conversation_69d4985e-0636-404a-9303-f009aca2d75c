---
title: "Introducing smolagents: simple agents that write actions in code."
source: "https://huggingface.co/blog/smolagents"
author:
published: 2025-01-15
created: 2025-03-28
description: "We’re on a journey to advance and democratize artificial intelligence through open source and open science."
tags:
  - "clippings"
---
今天我们发布 `smolagents` ，一个非常简单的库，它为语言模型解锁了智能体能力。先睹为快：


```
from smolagents import CodeAgent, DuckDuckGoSearchTool, HfApiModel

agent = CodeAgent(tools=[DuckDuckGoSearchTool()], model=HfApiModel())

agent.run("How many seconds would it take for a leopard at full speed to run through Pont des Arts?")
```

## 🤔 What are agents?

  
任何利用 AI 的高效系统都需要为LLMs提供某种接触现实世界的途径：例如调用搜索工具获取外部信息的能力，或操作特定程序以完成任务。换言之，LLMs应具备代理能力。代理程序是LLMs通往外部世界的门户。

  
下表展示了不同系统中代理能力的差异：

| Agency Level | Description                              | How that's called | Example Pattern                                    |
| ------------ | ---------------------------------------- | ----------------- | -------------------------------------------------- |
| ☆☆☆          | LLM output has no impact on program flow | Simple processor  | `process_llm_output(llm_response)`                 |
| ★☆☆          | LLM output determines basic control flow | Router            | `if llm_decision(): path_a() else: path_b()`       |
| ★★☆          | LLM output determines function execution | Tool call         | `run_function(llm_chosen_tool, llm_chosen_args)`   |
| ★★★          | LLM 输出控制迭代与程序继续执行                        | Multi-step Agent  | `while llm_should_continue(): execute_next_step()` |
| ★★★          | 一个代理工作流可以启动另一个代理工作流                      | Multi-Agent       | `if llm_trigger(): execute_agent()`                |

The multi-step agent has this code structure:

```
memory = [user_defined_task]
while llm_should_continue(memory): # this loop is the multi-step part
    action = llm_get_next_action(memory) # this is the tool-calling part
    observations = execute_action(action)
    memory += [action, observations]
```

##   
✅ 何时使用代理 / ⛔ 何时避免使用

  
如果确定性工作流适用于所有查询，务必直接编写所有代码！这将为您提供一个 100%可靠的系统，无需担心不可预测的LLMs干扰工作流而引入错误风险。为了简单和稳健起见，建议规范化处理，避免使用任何代理行为。

如果预定的工作流程经常达不到要求，那就意味着你需要更大的灵活性。

## Code agents

  
在多步骤代理中，每一步LLM都可以通过调用外部工具的形式编写一个动作。常见的动作编写格式（被 Anthropic、OpenAI 等众多机构采用）通常表现为"将动作写成包含工具名称及调用参数的 JSON 对象，随后解析该对象以确定执行哪个工具及对应参数"的不同变体。

多篇研究论文表明，在代码中使用工具调用LLMs效果更佳。

下图摘自《可执行代码行动引发更好的LLM代理》，展示了用代码编写行动的一些优势：

![](https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/transformers/code_vs_json_actions.png)

##   
介绍 smolagents：让代理变得简单 🥳

### Building an agent

  
要构建一个代理，至少需要两个要素：

- `tools`: a list of tools the agent has access to
- `model` : 一个将成为您代理引擎的LLM。

  
对于 `model` ，您可以使用任何LLM，无论是使用我们的 `HfApiModel` 类（利用 Hugging Face 的免费推理 API，如上文豹子示例所示）的开放模型，还是使用 `LiteLLMModel` 借助 litellm 并从 100 多种不同的云LLMs中进行选择。

  
对于该工具，只需创建一个带有输入输出类型提示的函数，并通过文档字符串描述输入参数，然后使用 `@tool` 装饰器将其转换为工具即可。

  
以下是创建自定义工具以从 Google Maps 获取旅行时间，并将其用于旅行规划代理的方法：

```
from typing import Optional
from smolagents import CodeAgent, HfApiModel, tool

@tool
def get_travel_duration(start_location: str, destination_location: str, transportation_mode: Optional[str] = None) -> str:
    """Gets the travel time between two places.

    Args:
        start_location: the place from which you start your ride
        destination_location: the place of arrival
        transportation_mode: The transportation mode, in 'driving', 'walking', 'bicycling', or 'transit'. Defaults to 'driving'.
    """
    import os   # All imports are placed within the function, to allow for sharing to Hub.
    import googlemaps
    from datetime import datetime

    gmaps = googlemaps.Client(os.getenv("GMAPS_API_KEY"))

    if transportation_mode is None:
        transportation_mode = "driving"
    try:
        directions_result = gmaps.directions(
            start_location,
            destination_location,
            mode=transportation_mode,
            departure_time=datetime(2025, 6, 6, 11, 0), # At 11, date far in the future
        )
        if len(directions_result) == 0:
            return "No way found between these places with the required transportation mode."
        return directions_result[0]["legs"][0]["duration"]["text"]
    except Exception as e:
        print(e)
        return e

agent = CodeAgent(tools=[get_travel_duration], model=HfApiModel(), additional_authorized_imports=["datetime"])

agent.run("Can you give me a nice one-day trip around Paris with a few locations and the times? Could be in the city or outside, but should fit in one day. I'm travelling only with a rented bicycle.")
```