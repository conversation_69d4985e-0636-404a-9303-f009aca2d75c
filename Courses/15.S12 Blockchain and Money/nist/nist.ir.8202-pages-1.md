## **NISTIR 8202**

# **Blockchain Technology Overview**

#### **Abstract**

Blockchains are tamper evident and tamper resistant digital ledgers implemented in a distributed fashion (i.e., without a central repository) and usually without a central authority (i.e., a bank, company, or government). At their basic level, they enable a community of users to record transactions in a shared ledger within that community, such that under normal operation of the blockchain network no transaction can be changed once published. This document provides a high-level technical overview of blockchain technology. The purpose is to help readers understand how blockchain technology works.

#### **Keywords**

blockchain; consensus model; cryptocurrency; cryptographic hash function; asymmetric-key cryptography; distributed ledger; distributed consensus algorithm; proof of work; proof of stake; round robin; proof of authority; proof of identity; proof of elapsed time; soft fork, hard fork; smart contracts; data oracle.

#### **Acknowledgments**

The authors wish to thank all contributors to this publication, and their colleagues who reviewed drafts of this report and contributed technical and editorial additions. This includes NIST staff <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON>.

Additional thanks to all the people and organizations who submitted comments during the public comment period.

### **Audience**

This publication is designed for readers with little or no knowledge of blockchain technology who wish to understand at a high level how it works. It is not intended to be a technical guide; the discussion of the technology provides a conceptual understanding. Note that some examples, figures, and tables are simplified to fit the audience.

### **Trademark Information**

All registered trademarks and trademarks belong to their respective organizations.

#### <span id="page-5-0"/>**Executive Summary**

Blockchains are tamper evident and tamper resistant digital ledgers implemented in a distributed fashion (i.e., without a central repository) and usually without a central authority (i.e., a bank, company, or government). At their basic level, they enable a community of users to record transactions in a shared ledger within that community, such that under normal operation of the blockchain network no transaction can be changed once published. In 2008, the blockchain idea was combined with several other technologies and computing concepts to create modern cryptocurrencies: electronic cash protected through cryptographic mechanisms instead of a central repository or authority. The first such blockchain based cryptocurrency was Bitcoin.

Within the Bitcoin blockchain, information representing electronic cash is attached to a digital address. Bitcoin users can digitally sign and transfer rights to that information to another user and the Bitcoin blockchain records this transfer publicly, allowing all participants of the network to independently verify the validity of the transactions. The Bitcoin blockchain is stored, maintained, and collaboratively managed by a distributed group of participants. This, along with certain cryptographic mechanisms, makes the blockchain resilient to attempts to alter the ledger later (modifying blocks or forging transactions).

Because there are countless news articles and videos describing the "magic" of blockchain technology, this paper aims to describe the method behind the magic (i.e., how blockchain technology works). Arthur C. Clarke once wrote, "Any sufficiently advanced technology is indistinguishable from magic" [\[1\].](#page--1-0) Clarke's statement is a perfect representation for the emerging applications of blockchain technology. There is hype around the use of blockchain technology, yet the technology is not well understood. It is not magical; it will not solve all problems. As with all new technology, there is a tendency to want to apply it to every sector in every way imaginable. To help promote correct application, this document provides information necessary to develop a high-level understanding of the technology.

Blockchain technology is the foundation of modern cryptocurrencies, so named because of the heavy usage of cryptographic functions. Users utilize public and private keys to digitally sign and securely transact within the system. For cryptocurrency based blockchain networks which utilize mining (see section [4.1)](#page--1-1), users may solve puzzles using cryptographic hash functions in hopes of being rewarded with a fixed amount of the cryptocurrency. However, blockchain technology may be more broadly applicable than cryptocurrencies. In this work, we focus on the cryptocurrency use case, since that is the primary use of the technology today; however, there is a growing interest in other sectors.

Organizations considering implementing blockchain technology need to understand fundamental aspects of the technology. For example, what happens when an organization implements a blockchain network and then decides they need to make modifications to the data stored? When using a database, modifying the actual data can be accomplished through a database query and update. Organizations must understand that while changes to the actual blockchain data may be difficult, applications using the blockchain as a data layer work around this by treating later blocks and transactions as updates or modifications to earlier blocks and transactions. This software abstraction allows for modifications to working data, while providing a full history of

changes. Another critical aspect of blockchain technology is how the participants agree that a transaction is valid. This is called "reaching consensus", and there are many models for doing so, each with positives and negatives for particular business cases. It is important to understand that a blockchain is just one part of a solution.

Blockchain implementations are often designed with a specific purpose or function. Example functions include cryptocurrencies, smart contracts (software deployed on the blockchain and executed by computers running that blockchain), and distributed ledger systems between businesses. There has been a constant stream of developments in the field of blockchain technology, with new platforms being announced constantly – the landscape is continuously changing.

There are two general high-level categories for blockchain approaches that have been identified: permissionless, and permissioned. In a permissionless blockchain network anyone can read and write to the blockchain without authorization. Permissioned blockchain networks limit participation to specific people or organizations and allow finer-grained controls. Knowing the differences between these two categories allows an organization to understand which subset of blockchain technologies may be applicable to its needs.

Despite the many variations of blockchain networks and the rapid development of new blockchain related technologies, most blockchain networks use common core concepts. Blockchains are a distributed ledger comprised of blocks. Each block is comprised of a block header containing metadata about the block, and block data containing a set of transactions and other related data. Every block header (except for the very first block of the blockchain) contains a cryptographic link to the previous block's header. Each transaction involves one or more blockchain network users and a recording of what happened, and it is digitally signed by the user who submitted the transaction.

Blockchain technology takes existing, proven concepts and merges them together into a single solution. This document explores the fundamentals of how these technologies work and the differences between blockchain approaches. This includes how the participants in the network come to agree on whether a transaction is valid and what happens when changes need to be made to an existing blockchain deployment. Additionally, this document explores when to consider using a blockchain network.

The use of blockchain technology is not a silver bullet, and there are issues that must be considered such as how to deal with malicious users, how controls are applied, and the limitations of the implementations. Beyond the technology issues that need to be considered, there are operational and governance issues that affect the behavior of the network. For example, in permissioned blockchain networks, described later in this document, there are design issues surrounding what entity or entities will operate and govern the network for the intended user base.

Blockchain technology is still new and should be investigated with the mindset of "how could blockchain technology potentially benefit us?" rather than "how can we make our problem fit into the blockchain technology paradigm?". Organizations should treat blockchain technology like they would any other technological solution at their disposal and use it in appropriate situations.

|   |     | Executive Summaryiv                                  |    |
|---|-----|------------------------------------------------------|----|
| 1 |     | Introduction<br>1                                    |    |
|   | 1.1 | Background and History                               | 2  |
|   | 1.2 | Purpose and Scope<br>                                | 3  |
|   | 1.3 | Notes on Terms<br>                                   | 3  |
|   | 1.4 | Results of the Public Comment Period<br>             | 4  |
|   | 1.5 | Document Structure                                   | 4  |
| 2 |     | Blockchain Categorization5                           |    |
|   | 2.1 | Permissionless                                       | 5  |
|   | 2.2 | Permissioned<br>                                     | 5  |
| 3 |     | Blockchain Components<br>7                           |    |
|   | 3.1 | Cryptographic Hash Functions                         | 7  |
|   |     | 3.1.1<br>Cryptographic Nonce                         | 9  |
|   | 3.2 | Transactions<br>                                     | 9  |
|   | 3.3 | Asymmetric-Key Cryptography<br>                      | 11 |
|   | 3.4 | Addresses and Address Derivation                     | 12 |
|   |     | 3.4.1<br>Private Key Storage                         | 13 |
|   | 3.5 | Ledgers                                              | 13 |
|   | 3.6 | Blocks<br>                                           | 15 |
|   | 3.7 | Chaining Blocks<br>                                  | 17 |
| 4 |     | Consensus Models<br>18                               |    |
|   | 4.1 | Proof of Work Consensus Model<br>                    | 19 |
|   | 4.2 | Proof of Stake Consensus Model<br>                   | 21 |
|   | 4.3 | Round Robin Consensus Model<br>                      | 23 |
|   | 4.4 | Proof of Authority/Proof of Identity Consensus Model | 23 |
|   | 4.5 | Proof of Elapsed Time Consensus Model                | 23 |
|   | 4.6 | Consensus Comparison Matrix                          | 25 |
|   | 4.7 | Ledger Conflicts and Resolutions<br>                 | 27 |
| 5 |     | Forking29                                            |    |
|   | 5.1 | Soft Forks<br>                                       | 29 |
|   | 5.2 | Hard Forks<br>                                       | 29 |

|   | 5.3  | Cryptographic Changes and Forks<br>             | 30 |
|---|------|-------------------------------------------------|----|
| 6 |      | Smart Contracts<br>32                           |    |
| 7 |      | Blockchain Limitations and Misconceptions<br>34 |    |
|   | 7.1  | Immutability                                    | 34 |
|   | 7.2  | Users Involved in Blockchain Governance         | 35 |
|   | 7.3  | Beyond the Digital                              | 36 |
|   | 7.4  | Blockchain Death                                | 36 |
|   | 7.5  | Cybersecurity                                   | 36 |
|   |      | 7.5.1<br>Cyber and Network-based Attacks<br>    | 37 |
|   | 7.6  | Malicious Users                                 | 37 |
|   | 7.7  | No Trust<br>                                    | 38 |
|   | 7.8  | Resource Usage<br>                              | 38 |
|   | 7.9  | Inadequate Block Publishing Rewards             | 39 |
|   | 7.10 | Public Key Infrastructure and Identity<br>      | 39 |
| 8 |      | Application Considerations<br>41                |    |
|   | 8.1  | Additional Blockchain Considerations            | 44 |
| 9 |      | Conclusions46                                   |    |

| Appendix A—<br>Acronyms47          |  |
|------------------------------------|--|
| Appendix B—<br>Glossary<br>49      |  |
| Appendix<br>C—<br>References<br>55 |  |