- 将匹配的路径视为一个表——table(a, e, b, e2, c)，或者将它们的属性展开到表中（a.attr1, a.attr2..., e.attr1, e.attr2...,b.attr1, b.attr2...）。你可以像在 SQL 中一样对其列进行分组和聚合
```sql
USE GRAPH financialGraph

// create a query
CREATE OR REPLACE QUERY q3b (datetime low, datetime high, string acctName) SYNTAX v3 {

   // a path pattern in ascii art () -[]->()-[]->()
   // think the FROM clause is a matched table with columns (a, e, b, e2, c)
   // you can use SQL syntax to group by on the matched table
   // Below query find 2-hop reachable account c from a, and group by the path a, b, c
   // find out how much each hop's total transfer amount.
   SELECT a, b, c, sum(DISTINCT e.amount) AS hop_1_sum,  sum(DISTINCT e2.amount) AS hop_2_sum INTO T1
   FROM (a:Account)-[e:transfer]->(b)-[e2:transfer]->(c:Account)
   WHERE e.date >= low AND e.date <= high
   GROUP BY a, b, c;

   PRINT T1;

   /* below we use variable length path.
      *1.. means 1 to more steps of the edge type "transfer"
      select the reachable end point and bind it to vertex alias "b"
     note: 
      1. the path has "shortest path" semantics. If you have a path that is longer than the shortest,
      we only count the shortest. E.g., scott to scott shortest path length is 4. Any path greater than 4 will
      not be matched.
     1. we can not put an alias to bind the edge in the variable length part -[:transfer*1..]->, but 
     we can bind the end points (a) and (b) in the variable length path, and group by on them.
   */
   SELECT a, b, count(*) AS path_cnt INTO T2
   FROM (a:Account {name: acctName})-[:transfer*1..]->(b:Account)
   GROUP BY a, b;

   PRINT T2;

}

# Two methods to run the query. The compiled method gives the best performance.

# Method 1: Run immediately with our interpret engine
interpret query q3b("2024-01-01", "2024-12-31", "Scott")

# Method 2: Compile and install the query as a stored procedure
install query q3b

run query q3b("2024-01-01", "2024-12-31", "Scott")
```
## Accum by `s.name`
```sql
use graph financialGraph

CREATE OR REPLACE QUERY selectExample2() SYNTAX v3 {
  SELECT s.name AS acct, SUM(e.amount) AS totalAmt INTO T1
  FROM (s:Account)- [e:transfer]-> (t:Account)
  WHERE not s.isBlocked
  HAVING totalAmt > 1000
  ORDER BY totalAmt DESC
  LIMIT 5 OFFSET 0
  ;

  PRINT T1;
}

install query selectExample2
run query selectExample2()
```
