- if it's prod, "new access request" in https://tigergraph.teleport.sh/web/requests
	- assume role once the request was approved
- search the cloud-provider id(the second part of workspace url) in https://tigergraph.teleport.sh/web/cluster/tigergraph.teleport.sh/resources
- click connect to get info 
	- ![[Pasted image 20250616160231.png|400]]
- run `tsh login`
- get pods: `kubectl get pods -n workgroupId`
- enter pod: `kubectl exec -it tg-1f773d11-91da-4941-be6d-0406f030c181-0 -n 181be91b-c084-42e3-907f-6b6af022759f -- bash`