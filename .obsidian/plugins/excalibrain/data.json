{"compactView": false, "compactingFactor": 1.5, "minLinkLength": 35, "excalibrainFilepath": "excalibrain.md", "indexUpdateInterval": 5000, "hierarchy": {"exclusions": ["excalidraw-border-color", "excalidraw-css", "excalidraw-default-mode", "excalidraw-export-dark", "excalidraw-export-pngscale", "excalidraw-export-svgpadding", "excalidraw-export-transparent", "excalidraw-font", "excalidraw-font-color", "excalidraw-link-brackets", "excalidraw-link-prefix", "excalidraw-linkbutton-opacity", "excalidraw-onload-script", "excalidraw-plugin", "excalidraw-url-prefix", "kanban-plugin"], "parents": ["inception", "North", "origin", "Parent", "parent domain", "Parents", "source", "u", "up"], "children": ["Child", "Children", "contributes to", "d", "down", "leads to", "nurtures", "South"], "leftFriends": ["advantages", "alternatives", "Friend", "Friends", "j", "Jump", "Jumps", "pros", "similar", "supports"], "rightFriends": ["cons", "disadvantages", "missing", "opposes"], "previous": ["Before", "Prev", "Previous", "w", "West"], "next": ["After", "e", "East", "n", "Next"], "hidden": ["hidden"]}, "inferAllLinksAsFriends": false, "inverseInfer": false, "inverseArrowDirection": true, "renderAlias": true, "nodeTitleScript": "", "backgroundColor": "#0c3e6aff", "excludeFilepaths": ["Weekly/"], "autoOpenCentralDocument": true, "toggleEmbedTogglesAutoOpen": true, "showInferredNodes": true, "showAttachments": false, "showURLNodes": false, "showVirtualNodes": true, "showFolderNodes": false, "showTagNodes": false, "showPageNodes": true, "showNeighborCount": true, "showFullTagName": false, "maxItemCount": 150, "renderSiblings": true, "applyPowerFilter": false, "baseNodeStyle": {"prefix": "", "backgroundColor": "#00000066", "fillStyle": "solid", "textColor": "#ffffffff", "borderColor": "#00000000", "fontSize": 20, "fontFamily": 3, "maxLabelLength": 30, "roughness": 0, "strokeShaprness": "round", "strokeWidth": 1, "strokeStyle": "solid", "padding": 10, "gateRadius": 5, "gateOffset": 15, "gateStrokeColor": "#ffffffff", "gateBackgroundColor": "#ffffffff", "gateFillStyle": "solid"}, "centralNodeStyle": {"fontSize": 30, "backgroundColor": "#B5B5B5", "textColor": "#000000ff"}, "inferredNodeStyle": {"backgroundColor": "#000005b3", "textColor": "#95c7f3ff"}, "urlNodeStyle": {"prefix": "🌐 "}, "virtualNodeStyle": {"backgroundColor": "#ff000066", "fillStyle": "hachure", "textColor": "#ffffffff"}, "siblingNodeStyle": {"fontSize": 15}, "attachmentNodeStyle": {"prefix": "📎 "}, "folderNodeStyle": {"prefix": "📂 ", "strokeShaprness": "sharp", "borderColor": "#ffd700ff", "textColor": "#ffd700ff"}, "tagNodeStyle": {"prefix": "#", "strokeShaprness": "sharp", "borderColor": "#4682b4ff", "textColor": "#4682b4ff"}, "tagNodeStyles": {}, "tagStyleList": [], "primaryTagField": "Note type", "primaryTagFieldLowerCase": "note-type", "displayAllStylePrefixes": true, "baseLinkStyle": {"strokeColor": "#696969FF", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 0, "startArrowHead": "none", "endArrowHead": "none", "showLabel": false, "fontSize": 10, "fontFamily": 3, "textColor": "#ffffffff"}, "inferredLinkStyle": {"strokeStyle": "dashed"}, "folderLinkStyle": {"strokeColor": "#ffd700ff"}, "tagLinkStyle": {"strokeColor": "#4682b4ff"}, "hierarchyLinkStyles": {"hidden": {}, "inception": {}, "North": {}, "origin": {}, "Parent": {}, "parent domain": {}, "Parents": {}, "source": {}, "u": {}, "up": {}, "Child": {}, "Children": {}, "contributes to": {}, "d": {}, "down": {}, "leads to": {}, "nurtures": {}, "South": {}, "advantages": {}, "alternatives": {}, "Friend": {}, "Friends": {}, "j": {}, "Jump": {}, "Jumps": {}, "pros": {}, "similar": {}, "supports": {}, "cons": {}, "disadvantages": {}, "missing": {}, "opposes": {}, "Before": {}, "Prev": {}, "Previous": {}, "w": {}, "West": {}, "After": {}, "e": {}, "East": {}, "n": {}, "Next": {}}, "navigationHistory": ["什么是 RAG.md", "在 LlamaIndex 中使用工具.md", "tmp.md", "GSQL editor file.md", "TG/GSQL Queries.md", "初始执行阶段提供图像.md", "浏览器自动化工具.md", "LlamaIndex QueryEngine.md", "创建多智能体系统.md", "Clippings/在 LlamaIndex 中创建智能工作流 - Hugging Face Agents Course.md", "Clippings/AI agent course/在 LlamaIndex 中创建智能工作流 - Hugging Face Agents Course.md", "在 LlamaIndex 中创建智能工作流 - Hugging Face Agents Course.md", "Clippings/AI agent course/在 LlamaIndex 中创建智能工作流 - Hugging Face Agents Course 1.md", "Clippings/AI agent course/构建你的第一个 LangGraph.md", "Clippings/AI agent course/文档分析图.md", "Clippings/AI agent course/LangGraph资源.md", "Clippings/AI agent course/智能体增强检索生成（Agentic RAG）.md", "Clippings/AI agent course/创建宾客信息检索生成（RAG）工具.md", "Clippings/AI agent course/什么是函数调用？(What is Function Calling?).md", "Clippings/AI agent course/Fine-Tune your model for function-calling.md", "Clippings/LlamaHub 简介 - Hugging Face Agents Course 2.md", "Clippings/LlamaHub 简介 - Hugging Face Agents Course 1.md", "Clippings/LlamaHub 简介 - Hugging Face Agents Course.md", "Clippings/Introduction to Identity and Access Management (IAM).md", "Clippings/Introduction to Identity and Access Management (IAM) 1.md", "Weekly/2025 Week3.md", "Clippings/AI agent course/AI Agents Roadmap.md", "TG/cloud universe.md", "事业单位考试和公务员考试的区别.md", "Notes/事业单位考试和公务员考试的区别.md", "备忘/Insurance/太平洋e享护20年.md", "提供屏幕截图.md", "使用 smolagents 构建视觉智能体.md", "Tech/AI/AI agents/smolagents.md", "三种主要类型的推理智能体.md", "Pasted image 20250520010622.png", "RAG五个关键阶段.md", "RAG.md", "LlamaIndex数据加载与嵌入.md", "LlamaIndex.md", "How CodeAgent run.md", "Untitled.md", "Tech/AI/AI agents/How CodeAgent run.md", "TG/Query Parameters.md", "Notes/Depression App.md", "Weekly/2025 Week4.md", "Notes/kaobian.md", "Tech/AI/AI agents/AI agent.md", "Tech/AI/AI agents/Multi-Agent Systems.md", "home.md"], "allowOntologySuggester": true, "ontologySuggesterParentTrigger": "::p", "ontologySuggesterChildTrigger": "::c", "ontologySuggesterLeftFriendTrigger": "::l", "ontologySuggesterRightFriendTrigger": "::r", "ontologySuggesterPreviousTrigger": "::e", "ontologySuggesterNextTrigger": "::n", "ontologySuggesterTrigger": ":::", "ontologySuggesterMidSentenceTrigger": "(", "boldFields": false, "allowAutozoom": true, "maxZoom": 1, "allowAutofocuOnSearch": true, "defaultAlwaysOnTop": false, "embedCentralNode": false, "centerEmbedWidth": 550, "centerEmbedHeight": 700}