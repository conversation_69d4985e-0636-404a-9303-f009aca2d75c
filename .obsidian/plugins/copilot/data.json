{"plusLicenseKey": "", "openAIApiKey": "", "openAIOrgId": "", "huggingfaceApiKey": "", "cohereApiKey": "", "anthropicApiKey": "", "azureOpenAIApiKey": "", "azureOpenAIApiInstanceName": "", "azureOpenAIApiDeploymentName": "", "azureOpenAIApiVersion": "", "azureOpenAIApiEmbeddingDeploymentName": "", "googleApiKey": "", "openRouterAiApiKey": "", "defaultChainType": "llm_chain", "defaultModelKey": "deepseek-chat|openai", "embeddingModelKey": "copilot-plus-small|copilot-plus", "temperature": 0.25, "maxTokens": 8000, "contextTurns": 15, "userSystemPrompt": "", "openAIProxyBaseUrl": "", "openAIEmbeddingProxyBaseUrl": "", "stream": true, "defaultSaveFolder": "copilot-conversations", "defaultConversationTag": "copilot-conversation", "autosaveChat": false, "defaultOpenArea": "editor", "customPromptsFolder": "copilot-custom-prompts", "indexVaultToVectorStore": "ON MODE SWITCH", "qaExclusions": "", "qaInclusions": "", "chatNoteContextPath": "", "chatNoteContextTags": [], "enableIndexSync": true, "debug": true, "enableEncryption": false, "maxSourceChunks": 3, "groqApiKey": "", "activeModels": [{"name": "gpt-4o", "provider": "openai", "enabled": false, "isBuiltIn": true, "core": true}, {"name": "gpt-4o-mini", "provider": "openai", "enabled": true, "isBuiltIn": true, "core": true}, {"name": "claude-3-5-sonnet-latest", "provider": "anthropic", "enabled": false, "isBuiltIn": true, "core": true}, {"name": "gpt-4-turbo", "provider": "openai", "enabled": false, "isBuiltIn": true}, {"name": "claude-3-5-haiku-latest", "provider": "anthropic", "enabled": false, "isBuiltIn": true}, {"name": "command-r", "provider": "cohereai", "enabled": false, "isBuiltIn": true}, {"name": "command-r-plus", "provider": "cohereai", "enabled": false, "isBuiltIn": true}, {"name": "gemini-1.5-pro", "provider": "google", "enabled": false, "isBuiltIn": true}, {"name": "gemini-1.5-flash", "provider": "google", "enabled": true, "isBuiltIn": true}, {"name": "azure-openai", "provider": "azure openai", "enabled": false, "isBuiltIn": true}, {"name": "deepseek-chat", "provider": "openai", "enabled": true, "isBuiltIn": false, "baseUrl": "https://api.deepseek.com/v1", "apiKey": "***********************************", "isEmbeddingModel": false, "temperature": 0.1, "context": 1000, "stream": true, "enableCors": false}], "activeEmbeddingModels": [{"name": "copilot-plus-small", "provider": "copilot-plus", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true, "core": true}, {"name": "copilot-plus-multilingual", "provider": "copilot-plus-jina", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true, "core": true}, {"name": "text-embedding-3-small", "provider": "openai", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true, "core": true}, {"name": "text-embedding-3-large", "provider": "openai", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true}, {"name": "embed-multilingual-light-v3.0", "provider": "cohereai", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true}, {"name": "text-embedding-004", "provider": "google", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true}, {"name": "azure-openai", "provider": "azure openai", "enabled": true, "isBuiltIn": true, "isEmbeddingModel": true}], "embeddingRequestsPerSecond": 10, "disableIndexOnMobile": true, "showSuggestedPrompts": true, "showRelevantNotes": true, "numPartitions": 1, "enabledCommands": {}, "promptUsageTimestamps": {}, "defaultConversationNoteName": "{$topic}@{$date}_{$time}"}