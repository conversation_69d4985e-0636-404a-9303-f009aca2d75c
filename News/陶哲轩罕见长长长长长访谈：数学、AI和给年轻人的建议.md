---
tags:
  - news
status: 📍 Todo
---
## Source
[https://mp.weixin.qq.com/s/W\_rtX\_YX4P\_9NdDQ6uZYSg](https://mp.weixin.qq.com/s/W_rtX_YX4P_9NdDQ6uZYSg)

## Summary
**对年轻人的建议**：需要适应和灵活，获得可转移的技能。虽然学习特定的编程语言或数学学科本身不完全可迁移，但可以从中学会如何用抽象概念进行推理，以及在出现问题时如何解决问题。即使在AI支持下工作，这些技能仍然是必需的。

**结构化拖延法**：当你不想做一件事时，去想象一件比这更糟糕的事情，然后以不做更糟糕的事情来拖延。心理学很重要，只要自我欺骗相信问题是可行的，就会有动力去做它。

**工作方法**：主要使用纸和笔，办公室有四块巨大的黑板，把所有关于问题的信息都写在上面。现在也越来越多地使用计算机，因为AI能执行简单的编码工作，让探索变得更容易。

**解决问题的策略**：就像通关电脑游戏，可以使用"作弊码"。如果有10件事让生活困难，可以先关闭其中9个困难，专注解决1个问题。学会分别解决这10个问题后，再将其中几个合并处理。不应该在"铁人模式"下让事情最大化困难。

## 想法

## Tags