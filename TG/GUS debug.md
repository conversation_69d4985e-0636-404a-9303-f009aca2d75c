## Launch.json
```json
{ // Use IntelliSense to learn about possible attributes. // Hover to view descriptions of existing attributes. // For more information, visit: [https://go.microsoft.com/fwlink/?linkid=830387](https://go.microsoft.com/fwlink/?linkid=830387) "version": "0.2.0", "configurations": [ { "name": "gus", "type": "go", "request": "launch", "mode": "debug", "program": "main.go", "args": [ "-c", "/home/<USER>/tigergraph/data/configs/tg.cfg", "-r", "1" ] } ] }
```