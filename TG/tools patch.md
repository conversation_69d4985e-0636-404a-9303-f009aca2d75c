---
up: "[[TG projects]]"
---
## Notes
- [patch_template.sh](https://github.com/tigergraph/tmd/blob/master/mit/jenkins_script/shell_script/patch_template.sh)
- [generate_patch.sh](https://github.com/tigergraph/tmd/blob/master/mit/jenkins_script/shell_script/generate_patch.sh)
	- input: PATCH_ROOT, PATCH_NAME
		- PATCH_PATTERN: patterns list
		- $BINARY_META_FILE: all binary paths that can be patched
	- process
		- 使用pattern list在meta files中查找出需要用到的binary
		- 替换template中的patch number
		* 对于每个binary
			* 将file复制进patch folder，维持原来的path
			* `gen_lines "$binary_file`
				* 生成backup指令
				   `grun_p all \"$op \$TG_APP_ROOT/$bin \$TG_APP_ROOT/${bin}.bkup.${patch_postfix}`
				* 生成替换指令
				  `gscp all ./$bin \$(dirname \$TG_APP_ROOT/$bin)`
			*  gen_revert_lines "$binary_file
- what need to do for tools patch?
	- add all tool paths to binary meta file
	- backup folder
	- replace folder
- `wip -patch_prebuild <customer_name>_<zendesk_id>:tg_app_guid gus#906 -b tg_3.10.1_dev -bb `
## Command
```
wip -patch_dev jpmc_54854:tg_app_guid,gst gus#1057 tools#2419 -bb tools-patch
```