---
up: "[[GSQL autocompletions]]"
---
- how to get alias
	- split from clause with '-' to get each step
	- split step with ':'
	- use regex to match types from the left part, match alias from the right part
## Tasks
- [x] #task update, delete statement 📅 2024-09-04
- [x] #task type inference for alias 📅 2024-09-05
- [x] Add auto-completion of vertex/edge types
- [x] Add auto-completion of attributes
## Select Snippet
- Multiple edge or types with one alias
```sql
CREATE QUERY multiple_edge_type_where_ex(VERTEX<Person> m1) FOR GRAPH Social_Net {
    all_user = {m1};
    filtered_user = SELECT s
        FROM all_user:s - ((Posted|Liked|Friend):e) - (Post|Person):t
        // WHERE e.action_time > epoch_to_datetime(1) AND t.gender == "Male";
        WHERE ( e.type == "Liked" AND e.action_time > epoch_to_datetime(1) ) OR
          ( e.type == "Friend" AND t.gender == "Male" );
    PRINT filtered_user;
}
```
-  accumulators
```sql
CREATE QUERY accum_wrong_example() SYNTAX v2 {
    SumAccum<INT> @@count_total;
    SumAccum<INT> @active_flag = 0;

    result = SELECT p
             FROM Person: p - (KNOWS) - Person: w
             WHERE w.lastName == "Wang" AND p.firstName == "Peter"
             ACCUM p.@active_flag += 1,
                   @@count_total += p.@active_flag;
    PRINT @@count_total, result[result.@active_flag];
}
```
- vertex/edge type is omitted
```sql
INTERPRET QUERY () {

    SumAccum<INT> @cnt1;
    SumAccum<INT> @cnt2;
    SumAccum<INT> @@global_t_count;

    R   =  SELECT s
        FROM Person:s-(Likes>) -:msg - (Has_Creator>)-Person:t
        WHERE s.first_name == "Viktor" AND s.last_name == "Akhiezer"
          AND t.last_name LIKE "S%" AND year(msg.creation_date) == 2012
        ACCUM s.@cnt1 +=1 //execute this per match of the FROM pattern.
        POST-ACCUM s.@cnt2 += s.@cnt1
        POST-ACCUM t.@cnt2 +=1
        POST-ACCUM(t) @@global_t_count += 1;

        PRINT R [R.first_name, R.last_name, R.@cnt1, R.@cnt2];
}
```
## Source of auto-completion
- keyword: control keyword, types...
- vertex/edge types
- vertex/edge attributes
- accumulator names
- function
	- accumulator's function
	- vertex/edge's function
	- global function
- parameters, variables
## Workflow
- 