## Tasks
- [x] #task Schema list ⏳ 2025-03-19 ✅ 2025-03-20
- [x] #task patch issue ⏳ 2025-03-19 ✅ 2025-03-20
- [x] #task Query functionalities ⏳ 2025-04-02 ✅ 2025-07-07
	- [x] #task query item: status icon, name ⏳ 2025-03-20 ✅ 2025-04-01
	- [x] #task query detail: query type, name, content, status ⏳ 2025-03-20 ✅ 2025-04-01
	- [x] #task query actions: edit(in new file) ⏳ 2025-03-20 ✅ 2025-04-01
	- [x] #task query action: install ⏳ 2025-04-02 ✅ 2025-04-02
	- [x] #task query action: drop ⏳ 2025-04-02 ✅ 2025-04-02
	- [x] #task query rename ⏳ 2025-04-03 ✅ 2025-04-03
	- [x] #task add query ⏳ 2025-04-03 ✅ 2025-04-03
## Notes
- icon types:
	- cypher: installed/uninstalled
	- gsql: installed/uninstalled
- status: draft, installed
## Feature
- Graph
	- schema
		- vertex/edges
	- queries
		- edit/create/install/drop/rename
		- info
		- instal all queries
- files
- algorithm library
	- info
	- install/edit
- Run query/api
- bulit-in endpoints
## Components
- context
	- isRunPanelOpen
	- runPanelInfo
- `<GraphStore graphName />`
	- state
		- isCollapsed
		- searchText
	- fetch schema
	- fetch queries
	-  `<SchemaList graphName schema />`
		- isCollapsed
	- `<QueryList graphName queries />`
		- isCollapsed
		- addQuery
		- installAll
		- `<QueryItem query />`
			- `<QueryDetail query />`
				- run 
				- editInNewFile
				- editInCurrentFile
				- install
				- drop
				- `<QueryContentPreview content />`
- `<RunPanel />`
	- get runPanelInfo from context
	- runPanelInfo: queryInfo or apiInfo
	## Winsurf prompt
	implement the design in the pictures(ignore the built-in queries part). You will need to finish 3 react components: GraphStore, SchemaList, QueryList. I describe their props, and tasks in the following:
	- GraphStoree
		- props: graphName, searchText
		- tasks
			- fetch the graph schema and query list when users expand it the first time
			- filter schema items and query list based on the search text
	- SchemaList
		- props: schema(type: `Schema`)
		- tasks
			- display the schema items
	- QueryList
		- props: queryList(type: `QueryMeta[]`)
		- tasks
			- display the query list: name, install status, type for each query
			- a popover that contains the query detail will display if users clicked the query item

Pick proper icons from `react-icons` library to replace icons in the pictures.
Implement the tree view by yourself or use a third-party library.