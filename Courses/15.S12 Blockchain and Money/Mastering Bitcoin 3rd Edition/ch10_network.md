---
title: "bitcoinbook/ch11_blockchain.adoc at develop · bitcoinbook/bitcoinbook"
source: "https://github.com/bitcoinbook/bitcoinbook/blob/develop/ch10_network.adoc"
author:
  - "[[GitHub]]"
published:
created: 2025-02-15
description: "Mastering Bitcoin 3rd Edition - Programming the Open Blockchain - bitcoinbook/ch11_blockchain.adoc at develop · bitcoinbook/bitcoinbook"
tags:
  - "clippings"
---
![mbc3 1101](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_1101.png)

### Merkle Trees 默克尔树

当 N 个数据元素在默克尔树中进行哈希和汇总时，你可以通过大约 log₂(N) 次计算来检查任何一个数据元素是否包含在树中，这使得它成为一种非常高效的数据结构。

![merkle_tree](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_1102.png)

由于默克尔树是一棵二叉树，所以它需要偶数个叶节点。如果要汇总的交易数量为奇数，则将最后一个交易哈希重复，以创建偶数个叶节点，这也被称为“平衡树”。

为了证明特定交易被包含在一个区块中，一个节点只需要生成大约 log₂(N)个 32 字节的哈希，构成一个“认证路径”或“默克尔路径”，将特定交易连接到树的根。

节点可以通过生成仅四个 32 字节哈希长度（总共 128 个字节）的默克尔路径来证明交易 K 包含在区块中。该路径由四个哈希（以阴影背景显示）HL、HIJ、HMNOP 和 HABCDEFGH 组成。有了这四个哈希作为认证路径，任何节点都可以通过计算另外四个成对哈希 HKL、HIJKL、HIJKLMNOP 和默克尔树的根（在图中以虚线轮廓显示）来证明 HK（在图底部以黑色背景显示）包含在默克尔根中。

![merkle_tree_path](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_1106.png)

### The Network 《网络》

2015 年，比特币核心的一个新版本增加了一个名为“紧凑区块中继”（在 BIP152 中指定）的功能，该功能可以更快地传输新区块并使用更少的带宽。

### Private Block Relay Networks 私有区块链中继网络

### Network Discovery 网络发现

要连接到已知的对等节点，节点会建立一个 TCP 连接，通常连接到端口 8333（这个端口通常被认为是比特币所使用的端口），或者如果提供了替代端口，则连接到替代端口。在建立连接后，节点将通过发送一个版本消息开始“握手”（见[对等节点之间的初始握手。](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#network_handshake)），该版本消息包含基本的识别信息，包括：

新节点如何找到对等节点？第一种方法是使用一些“DNS 种子”查询 DNS，DNS 种子是提供比特币节点 IP 地址列表的 DNS 服务器。其中一些 DNS 种子提供稳定比特币监听节点的 IP 地址静态列表。一些 DNS 种子是 BIND（伯克利互联网名称守护程序）的自定义实现，它从爬虫或长期运行的比特币节点收集的比特币节点地址列表中返回随机子集。比特币核心客户端包含几个不同 DNS 种子的名称。不同 DNS 种子的所有权多样性和实现多样性为初始引导过程提供了高度的可靠性。在比特币核心客户端中，使用 DNS 种子的选项由选项开关 -dnsseed 控制（默认设置为 1，以使用 DNS 种子）。

一旦建立了一个或多个连接，新节点将向其邻居发送一个包含自身 IP 地址的 addr 消息。邻居们将依次把这个 addr 消息转发给他们的邻居，确保新连接的节点广为人知且连接更良好。此外，新连接的节点可以向其邻居发送 getaddr，要求他们返回其他对等节点的 IP 地址列表。这样，一个节点可以找到要连接的对等节点，并在网络上宣传其存在，以便其他节点找到它。[地址传播和发现。](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#address_propagation)展示了地址发现协议。

节点必须连接到几个不同的对等节点，以便在比特币网络中建立多样化的路径。路径并不可靠——节点来来去去——因此，当节点失去旧连接时，必须继续发现新节点，并在其他节点启动时为它们提供帮助。启动只需要一个连接，因为第一个节点可以向其对等节点介绍，而这些对等节点可以进一步介绍。连接过多的节点也是不必要的，并且会浪费网络资源。启动后，节点将记住其最近成功的对等连接，因此如果它重新启动，它可以快速与以前的对等网络重新建立连接。如果以前的对等节点都没有响应其连接请求，节点可以使用种子节点再次启动。

```
$ bitcoin-cli getpeerinfo
```

全节点是在具有最多工作量证明的有效区块链上验证每个区块中的每笔交易的节点。

### Exchanging "Inventory" “交换‘库存’”。

区块链的同步过程始于版本消息，因为该消息包含最佳高度（BestHeight），即一个节点当前的区块链高度（区块数量）。一个节点将看到来自其对等节点的版本消息，了解它们各自拥有多少个区块，并能够与自己的区块链中的区块数量进行比较。对等节点将交换一个包含其本地区块链顶部区块哈希的获取区块头（getheaders）消息。其中一个对等节点将能够识别接收到的哈希属于一个不在顶部的区块，而是属于一个较旧的区块，从而推断出其自身的本地区块链比远程节点的区块链更长。

同时，节点将开始使用 getdata 消息请求它之前接收的每个头的块。节点将从其选定的每个对等节点请求不同的块，这使得它可以断开连接那些明显比平均速度慢的对等节点，以便找到更新的（并且可能更快的）对等节点。

### Lightweight Clients 轻量级客户端

轻量级客户端仅下载区块头，而不下载每个区块中包含的交易。由此产生的没有交易的区块头链比完整的区块链小约 10000 倍。轻量级客户端无法构建所有可供支出的未花费交易输出（UTXO）的完整画面，因为它们不知道网络上的所有交易。相反，它们使用略有不同的方法验证交易，该方法依赖于对等节点根据需要提供区块链相关部分的局部视图。

轻量级客户端肯定可以验证交易的存在，但不能验证交易（例如同一未花费交易输出的双重支付）不存在，因为它没有所有交易的记录。

布隆过滤器是一种概率搜索过滤器，是一种无需精确指定即可描述所需模式的方法。布隆过滤器为表达搜索模式提供了一种高效的方式，同时保护隐私。轻量级客户端使用它们向其对等方询问与特定模式匹配的交易，而无需确切透露他们正在搜索哪些地址、密钥或交易。

隆过滤器通过允许轻量级客户端指定可针对精度或隐私进行调整的交易搜索模式来实现此功能。更具体的布隆过滤器将产生准确的结果，但代价是揭示轻量级客户端感兴趣的模式，从而揭示用户钱包拥有的地址。

布隆过滤器被初始化，使得位数组全为零。要将一个模式添加到布隆过滤器中，该模式依次由每个哈希函数进行哈希。将第一个哈希函数应用于输入会得到一个介于 1 和 N 之间的数字。找到数组中相应的位（从 1 到 N 进行索引）并将其设置为 1，从而记录哈希函数的输出。然后，使用下一个哈希函数设置另一位，依此类推。一旦应用了所有 M 个哈希函数，搜索模式将作为已从 0 变为 1 的 M 个位被“记录”在布隆过滤器中。

相反，如果一个模式针对布隆过滤器进行测试，并且其中任何一位被设置为 0，这就证明该模式没有记录在布隆过滤器中。否定结果不是一种概率，而是一种确定性。简单来说，布隆过滤器上的否定匹配是一个“绝对不是！”。

轻量级客户端随后将向对等节点发送一个包含布隆过滤器的 filterload 消息，以便在连接上使用该布隆过滤器。在对等节点上，每个传入的交易都要对照布隆过滤器进行检查。全节点对照布隆过滤器检查交易的几个部分，寻找包括以下内容的匹配项：

响应来自客户端的 getdata 消息时，对等节点将发送一个 merkleblock 消息，该消息仅包含与过滤器匹配的区块的区块头，以及每个匹配交易的默克尔路径（见[\[merkle\_trees\]](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#merkle_trees)）。然后，该对等节点还将发送包含由过滤器匹配的交易的 tx 消息。

例如，考虑一个轻量级客户端，它对其钱包中包含的地址的传入付款感兴趣。轻量级客户端将在其与对等节点的连接上建立一个布隆过滤器（见[\[布隆过滤器\]](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#bloom_filters)），以将接收到的交易限制为仅包含其感兴趣的地址的交易。当一个对等节点看到与布隆过滤器匹配的交易时，它将使用默克尔区块消息发送该区块。默克尔区块消息包含区块头以及将感兴趣的交易链接到区块中默克尔根的默克尔路径。轻量级客户端可以使用这个默克尔路径将交易连接到区块头，并验证该交易是否包含在区块中。

## The Bitcoin Network 比特币网络

术语“对等”（peer-to-peer）或 P2P 意味着参与网络的全节点彼此对等，它们都可以执行相同的功能，并且没有“特殊”节点。

比特币网络”一词是指运行比特币 P2P 协议的节点集合。除了比特币 P2P 协议之外，还有其他用于挖矿和轻量级钱包的协议。

### Node Types and Roles 节点类型和角色

虽然比特币 P2P 网络中的全节点（对等节点）彼此平等，但根据它们所支持的功能，它们可能承担不同的角色。比特币全节点验证区块，并可能包含其他功能，如路由、挖矿和钱包服务。

### Compact Block Relay 紧凑型区块中继

而是允许节点发送每个交易的一个简短的 6 字节标识符。当你的节点接收到一个带有一个或多个标识符的紧凑型区块时，它会在其内存池中检查这些交易，如果找到就使用它们。对于在本地节点的内存池中未找到的任何交易，你的节点可以向对等节点发送请求以获取副本。

虽然紧凑区块在很大程度上减少了区块在网络中传播所需的时间，但可以进一步减少延迟。然而，与紧凑区块不同的是，其他解决方案涉及权衡，这使得它们在公共 P2P 中继网络中不可用或不适合。出于这个原因，人们一直在对区块的专用中继网络进行试验。

当一个新节点启动时，它必须发现网络上的其他比特币节点才能参与。要启动这个过程，一个新节点必须发现网络上至少一个现有节点并连接到它。其他节点的地理位置是无关紧要的；比特币网络拓扑结构不是按地理位置定义的。因此，可以随机选择任何现有的比特币节点。

版本消息始终是任何对等方发送给另一个对等方的第一条消息。接收版本消息的本地对等方将检查远程对等方报告的版本，并确定远程对等方是否兼容。如果远程对等方兼容，本地对等方将确认版本消息并通过发送确认消息（verack）来建立连接。

另外，一个对网络一无所知的自举节点必须被赋予至少一个比特币节点的 IP 地址，之后它可以通过进一步的介绍建立连接。命令行参数-seednode 可用于连接到一个节点，仅将其用作种子进行介绍。在初始种子节点用于形成介绍之后，客户端将与它断开连接并使用新发现的对等节点。

![AddressPropagation](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_1004.png)

在运行比特币核心客户端的节点上，你可以使用命令“getpeerinfo”列出对等连接。

### Full Nodes 全节点

全节点独立处理每个区块，从第一个区块（创世区块）开始，一直处理到网络中的最新已知区块。全节点可以独立且权威地验证任何交易。全节点依靠网络接收关于新交易区块的更新，然后进行验证并将其纳入其本地对哪些脚本控制哪些比特币的视图中，这被称为一组未花费交易输出（UTXO）。

全节点一旦连接到对等节点，首先要做的事情是尝试构建一个完整的区块头链。如果它是一个全新的节点并且根本没有区块链，那么它只知道一个区块，即创世区块，该区块静态地嵌入在客户端软件中。从第 0 个区块（创世区块）开始，新节点将不得不下载数十万个区块以与网络同步并重新建立完整的区块链。

拥有更长区块链的对等节点比其他节点拥有更多的区块，并且可以确定其他节点需要哪些区块头才能“赶上”。它将使用一个区块头消息确定要共享的前 2000 个区块头。该节点将不断请求额外的区块头，直到它接收到远程对等节点声称拥有的每个区块的一个区块头为止。

当每个区块被接收时，它会被添加到区块链中，正如我们将在[\[区块链\]](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#blockchain)中看到的那样。随着本地区块链逐渐构建起来，会请求并接收更多的区块，这个过程会持续进行，直到节点赶上网络中的其他节点。

许多比特币客户端被设计为在空间和功率受限的设备上运行，例如智能手机、平板电脑或嵌入式系统。对于此类设备，使用一种“简化支付验证”（SPV）方法，使它们能够在不验证完整区块链的情况下运行。这些类型的客户端被称为轻量级客户端。

轻量级客户端通常不能在交易实际上并不存在于区块中时被说服该交易存在于区块中。轻量级客户端通过请求默克尔路径证明并验证区块链中的工作量证明来确定交易在区块中的存在。

区块头允许轻量级客户端验证任何单个区块是否属于具有最多工作量证明的区块链，但它们不会告诉客户端哪些区块包含对其钱包有意义的交易。客户端可以下载每个区块并进行检查，但这将使用运行完整节点所需资源的很大一部分，因此开发人员一直在寻找其他方法来解决这个问题。

#### How Bloom Filters Work 布隆过滤器的工作原理

![Bloom1](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_1006.png)

![Bloom2](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_1007.png)

轻量级客户端将把布隆过滤器初始化为“空”状态；在这种状态下，布隆过滤器不会匹配任何模式。然后，轻量级客户端将列出它感兴趣的所有地址、密钥和哈希值。它将通过从其钱包控制的任何未花费交易输出（UTXO）中提取公钥哈希、脚本哈希和交易 ID 来实现这一点。接着，轻量级客户端将这些中的每一个添加到布隆过滤器中，以便在交易中存在这些模式时布隆过滤器会“匹配”，同时不会泄露这些模式本身。

- The transaction ID 交易 ID
- The data components from the scripts of each of the transaction outputs (every key and hash in the script)来自每个交易输出脚本中的数据组件（脚本中的每个键和哈希）。
- Each of the transaction inputs 每笔交易输入项。
- Each of the input signature data components (or witness scripts)每个输入签名数据组件（或见证脚本）。

### Compact Block Filters 紧凑块过滤器

2016 年，一位匿名开发者向 Bitcoin-Dev 邮件列表发布了一个想法，即反转布隆过滤器流程。

这使得节点可以为每个区块创建一个单一的过滤器，它们可以将其保存到磁盘并反复提供服务，从而消除了 BIP37 中的拒绝服务漏洞。客户端不会向全节点提供任何关于其过去或未来地址的信息。他们只下载区块，其中可能包含数千个并非由客户端创建的交易。他们甚至可以从不同的对等节点下载每个匹配的区块，这使得全节点更难在多个区块中连接属于单个客户端的交易。

在基于布隆过滤器的原始想法描述之后，开发人员意识到对于服务器生成的过滤器有一个更好的数据结构，称为 Golomb-Rice 编码集（GCS）。

然后，爱丽丝发送第一个数字。对于剩余的数字，她发送该数字与前一个数字之间的差值。

我们可以看到，有序列表中两个数字之间的差异会产生比原始数字更短的数字。在收到这个列表后，鲍勃可以通过简单地将每个数字与其前一个数字相加来重建原始列表。这意味着我们在不丢失任何信息的情况下节省了空间，这被称为“无损编码”。

如果我们知道可能需要存储大数字（因为即使大的差异很少见，但也可能发生），但大多数情况下需要存储小数字，我们可以使用一种系统对每个数字进行编码，该系统为小数字使用较少的空间，为大数字使用额外的空间。平均而言，该系统将比为每个数字使用相同数量的空间表现更好。

哥隆编码提供了这种功能。莱斯编码是哥隆编码的一个子集，在某些情况下使用起来更方便，包括在比特币区块过滤器的应用中。

#### What Data to Include in a Block Filter“什么数据应包含在块过滤器中”。

#### Reducing Bandwidth with Lossy Encoding有损编码降低带宽。

### Bitcoin’s Test Blockchains 比特币的测试区块链

加密和认证连接

大多数比特币的新用户认为比特币节点的网络通信是加密的。事实上，比特币的原始实现完全以明文进行通信，在撰写本文时，比特币核心的现代实现也是如此。

作为提高比特币 P2P 网络隐私和安全性的一种方法，有一种解决方案可提供通信加密：*Tor 传输*。

比特币核心（Bitcoin Core）提供了多种配置选项，允许你在通过 Tor 网络传输流量的情况下运行比特币节点。此外，比特币核心还可以提供一个 Tor 隐藏服务，允许其他 Tor 节点直接通过 Tor 连接到你的节点。

几乎每个比特币网络节点都维护着一个名为*内存池*（*mempool*）的未确认交易临时列表。节点使用这个池来跟踪网络已知但尚未包含在区块链中的交易，这些交易被称为*未确认交易*。

一些节点实现还维护一个单独的孤立交易池。如果一个交易的输入引用了一个尚不知道的交易，例如缺失的父交易，那么孤立交易将暂时存储在孤立交易池中，直到父交易到达。

比特币的一些实现还维护一个未花费交易输出（UTXO）数据库，它是区块链上所有未花费输出的集合。这代表了一组与内存池不同的数据。与内存池和孤立交易池不同，UTXO 数据库包含数百万个未花费交易输出条目，即从创世区块以来所有未花费的内容。UTXO 数据库作为一个表存储在持久存储上。