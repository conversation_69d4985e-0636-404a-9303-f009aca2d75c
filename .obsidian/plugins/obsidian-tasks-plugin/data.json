{"globalQuery": "", "globalFilter": "#task", "removeGlobalFilter": true, "taskFormat": "tasksPluginEmoji", "setCreatedDate": true, "setDoneDate": true, "setCancelledDate": true, "autoSuggestInEditor": true, "autoSuggestMinMatch": 0, "autoSuggestMaxItems": 20, "provideAccessKeys": true, "useFilenameAsScheduledDate": false, "filenameAsScheduledDateFormat": "", "filenameAsDateFolders": [], "recurrenceOnNextLine": false, "statusSettings": {"coreStatuses": [{"symbol": " ", "name": "Todo", "nextStatusSymbol": "x", "availableAsCommand": true, "type": "TODO"}, {"symbol": "x", "name": "Done", "nextStatusSymbol": " ", "availableAsCommand": true, "type": "DONE"}], "customStatuses": [{"symbol": "/", "name": "In Progress", "nextStatusSymbol": "x", "availableAsCommand": true, "type": "IN_PROGRESS"}, {"symbol": "-", "name": "Cancelled", "nextStatusSymbol": " ", "availableAsCommand": true, "type": "CANCELLED"}, {"symbol": " ", "name": "to-do", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "/", "name": "incomplete", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "IN_PROGRESS"}, {"symbol": "x", "name": "done", "nextStatusSymbol": " ", "availableAsCommand": false, "type": "DONE"}, {"symbol": "-", "name": "canceled", "nextStatusSymbol": " ", "availableAsCommand": false, "type": "CANCELLED"}, {"symbol": ">", "name": "forwarded", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "<", "name": "scheduling", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "?", "name": "question", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "!", "name": "important", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "*", "name": "star", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "\"", "name": "quote", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "l", "name": "location", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "b", "name": "bookmark", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "i", "name": "information", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "S", "name": "savings", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "I", "name": "idea", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "p", "name": "pros", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "c", "name": "cons", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "f", "name": "fire", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "k", "name": "key", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "w", "name": "win", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "u", "name": "up", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "d", "name": "down", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": " ", "name": "Unchecked", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "x", "name": "Checked", "nextStatusSymbol": " ", "availableAsCommand": false, "type": "DONE"}, {"symbol": ">", "name": "Rescheduled", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "<", "name": "Scheduled", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "!", "name": "Important", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "?", "name": "Question", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "*", "name": "Star", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "n", "name": "Note", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "l", "name": "Location", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "i", "name": "Information", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "I", "name": "Idea", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "S", "name": "Amount", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "p", "name": "Pro", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "c", "name": "Con", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "b", "name": "Bookmark", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "\"", "name": "Quote", "nextStatusSymbol": "x", "availableAsCommand": false, "type": "TODO"}, {"symbol": "0", "name": "Speech bubble 0", "nextStatusSymbol": "0", "availableAsCommand": false, "type": "NON_TASK"}, {"symbol": "1", "name": "Speech bubble 1", "nextStatusSymbol": "1", "availableAsCommand": false, "type": "NON_TASK"}, {"symbol": "2", "name": "Speech bubble 2", "nextStatusSymbol": "2", "availableAsCommand": false, "type": "NON_TASK"}, {"symbol": "3", "name": "Speech bubble 3", "nextStatusSymbol": "3", "availableAsCommand": false, "type": "NON_TASK"}, {"symbol": "4", "name": "Speech bubble 4", "nextStatusSymbol": "4", "availableAsCommand": false, "type": "NON_TASK"}, {"symbol": "5", "name": "Speech bubble 5", "nextStatusSymbol": "5", "availableAsCommand": false, "type": "NON_TASK"}, {"symbol": "6", "name": "Speech bubble 6", "nextStatusSymbol": "6", "availableAsCommand": false, "type": "NON_TASK"}, {"symbol": "7", "name": "Speech bubble 7", "nextStatusSymbol": "7", "availableAsCommand": false, "type": "NON_TASK"}, {"symbol": "8", "name": "Speech bubble 8", "nextStatusSymbol": "8", "availableAsCommand": false, "type": "NON_TASK"}, {"symbol": "9", "name": "Speech bubble 9", "nextStatusSymbol": "9", "availableAsCommand": false, "type": "NON_TASK"}]}, "features": {"INTERNAL_TESTING_ENABLED_BY_DEFAULT": true}, "generalSettings": {}, "headingOpened": {"Core Statuses": true, "Custom Statuses": true}, "debugSettings": {"ignoreSortInstructions": false, "showTaskHiddenData": false, "recordTimings": false}, "loggingOptions": {"minLevels": {"": "info", "tasks": "info", "tasks.Cache": "info", "tasks.Events": "info", "tasks.File": "info", "tasks.Query": "info", "tasks.Task": "info"}}}