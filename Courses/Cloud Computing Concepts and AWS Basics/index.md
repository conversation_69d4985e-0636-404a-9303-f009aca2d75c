---
startTime: 2024-11-11
endTime: 
progress: 40
target: "100"
tags:
  - flash
---
> [!success]+ progress
> ```meta-bind
> INPUT[progressBar( minValue(1), maxValue(100)):progress]
- Why is this goal Important to me?
- What are the possible risks & Obstacles
## Slides
![[Introduction+to+Cloud+Course+Slides 1.pdf]]
## IAM
- avoid use root user
- access keys are used for programmatic access
- user, user group, policy, role
- role: be assumed
## Amazon EC2, Auto Scaling, and Load Balancing
- server with virtualization
	- hypervisor
- scale up and scale out
	- scale up(scale vertically)
		- add more resources to the server
		- 比如切换到更高型号的instance
	- scale out(or scale horizontallly)
		- add more instances of the app
		- 比如开启更多的instances
- High availability vs fault tolerance
	- High availability
		- no single point of failure(redundancy)
		- lower cost
	- fault tolerance
		- no downtime at all
		- higher cost
- AWS EC2 overview
	- EC2 instance is a virtual server, can choose windows/linux/mac
	- it's **infra as a service**, we need to manage os and app run on it
	- public/private ip address
	- can deploy to public subnet or private subnet
		- if deploy to public subnet, can connect to the internet through internet gateway
		- deploy to private subnet, no internet connectivity
- Launching a EC2 instance
	- choose AMI image: decide the configurations of the server
	- create key pairs: connect to the instance outside AWS
- connect to EC2
	- need to have a public address and port 22 open
- Access keys and IAM roles with EC2
	- do not store long term access keys in EC2
	- EC2 can assume role, credentials are not stored on the instance
		- create a role for EC2
		- assign policies to the role
- EC2 user data
	- script to execute the first time instance launches
- EC2 auto scaling
	- scaling is horizontal
	- auto scaling group
		- launch template
		- can select multiple availability zones
-  Amazon Elastic Load Balancing
	- what does ELB mean? ;; Elastic Load Balancing
<!--SR:!2025-03-03,42,250-->
	- Application Load Balancer
		- routes based on the content of the request(L7)
		- path based routing
	- create load balancer
		- target group
## Amazon Virtual Private Cloud (VPC)
 - An IPv4 address可以分为哪两个部分 ;; An IPv4 address has a network and host ID
<!--SR:!2025-02-07,18,250-->
 - subnet mask的作用 ;; is used to define the network and host ID
<!--SR:!2025-02-08,19,250-->
 - what is vpc? ;; A VPC is a logically isolated portion of the AWS cloud within a region
<!--SR:!2025-02-10,21,250-->
 - region, vpc, availability zone的层级是怎样的 ;; ![[Pasted image **************.png|100]]
<!--SR:!2025-02-12,23,250-->
 - vpc通过配置什么service访问internet ?? An Internet Gateway is used to connect to the Internet
 - stateful firewall有什么特点 ;; A stateful firewall allows the return traffic automatically
<!--SR:!2025-02-06,17,250-->