## Tasks
- [x] #task Add unit tests for ResultTab component ➕ 2024-11-27 ⏳ 2024-11-28 ✅ 2024-11-28
- [x] #task Add ut for FileList component ⏳ 2024-12-02 ✅ 2024-12-04
	- [x] #task refactor FileList ⏳ 2024-12-02 ✅ 2024-12-02
- [x] #task Fix the error when render ReactCodeMirror ⏳ 2024-12-04 ✅ 2024-12-04
## Vitest
### API
#### Test
- test
	- 一个测试单元
	- `test.for`: 用多组参数运行
- describe
	- 一个测试套件，包含多个相关的测试单元
	- `describe.each`
- beforeEach, afterEach
	- 在一个测试套件或文件级别生效
- beforeAll, afterAll
- onTestFinished, onTestFailed
	- 在test单元内调用
#### Vi
- fn(func)
	- 监视一个function
	- mock它的返回值
- restoreAllMocks(): 清除所有的mocks，常在afterEach中调用
- spyOn(obj, prop)
	- 监视一个对象的属性
	- 重写其实现
- waitUntil
- waitFor
- hoisted(func): 在所有静态import前执行函数，比如mock导入值
## React Testing Library
- render
- renderHook