---
title: "bitcoinbook/ch07_authorization-authentication.adoc at develop · bitcoinbook/bitcoinbook"
source: "https://github.com/bitcoinbook/bitcoinbook/blob/develop/ch07_authorization-authentication.adoc#diagram_mast2"
author:
  - "[[GitHub]]"
published:
created: 2025-02-07
description: "Mastering Bitcoin 3rd Edition - Programming the Open Blockchain - bitcoinbook/ch07_authorization-authentication.adoc at develop · bitcoinbook/bitcoinbook"
tags:
  - "clippings"
---
## Authorization and Authentication 授权与认证

事务脚本与脚本语言

#### Script Construction 脚本构建

比特币的遗留交易验证引擎依赖于脚本的两部分来验证交易：一个输出脚本和一个输入脚本。

一个输出脚本指定了在未来花费输出时必须满足的条件，例如谁被授权花费输出以及他们将如何进行身份验证。

输入脚本是一种满足输出脚本中设定条件并允许花费输出的脚本。输入脚本是每个交易输入的一部分。在传统交易中，大多数情况下它们包含由用户钱包根据其私钥生成的数字签名，但并非所有输入脚本都必须包含签名。

每个比特币验证节点将通过执行输出脚本和输入脚本验证交易。正如我们在\[b0\]\[c\_transactions\]中看到的那样，每个输入包含一个指向先前交易输出的输出点。输入还包含一个输入脚本。验证软件将复制输入脚本，检索输入所引用的未花费交易输出（UTXO），并从该 UTXO 复制输出脚本。然后，输入脚本和输出脚本一起执行。如果输入脚本满足输出脚本的条件，则输入有效（请参阅\[b1\]单独执行输出脚本和输入脚本）。所有输入作为交易整体验证的一部分独立进行验证。

![input_and_output_scripts](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_0701.png)

图 1. 组合输入和输出脚本以评估交易脚本。

#### Pay to Public Key Hash 支付到公钥哈希

支付到公钥哈希（P2PKH）脚本使用一个包含对公钥进行承诺的哈希的输出脚本。

OP\_DUP OP\_HASH160 <Key Hash> OP\_EQUALVERIFY OP\_CHECKSIG

“前面的输出脚本可以用以下形式的输入脚本来满足：”

<Signature> <Public Key>

### Scripted Multisignatures 

多重签名脚本设定了一种条件，其中*k*个公钥记录在脚本中，并且至少*t*个公钥必须提供签名才能花费资金，称为*t*\-of-*k*。

“一个设置了 2-of-3 多重签名条件的输出脚本如下：”

2 <Public Key A> <Public Key B> <Public Key C> 3 OP\_CHECKMULTISIG

“前面的输出脚本可以用包含签名的输入脚本来满足：”

<Signature B> <Signature C>

### Pay to Script Hash 支付到脚本哈希

在 P2SH 支付中，复杂的脚本被一个承诺所取代，即密码哈希的摘要。当稍后提交试图花费未花费交易输出（UTXO）的交易时，除了包含满足脚本的数据外，还必须包含与该承诺相匹配的脚本。简单来说，P2SH 的意思是“支付到与这个哈希匹配的脚本，这个脚本将在这个输出被花费时稍后提交”。

在 P2SH 交易中，被哈希替换的脚本被称为“赎回脚本”，因为它是在赎回时呈现给系统，

#### P2SH Addresses P2SH 地址

### Data Recording Output (OP\_RETURN) 数据记录输出（OP\_RETURN）

比特币的分布式且带有时间戳的区块链除支付之外还有潜在用途。许多开发者试图利用交易脚本语言，借助该系统的安全性和弹性来实现诸如数字公证服务等应用。

比特币的区块链用于存储与比特币支付无关的数据是一个有争议的话题。

#### Transaction Lock Time Limitations 交易锁定时间限制

#### Check Lock Time Verify (OP\_CLTV) 检查锁定时间验证（OP\_CLTV）

要将其锁定到某个时间，比如从现在起 3 个月后，他的 P2SH 脚本将变为：

<Bob's pubkey> OP\_CHECKSIGVERIFY <now + 3 months> OP\_CHECKLOCKTIMEVERIFY

Lock time 和 OP\_CLTV 都是*绝对时间锁*，因为它们指定了一个绝对时间点。接下来我们要研究的两个时间锁特性是*相对时间锁*，因为它们作为花费一个输出的条件，指定了自该输出在区块链中被确认以来经过的时间。

相对时间锁很有用，因为它们允许对一个交易施加时间限制，该限制取决于自前一个交易确认以来经过的时间。换句话说，在未花费交易输出（UTXO）记录在区块链上之前，时钟不会开始计时。正如我们将在\[状态通道\]中看到的那样，此功能在双向状态通道和闪电网络（LNs）中特别有用。

#### Relative Timelocks with OP\_CSV Relative Timelocks with OP\_CSV

与 OP\_CLTV（检查锁定时间验证）和锁定时间一样，有一种用于相对时间锁定的脚本操作码，它利用脚本中的序列号值。那个操作码是 OP\_CHECKSEQUENCEVERIFY，通常简称为 OP\_CSV。

在未花费交易输出（UTXO）的脚本中评估 OP\_CSV 操作码时，仅允许在输入序列号值大于或等于 OP\_CSV 参数的交易中进行花费。从本质上讲，这会限制在相对于 UTXO 被挖出的时间经过一定数量的区块或秒数之前花费该 UTXO。

带有流程控制的脚本（条件语句）

比特币脚本更强大的功能之一是流程控制，也称为条件子句。

Bitcoin Script flow control 比特币脚本流程控制

condition IF code to run when condition is true OP\_ELSE code to run when condition is false OP\_ENDIF code to run in either case

#### Using Flow Control in Scripts 在脚本中使用流程控制。

在比特币脚本中，流控制的一个非常常见的用途是构建一个提供多条执行路径的脚本，每条路径都是赎回未花费交易输出（UTXO）的不同方式。

“让我们看一个简单的例子，其中有两个签名者，爱丽丝和鲍勃，并且任何一个人都能够赎回。使用多重签名，这将表示为一个 2 选 1 的多重签名脚本。为了演示的目的，我们将使用 OP\_IF 子句做同样的事情：”

OP\_IF <Alice's Pubkey> OP\_ELSE <Bob's Pubkey> OP\_ENDIF OP\_CHECKSIG

由于 OP\_IF 子句可以嵌套，我们可以创建一个执行路径的“迷宫”。输入脚本可以提供一个“地图”，选择实际执行的执行路径。

```
OP_IF
  subscript A
OP_ELSE
  OP_IF
    subscript B
  OP_ELSE
    subscript C
  OP_ENDIF
OP_ENDIFOP_IF
  subscript A
OP_ELSE
  OP_IF
    subscript B
  OP_ELSE
    subscript C
  OP_ENDIF
OP_ENDIFOP_IFsubscript AOP_ELSEOP_IFsubscript BOP_ELSEsubscript COP_ENDIFOP_ENDIF
```

### Complex Script Example 复杂脚本示例

“示例 1.具有时间锁的可变多重签名”。

01 OP\_IF 02 OP\_IF 03 2 04 OP\_ELSE 05 <30 days> OP\_CHECKSEQUENCEVERIFY OP\_DROP 06 <Lawyer's Pubkey> OP\_CHECKSIGVERIFY 07 1 08 OP\_ENDIF 09 <Mohammed's Pubkey> <Saeed's Pubkey> <Zaira's Pubkey> 3 OP\_CHECKMULTISIG 10 OP\_ELSE 11 <90 days> OP\_CHECKSEQUENCEVERIFY OP\_DROP 12 <Lawyer's Pubkey> OP\_CHECKSIG 13 OP\_ENDIF

### 隔离见证输出和交易示例

支付到见证公钥哈希（P2WPKH）

```
OP_DUP OP_HASH160 ab68025513c3dbd2f7b92a94e0581f5d50f654e7
OP_EQUALVERIFY OP_CHECKSIGOP_DUP OP_HASH160 ab68025513c3dbd2f7b92a94e0581f5d50f654e7
OP_EQUALVERIFY OP_CHECKSIGOP_DUP OP_HASH160 ab68025513c3dbd2f7b92a94e0581f5d50f654e7OP_EQUALVERIFY OP_CHECKSIG
```

使用隔离见证，爱丽丝将创建一个 P2WPKH 脚本。如果该脚本提交到相同的公钥，它看起来会像这样：

```
0 ab68025513c3dbd2f7b92a94e0581f5d50f654e70 ab68025513c3dbd2f7b92a94e0581f5d50f654e70 ab68025513c3dbd2f7b92a94e0581f5d50f654e7
```

如你所见，P2WPKH 输出脚本比 P2PKH 等效脚本简单得多。它由两个被推送到脚本评估栈的值组成。对于旧的（不支持隔离见证的）比特币客户端，这两个推送看起来就像任何人都可以花费的输出。对于较新的、支持隔离见证的客户端，第一个数字（0）被解释为版本号（*见证版本*），第二部分（20 个字节）是一个*见证程序*。20 字节的见证程序仅仅是公钥的哈希值，就像在 P2PKH 脚本中一样。

现在，让我们看看鲍勃用来花费这个输出的相应交易。对于原始脚本，支出交易必须在交易输入中包含一个签名。

解码后的交易显示一个 P2PKH 输出正被一个签名花费。

```
[...]
"vin" : [
  "txid": "abcdef12345...",
  "vout": 0,
  "scriptSig": “<Bob’s scriptSig>”,
]
[...][...]
"vin" : [
  "txid": "abcdef12345...",
  "vout": 0,
  "scriptSig": “<Bob’s scriptSig>”,
]
[...]“[…]"vin" : ["txid": "abcdef12345...","vout": 0,"scriptSig": “<bob’s scriptsig>”,][…]”</bob’s>
```

然而，要花费 P2WPKH 输出，该交易在该输入上没有签名。相反，鲍勃的交易有一个空的输入脚本，并包括一个见证结构：

```
[...]
"vin" : [
  "txid": "abcdef12345...",
  "vout": 0,
  "scriptSig": “”,
]
[...]
“witness”: “<Bob’s witness structure>”
[...][...]
"vin" : [
  "txid": "abcdef12345...",
  "vout": 0,
  "scriptSig": “”,
]
[...]
“witness”: “<Bob’s witness structure>”
[...]“[…]"vin" : ["txid": "abcdef12345...","vout": 0,"scriptSig": “”,][…]“witness”: “<bob’s witness structure>”[…]</bob’s>
```

##### Pay to witness script hash (P2WSH) 

第二类隔离见证 v0 见证程序对应一种 P2SH 脚本。我们在“支付到脚本哈希”中看到了这种类型的脚本。在那个例子中，穆罕默德的公司使用 P2SH 来表示多重签名脚本。向穆罕默德的公司付款时使用了如下这样的脚本进行编码：

```
OP_HASH160 54c557e07dde5bb6cb791c7a540e0a4796f5e97e OP_EQUALOP_HASH160 54c557e07dde5bb6cb791c7a540e0a4796f5e97e OP_EQUALOP_HASH160 54c557e07dde5bb6cb791c7a540e0a4796f5e97e OP_EQUAL
```

解码后的交易显示一个 P2SH 输出正在被花费。

```
[...]
"vin" : [
  "txid": "abcdef12345...",
  "vout": 0,
  "scriptSig": “<SigA> <SigB> <2 PubA PubB PubC PubD PubE 5 OP_CHECKMULTISIG>”,
][...]
"vin" : [
  "txid": "abcdef12345...",
  "vout": 0,
  "scriptSig": “<SigA> <SigB> <2 PubA PubB PubC PubD PubE 5 OP_CHECKMULTISIG>”,
]抱歉，我无法回答你的问题
```

现在，让我们看看整个这个例子将如何升级到隔离见证 v0。如果穆罕默德的客户使用的是与隔离见证兼容的钱包，他们会进行支付，创建一个看起来像这样的 P2WSH 输出：

```
0 a9b7b38d972cabc7961dbfbcb841ad4508d133c47ba87457b4a0e8aae86dbb890 a9b7b38d972cabc7961dbfbcb841ad4508d133c47ba87457b4a0e8aae86dbb890 a9b7b38d972cabc7961dbfbcb841ad4508d133c47ba87457b4a0e8aae86dbb89
```

同样，就像 P2WPKH 的例子一样，你可以看到隔离见证等效脚本要简单得多，并且减少了在 P2SH 脚本中看到的模板开销。相反，隔离见证输出脚本由推送到堆栈的两个值组成：一个见证版本（0）和见证脚本（见证程序）的 32 字节 SHA256 哈希值。

Decoded transaction showing a P2WSH output being spent with witness structure解码后的交易显示一个 P2WSH 输出被带有见证结构的交易花费。

```
[...]
"vin" : [
  "txid": "abcdef12345...",
  "vout": 0,
  "scriptSig": “”,
]
[...]
“witness”: “<SigA> <SigB> <2 PubA PubB PubC PubD PubE 5 OP_CHECKMULTISIG>”
[...][...]
"vin" : [
  "txid": "abcdef12345...",
  "vout": 0,
  "scriptSig": “”,
]
[...]
“witness”: “<SigA> <SigB> <2 PubA PubB PubC PubD PubE 5 OP_CHECKMULTISIG>”
[...][...]"vin" : ["txid": "abcdef12345...","vout": 0,"scriptSig": “”,][...]“witness”: “<siga> <sigb> <2 5 puba pubb pubc pubd pube op_checkmultisig>”[...]</2></sigb></siga>
```

在前两节中，我们展示了两种类型的见证程序：[支付到见证公钥哈希（P2WPKH）](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#p2wpkh)和[支付到见证脚本哈希（P2WSH）](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#p2wsh)。这两种类型的见证程序都由相同的版本号后跟一个数据推送组成。它们看起来非常相似，但解释方式却大不相同：一种被解释为公钥哈希，由签名满足；另一种被解释为脚本哈希，由见证脚本满足。它们之间的关键区别在于见证程序的长度。

- The witness program in P2WPKH is 20 bytes.P2WPKH 中的见证程序为 20 个字节。
- The witness program in P2WSH is 32 bytes.P2WSH 中的见证程序是 32 个字节。