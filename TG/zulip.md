- [[wip]]
* autoport
```shell
# Format: wip -autoport source_jira source_pr targert_version
wip -autoport CORE-1994 engine#742 3.8.0,3.7.0
```
* run toolsE2E
```shell
wip -ut none -it "toolsE2E" tools#1127
```
* install tigergraph
```shell
install_load skip_load=true ip_list=************ sudo_user=graphsql pkg_url=https://tigergraph-release-prebuild.s3.amazonaws.com/prebuild/tigergraph-4.1.1-prod_sv4-wip_test_54881-offline.tar.gz
```