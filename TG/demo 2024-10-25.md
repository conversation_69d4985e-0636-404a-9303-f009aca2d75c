## Script
Hi everyone, I'll demo some new improvements we did **recently** that can enhance user's coding experience in the GSQL editor.

**Narrator:**  
Currently, a GSQL query parser is implemented in the front-end and will generate the **abstract syntax tree** when users input. The GSQL grammar rules defined in the front-end covers most of GSQL query statements as well as commonly used GSQL commands like 'Use graph'.

**[On-Screen: Show AST tree being printed in the console]**

**Narrator:**  
As you can see, the abstract syntax tree is printed in the console and it identifies different statements. Based on it, we can provide a more acurate and shorter autocompletion list for users.

**Narrator:**  
We've also defined more token types, including GSQL types, accumulators, constants,  operators, keywords and so on. They are highlighted with different colors in the editor theme and use different icons in the autocompletion list. 
**[On-Screen: Show different colored tokens and icons in the editor]**

**Narrator:**  
we also seperate GSQL script keywords and query keywords. for example, `user` is a keyword in the ddl statements(`create user`), but it's not a keyword in the query context. 
**[On-Screen:  show user as a keyword in the ddl]**

**Narrator:**  
we provide more context-aware and schema based autocompletions for users now. 
**Narrator:**  
when the user is writing in a query block, we will find the graph name from the UseGraph stmt and **fetches the schema dynamically**. Then we will provide autocomplete options from schema based on the context.
For example, if users are defining a vertex set, the autocompletion list will include vertex and edge names.( or if they are writing a path pattern, there are also vertex and edge names in the list)
**[On-Screen: Show autocompletion in vertex set declaration]**

**Narrator:**  
When user is accessing attributes from a alias in the select statement, the list will include attribute names and local accumulator names.
One thing I need to mention is that we didn't do much type inference work. for example, in this statement, we assign the result of a select clause to variable `set1`, if users accss the attributes from `set1`, they will get a list including all attributes from all vertex/edge in the graph. This is a place we need to improve in the future.

**[On-Screen: Show autocompletion in select statement subclauses]**

**Narrator:**  
In other contexts, we also provide different autocompletion list. For example, in the beginning of statements or parameter lists, the autocompletion list will include GSQL type names. 
In expressions, the list will include all variable names, global accumulator names and built-in functions.

**[On-Screen: Demonstrate autocompletion in different contexts]**

**Narrator:**  
There are some enhancements not finished. Soon, the editor will display errors and warnings from codecheck endpoints like graph studio, allowing users to see syntax errors without actually running the query.

**[On-Screen: Demonstrate quick code block selection]**
## Go through features
- An AST tree was generated in the front end(based on this syntax tree, we can provide a more acurate and shorter autocompletion list for users)
	- covered most GSQL statements: SelectStmt, DeclarationStmt, WhileStmt...
	- some commonly used GSQL command: Use graph, query commands...
- More tokens defined(they will have different color in the editor theme and icons in the autocompletion list)
	- Types, Accumulators, Constants, Operators...
	- Keywords inside the query block are different than those outside(script keywords and query keywords)
- Context-aware autocompletions
	- Complete options from schema: Vertex/Edge types, attributes, graph names
	- Complete options from identifiers: user declared variables and accumulators, built-in functions/attributes
	- Complete options from keywords
## What I want to demo
- print ast tree in the console, we can see different statements identified
- schema based autocompletions
	- detect the graph the query uses and fetch graph schema
	- provide different schema options in different context
		- in vertex set declaration statement and path pattern: provide vertex and edge names for autocompletions
		- when use alias in subclauses of select statement: provide vertex/edge attribute names and local accumulator names for autocompletions
- other contexts
	- in the begin of statements, parameter list: GSQL types
	- in the expression: variable names and global accumulator names
## To do in the future
- Display errors/warnings from codecheck endpoints
	- users won't need to run the query to see the syntax errors
- Stronger type inferences
	- the type inference is weak now. in the case we don't do type inference, we provide all attributes from schema in the autocompletion list
- Make it easier for user to select code block
	- uses can select a query block or command quickly. for example, if users put the cursor in the end of a query block, we can make the whole query block selected