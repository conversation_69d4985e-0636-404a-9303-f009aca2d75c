---
title: What are LLMs? - Hugging Face Agents Course
source: https://huggingface.co/learn/agents-course/unit1/what-are-llms#what-is-a-large-language-model
author: 
published: 
created: 2025-03-27
description: We’re on a journey to advance and democratize artificial intelligence through open source and open science.
tags:
  - clippings
  - processed
---
Partial conversion completed with errors. Original HTML:

<div><p data-svelte-h="svelte-twq6h9">There are 3 types of transformers:</p></div><div>Although Large Language Models come in various forms, LLMs are typically decoder-based models with billions of parameters.</div><div><p data-svelte-h="svelte-1p4vse1">The underlying principle of an LLM is simple yet highly effective: <strong>its objective is to predict the next token, given a sequence of previous tokens</strong>. A “token” is the unit of information an LLM works with. You can think of a “token” as if it was a “word”, but for efficiency reasons LLMs don’t use whole words.</p></div><div><p data-svelte-h="svelte-hdd8du">Each LLM has some <strong>special tokens</strong> specific to the model. The LLM uses these tokens to open and close the structured components of its generation. For example, to indicate the start or end of a sequence, message, or response. Moreover, the input prompts that we pass to the model are also structured with special tokens. The most important of those is the <strong>End of sequence token</strong> (EOS).</p></div><div><h2 class="relative group"><a id="understanding-next-token-prediction" class="header-link block pr-1.5 text-lg no-hover:hidden with-hover:absolute with-hover:p-1.5 with-hover:opacity-0 with-hover:group-hover:opacity-100 with-hover:right-full" href="https://huggingface.co/learn/agents-course/unit1/#understanding-next-token-prediction"><span><svg class="" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" preserveAspectRatio="xMidYMid meet" viewBox="0 0 256 256"><path d="M167.594 88.393a8.001 8.001 0 0 1 0 11.314l-67.882 67.882a8 8 0 1 1-11.314-11.315l67.882-67.881a8.003 8.003 0 0 1 11.314 0zm-28.287 84.86l-28.284 28.284a40 40 0 0 1-56.567-56.567l28.284-28.284a8 8 0 0 0-11.315-11.315l-28.284 28.284a56 56 0 0 0 79.196 79.197l28.285-28.285a8 8 0 1 0-11.315-11.314zM212.852 43.14a56.002 56.002 0 0 0-79.196 0l-28.284 28.284a8 8 0 1 0 11.314 11.314l28.284-28.284a40 40 0 0 1 56.568 56.567l-28.285 28.285a8 8 0 0 0 11.315 11.314l28.284-28.284a56.065 56.065 0 0 0 0-79.196z" fill="currentColor"></path></svg></span></a> <span>Understanding next token prediction.</span></h2></div><div><p data-svelte-h="svelte-1lio3tg">LLMs are said to be <strong>autoregressive</strong>, meaning that <strong>the output from one pass becomes the input for the next one</strong>. This loop continues until the model predicts the next token to be the EOS token, at which point the model can stop.</p></div><div><p data-svelte-h="svelte-1nrwqur">While the full process can be quite technical for the purpose of learning agents, here’s a brief overview:</p></div><div><li>Once the input text is <strong>tokenized</strong>, the model computes a representation of the sequence that captures information about the meaning and the position of each token in the input sequence.</li></div><div><li>This representation goes into the model, which outputs scores that rank the likelihood of each token in its vocabulary as being the next one in the sequence.</li></div><div><img src="https://huggingface.co/datasets/agents-course/course-images/resolve/main/en/unit1/DecodingFinal.gif" alt="Visual Gif of decoding" width="60%"></div><div><h2 class="relative group"><a id="attention-is-all-you-need" class="header-link block pr-1.5 text-lg no-hover:hidden with-hover:absolute with-hover:p-1.5 with-hover:opacity-0 with-hover:group-hover:opacity-100 with-hover:right-full" href="https://huggingface.co/learn/agents-course/unit1/#attention-is-all-you-need"><span><svg class="" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" preserveAspectRatio="xMidYMid meet" viewBox="0 0 256 256"><path d="M167.594 88.393a8.001 8.001 0 0 1 0 11.314l-67.882 67.882a8 8 0 1 1-11.314-11.315l67.882-67.881a8.003 8.003 0 0 1 11.314 0zm-28.287 84.86l-28.284 28.284a40 40 0 0 1-56.567-56.567l28.284-28.284a8 8 0 0 0-11.315-11.315l-28.284 28.284a56 56 0 0 0 79.196 79.197l28.285-28.285a8 8 0 1 0-11.315-11.314zM212.852 43.14a56.002 56.002 0 0 0-79.196 0l-28.284 28.284a8 8 0 1 0 11.314 11.314l28.284-28.284a40 40 0 0 1 56.568 56.567l-28.285 28.285a8 8 0 0 0 11.315 11.314l28.284-28.284a56.065 56.065 0 0 0 0-79.196z" fill="currentColor"></path></svg></span></a> <span>Attention is all you need</span></h2></div><div><p data-svelte-h="svelte-pndgar">A key aspect of the Transformer architecture is <strong>Attention</strong>. When predicting the next word,
not every word in a sentence is equally important; words like “France” and “capital” in the sentence <em>“The capital of France is …”</em> carry the most meaning.</p></div><div><h2 class="relative group"><a id="prompting-the-llm-is-important" class="header-link block pr-1.5 text-lg no-hover:hidden with-hover:absolute with-hover:p-1.5 with-hover:opacity-0 with-hover:group-hover:opacity-100 with-hover:right-full" href="https://huggingface.co/learn/agents-course/unit1/#prompting-the-llm-is-important"><span><svg class="" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" preserveAspectRatio="xMidYMid meet" viewBox="0 0 256 256"><path d="M167.594 88.393a8.001 8.001 0 0 1 0 11.314l-67.882 67.882a8 8 0 1 1-11.314-11.315l67.882-67.881a8.003 8.003 0 0 1 11.314 0zm-28.287 84.86l-28.284 28.284a40 40 0 0 1-56.567-56.567l28.284-28.284a8 8 0 0 0-11.315-11.315l-28.284 28.284a56 56 0 0 0 79.196 79.197l28.285-28.285a8 8 0 1 0-11.315-11.314zM212.852 43.14a56.002 56.002 0 0 0-79.196 0l-28.284 28.284a8 8 0 1 0 11.314 11.314l28.284-28.284a40 40 0 0 1 56.568 56.567l-28.285 28.285a8 8 0 0 0 11.315 11.314l28.284-28.284a56.065 56.065 0 0 0 0-79.196z" fill="currentColor"></path></svg></span></a> <span>Prompting the LLM is important</span></h2></div><div>Careful design of the prompt makes it easier <strong>to guide the generation of the LLM toward the desired output</strong>.</div>