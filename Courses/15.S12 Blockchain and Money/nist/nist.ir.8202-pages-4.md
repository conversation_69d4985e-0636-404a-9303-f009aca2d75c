incentivized to discard their current work and to start building off the newly received block instead because they know the other publishing nodes will be building off it.

As an example, consider a puzzle where, using the SHA-256 algorithm, a computer must find a hash value meeting the following target criteria (known as the difficulty level):

SHA256("blockchain" + Nonce) = Hash Digest starting with "**000000**"

In this example, the text string "blockchain" is appended with a nonce value and then the hash digest is calculated. The nonce values used will be numeric values only. This is a relatively easy puzzle to solve and some sample output follows:

```
SHA256("blockchain0") = 
0xbd4824d8ee63fc82392a6441444166d22ed84eaa6dab11d4923075975acab938
(not solved)
SHA256("blockchain1") = 
0xdb0b9c1cb5e9c680dfff7482f1a8efad0e786f41b6b89a758fb26d9e223e0a10
(not solved)
…
SHA256("blockchain10730895") = 
0x000000ca1415e0bec568f6f605fcc83d18cac7a4e6c219a957c10c6879d67587
(solved)
```
To solve this puzzle, it took 10,730,896 guesses (completed in 54 seconds on relatively old hardware, starting at 0 and testing one value at a time).

In this example, each additional "leading zero" value increases the difficulty. By increasing the target by one additional leading zero ("**0000000**"), the same hardware took 934,224,175 guesses to solve the puzzle (completed in 1 hour, 18 minutes, 12 seconds):

```
SHA256("blockchain934224174") = 
0x0000000e2ae7e4240df80692b7e586ea7a977eacbd031819d0e603257edb3a81
```
There is currently no known shortcut to this process; publishing nodes must expend computation effort, time, and resources to find the correct nonce value for the target. Often the publishing nodes attempt to solve this computationally difficult puzzle to claim a reward of some sort (usually in the form of a cryptocurrency offered by the blockchain network). The prospect of being rewarded for extending and maintaining the blockchain is referred to as a reward system or incentive model.

Once a publishing node has performed this work, they send their block with a valid nonce to full nodes in the blockchain network. The recipient full nodes verify that the new block fulfills the puzzle requirement, then add the block to their copy of the blockchain and resend the block to their peer nodes. In this manner, the new block gets quickly distributed throughout the network of participating nodes. Verification of the nonce is easy since only a single hash needs to be done to check to see if it solves the puzzle.

For many proof of work based blockchain networks, publishing nodes tend to organize

themselves into "pools" or "collectives" whereby they work together to solve puzzles and split the reward. This is possible because work can be distributed between two or more nodes across a collective to share the workload and rewards. Splitting the example program into quarters, each node can take an equal amount of the nonce value range to test:

- Node 1: check nonce 0000000000 to 0536870911
- Node 2: check nonce 0536870912 to 1073741823
- Node 3: check nonce 1073741824 to 1610612735
- Node 4: check nonce 1610612736 to 2147483647

The following result was the first to be found to solve the puzzle:

```
SHA256("blockchain1700876653") = 
0x00000003ba55d20c9cbd1b6fb34dd81c3553360ed918d07acf16dc9e75d7c7f1
```
This is a completely new nonce, but still one that solved the puzzle. It took 90,263,918 guesses (completed in 10 minutes, 14 seconds). Dividing up the work amongst many more machines yields much better results, as well as more consistent rewards in a proof of work model.

The use of a computationally difficult puzzle helps to combat the "Sybil Attack" – a computer security attack (not limited to blockchain networks) where an attacker can create many nodes (i.e., creating multiple identities) to gain influence and exert control. The proof of work model combats this by having the focus of network influence being the amount of computational power (hardware, which costs money) mixed with a lottery system (the most hardware increases likelihood but does not guarantee it) versus in network identities (which are generally costless to create).

### **4.2 Proof of Stake Consensus Model**

The proof of stake (PoS) model is based on the idea that the more stake a user has invested into the system, the more likely they will want the system to succeed, and the less likely they will want to subvert it. Stake is often an amount of cryptocurrency that the blockchain network user has invested into the system (through various means, such as by locking it via a special transaction type, or by sending it to a specific address, or holding it within special wallet software). Once staked, the cryptocurrency is generally no longer able to be spent. Proof of stake blockchain networks use the amount of stake a user has as a determining factor for publishing new blocks. Thus, the likelihood of a blockchain network user publishing a new block is tied to the ratio of their stake to the overall blockchain network amount of staked cryptocurrency.

With this consensus model, there is no need to perform resource intensive computations (involving time, electricity, and processing power) as found in proof of work. Since this consensus model utilizes fewer resources, some blockchain networks have decided to forego a block creation reward; these systems are designed so that all the cryptocurrency is already distributed among users rather than new cryptocurrency being generated at a constant pace. In such systems, the reward for block publication is then usually the earning of user provided transaction fees.

The methods for how the blockchain network uses the stake can vary. Here we discuss four

approaches: random selection of staked users, multi-round voting, coin aging systems and delegate systems. Regardless of the exact approach, users with more stake are more likely to publish new blocks.

When the choice of block publisher is a random choice (sometimes referred to as *chain-based proof of stake*), the blockchain network will look at all users with stake and choose amongst them based on their ratio of stake to the overall amount of cryptocurrency staked. So, if a user had 42 % of the entire blockchain network stake they would be chosen 42 % of the time; those with 1 % would be chosen 1 % of the time.

When the choice of block publisher is a multi-round voting system (sometime referred to as *Byzantine fault tolerance proof of stake* [\[12\])](#page--1-0) there is added complexity. The blockchain network will select several staked users to create proposed blocks. Then all staked users will cast a vote for a proposed block. Several rounds of voting may occur before a new block is decided upon. This method allows all staked users to have a voice in the block selection process for every new block.

When the choice of block publisher is through a coin age system referred to as a *coin age proof of stake,* staked cryptocurrency has an *age* property. After a certain amount of time (such as 30 days) the staked cryptocurrency can *count* towards the owning user being selected to publish the next block. The staked cryptocurrency then has its *age* reset, and it cannot be used again until after the requisite time has passed. This method allows for users with more stake to publish more blocks, but to not dominate the system – since they have a cooldown timer attached to every cryptocurrency coin *counted* towards creating blocks. Older coins and larger groups of coins will increase the probability of being chosen to publish the next block. To prevent stakeholders from hoarding aged cryptocurrencies, there is generally a built-in maximum to the probability of winning.

When the choice of block publisher is through a delegate system, users vote for nodes to become publishing nodes – therefore creating blocks on their behalf. Blockchain network users' voting power is tied to their stake so the larger the stake, the more weight the vote has. Nodes who receive the most votes become publishing nodes and can validate and publish blocks. Blockchain network users can also vote against an established publishing node, to try to remove them from the set of publishing nodes. Voting for publishing nodes is continuous and remaining a publishing node can be quite competitive. The threat of losing publishing node status, and therefore rewards and reputation is constant so publishing nodes are incentivized to not act maliciously. Additionally, blockchain network users vote for delegates, who participate in the governance of the blockchain. Delegates will propose changes, and improvements, which will be voted on by blockchain network users.

It is worth noting that a problem known as "nothing at stake" may arise from some proof of stake algorithms. If multiple competing blockchains were to exist at some point (because of a temporary ledger conflict as discussed in Section [4.7)](#page-7-0), a staked user could act on every such competing chain – since it is essentially free to do so. The staked user may do this as a way of increasing their odds of earning a reward. This can cause multiple blockchain branches to continue to grow without being reconciled into a singular branch for extended periods of time.

Under proof of stake systems, the "rich" can more easily stake more of the digital assets, earning themselves more digital assets; however, to obtain the majority of digital assets within a system to "control" it is generally cost prohibitive.

### **4.3 Round Robin Consensus Model**

Round Robin is a consensus model that is used by some permissioned blockchain networks. Within this model of consensus, nodes take turns in creating blocks. Round Robin Consensus has a long history grounded in distributed system architecture. To handle situations where a publishing node is not available to publish a block on its turn, these systems may include a time limit to enable available nodes to publish blocks so that unavailable nodes will not cause a halt in block publication. This model ensures no one node creates the majority of the blocks. It benefits from a straightforward approach, lacks cryptographic puzzles, and has low power requirements.

Since there is a need for trust amongst nodes, round robin does not work well in the permissionless blockchain networks used by most cryptocurrencies. This is because malicious nodes could continuously add additional nodes to increase their odds of publishing new blocks. In the worst case, they could use this to subvert the correct operation of the blockchain network.

# **4.4 Proof of Authority/Proof of Identity Consensus Model**

The proof of authority (also referred to as proof of identity) consensus model relies on the partial trust of publishing nodes through their known link to real world identities. Publishing nodes must have their identities proven and verifiable within the blockchain network (e.g., identifying documents which have been verified and notarized and included on the blockchain). The idea is that the publishing node is staking its identity/reputation to publish new blocks. Blockchain network users directly affect a publishing node's reputation based on the publishing node's behavior. Publishing nodes can lose reputation by acting in a way that the blockchain network users disagree with, just as they can gain reputation by acting in a manner that the blockchain network users agree with. The lower the reputation, the less likelihood of being able to publish a block. Therefore, it is in the interest of a publishing node to maintain a high reputation. This algorithm only applies to permissioned blockchain networks with high levels of trust.

# **4.5 Proof of Elapsed Time Consensus Model**

Within the proof of elapsed time (PoET) consensus model, each publishing node requests a wait time from a secure hardware time source within their computer system. The secure hardware time source will generate a random wait time and return it to the publishing node software. Publishing nodes take the random time they are given and become idle for that duration. Once a publishing node wakes up from the idle state, it creates and publishes a block to the blockchain network, alerting the other nodes of the new block; any publishing node that is still idle will stop waiting, and the entire process starts over.

This model requires ensuring that a random time was used, since if the time to wait was not selected at random a malicious publishing node would just wait the minimum amount of time by default to dominate the system. This model also requires ensuring that the publishing node waited the actual time and did not start early. These requirements are being solved by executing

software in a trusted execution environment found on some computer processors (such as Intel's Software Guard Extensions[5](#page-4-0) , or AMD's Platform Security Processor[6](#page-4-1) , or ARM's TrustZone[7](#page-4-2) ).

Verified and trusted software can run in these secure execution environments and cannot be altered by outside programs. A publishing node would query software running in this secure environment for a random time and then wait for that time to pass. After waiting the assigned time, the publishing node could request a signed certificate that the publishing node waited the randomly assigned time. The publishing node then publishes the certificate along with the block.

<span id="page-4-0"/><sup>5</sup> Intel SGX - <https://software.intel.com/en-us/sgx>

<span id="page-4-1"/><sup>6</sup> AMD Secure Technology - <https://www.amd.com/en/technologies/security>

<span id="page-4-2"/><sup>7</sup> ARM TrustZone - <https://www.arm.com/products/silicon-ip-security>

#### **4.6 Consensus Comparison Matrix**

| Name                    | Goals                                                                                                                                                                                                                                                                     | Advantages                                                                                                                              | Disadvantages                                                                                                                                                                                                                                                                                                                | Domains                                                       | Implementations                      |
|-------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------|--------------------------------------|
| Proof of work<br>(PoW)  | To provide a<br>barrier to<br>publishing blocks<br>in the form of a<br>computationally<br>difficult puzzle<br>to<br>solve to enable<br>transactions<br>between untrusted<br>participants.                                                                                 | Difficult to perform denial of service by<br>flooding network with bad blocks.<br>Open to anyone with hardware to solve the<br>puzzle.  | Computationally intensive (by design),<br>power consumption, hardware arms race.<br>Potential for 51<br>% attack<br>by obtaining<br>enough computational power.                                                                                                                                                              | Permissionless<br>cryptocurrencies                            | Bitcoin,<br>Ethereum, many<br>more   |
| Proof of stake<br>(PoS) | To enable a less<br>computationally<br>intensive barrier<br>to publishing<br>blocks, but still<br>enable<br>transactions<br>between untrusted<br>participants.                                                                                                            | Less computationally intensive than PoW.<br>Open to anyone who wishes to stake<br>cryptocurrencies.<br>Stakeholders control the system. | Stakeholders control the system.<br>Nothing to prevent formation of a pool of<br>stakeholders to create a centralized power.<br>Potential for 51<br>% attack by obtaining<br>enough financial power.                                                                                                                         | Permissionless<br>cryptocurrencies                            | Ethereum<br>Casper,<br>Krypton       |
| Delegated PoS           | To enable a more<br>efficient<br>consensus model<br>through a 'liquid<br>democracy'<br>where<br>participants vote<br>(using<br>cryptographically<br>signed messages)<br>to elect and<br>revoke the rights<br>of delegates to<br>validate and<br>secure the<br>blockchain. | Elected delegates are economically<br>incentivized to remain honest<br>More computationally efficient than PoW                          | Less node diversity than PoW<br>or pure PoS<br>consensus implementations<br>Greater security risk for node compromise<br>due to constrained set of operating nodes<br>As all delegates are 'known' there may an<br>incentive for block producers to collude<br>and accept bribes, compromising the<br>security of the system | Permissionless<br>cryptocurrencies<br>Permissioned<br>Systems | Bitshares,<br>Steem, Cardano,<br>EOS |

| Name                               | Goals                                                                                                                                                           | Advantages                                                                                                                                                          | Disadvantages                                                                                                                                                                                                                                              | Domains                                                   | Implementations                                                                                |
|------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------|------------------------------------------------------------------------------------------------|
| Round Robin                        | Provide a system<br>for publishing<br>blocks amongst<br>approved/trusted<br>publishing nodes                                                                    | Low computational power.<br>Straightforward to understand.                                                                                                          | Requires large amount of trust amongst<br>publishing nodes.                                                                                                                                                                                                | Permissioned<br>Systems                                   | MultiChain                                                                                     |
| Proof of<br>Authority/Identity     | To create a<br>centralized<br>consensus process<br>to minimize block<br>creation and<br>confirmation rate                                                       | Fast confirmation time<br>Allows for dynamic block production rates<br>Can be used in sidechains to blockchain<br>networks which utilize another<br>consensus model | Relies on the assumption that the current<br>validating node has not been compromised<br>Leads to centralized points of failure<br>The reputation of a given node is subject<br>to potential for high tail-risk as it could be<br>compromised at any time. | Permissioned<br>Systems, Hybrid<br>(sidechain)<br>Systems | Ethereum<br>Kovan testnet,<br>POA Chain,<br>various<br>permissioned<br>systems using<br>Parity |
| Proof of Elapsed<br>Time<br>(PoET) | To enable a more<br>economic<br>consensus model<br>for blockchain<br>networks, at the<br>expense of deeper<br>security<br>guarantees<br>associated with<br>PoW. | Less computationally expensive than PoW                                                                                                                             | Hardware requirement to obtain time.<br>Assumes the hardware clock used to<br>derive time is not compromised<br>Given speed-of-late latency limits, true<br>time synchronicity is essentially<br>impossible in distributed systems [13]                    | Permissioned<br>Networks                                  | Hyperledger<br>Sawtooth                                                                        |

### <span id="page-7-0"/>**4.7 Ledger Conflicts and Resolutions**

As discussed previously, for some blockchain networks it is possible that multiple blocks will be published at approximately the same time. This can cause differing versions of a blockchain to exist at any given moment; these must be resolved quickly to have consistency in the blockchain network. In this section, we discuss how these situations are generally handled.

With any distributed network, some systems within the network will be behind on information or have alternative information. This depends on network latency between nodes and the proximity of groups of nodes. Permissionless blockchain networks are more prone to have conflicts due to their openness and number of competing publishing nodes. A major part of agreeing on the state of the blockchain network (coming to consensus) is resolving conflicting data.

For example:

- node_A creates block_n(A)with transactions #1, 2 and 3. node_A distributes it to some nodes.
- node_B creates block_n(B)with transactions #1, 2 and 4. node_B distributes it to some nodes.
- **There is a conflict.**
	- o block_n will not be the same across the network.
		- block_n(A) contains transaction #3, but not transaction #4.
		- block_n(B) contains transaction #4, but not transaction #3.

Conflicts temporarily generate different versions of the blockchain, which is depicted in [Figure](#page-7-1)  [4.](#page-7-1) These differing versions are not "wrong"; rather, they were created with the information each node had available. The competing blocks will likely contain different transactions, so those with block_n(A) may see transfers of digital assets not present in block_n(B). If the blockchain network deals with cryptocurrency, then a situation may occur where some cryptocurrency may both be spent and unspent, depending on which version of the blockchain is being viewed.

![](_page_7_Figure_13.jpeg)

**Figure 4: Ledger in Conflict**

<span id="page-7-1"/>Conflicts are usually quickly resolved. Most blockchain networks will wait until the next block is published and use that chain as the "official" blockchain, thus adopting the "longer blockchain". As in [Figure 5,](#page-8-0) the blockchain containing block_n(B) becomes the "official" chain, as it got

the next valid block. Any transaction that was present in block_n(A), the orphaned block, but not present in the block_n(B) chain, is returned to the pending transaction pool (which is where all transactions which have not been included within a block reside). Note that this set of pending transactions is maintained locally at each node as there is no central server in the architecture.

![](_page_8_Figure_3.jpeg)

<span id="page-8-0"/>**Figure 5: The chain with block_n(B) adds the next block, the chain with block_n(A) is now orphaned**

Due to the possibility of blocks being overwritten, a transaction is not usually accepted as confirmed until several additional blocks have been created on top of the block containing the relevant transaction. The acceptance of a block is often probabilistic rather than deterministic since blocks can be superseded. The more blocks that have been built on top of a published block, the more likely it is that the initial block will not be overwritten.

Hypothetically, a node in a proof of work blockchain network with enormous amounts of computing power could start at the genesis block and create a longer chain than the currently existing chain, thereby wiping out the entire blockchain history. This does not happen in practice due to the prohibitively large amount of resources that this would require. Also, some blockchain implementations lock specific older blocks within the blockchain software by creating checkpoints to ensure that this can never happen.

### **5 Forking**

Performing changes and updating technology can be difficult at the best of times. For permissionless blockchain networks which are comprised of many users, distributed around the world, and governed by the consensus of the users, it becomes extremely difficult. Changes to a blockchain network's protocol and data structures are called *forks.* They can be divided into two categories: *soft forks* and *hard forks*. For a soft fork, these changes are backwards compatible with nodes that have not been updated. For a hard fork, these changes are not backwards compatible because the nodes that have not been updated will reject the blocks following the changes. This can lead to a split in the blockchain network creating multiple versions of the same blockchain. Permissioned blockchain networks, due to the publishing nodes and users being known, can mitigate the issues of forking by requiring software updates.

Note that the term fork is also used by some blockchain networks to describe temporary ledger conflicts (e.g., two or more blocks within the blockchain network with the same block number) as described in Section [4.7.](#page-7-0) While this is a fork in the ledger, it is temporary and does not stem from a software change.

# **5.1 Soft Forks**

A *soft fork* is a change to a blockchain implementation that is backwards compatible. Nonupdated nodes can continue to transact with updated nodes. If no (or very few) nodes upgrade, then the updated rules will not be followed.

An example of a soft fork occurred on Bitcoin when a new rule was added to support escrow[8](#page-9-0) and time-locked refunds. In 2014, a proposal was made to repurpose an operation code that performed no operation (OP_NOP2) to CHECKLOCKTIMEVERIFY, which allows a transaction output to be made spendable at a point in the future [\[14\].](#page--1-2) For nodes that implement this change, the node software will perform this new operation, but for nodes that do not support the change, the transaction is still valid, and execution will continue as if a NOP [9](#page-9-1) had been executed.

A fictional example of a soft fork would be if a blockchain decided to reduce the size of blocks (for example from 1.0 MB to 0.5 MB). Updated nodes would adjust the block size and continue to transact as normal; non-updated nodes would see these blocks as valid – since the change made does not violate their rules (i.e., the block size is under their maximum allowed). However, if a non-updated node were to create a block with a size greater than 0.5 MB, updated nodes would reject them as invalid.

# **5.2 Hard Forks**

A *hard fork* is a change to a blockchain implementation that is not backwards compatible. At a

<span id="page-9-0"/><sup>8</sup> Funds placed into a third party to be disseminated based on conditions (via multi-signature transactions)

<span id="page-9-1"/><sup>9</sup> NOP meaning No Operation