---
tags:
  - flash
---
[[SAML]] 
[[claim based access]]
## Ref
- [Microsoft Azure](https://portal.azure.com/?microsoft_azure_marketplace_ItemHideKey=328e1f88-4686-4ec0-b793-c767b53cb238#view/Microsoft_AAD_IAM/StartboardApplicationsMenuBlade/~/Overview)
- [Identity and Access Management: Technical Overview - YouTube](https://www.youtube.com/watch?v=Tcvsefz5DmA)
- [A Developer's Guide to SAML - YouTube](https://www.youtube.com/watch?v=l-6QSEqDJPo&t=1413s)
- [SAML 2.0: Technical Overview - YouTube](https://www.youtube.com/watch?v=SvppXbpv-5k)
## Notes
- What is identity and access management (IAM)?
	- 提供用户验证和资源访问的控制。
- The difference between authentication and authorization
	- authentication: 验证用户是否属于这个系统
	- authorization: 验证用户是否有权限访问当前的资源
- IAM解决方案
	- 多用户身份来源：用户期望能够使用各种社交（如谷歌或领英）、企业（如微软活动目录）和其他身份提供者进行登录
	- 多因素认证（MFA）
	- 基于角色的访问控制（RBAC）
- Identity providers
