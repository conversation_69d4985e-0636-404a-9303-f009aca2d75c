---
title: "What are LLMs? - Hugging Face Agents Course"
source: "https://huggingface.co/learn/agents-course/unit1/actions"
author:
published:
created: 2025-03-28
description: "We’re on a journey to advance and democratize artificial intelligence through open source and open science."
tags:
  - "clippings"
---
动作是 AI 代理与环境交互所采取的具体步骤。

  
无论是浏览网页获取信息还是控制物理设备，每个动作都是代理有意执行的操作。

例如，一个协助客户服务的代理可能会检索客户数据、提供支持文章或将问题转交给人工代表。

## Types of Agent Actions

  
存在多种类型的代理，它们采取行动的方式各不相同：

| Type of Agent | Description |
| --- | --- |
| JSON Agent | The Action to take is specified in JSON format. |
| Code Agent | 该代理编写了一个代码块，由外部解释执行。 |
| Function-calling Agent | 这是 JSON 代理的一个子类别，经过微调后能为每个动作生成新的消息。 |

Actions themselves can serve many purposes:

| Type of Action | Description |
| --- | --- |
| Information Gathering | 执行网络搜索、查询数据库或检索文档。 |
| Tool Usage | 进行 API 调用、运行计算和执行代码。 |
| Environment Interaction | 操作数字界面或控制物理设备。 |
| Communication | 通过聊天与用户互动或与其他代理协作。 |

智能体的一个关键能力是在动作完成后能够停止生成新令牌，这对所有格式的智能体（JSON、代码或函数调用）都适用。这避免了意外输出，确保智能体的响应清晰且精确。

## Code Agents

  
另一种方法是使用代码代理。其核心思想是：代码代理不输出简单的 JSON 对象，而是生成一个可执行的代码块——通常采用如 Python 这样的高级语言。

![Code Agents](https://huggingface.co/datasets/agents-course/course-images/resolve/main/en/unit1/code-vs-json-actions.png)