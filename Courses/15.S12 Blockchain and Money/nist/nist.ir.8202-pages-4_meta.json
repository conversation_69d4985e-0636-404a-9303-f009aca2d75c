{"table_of_contents": [{"title": "4.2 Proof of Stake Consensus Model", "heading_level": null, "page_id": 1, "polygon": [[70.59814453125, 422.296875], [273.12890625, 422.296875], [273.12890625, 436.3830261230469], [70.59814453125, 436.3830261230469]]}, {"title": "4.3 Round <PERSON>", "heading_level": null, "page_id": 3, "polygon": [[71.2705078125, 124.5234375], [267.0029296875, 124.5234375], [267.0029296875, 138.638671875], [71.2705078125, 138.638671875]]}, {"title": "4.4 Proof of Authority/Proof of Identity Consensus Model", "heading_level": null, "page_id": 3, "polygon": [[72.0, 324.45703125], [379.810546875, 324.45703125], [379.810546875, 339.15234375], [72.0, 339.15234375]]}, {"title": "4.5 Proof of Elapsed Time Consensus Model", "heading_level": null, "page_id": 3, "polygon": [[71.8681640625, 512.7890625], [314.96484375, 512.7890625], [314.96484375, 527.484375], [71.8681640625, 527.484375]]}, {"title": "4.6 Consensus Comparison Matrix", "heading_level": null, "page_id": 5, "polygon": [[72.0, 72.4658203125], [262.6665954589844, 72.4658203125], [262.6665954589844, 85.2030029296875], [72.0, 85.2030029296875]]}, {"title": "4.7 Ledger Conflicts and Resolutions", "heading_level": null, "page_id": 7, "polygon": [[70.44873046875, 71.4462890625], [277.013671875, 71.4462890625], [277.013671875, 85.271484375], [70.44873046875, 85.271484375]]}, {"title": "5 Forking", "heading_level": null, "page_id": 9, "polygon": [[72.0, 73.86328125], [144.79200744628906, 73.86328125], [144.79200744628906, 87.9307861328125], [72.0, 87.9307861328125]]}, {"title": "5.1 Soft Forks", "heading_level": null, "page_id": 9, "polygon": [[68.991943359375, 316.72265625], [155.8388671875, 316.72265625], [155.8388671875, 331.8046875], [68.991943359375, 331.8046875]]}, {"title": "5.2 Hard Forks", "heading_level": null, "page_id": 9, "polygon": [[70.635498046875, 597.8671875], [158.9765625, 597.8671875], [158.9765625, 612.6030120849609], [70.635498046875, 612.6030120849609]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 154], ["Line", 41], ["Text", 9], ["<PERSON><PERSON><PERSON><PERSON>", 3], ["Code", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 178], ["Line", 42], ["Text", 7], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 3], ["Code", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 183], ["Line", 45], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 185], ["Line", 43], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 3], ["SectionHeader", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 60], ["Line", 18], ["<PERSON><PERSON><PERSON><PERSON>", 3], ["Footnote", 3], ["Reference", 3], ["Text", 2], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 205], ["Line", 89], ["TableCell", 24], ["<PERSON><PERSON><PERSON><PERSON>", 3], ["SectionHeader", 1], ["Table", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 155], ["Line", 68], ["TableCell", 24], ["<PERSON><PERSON><PERSON><PERSON>", 3], ["Table", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 141], ["Line", 33], ["ListItem", 6], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 3], ["Reference", 2], ["SectionHeader", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 67], ["Line", 20], ["<PERSON><PERSON><PERSON><PERSON>", 3], ["Text", 3], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 215], ["Line", 41], ["Text", 7], ["<PERSON><PERSON><PERSON><PERSON>", 3], ["SectionHeader", 3], ["Footnote", 2], ["Reference", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}], "debug_data_path": "debug_data/nist.ir.8202-pages-4"}