# 📋 Projects Overview

> [!info]+ Project Dashboard
> Central hub for tracking all projects, courses, and books across your vault.

---

## 🚀 Projects

### Active Projects
```dataview
TABLE
  status as Status,
  (round((progress / target) * 100) + "%") as Progress,
  started as "Started"
FROM #project
WHERE category = "project" AND !contains(file.path, "Template/")
SORT file.mtime DESC
```

### Project Statistics
```dataview
TABLE WITHOUT ID
  length(rows) as "Count"
FROM #project
WHERE category = "project" AND !contains(file.path, "Template/")
GROUP BY choice(contains(string(status), "Progress"), "🔄 In Progress", choice(contains(string(status), "Complete"), "✅ Complete", choice(contains(string(status), "Paused"), "⏸️ Paused", "📋 Other"))) as "Status"
SORT "Count" DESC
```

---

## 📚 Courses

### Active Courses
```dataview
TABLE
  status as Status,
  (round((progress / target) * 100) + "%") as Progress,
  started as "Started",
  deadline as "Deadline",
  instructor as "Instructor"
FROM #course
WHERE !contains(file.path, "Template/")
SORT file.mtime DESC
```

### Course Progress Summary
```dataview
TABLE WITHOUT ID
  length(rows) as "Count",
  round(average(progress / target * 100), 1) + "%" as "Avg Progress"
WHERE #course AND !contains(file.path, "Template/")
GROUP BY choice(
  status = "🔄 In Progress", "🔄 In Progress",
  status = "✅ Complete", "✅ Complete",
  status = "⏸️ Paused", "⏸️ Paused",
  "📚 Other"
) as "Status"
SORT "Count" DESC
```

---

## 📖 Books

### Currently Reading
```dataview
TABLE
  author as Author,
  status as Status,
  rating as Rating,
  (round((progress / target) * 100) + "%") as Progress,
  started as "Started"
FROM #book
WHERE !contains(file.path, "Template/")
SORT file.mtime DESC
```

### Reading Statistics
```dataview
TABLE WITHOUT ID
  length(rows) as "Count",
  round(average(rating), 1) as "Avg Rating"
WHERE #book AND !contains(file.path, "Template/")
GROUP BY choice(
  status = "🔄 In Progress", "📖 Reading",
  status = "✅ Complete", "✅ Finished",
  status = "⏸️ Paused", "⏸️ Paused",
  "📚 Other"
) as "Status"
SORT "Count" DESC
```

---

## 📊 Overall Statistics

### All Projects Summary
```dataview
TABLE WITHOUT ID
  category as "Category",
  length(rows) as "Total",
  length(filter(rows, (r) => contains(r.status, "Progress"))) as "In Progress",
  length(filter(rows, (r) => contains(r.status, "Complete"))) as "Completed",
  round(length(filter(rows, (r) => contains(r.status, "Complete"))) / length(rows) * 100, 1) + "%" as "Completion Rate"
FROM #project
WHERE !contains(file.path, "Template/")
GROUP BY category
SORT "Total" DESC
```

### Recent Activity
```dataview
LIST file.mtime as "Last Updated"
FROM #project
WHERE !contains(file.path, "Template/")
SORT file.mtime DESC
LIMIT 10
```

---

## 🎯 Quick Actions

### Templates
- [[Template/Project|📋 New Project]]
- [[Course|📚 New Course]]
- [[Book|📖 New Book]]

### Views
- [[#Projects|🚀 Projects Only]]
- [[#Courses|📚 Courses Only]]
- [[#Books|📖 Books Only]]

---

*Last updated: `=this.file.mtime`*