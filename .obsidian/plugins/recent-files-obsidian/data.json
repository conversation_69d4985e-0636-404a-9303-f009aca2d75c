{"recentFiles": [{"basename": "home", "path": "home.md"}, {"basename": "Inbox", "path": "Inbox.md"}, {"basename": "guitar_practice", "path": "guitar_practice.md"}, {"basename": "金钱博弈", "path": "Books/金钱博弈.md"}, {"basename": "projects_index", "path": "projects_index.md"}, {"basename": "门将之死 罗伯特恩克的故事", "path": "Books/门将之死 罗伯特恩克的故事.md"}, {"basename": "资源Resources", "path": "Notes/资源Resources.md"}, {"basename": "AI killed my coding brain but I'm rebuilding it", "path": "News/AI killed my coding brain but I'm rebuilding it.md"}, {"basename": "Deep Learning By 3blue1brown", "path": "Courses/Deep Learning By 3blue1brown.md"}, {"basename": "choices", "path": "choices.md"}, {"basename": "Project", "path": "Template/Project.md"}, {"basename": "Build an LLM Trading Agent with Natural Language & MCP Server", "path": "News/Build an LLM Trading Agent with Natural Language & MCP Server.md"}, {"basename": "传统前端开发正在消亡", "path": "News/传统前端开发正在消亡.md"}, {"basename": "News", "path": "Template/News.md"}, {"basename": "Huggingface LLM Course", "path": "Courses/Huggingface LLM Course.md"}, {"basename": "Mediation Practice", "path": "Template/Mediation Practice.md"}, {"basename": "Guitar Practice", "path": "Template/Guitar Practice.md"}, {"basename": "灵感Ideas", "path": "Notes/灵感Ideas.md"}, {"basename": "How to take notes in Obsidian", "path": "How to take notes in Obsidian.md"}, {"basename": "Book", "path": "Template/Book.md"}, {"basename": "Course", "path": "Template/Course.md"}, {"basename": "碎片知识News", "path": "碎片知识News.md"}, {"basename": "这是我的30岁生日礼物", "path": "News/这是我的30岁生日礼物.md"}, {"basename": "Felix Hill临终信公开：耗时18个月写完，AI天才的挣扎与告别", "path": "News/Felix Hill临终信公开：耗时18个月写完，AI天才的挣扎与告别.md"}, {"basename": "得了不治之症后，我卖命存了100万留给老婆", "path": "News/得了不治之症后，我卖命存了100万留给老婆.md"}, {"basename": "可能是方大同新专辑的唯一专访!丨真假方大同终于同框丨HOPICO", "path": "News/可能是方大同新专辑的唯一专访!丨真假方大同终于同框丨HOPICO.md"}, {"basename": "倍速人生：97年尘肺夫妻的爱与绝望", "path": "News/倍速人生：97年尘肺夫妻的爱与绝望.md"}, {"basename": "三个小时，我救回了我爸的命 - 小红书", "path": "News/三个小时，我救回了我爸的命 - 小红书.md"}, {"basename": "<PERSON>aker Living as a pro gamer is like a rollercoaster", "path": "News/Faker Living as a pro gamer is like a rollercoaster.md"}, {"basename": "EP 58. 你所不知道的AI产品，哪些正在“闷声赚大钱”？", "path": "News/EP 58. 你所不知道的AI产品，哪些正在“闷声赚大钱”？.md"}, {"basename": "埃及志愿者从埃及进入加沙被阻拦", "path": "News/埃及志愿者从埃及进入加沙被阻拦.md"}, {"basename": "陶哲轩罕见长长长长长访谈：数学、AI和给年轻人的建议", "path": "News/陶哲轩罕见长长长长长访谈：数学、AI和给年轻人的建议.md"}, {"basename": "脑科医生不常告诉你的事", "path": "News/脑科医生不常告诉你的事.md"}, {"basename": "年轻人不考研，去考公了", "path": "News/年轻人不考研，去考公了.md"}, {"basename": "吴恩达谈GenAI工程师", "path": "News/吴恩达谈GenAI工程师.md"}, {"basename": "CS50AI", "path": "Courses/CS50/CS50AI.md"}, {"basename": "A Matter of Death and Life", "path": "Books/A Matter of Death and Life.md"}, {"basename": "人生观Life lessons", "path": "Notes/人生观Life lessons.md"}, {"basename": "Life lessons", "path": "Archive/Life lessons.md"}, {"basename": "<PERSON>", "path": "Template/Eisenhower Matrix.md"}, {"basename": "Weekly Review", "path": "Template/Weekly Review.md"}, {"basename": "Cornell Notes", "path": "Template/Cornell Notes.md"}, {"basename": "tmp", "path": "tmp.md"}, {"basename": "AI Products", "path": "Programming/AI/AI Products.md"}, {"basename": "tmp-1", "path": "TG/tmp-1.md"}, {"basename": "Access k8s pod to check tg logs", "path": "TG/Access k8s pod to check tg logs.md"}, {"basename": "overview", "path": "Programming/overview.md"}, {"basename": "Swimming", "path": "Notes/Swimming.md"}, {"basename": "Query Parameters", "path": "TG/Query Parameters.md"}, {"basename": "ka<PERSON>n", "path": "Notes/kaobian.md"}], "omittedPaths": [], "omittedTags": [], "updateOn": "file-edit", "omitBookmarks": false}