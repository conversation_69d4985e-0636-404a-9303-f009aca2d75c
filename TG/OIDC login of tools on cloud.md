---
up: "[[TG projects]]"
---
## Process
- ua already login the cloud portal
- ua opens tools on cloud
- ua call `/oidc/authnrequest` to get auth0 auth url
```
{
  "OIDCRequest": "https://auth.tgcloud-dev.com/authorize?organization=org_zlNmd7BDhpdrZRPc\u0026
scope=openid+profile+email\u0026
response_type=id_token+token\u0026
redirect_uri=https%3A%2F%2Ftg-15c0755f-3aca-422b-92cd-d56041e21b80.us-east-dev.i.tgcloud-dev.com%2Fapi%2Fauth%2Foidc%2Fcallback\u0026
nonce=F240X9t9BXncrCnz3ju-XTYC0V2M9t9gmZbF8_k96mg\u0026 client_id=PBBtD7unto4oYb0eNc6KuJbUNyZQXzka\u0026
response_mode=form_post",
  "ssoBinding": "GET"
}
```
- ua is redirected to auth0 auth url(append returnURL to the above URL)
- ua is redirected to GUS `/callback` endpoint with id token
	- GUS call GSQL to authenticate the id token
	- GUS records the id token and maintain a session for the end user
- ua is redirected to target tool URL
## QA
- [x] is the above client_id the same as cloud controller?
## Ref
- [Support+OIDC+Integration+with+Identity+Provider](https://graphsql.atlassian.net/wiki/spaces/~132284906/pages/**********/Support+OIDC+Integration+with+Identity+Provider)
- [OpenID+Connect+Implicit+flow+with+Auth0](https://graphsql.atlassian.net/wiki/spaces/TGC/pages/**********/OpenID+Connect+Implicit+flow+with+Auth0)