---
status: 🔄 In Progress
tags:
  - course
started: 2025-07-07
deadline: 2025-07-11
completed: ""
progress: 50
target: 100
category: course
---
## 🎯 Course Summary

### Why am I taking this course?
<!-- Personal motivation, career goals, or learning objectives -->

---

## 📊 Progress Tracking

> [!success]+ Progress Bar
> ```meta-bind
> INPUT[progressBar(minValue(0), maxValue(100)):progress]
> ```

---

## 📋 Course Structure

---

## 📚 Resources & Materials

### Course Materials
- [Neural networks - YouTube](https://www.youtube.com/playlist?list=PLZHQObOWTQDNU6R1_67000Dx_ZCJB-3pi)
### Additional Resources
---

## Lectures
### Notes
- [Transformers, the tech behind LLMs \| Deep Learning Chapter 5 - YouTube](https://www.youtube.com/watch?v=wjZofJX0v4M&list=PLZHQObOWTQDNU6R1_67000Dx_ZCJB-3pi&index=6)
	- token表示为数值向量
		- token表，gpt3包含5w多个token，每个向量size是12288
		- 向量能表征单词的含义，语义相似的token在空间中的距离越接近
		- 向量可以进行操作，其结果类似于对语义进行操作："man“-”woman“的结果和"king"-"queen"相似
			- 点积：能衡量向量是否对齐
	- 多层网络
		- attention层: 根据context修改token的向量，因为单词的意义往往是根据上下文来的
	- unembedding matrix: 和最后一层的最后一个向量相乘，获得一个跟词表相同大小的向量，用softmax函数转换成概率
	- softmax
		- 将最终计算出的向量转换成概率
		- 设置Temperature可以使小数值获得更大的概率
---

## 🔗 Related Courses & Next Steps

### Prerequisites

### Related

### Next

---

*Course started: `=this.started` | Last updated: `=this.file.mtime` | Completion: `=this.progress`%*