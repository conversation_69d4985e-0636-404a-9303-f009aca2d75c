---
id: hqw71dh5po6yr0yq5c9w137
title: uncertainty
desc: ''
updated: 1708065750081
created: 1707892803416
---
## Children
- [[Project.pagerank]]
- [[Project.Heredity]]
## Source
- https://learning.edx.org/course/course-v1:HarvardX+CS50AI+1T2020/block-v1:HarvardX+CS50AI+1T2020+type@sequential+block@4b8b77db65e54ec9bf3e397b012074bd/block-v1:HarvardX+CS50AI+1T2020+type@vertical+block@f7f87c00eace4aa0b88799eec602e4bf

## Notes
### Probability
- Possible Worlds: To get the probability of an event, we divide the number of worlds in which it occurs by the number of total possible worlds. 
- Axioms in Probability
- events a and b are independent if and only if the probability of a and b is equal to the probability of a times the probability of b: P(a ∧ b) = P(a)P(b).
- Unconditional Probability: no other evidence
### Conditional Probability
- given some evidence
- P(a | b), meaning “the probability of event a occurring given that we know event b to have occurred,”
  ![alt text](image-89.png)
### Bayes’ rule
- Bay<PERSON>’ rule: Knowing P(a | b), in addition to P(a) and P(b), allows us to calculate P(b | a).
  ![alt text](image-90.png)

### Joint Probability
- Using joint probabilities, we can deduce conditional probability. P(C, rain)/P(rain) = αP(C, rain), or α<0.08, 0.02>.

### Probability Rules

### Bayesian Networks
- A Bayesian network is a data structure that represents the dependencies among random variables. Bayesian networks have the following properties:
  - They are directed graphs.
  - Each node on the graph represent a random variable.
  - An arrow from X to Y represents that X is a parent of Y. That is, the probability distribution of Y depends on the value of X.
  - Each node X has probability distribution P(X | Parents(X)).
- Inference by Enumeration
  ![alt text](image-91.png)
- 有一些python库可以生成贝叶斯网络并进行计算，如pomegranate

### Sampling
- Inference by Enumeration可以计算出准确的概率分布，但是效率低，很多时候我们只需要一个估计值即可
- Sampling is one technique of approximate inference. 
- In sampling, each variable is sampled for a value according to its probability distribution. 
- Likelihood Weighting: 

### Markov Models
- The Markov Assumption:  assumption that the current state depends on only a finite fixed number of previous states
- Markov Chain: A Markov chain is a sequence of random variables where the distribution of each variable follows the Markov assumption. 
- transition model:
  ![alt text](image-92.png)
- Using this transition model, it is possible to sample a Markov chain.
  ![alt text](image-93.png)
  Given this Markov chain, we can now answer questions such as “what is the probability of having four rainy days in a row?”

### Hidden Markov Models
