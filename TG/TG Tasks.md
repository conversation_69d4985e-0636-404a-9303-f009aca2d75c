## Scheduled

```tasks
(has scheduled date) OR (has due date)
not done
path includes TG
group by scheduled
```
##  Not done
```tasks
not done
path includes TG
```
## Tasks
- [x] #task [Snyk high severity issues in tools](https://graphsql.atlassian.net/browse/APPS-4107) ➕ 2025-07-21 ⏳ 2025-07-21 ✅ 2025-07-21
- [ ] #task [APPS-4051 Don't return error message when failed to install all queries due to sub-query](https://graphsql.atlassian.net/browse/APPS-4051) ➕ 2025-07-18 ⏳ 2025-07-18
- [x] #task [renaming the file or deleting the filename will trigger the deletion of the vertex](https://graphsql.atlassian.net/browse/TCE-6847) ➕ 2025-07-15 ⏳ 2025-07-15 ✅ 2025-07-15
- [x] #task [Explore graph](https://graphsql.atlassian.net/browse/TCE-6850) ➕ 2025-07-14 ⏳ 2025-07-14 ✅ 2025-07-15
- [x] #task UI review issues: https://docs.google.com/spreadsheets/d/1dd1Au-VQuFzAPBTY5qOmezqtbgQhJ7fPm8X7twKp-NM/edit?gid=0#gid=0 ➕ 2025-07-14 ⏳ 2025-07-14 ✅ 2025-07-18
- [x] #task UI review issues: https://docs.google.com/spreadsheets/d/1dd1Au-VQuFzAPBTY5qOmezqtbgQhJ7fPm8X7twKp-NM/edit?gid=0#gid=0 ➕ 2025-07-11 ✅ 2025-07-14
- [x] #task Review: https://graphsql.atlassian.net/issues/?jql=project%20%3D%20APPS%0AAND%20status%20IN%20%28Open%2C%20Reopened%2C%20%22Pull%20Request%22%2C%20%22In%20Design%22%2C%20%22QA%20Test%22%2C%20%22To%20Do%22%29%0AAND%20type%20%3D%20Bug%0AORDER%20BY%20assignee%20DESC%2C%20priority%20DESC%2C%20created%20DESC ➕ 2025-07-10 ⏳ 2025-07-10 ✅ 2025-07-11
- [x] #task Fix the UI issues in headers ➕ 2025-07-09 ⏳ 2025-07-09 ✅ 2025-07-09
- [ ] #task change billing email and address ➕ 2025-07-08 ⏳ 2025-07-09
- [x] #task publish: solution download site ➕ 2025-07-08 ⏳ 2025-07-08 ✅ 2025-07-08
- [x] #task submit code review: https://github.com/tigergraph/cloud-portal/pull/773 ➕ 2025-07-08 ⏳ 2025-07-08 ✅ 2025-07-08
- [x] #task Tools version display ➕ 2025-07-07 ⏳ 2025-07-07 ✅ 2025-07-08
- [x] #task reproduce interpret issue in 4.2 ➕ 2025-06-24 ✅ 2025-07-14
- [x] #task Fix abort query in old versions ➕ 2025-06-16 ⏳ 2025-06-16 ✅ 2025-07-07
- [x] #task RCA: https://graphsql.atlassian.net/browse/TSE-710 ➕ 2025-06-12 ⏳ 2025-06-13 ✅ 2025-07-07
- [x] #task GUI: check installed status after installing all ➕ 2025-06-11 ⏳ 2025-06-12 ✅ 2025-06-13
- [x] #task Fix ut ➕ 2025-06-09 ⏳ 2025-06-09 ✅ 2025-06-11
- [x] #task RCA for: 3624 4007 ➕ 2025-06-05 ⏳ 2025-06-05 ✅ 2025-06-06
- [x] #task New UI of run query drawer ➕ 2025-06-04 ⏳ 2025-06-04 ✅ 2025-06-04
- [x] #task API: How to use query parameters components ➕ 2025-06-04 ⏳ 2025-06-04 ✅ 2025-06-05
- [x] #task Refresh query tab ➕ 2025-06-04 ⏳ 2025-06-04 ✅ 2025-06-04
- [x] #task UI of query popover ➕ 2025-06-04 ⏳ 2025-06-04 ✅ 2025-06-04
- [x] #task JPMC patch fixes [[Tmp/jpmc patch]] ➕ 2025-05-22 ⏳ 2025-05-26 ✅ 2025-05-27
- [x] #task mit gle#6327 ➕ 2025-05-22 ⏳ 2025-05-26 ✅ 2025-05-27
- [x] #task double merge patch fixes to main: https://github.com/tigergraph/tools/pull/2528 ➕ 2025-05-22 ⏳ 2025-05-26 ✅ 2025-05-27
- [x] #task Merge PR: https://github.com/tigergraph/tools/pull/2518 https://github.com/tigergraph/gus/pull/1098 ➕ 2025-05-21 📅 2025-05-21 ✅ 2025-05-26
- [x] #task double merged patch fixes to all LTS versions, ~including 4.3.0, 4.2.1~ and 4.1.4 ➕ 2025-05-19 ⏳ 2025-05-27 ✅ 2025-07-07
- [x] #task net error timeout ➕ 2025-05-15 ⏳ 2025-05-15 ✅ 2025-05-16
- [x] #task Should disable the run query button when query is disabled in Write Queries ➕ 2025-05-15 ⏳ 2025-05-15 ✅ 2025-05-16
- [x] #task [in test] Fix abort query in gap ➕ 2025-05-15 ⏳ 2025-05-15 ✅ 2025-05-16
- [x] #task download count by week, month... group by edition 🔺 ➕ 2025-05-14 ⏳ 2025-05-14 ✅ 2025-05-14
- [x] #task download count by week, month... group by edition ➕ 2025-05-14 ✅ 2025-07-14
- [-] #task Add GUI.EnableCORS Configuration Options to AdminPortal UI https://graphsql.atlassian.net/browse/APPS-3602 ➕ 2025-05-14 ⏳ 2025-05-14 ❌ 2025-05-14
- [-] #task Some queries were missing during "install query all" https://graphsql.atlassian.net/browse/APPS-3959 ➕ 2025-05-14 ⏳ 2025-05-14 ❌ 2025-05-14
- [-] #task APPS-3940 display saving query error ➕ 2025-05-13 ⏳ 2025-05-13 ❌ 2025-05-14
- [x] #task build a new bofa patch ➕ 2025-05-13 ⏳ 2025-05-13 ✅ 2025-05-14
- [x] #task build a new tdbank patch ➕ 2025-05-13 ⏳ 2025-05-13 ✅ 2025-05-13
- [x] #task 3938 ➕ 2025-05-12 ⏳ 2025-05-13 ✅ 2025-05-13
- [x] #task Should enable import and export solution in community version when license invalid https://github.com/tigergraph/tools/pull/2528 ➕ 2025-05-12 ⏳ 2025-05-13 ✅ 2025-07-07
- [x] #task marketplace pending status; warning if there is no active marketplace subscription ➕ 2025-05-12 ⏳ 2025-05-12 ✅ 2025-05-12
- [x] #task marketplace pending status & https://graphsql.atlassian.net/browse/TCE-5709?focusedCommentId=202810 ➕ 2025-05-12 ✅ 2025-07-14
- [x] #task [in wip] Review system auth token accessibility to customer's data https://github.com/tigergraph/gle/pull/6327 ➕ 2025-05-12 ⏳ 2025-05-12 ✅ 2025-07-07
- [-] #task review: APPS-3712 fear(jwt): Add gsql jwt authentication support; https://github.com/tigergraph/gus/pull/1068 ➕ 2025-05-12 ⏳ 2025-05-14 ❌ 2025-05-14
- [-] #task Double merge tdbank patch(gus, tools) to 4.1.3 ➕ 2025-05-09 ⏳ 2025-05-09 ❌ 2025-05-09
- [-] #task Should disable discard draft button for saving in GSQL query in Write Queries ➕ 2025-05-08 ⏳ 2025-05-08 ❌ 2025-05-09
- [x] #task Patch for bofa: ➕ 2025-05-08 ⏳ 2025-05-08 ✅ 2025-07-07
- [x] #task [building] Patch for tdbank: https://graphsql.atlassian.net/browse/TSE-664 ➕ 2025-05-08 ⏳ 2025-05-08 ✅ 2025-07-07
- [x] #task RCA for  Query changes lost when saving quickly after fixing syntax errors ➕ 2025-05-07 ⏳ 2025-05-07 ✅ 2025-05-08
- [x] #task Switch payment method API ➕ 2025-04-18 ⏳ 2025-04-18 ✅ 2025-04-21
- [-] #task [v4.2.0] GlobalDesigner can view "AdminPortal -> Monitor -> Queries" ➕ 2025-04-15 ⏳ 2025-04-15 ❌ 2025-04-16
- [x] #task APPS-3784 Save as query function report error in GST of tg_4.2.0 ➕ 2025-04-15 ⏳ 2025-04-15 ✅ 2025-04-15
- [-] #task TCE-6504 Savanna Pricing | Remove TigerGraph Credits & Replace with Dollars ➕ 2025-04-15 ⏳ 2025-04-15 ❌ 2025-04-15
- [x] #task TCE-6452 Entering '1' into the endpoint parameter in the Connect Via API pan… ➕ 2025-04-15 ⏳ 2025-04-16 ✅ 2025-04-16

- [x] #task TCE-6495 fix(cluster): hide azure private link; #295 https://github.com/tigergraph/cloud-frontend/pull/295 ➕ 2025-04-11 ⏳ 2025-04-11 ✅ 2025-04-11
- [x] #task What's v3 marketplace? ➕ 2025-04-10 ⏳ 2025-04-10 ✅ 2025-04-11
- [x] #task Merge to hot fix branch ➕ 2025-04-10 ⏳ 2025-04-10 ✅ 2025-04-11
- [x] #task TCE-6493 [GSQL Editor] Do not show table tab when there is no row data ➕ 2025-04-09 ⏳ 2025-04-09 ✅ 2025-04-09
- [x] #task [GSQL editor stuck when rendering table result with ~10000 rows](https://graphsql.atlassian.net/browse/TCE-6476) ➕ 2025-04-09 ⏳ 2025-04-09 ✅ 2025-04-09
- [x] #task Fix: extract the JSON array when running a gsql command ➕ 2025-04-07 ⏳ 2025-04-07 ✅ 2025-04-07
- [x] #task Reproduce and fix the issue when "select s from (s:Comment) limit 10000 " ➕ 2025-04-07 ⏳ 2025-04-07 ✅ 2025-04-07
- [x] #task [TCE-6472 Prettier the JSON result when running a query](https://graphsql.atlassian.net/browse/TCE-6472) ➕ 2025-04-07 ⏳ 2025-04-07 ✅ 2025-04-07
- [x] #task refresh query ➕ 2025-04-03 ⏳ 2025-04-03 ✅ 2025-04-07
- [x] #task APPS-3551 fix(gst): filter code check items with incorrect type; ➕ 2025-04-02 ⏳ 2025-04-02 ✅ 2025-04-02
- [x] #task Add org_id: org_lSrNWagZNHUufCMN ➕ 2025-04-01 ⏳ 2025-04-01 ✅ 2025-04-01
- [x] #task Merge: https://github.com/tigergraph/cloud-org-service/pull/65 https://github.com/tigergraph/cloud-frontend/pull/293 ➕ 2025-03-31 ⏳ 2025-04-01 ✅ 2025-04-01
- [x] #task Fix ut issue: TCE-6419 feat(login): Redirect users to TGCloud v3 for paying v3 customers; ➕ 2025-03-27 ⏳ 2025-03-27 ✅ 2025-07-07
- [-] #task [Run big data query (page_rank1), reported error: failed to fetch](https://graphsql.atlassian.net/browse/APPS-3682) ➕ 2025-03-27 ⏳ 2025-03-27 ❌ 2025-03-27
- [x] #task Fix tutorial issue ➕ 2025-03-27 📅 2025-03-27 ✅ 2025-03-27
- [x] #task APPS-3684 Fix: cloud header issue in 3.x ➕ 2025-03-26 ⏳ 2025-03-26 ✅ 2025-03-27
- [x] #task TCE-6427 Run query make page crush ➕ 2025-03-25 ⏳ 2025-03-25 ✅ 2025-07-07
- [x] #task request IAM service on v4/v3 ➕ 2025-03-24 ⏳ 2025-03-25 ✅ 2025-03-26
- [x] #task reduce home page bundle size ➕ 2025-03-24 ⏳ 2025-03-24 ✅ 2025-03-25
- [-] #task Shirley约饭 ➕ 2025-03-22 ⏳ 2025-03-23 ❌ 2025-03-24
- [-] #task query list and query detail ➕ 2025-03-20 ⏳ 2025-03-20 ❌ 2025-03-20
- [x] #task 合入delete query的fix进main分支 ➕ 2025-03-20 ⏳ 2025-03-20 ✅ 2025-03-20
- [x] #task build一个新的patch并进行验证 ➕ 2025-03-20 ⏳ 2025-03-21 ✅ 2025-03-21
- [x] #task build ➕ 2025-03-20 ✅ 2025-07-14
- [x] #task build的一个新的patch ➕ 2025-03-20 ✅ 2025-07-14
- [x] #task GSQL editor phase 4: split sub tickets ➕ 2025-03-18 ⏳ 2025-03-19 ✅ 2025-03-19
- [x] #task build release patch for jpmc ➕ 2025-03-17 ⏳ 2025-03-17 ✅ 2025-03-18
- [-] #task APPS-3654 feat(version): get IsCommunityEdition from GetRepoInfo rpc call; ➕ 2025-03-14 ⏳ 2025-03-14 ❌ 2025-03-14
- [-] #task Review: https://github.com/tigergraph/gus/pull/1043 ➕ 2025-03-13 ⏳ 2025-03-14 ❌ 2025-03-14
- [x] #task [PATCH REQUEST] JPMC 4.1.1 patch branch patch_4.1.1_jpmc ➕ 2025-03-13 ⏳ 2025-03-16 ✅ 2025-03-16
- [-] #task Merge: https://github.com/tigergraph/gus/pull/1053 ➕ 2025-03-13 ⏳ 2025-03-13 ❌ 2025-03-13
- [-] #task APPS-2536 [GS][4.2.0] New GraphStudio Privileges TigerGraph Applications • Recently viewed ➕ 2025-03-11 ⏳ 2025-03-13 ❌ 2025-03-13
- [x] #task Investigate the network request during auto-completion ➕ 2025-03-11 ⏳ 2025-03-12 ✅ 2025-07-07
- [x] #task VS code extension to integrate ai auto-completion ➕ 2025-03-10 ⏳ 2025-03-10 ✅ 2025-03-11
- [x] #task https://graphsql.atlassian.net/browse/APPS-2536 ➕ 2025-03-09 ⏳ 2025-03-10 ✅ 2025-03-10
- [x] #task APPS-3643 Should close the pop window after finishing install query in Write… 🔺 ➕ 2025-03-07 ⏳ 2025-03-07 ✅ 2025-03-07
- [x] #task Learn more GSQL ➕ 2025-03-10 ⏳ 2025-03-07 ✅ 2025-03-07
- [x] #task Learn some GSQL ➕ 2025-03-06 ⏳ 2025-03-06 ✅ 2025-03-06
- [-] #task Gap delete user name failed ➕ 2025-03-05 ⏳ 2025-03-06 ❌ 2025-03-06
- [x] #task Deprecate file upload API; add edition column ➕ 2025-03-05 ⏳ 2025-03-05 ✅ 2025-03-05
- [x] #task Prepare demos ➕ 2025-03-05 ⏳ 2025-03-06 ✅ 2025-03-06
- [-] #task Investigate go email template engine ➕ 2025-03-04 ⏳ 2025-03-05 ❌ 2025-03-05
- [x] #task Learn: what is TG vector search ➕ 2025-03-04 ⏳ 2025-03-05 ✅ 2025-03-05
- [x] #task Community Email change: video links 🔺 ➕ 2025-03-04 ⏳ 2025-03-05 ✅ 2025-03-05
- [-] #task change csv file to `Community|4.2.0|Community Edition (Alpha)|||Mar 31, 2027` 🔺 ➕ 2025-03-04 ⏳ 2025-03-05 ❌ 2025-03-04
- [x] #task Add tutorial/docker links to dl page 🔺 ➕ 2025-03-04 ⏳ 2025-03-04 ✅ 2025-03-04
- [x] #task Review: https://github.com/tigergraph/cloud-portal/pull/685/files ➕ 2025-03-03 ⏳ 2025-03-03 ✅ 2025-03-03
- [x] #task Merge: TCE-5554 feat(editor): Display GSQL results as a series of chunks and visualize each chunk;TCE-5889;TCE-6293;TCE-4745;TCE-6330; ➕ 2025-03-03 ⏳ 2025-03-03 ✅ 2025-03-03
- [x] #task Merge the tools-patch PR ➕ 2025-03-09 ⏳ 2025-03-19 ✅ 2025-03-17
- [x] #task Fix expression grammar(query `age`) ➕ 2025-03-03 ⏳ 2025-03-03 ✅ 2025-03-03
- [x] #task Fix console warning in GSQL editor ➕ 2025-03-03 ⏳ 2025-03-03 ✅ 2025-03-03
- [-] #task [[JP Morgan Chase] Failed to create query and report "query not found"](https://graphsql.atlassian.net/browse/APPS-3624) ➕ 2025-03-03 ⏳ 2025-03-05 ❌ 2025-03-04
- [x] #task change download emails: enterprise and community ➕ 2025-02-28 ⏳ 2025-03-03 ✅ 2025-03-03
- [x] #task [Can't open proxy group page in GAP](https://graphsql.atlassian.net/browse/APPS-3618) ➕ 2025-02-28 ⏳ 2025-02-28 ✅ 2025-02-28
- [x] #task TCE-6330 Shared files' content does not display ➕ 2025-02-26 ⏳ 2025-02-26 ✅ 2025-02-27
- [x] #task deploy download UI to test site ➕ 2025-02-26 ⏳ 2025-02-26 ✅ 2025-02-27
- [x] #task 使用deepseek生成query ➕ 2025-02-26 ⏳ 2025-02-26 ✅ 2025-02-27
- [x] #task 使用deepseek生成g s q l ➕ 2025-02-26 ✅ 2025-07-14
- [x] #task 使用deepsee k ➕ 2025-02-26 ✅ 2025-07-14
- [-] #task 使用deep see k ➕ 2025-02-26 ❌ 2025-03-07
- [-] #task 使用deep see k ➕ 2025-02-26 ❌ 2025-03-07
- [x] #task tools升级issue ➕ 2025-02-26 ⏳ 2025-02-26 ✅ 2025-02-26
- [-] #task check community version email ➕ 2025-02-25 ⏳ 2025-02-25 ❌ 2025-02-25
- [x] #task APPS-3591 Unable to access admin portal with community version ➕ 2025-02-24 ⏳ 2025-02-25 ✅ 2025-02-25
- [-] #task Review: [Plan](https://graphsql.atlassian.net/wiki/spaces/QA/pages/3904634881/Test+Plan+-+Tools+-TigerGraph+Community+Edition+Installation+Limitations+Verification) ➕ 2025-02-24 ⏳ 2025-02-24 ❌ 2025-02-24
- [x] #task https://tigergraph.zendesk.com/agent/tickets/54854 ➕ 2025-02-24 ⏳ 2025-02-24 ✅ 2025-02-24
- [x] #task Adjust the layouts of download page ➕ 2025-02-21 ⏳ 2025-02-21 ✅ 2025-02-22
- [x] #task TCE-6313 Update the GSQL tutorial path in gsql editor ➕ 2025-02-21 ⏳ 2025-02-21 ✅ 2025-02-22
- [x] #task Improvement TCE-5889 Swipe right in the code area, where the code overlaps with the column number. Major Open ➕ 2025-02-20 ⏳ 2025-02-20 ✅ 2025-02-20
- [x] #task Task TCE-4745 [BYOC] UI - Cloud Provider List table should sort by create date of cloud provider (or follow WS style) Minor Reopened ➕ 2025-02-20 ⏳ 2025-02-20 ✅ 2025-02-20
- [x] #task Task TCE-6293 We will need to change the GSQL Editor to Query Editor Major Open ➕ 2025-02-20 ⏳ 2025-02-20 ✅ 2025-02-20
- [x] #task [wip] Disable enterprise features in community version ➕ 2025-02-18 ⏳ 2025-02-24 ✅ 2025-02-23
- [x] #task Display query response as JSON ➕ 2025-02-17 ⏳ 2025-02-18 ✅ 2025-02-23
- [x] #task TCE-6272 Adjust the tutorial's sort order to make the schema file first 🔺 ➕ 2025-02-17 ⏳ 2025-02-24 ✅ 2025-02-23
- [-] #task Extract query responses from sse messages ➕ 2025-02-14 ⏳ 2025-02-14 ❌ 2025-02-14
- [x] #task Merge cypher PR ➕ 2025-02-14 ⏳ 2025-02-14 ✅ 2025-02-15
- [x] #task [Display list of insufficient quotas and missing Permissions if reason for create Cloud Provider failure](https://graphsql.atlassian.net/browse/TCE-6253) ➕ 2025-02-12 ⏳ 2025-02-14 ✅ 2025-02-14
- [x] #task fix unit tests: https://github.com/tigergraph/cloud-portal/pull/706 ➕ 2025-02-12 ⏳ 2025-02-13 ✅ 2025-02-13
- [x] #task Add function names to autocompletion ➕ 2025-02-12 ⏳ 2025-02-12 ✅ 2025-02-12
- [x] #task go through cypher-editor 🔺 ➕ 2025-02-11 ⏳ 2025-02-11 ✅ 2025-02-11
- [x] #task https://graphsql.atlassian.net/browse/TCE-6262 🔺 ➕ 2025-02-08 ⏳ 2025-02-08 ✅ 2025-02-08
- [-] #task Support cypher in GSQL editor 🔺 ➕ 2025-02-06 ⏳ 2025-02-06 ❌ 2025-02-06
- [x] #task Replace all emails' image, replace v3 banner image 🔺 ➕ 2025-02-06 ⏳ 2025-02-06 ✅ 2025-02-06
- [x] #task Replace slack file.upload api in download site ⏫ ➕ 2025-01-24 ⏳ 2025-02-11 ✅ 2025-07-07
- [-] #task Fix org service PR 🔼 ➕ 2025-01-24 ⏳ 2025-01-24 ❌ 2025-01-24
- [x] #task TCE-6234 Directly Read File tutorials from Github Repo in GSQL Editor ⏫ ➕ 2025-01-22 ⏳ 2025-02-05 ✅ 2025-02-06
- [x] #task TCE-6221 feat(toc): Force users to accept TOC and Privacy Policy when there is an update; 🔺 ➕ 2025-01-22 ⏳ 2025-02-10 ✅ 2025-02-11
	- [x] #task After test, change the version number back ✅ 2025-02-05
- [x] #task auth0 config: savanna.tgcloud.io 🔺 ➕ 2025-01-20 ⏳ 2025-01-20 ✅ 2025-01-20
- [x] #task merge: drag-n-drop https://github.com/tigergraph/cloud-portal/pull/697 🔺 ➕ 2025-01-14 ⏳ 2025-01-14 ✅ 2025-01-14
	- [x] #task double merge to release branch ✅ 2025-01-14
- [x] #task [wf] TCE-5374 byoc new design: https://www.figma.com/design/y5RJm4Hs8suDdNfwLRUSFX/Admin?node-id=2077-61367&t=tZgiUKdZNrhpaNvd-0 🔺 ➕ 2025-01-13 ⏳ 2025-01-13 ✅ 2025-01-15
	- [x] #task merge to main ✅ 2025-01-15
	- [x] #task double merge to release branch ✅ 2025-01-14
- [x] #task https://graphsql.atlassian.net/browse/TCE-6172 🔺 ➕ 2025-01-09 ⏳ 2025-01-10 ✅ 2025-01-13
- [-] #task support cypher grammar 🔺 ➕ 2025-01-09 ⏳ 2025-01-21 ❌ 2025-01-21
- [x] #task investigate: https://graphsql.atlassian.net/browse/APPS-3489 🔺 ➕ 2025-01-09 ⏳ 2025-01-09 ✅ 2025-01-09
- [x] #task [mit] GUS 4.2.0 double merge: https://github.com/tigergraph/gus/pull/998 🔺 ➕ 2025-01-08 ⏳ 2025-01-11 ✅ 2025-01-15
- [x] #task review: https://github.com/tigergraph/tools/pull/2316/files 🔺 ➕ 2025-01-08 ⏳ 2025-01-08 ✅ 2025-01-08
- [x] #task review: https://github.com/tigergraph/tools/pull/2316/files ➕ 2025-01-08 ✅ 2025-07-14
- [x] #task http://**************:30669/studio/#/query-editor ➕ 2025-01-07 ⏳ 2025-01-07 ✅ 2025-01-07
- [x] #task [wip] [Improve Admin Portal User Interface: which roles are assigned to which proxy groups](https://github.com/tigergraph/tools/pull/2358) 🔺 ➕ 2025-01-06 ⏳ 2025-02-12 ✅ 2025-02-13
	- [x] #task  [Goldman Sachs-US] no need to Restart GSQL when update SSO setting in admin portal ✅ 2025-01-08
- [x] #task [in-test] Merge: APPS-3426 feat(gap): Hide/View Private Key and X509 Certificate in Admin Portal; 🔺 ➕ 2025-01-03 ⏳ 2025-02-20 ✅ 2025-02-20
- [x] #task TCE-4217 Verify email panel will random disappear and be log out after user sign up ➕ 2025-01-02 ⏳ 2025-01-07 ✅ 2025-01-07
- [x] #task Merge: APPS-3359 support bigint in GST and Insights; 🔺 ➕ 2024-12-31 ⏳ 2025-01-02 ✅ 2025-01-02
	- [x] #task fix interpret query in 3.9.3 ✅ 2024-12-31
- [x] #task [[TG/TCE-5986]] [Nubank] Allow drag-n-drop in GSQL Editor files list, to move files across different folders. 🔺 ➕ 2024-12-30 ⏳ 2025-01-02 ✅ 2025-01-02
- [x] #task code review: https://github.com/tigergraph/tools/pull/2329 ➕ 2024-12-25 ⏳ 2024-12-25 ✅ 2024-12-25
- [x] #task [[JavaScript integer precision]] 🔺 ➕ 2024-12-23 ⏳ 2024-12-23 ✅ 2024-12-27
	- [x] #task cloud portal ✅ 2024-12-25
	- [x] #task GST ✅ 2024-12-26
	- [x] #task Insights ✅ 2024-12-26
- [x] #task double merge to 4.2.0: add APP_ACCESS_LOG privilege; 🔺 ➕ 2024-12-23 ⏳ 2024-12-23 ✅ 2024-12-23
- [x] #task 打印话费账单 ➕ 2024-12-21 ⏳ 2024-12-24 ✅ 2024-12-24
- [x] #task huafei ➕ 2024-12-20 ⏳ 2024-12-20 ✅ 2024-12-21
- [x] #task Merge: [[Apple][4.2.0] Allow Global Designer to view "AdminPortal -> Monitor -> Queries"](https://graphsql.atlassian.net/browse/APPS-3172) 🔺 ➕ 2024-12-20 ⏳ 2025-01-10 ✅ 2025-01-13
	- [x] #task [wip] https://github.com/tigergraph/gus/pull/1007 ⏳ 2025-01-13 ✅ 2025-01-13
	- [x] #task reviewing: https://github.com/tigergraph/tools/pull/2336 ✅ 2025-01-13
- [-] #task [[GS][4.2] Improve Admin Portal User Interface: which roles are assigned to which proxy groups](https://graphsql.atlassian.net/browse/APPS-2906) ➕ 2024-12-20 ⏳ 2024-12-24 ❌ 2024-12-25
- [x] #task [in test] [Expose CodeMirror instance to global for testing](https://graphsql.atlassian.net/browse/TCE-6124) 🔺 ➕ 2024-12-20 ⏳ 2024-12-20 ✅ 2024-12-23
- [x] #task add unit tests for user role form ➕ 2024-12-19 ⏳ 2024-12-19 ✅ 2024-12-19
- [x] #task TCE-5741 feat(home): Change cloud v4 domain to https://portal.tgcloud.io; #2254 ➕ 2024-12-19 ⏳ 2024-12-19 ✅ 2024-12-20
- [x] #task change signup page wordings ➕ 2024-12-19 ⏳ 2024-12-19 ✅ 2024-12-19
- [-] #task installed query status issue ➕ 2024-12-16 ⏳ 2024-12-16 ❌ 2024-12-17
- [x] #task what is invitation link expiration time ➕ 2024-12-16 ⏳ 2024-12-17 ✅ 2024-12-17
- [x] #task [in-test] [firefox crash due to parseDate](https://graphsql.atlassian.net/browse/TCE-6088) ➕ 2024-12-16 ⏳ 2024-12-16 ✅ 2024-12-17
- [-] #task [Don't remove workspace card once click terminate button](https://graphsql.atlassian.net/browse/TCE-6051) ➕ 2024-12-16 ⏳ 2024-12-17 ❌ 2024-12-17
- [x] #task [in-test] TCE-6038 feat(gst): disable write queries page on cloud v4; #2326 ➕ 2024-12-16 ⏳ 2024-12-16 ✅ 2024-12-19
- [x] #task fix unstable unit tests ⏫ ➕ 2024-12-16 ⏳ 2024-12-17 ✅ 2024-12-17
- [x] #task 3.10.3 draft query ➕ 2024-12-11 ⏳ 2024-12-11 ✅ 2024-12-14
- [x] #task Add ut for QueryFileItem ➕ 2024-12-11 ⏳ 2024-12-11 ✅ 2024-12-11
- [x] #task beta env: aad login ➕ 2024-12-10 ⏳ 2024-12-10 ✅ 2024-12-11
- [x] #task UT for FileTabs ➕ 2024-12-10 ⏳ 2024-12-10 ✅ 2024-12-11
- [x] #task deliver patch ➕ 2024-12-10 ⏳ 2024-12-10 ✅ 2024-12-11
- [x] #task [workspace deletion](https://graphsql.atlassian.net/browse/TCE-5997?atlOrigin=eyJpIjoiNTQ0ODQ0ZTk3YjkzNDE0ZGJhMjAyMTIwOTg2YTE4ZTIiLCJwIjoiamlyYS1zbGFjay1pbnQifQ) ➕ 2024-12-09 ⏳ 2024-12-09 ✅ 2024-12-09
	- [x] #task handle unknown status ✅ 2024-12-09
	- [x] #task disable menu when deleting ✅ 2024-12-09
- [x] #task Config auth0 okta in beta env ➕ 2024-12-05 ⏳ 2024-12-05 ✅ 2024-12-06
- [x] #task Merge UT PR ➕ 2024-12-05 ⏳ 2024-12-09 ✅ 2024-12-09
- [x] #task [Add file owners to shared files in GSQL Editor](https://graphsql.atlassian.net/browse/TCE-6032) ➕ 2024-12-05 ⏳ 2024-12-07 ✅ 2024-12-09
- [x] #task [in test] build another patch for ATO ⏫ ➕ 2024-12-02 ⏳ 2024-12-05 ✅ 2024-12-06
- [x] #task RCA https://graphsql.atlassian.net/browse/TSE-565 ➕ 2024-12-01 ⏳ 2024-12-02 ✅ 2024-12-05
- [x] #task [mit] [Read-Only Access to Logs](https://graphsql.atlassian.net/browse/APPS-3253) 🔺 ➕ 2024-11-29 ⏳ 2024-11-29 ✅ 2024-12-08
	- [x] #task PR review ✅ 2025-07-14
- [x] #task [Add read-only icon to tutorials](https://graphsql.atlassian.net/browse/TCE-5984) ➕ 2024-11-28 ⏳ 2024-11-28 ✅ 2024-11-28
- [x] #task build patch for ato ⏳ 2024-11-21
- [-] #task GSQL output download file format is wrong in tg_4.2.0 https://graphsql.atlassian.net/browse/APPS-3410 ⏳ 2024-11-25 ❌ 2024-11-25
- [-] #task Download GSQL output file failed in tg_3.11.1 https://graphsql.atlassian.net/browse/APPS-3419 ⏳ 2024-11-25 ❌ 2024-11-25
- [x] #task ato release patch ⏳ 2024-11-22 ✅ 2024-11-27
- [x] #task page crash when running q3a(there is an error when converting array value to string, remove control characters [PR](https://github.com/tigergraph/cloud-portal/pull/642/commits/548930487e6598fc3195cfc6df82a91d5ad5acd7)) ⏫ ⏳ 2024-11-25 ✅ 2024-11-25
- [x] #task double merge to 4.2.0 https://github.com/tigergraph/tools/pull/2197 ⏳ 2024-11-25 ✅ 2024-11-26
- [x] #task request URL issue: https://docs.google.com/presentation/d/1A3pdSGbTpAMTqRz-qDLvf2YvTxr5q-TyK8cpgLcpqt4/edit#slide=id.p ⏳ 2024-11-22
- [x] #task  tutorial folder ⏳ 2024-11-22
- [-] #task Verify email panel will random disappear and be log out after user sign up ⏳ 2024-11-21 ❌ 2024-11-25
- [x] #task security issue: https://app.snyk.io/org/cloud-ondemand/projects?groupBy=targets&before&after&searc[…]y&filters[Show]=&filters[Integrations]=&filters[CollectionIds]= ⏳ 2024-11-19
- [x] #task TCE-5881 [GSQL Editor] Keep graph context of last command ⏳ 2024-11-15
- [-] #task [Parameter percision](https://graphsql.atlassian.net/browse/TCE-5861) ⏳ 2024-11-12 ❌ 2024-11-12
- [x] #task use BigInt json library in cloud portal ⏳ 2024-11-12
- [x] #task [[TG/issues/filter roles in cloud v3]] 📅 2024-11-12
- [x] #task [[TG/issues/TCE-4752]] [Support direct redirection to cloud v4 with ORG SSO login](https://graphsql.atlassian.net/browse/TCE-4752) ⏳ 2024-11-11
- [x] #task TCE-5741 remove beta description/img on cloud  v3 login page ✅ 2024-12-19
- [x] #task Replace banner image on cloud v3 ⏳ 2024-11-04 ✅ 2024-11-26
- [-] #task domain change: beta.tgcloud.io -> tgcloud.io ⏳ 2024-11-04 ❌ 2024-11-26
- [x] #task merge to 4.1.1 [tools#2248](https://github.com/tigergraph/tools/pull/2248) [gus#983](https://github.com/tigergraph/gus/pull/983) 🔺 ⏳ 2024-11-04
- [x] #task [in-test] Pre-check the export data on GraphStudio 🔺 ⏳ 2024-10-28
- [x] #task Will use the OIDC login when user access the tools with domain for cloud v4 ⏳ 2024-10-25
- [-] #task Should show cloud header after redirect from RO WS ⏫ ⏳ 2024-11-02 ❌ 2024-11-06
- [x] #task run button icon in windows ⏳ 2024-10-21
- [x] #task  upgrade tigergraph from 3.10.0 to 3.11.0 failed because /api/system/migrate-queries not found ⏳ 2024-10-11
- [-] #task change command shortcut, mac/win icons ⏳ 2024-10-12 ❌ 2024-10-21
- [x] #task [JS error](https://graphsql.atlassian.net/browse/APPS-3227) ⏳ 2024-10-09
- [x] #task [under wip] APPS-3217, 3218 ⏳ 2024-09-30
- [x] #task 3.11.0 mit ⏳ 2024-09-27
- [-] #task 3.10.2 mit ⏳ 2024-09-27 ❌ 2024-09-27
- [x] #task [under wip] 4.1.1 Abort query ⏳ 2024-09-30
- [x] #task [under test] 4.1.1 mit: [tools#2173](https://github.com/tigergraph/tools/pull/2173 "https://github.com/tigergraph/tools/pull/2173") revert draft query ⏳ 2024-09-30
- [x] #task [under mit]mit: Vulnerability in password changing ⏳ 2024-09-27
- [x] #task [Apple patch](https://graphsql.atlassian.net/browse/APPS-3195) ⏳ 2024-10-11
- [-] #task [[Async query]] ⏳ 2024-09-23 ❌ 2024-09-24
- [x] #task [Development Environment Setup on MacBook](https://graphsql.atlassian.net/wiki/spaces/Engineerin/pages/**********/Development+Environment+Setup+on+MacBook) ⏳ 2024-09-26
- [-] #task [Manage payment methods in the Dashboard by default | Stripe Documentation](https://docs.stripe.com/upgrades/manage-payment-methods) ❌ 2024-11-26
- [x] #task [under mit] merge APPS-3141 fix(loading): Pause loading job report error; ⏳ 2024-09-26
- [-] #task [[Unilever][4.1.1] Abort running query from GraphStudio](https://graphsql.atlassian.net/browse/APPS-2531) ⏳ 2024-09-20 ❌ 2024-09-24
- [x] #task [Display stale cloud providers in workgroup creation pages and it's not clickable](https://graphsql.atlassian.net/browse/TCE-5444) ⏳ 2024-09-18
- [x] #task [[Unilever][4.1.1] GraphStudio should be more "verbose" when installing queries](https://graphsql.atlassian.net/browse/APPS-2530) 🛫 2024-09-18 ⏳ 2024-09-19
- [x] #task [Can't open GST, GAP, insights](https://graphsql.atlassian.net/browse/TCE-5405) ⏳ 2024-09-18
- [x] #task [Update Amberflo UI kit version and use aggregation](https://graphsql.atlassian.net/browse/TCE-4481) ⏳ 2024-11-05
- [x] #task [Should show the full pop window after clicking import solution](https://graphsql.atlassian.net/browse/APPS-3072) ⏳ 2024-09-12
- [x] #task [Should show the error detail when create and install new query with sub_query](https://graphsql.atlassian.net/browse/APPS-3069) ⏳ 2024-09-12
- [x] #task [Pause or stop loading job report error](https://graphsql.atlassian.net/browse/APPS-3141) 📅 2024-09-04
- [-] #task health-check docs 📅 2024-08-30 ❌ 2024-09-11
- [x] #task Merge 080B [PR](https://github.com/tigergraph/cloud-portal/pull/507) 📅 2024-08-29
- [x] #task [Fix search logs](https://graphsql.atlassian.net/browse/APPS-3115) 📅 2024-08-23
- [x] #task 看一个IntelliSense的视频 ⏳ 2024-08-18 📅 2024-08-19
- [x] #task investigate auth0 management api to add callback url 📅 2024-08-15
- [x] #task  Merge [cloud-portal PR](https://github.com/tigergraph/cloud-portal/pull/493) 📅 2024-08-15
- [-] #task [Merge cloud-universe PR](https://github.com/tigergraph/cloud-universe/pull/652) 📅 2024-08-15 ❌ 2024-08-15
- [x] #task [Should prompt user to save gsql file when switch org](https://graphsql.atlassian.net/browse/TCE-5203) 📅 2024-08-14
- [x] #task release signup page ⏳ 2024-08-18 📅 2024-08-19
- [x] #task [Should not redirect to loaclhost after test-portal expire](https://graphsql.atlassian.net/browse/TCE-5189) ⏳ 2024-08-14 📅 2024-08-13 ✅ 2024-08-14
- [x] #task [When a command is running, switch to another interface and come back, the log information is missing](https://graphsql.atlassian.net/browse/TCE-3924) ⏳ 2024-08-16 📅 2024-08-19
- [x] #task [Keep font size and format the same between different result panel](https://graphsql.atlassian.net/browse/TCE-3326) 📅 2024-08-12 ✅ 2024-08-13
- [-] #task [Add a loading indicator to the backup button until the backup starts](https://graphsql.atlassian.net/browse/TCE-4115) 📅 2024-08-12 ❌ 2024-08-12
- [-] #task [Add file name/folder name uniqueness check](https://graphsql.atlassian.net/browse/TCE-3983) ⏳ 2024-08-14 📅 2024-08-16 ❌ 2024-08-16
- [-] #task [The font color is incorrect on the billing/overview page when using Google Chrome.](https://graphsql.atlassian.net/browse/TCE-5138) ⏳ 2024-08-09 ❌ 2024-08-09
- [x] #task [Should prompt user run the gsql file after edit query in GSQL Editor](https://graphsql.atlassian.net/browse/TCE-5160) 📅 2024-08-09
- [x] #task [An error is displayed when the Invoices page has finished loading.](https://graphsql.atlassian.net/browse/TCE-5136) 📅 2024-08-09
- [x] #task [Should show command hint panel in GSQL editor](https://graphsql.atlassian.net/browse/TCE-4968) 📅 2024-08-09
- [x] #task [It should be prompted to user no corresponding username or email found when search username or email not to match the result](https://graphsql.atlassian.net/browse/TCE-4862) 📅 2024-08-09
- [-] #task [The UI should be more fancy when there's no graph in the workspace](https://graphsql.atlassian.net/browse/TCE-3762) : 📅 2024-08-09 ❌ 2024-08-09
- [x] #task ["Login with ORG" button shouldn't display in RESET PASSWORD page](https://graphsql.atlassian.net/browse/TCE-4628) ⏳ 2024-08-08 ✅ 2024-08-08
- [x] #task Merge cloud e2e https://github.com/tigergraph/tools/pull/2069 ⏳ 2024-08-09 📅 2024-08-08 ✅ 2024-08-09
	- release a new model version since has conficts with the cloud branch 
- [-] #task [TCE-4599 Users to stop/restart a cluster when it is in error state.](https://graphsql.atlassian.net/browse/TCE-4599?atlOrigin=eyJpIjoiN2VjZTE3MDNlYWY0NGNjOGJkOGUyYWZjZjIxODk4MmYiLCJwIjoiamlyYS1zbGFjay1pbnQifQ) ⏳ 2024-08-07
- [x] #task Fix Sam's issues in the channel ⏳ 2024-08-07 ✅ 2024-08-07
- [x] #task submit a jira ticket to chen ⏳ 2024-08-07 ✅ 2024-08-07
- [x] #task In dark mode, the colors of the downloaded invoice PDF file are not clear. ⏳ 2024-08-06 ✅ 2024-08-06
