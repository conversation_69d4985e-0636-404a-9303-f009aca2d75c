## Request restpp
```go
token := getGsqlToken(mw.GetUsername(c), mw.GetPassword(c), graphName)

# if token expire, set new token
setGsqlToken(mw.GetUsername(c), mw.GetPassword(c), graphName, token)

req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token.Token))
```
## AuthMiddleware
```go
type AuthMiddleware interface {
	Allow(c *gin.Context) bool
	AuthUser(c *gin.Context) (u *model.UserInfoWithCredentials, err error)
	// only used for logging
	GetSessionID(c *gin.Context) string
}
```
### BasicAuthMiddleware
- Allow: 是否使用此种方式
- GetSessionID： `fmt.Sprintf("Basic %s", username)`
* `AuthUser(c *gin.Context) (*model.UserInfoWithCredentials, error)`
	* request GSQL with creds
### CookieAuthMiddleware
- Allow: has cookieName
- GetSessionID: get from cookie
- AuthUser
```go
// get claims
claims, err := m.authenticationService.Authenticate(c, sessionID)

// decode password
password, err := codec.AESCBCDecrypt(cfg.GetAuthToken(), claims.Password)

// call gsql authenticator
creds := &model.UserCredentials{Username: username, Password: password}
	userInfo, err := m.gsqlAuthenticator(c, cfg, creds, false)
```
### AuthenticateWithGSQL
- get `UserCredentials`, return `UserInfo`
### AuthenticationService
- KVStoreAuthenticationService
	- `Register(c context.Context, claims jwt.Claims)
		- return a session id, will be saved to cookie
		- the jwt claims created in login ![[Pasted image 20240419102834.png]]
		* saved to etcd, key is session id, value is jwt claims
	- Authenticate(c context.Context, sessionId string)
		- get info from etcd using sessionId as key
	
## Authentication
- 遍历所有middlewares，直到一个能用的
  ```go
  for _, middleware := range authMiddlewares {
			if !middleware.Allow(c) {
				continue
			}
```
* auth user，获得用户信息：`u, err := middleware.AuthUser(c)`
* 将信息设置到ctx：authorize(c, u)