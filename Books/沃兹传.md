---
tags:
  - book
status: ✅ Done
author: <PERSON>
rating: 5
progress: 100
target: 100
---

## 这本书如何改变了我？（行为/思想）

1. 清楚地看到你想成为什么样的人、想过什么样的生活、想构建什么样的社会，这些都是生活中最重要、最崇高的目标
2. 对于工作应该力臻完美，用心演绎
3. 快乐是第一位，如果同时还能让身边人快乐便更好

## 这本书讲了什么？

1. 沃兹的成长历程。在父亲的指导下学习电子学知识；青春期因为羞涩常常独自在家中制作电子设备；在大学时使用电视信号干扰器进行恶作剧；辍学加入惠普工作，并认为设计计算器是当时最好的工作；加入家酿计算机俱乐部，独自设计了Apple I，是第一台连接了显示器和键盘的电脑；创立了苹果公司，在苹果上市前实行了“沃兹计划”
2. 沃兹的性格：爱恶作剧、以快乐为中心、热爱电子学和工作、耐心、把设计电脑当作艺术、喜爱小孩、关怀同事
3. 为什么沃兹能发明苹果电脑
    1. 儿时父亲对他的教育及自学，扎实地掌握了电子学知识，培养了工程师的核心品格—耐心
    2. 热爱设计，大量地实践；对设计艺术性的追求；身边工程师朋友的帮助；始终掌握工程界最新的进展
    3. 偶然的事件：偶然读到了面向政府高级工程师的计算机杂志、加入homebrew计算机俱乐部、认识了志同道合的朋友

## 挑选书中的三个片段

1. 爸爸教给我许多重要的东西，我最想跟你分享的是在我看来他教给我的最重要的一课——他训练我如何做一名工程师。这对我的一生影响深远，甚至比爸爸的诚信观对我的影响还深。这里说的“工程师”指的是真正的工程师，或者说是工程师中的工程师。我清楚地记得爸爸坚定地告诉我，工程师是世界上最高尚、最重要的职业，作为一名工程师，你可以用自己的智慧创造出新的仪器，让人们活得更幸福，让世界变得更美好。他告诉我，工程师的工作能改变世界，能改变许多人的生活。
2. 最近，电影《与歌同行》（WalltheLine）中的一幕深深地触动了我：制作人告诉约翰尼·卡什说，唱歌时应该把这首歌当作可以拯救世界的一首歌来用心演绎。上面这句话也适用于工程学或其他一切职业，力臻完美，用心演绎。
3. 我对她说：“我已经找到快乐，也知道快乐的密码。我不需要这样的课程。”我的意思是，我唯一缺的可能是位女朋友，但其他东西我都拥有了。我幽默感不错，也一直秉承着快乐生活的态度。我知道我作选择的唯一标准就是是否快乐。我认为是否快乐，只取决于自己，完全取决于自己。这些是我的价值观，从小到大以来形成的价值观。我的内心感到很宁静。直到今天，我一直都很乐天，不会疑虑重重。大多时间里，我的确是快乐的。现在依然如此。
4. 这些早期的工程磨炼了我成为一名优秀工程师的核心品格——耐心。它意义非常重大。我是认真的，耐心的重要性常被低估。从三年级到八年级的大多数工程作品中，我学到的东西越来越多。很多时候，我不用参考任何书籍就知道如何将电子设备连接在一起。有时，我对自己说，嗨，你可真幸运。早期学习一步一个脚印，仿佛为我指出了生命中的幸运方向。我学会不在乎结果，只在乎过程，尽力做好当下的事情。当今工程学界并非每个人都明白这点。不论我在苹果公司还是其他地方，总会发现许多傻瓜，他们总想一步登天，这是行不通的，从来都是这样。显而易见，这就是认知的过程。你不能一下子跨过两个认知阶段——认识到这一点对我日后教育自己的孩子和教小学五年级学生也很有帮助。我一遍又一遍地告诉他们：一步一个脚印。

## 高亮和反思

## 第一章 我们这帮电子小孩

### 爸爸教给我许多重要的东西，我最想跟你分享的是在我看来他教给我的最重要的一课——他训练我如何做一名工程师。这对我的一生影响深远，甚至比爸爸的诚信观对我的影响还深。这里说的“工程师”指的是真正的工程师，或者说是工程师中的工程师。我清楚地记得爸爸坚定地告诉我，工程师是世界上最高尚、最重要的职业，作为一名工程师，你可以用自己的智慧创造出新的仪器，让人们活得更幸福，让世界变得更美好。他告诉我，工程师的工作能改变世界，能改变许多人的生活。

---

### 融合领先的技术和人文关怀成为我长大后的毕生追求之一。在设计电脑的时候，一些只为技术狂热的极客们考虑的仅仅是加上一些芯片，整个设计就完整了。

---

### 觉得想要验证某种说法，唯一可靠的方式就是通过实验来检验，只有通过这种检验才能成为真理。你不能从书本上看到了某个观点、从别人那里听来了某个观点，就去盲目地相信。

---

### 首先，我们需要集齐所需的全部材料。最主要的材料就是电线，很长很长的电线。我们这样一帮小孩怎样才能搞到上百码的电线呢？而我们获得这些电线的方式堪称奇遇。比尔·沃纳（BillWerner）是我们这群小伙伴中的一员，他看到一个电话工作人员的卡车上的电话线整整齐齐地盘在卷盘上，共有好几卷，就直接上前去问能不能给我们一卷。那个人非常友善，爽快地同意把其中一卷电话线给我们。

---

## 第二章 逻辑游戏

### 我记得有一集里汤姆建造出一艘太空飞船赢得了围绕地球的比赛，得了一大笔奖金，他把这笔钱投入造福全人类的事业中。他正是我心目中最向往的人物，建立属于自己的事业，获得资源能够帮助更多的人。从很小的时候开始，我就想像小汤姆·斯威夫特一样行侠仗义。

---

### 当时许多惊人的发现和发明正在计算机上成为现实。如果我当时不是那么内向害羞，只敢在家里读着旧杂志上的文章的话，我根本就无从了解计算机。我很庆幸在我很年幼的时候，就看到了爸爸的这本旧杂志，从而打开了计算机世界的大门。我偶然读到这本杂志并且被它所描述的技术所吸引，实在是个太幸运的偶然，这本杂志原本的目标读者是政府部门的高级工程师。

---

### 也许正是这样，我六年级时做的许多电子项目就已经达到或超过了高中生的水平。我所取得的这些奖项、获得的这些赞美促使我不断努力，直到在我所选择上的道路上出类拔萃。

---

### 这些早期的工程磨炼了我成为一名优秀工程师的核心品格——耐心。它意义非常重大。我是认真的，耐心的重要性常被低估。从三年级到八年级的大多数工程作品中，我学到的东西越来越多。很多时候，我不用参考任何书籍就知道如何将电子设备连接在一起。有时，我对自己说，嗨，你可真幸运。早期学习一步一个脚印，仿佛为我指出了生命中的幸运方向。我学会不在乎结果，只在乎过程，尽力做好当下的事情。当今工程学界并非每个人都明白这点。不论我在苹果公司还是其他地方，总会发现许多傻瓜，他们总想一步登天，这是行不通的，从来都是这样。显而易见，这就是认知的过程。你不能一下子跨过两个认知阶段——认识到这一点对我日后教育自己的孩子和教小学五年级学生也很有帮助。我一遍又一遍地告诉他们：一步一个脚印。

---

## 第三章 偶然学习

### 谁是说话最有影响力的人？谁是作决定的人？谁成为领袖人物？这些在青少年的社交生活中至关重要。进入青春期的我是如此羞涩、不善言谈，因此在社交中陷入低谷。这对我来说是一个不小的打击。尽管我的科技项目仍然帮我赢得了师长的好评，我却常常感到自己很奇怪。我很难与同龄人打成一片，我对他们说的话感到很陌生，仿佛我无法理解他们的语言。并且，我总是担心会说错话，因此非常畏惧说话。

---

### 这一切从六年级开始，时至今日我仍保留着几分当年的羞涩。我的一些朋友能够站起来直接面向一大群人侃侃而谈。他们把这称为闲谈。我怕是永远也做不到这一点。如果我有着三十年的从业经历，或者我从多年的公开演讲经历中也掌握了一些经验，我也可以对着一群人侃侃而谈。我更擅长的是开个玩笑让大家哈哈大笑，或是展示一台电子仪器吸引观众的注意，让他们的视线集中在我身上。如果你足够了解我的话就会发现，我更喜欢用恶作剧的方式打破沉默。我的恶作剧经验是如此丰富，绝对可以写成一本书！

---

### 我教少管所里那些惹是生非被送进来的不良少年们拆掉天花板上风扇的电线。我跟他们说：“把这些电线缠在囚室门上的铁闩上，把看守喊来。他碰到铁闩时就会被电到！”我在里面玩得很开心，那些人对我也非常不错。当然，当个书呆子反而成了最酷的事，这可不是一朝一夕就换来的。

---

### 从小学开始到成立苹果公司，乃至日后的岁月里，我总是借助自己那些巧妙的设计与别人更好地沟通。这种沟通方式对我来说更自然、更有效。我想，对于所有人来说，社交都是一种内在的需求。而对我来说，展示精巧的电子仪器，或是设计一些巧妙的恶作剧，使我与他人沟通变得简单许多。也许正是六年级之后那几年里让我挫败不已的羞涩感督促我更起劲儿地搜寻电子学杂志。通过它们，我无须与别人交谈就可以获得电子学知识。我害羞到了什么地步呢？这么说吧，我甚至不敢到图书馆去问管理员《计算机》一书放在哪里。因为我太害羞，无法以惯常的方式学习，我更多的是自学，最终学到了世界上对我来说最重要的知识。

---

### 我搜集到瓦里安（Varian）、惠普、数据设备、通用数据（DataGeneral）等公司出品的微型计算机的用户手册。每逢空闲的周末，我就会根据逻辑元件和芯片（它们是计算机的硬件组成部分）的目录和某一特定的微型计算机的用户手册，自己着手设计。在完成首次设计后，我常常会进行第二次、第三次设计，减少使用的芯片数量，优化整体设计。我把使用最少量的芯片来重新设计出这些微型计算机当成一个好玩的游戏。我把自己关在房间里设计电脑，不知不觉间，这已经变成我消磨时间的方式了。这是我个人的娱乐方式，我没有告诉过家长、老师或者朋友。

---

## 第四章 “道德”的电视干扰机

### 我和里奇还有另一个常常坐在一起的同学斯科特·桑普森（ScottSampson）决定，我们仨要一起研究一下上什么大学。我们想去加州理工学院看看。我们计划先飞到加州的波莫纳，斯克里普斯研究所、波莫纳学院和加州理工大学波莫纳分校都在那里。

---

### 科罗拉多州立大学录取了我，爸爸妈妈在开学前的暑假帮我缴清了第一年包括注册费和学费在内的所有费用。但爸爸仍在不停地劝我去迪安扎，这所学校离家近得多，学费也便宜。如果我同意去迪安扎的话，他还可以帮我买辆车。所以我就到迪安扎完成了注册，发现所有化学、物理和微积分的课程都已经满了。什么？我简直不敢相信。我这样一个高中时的数学和理科尖子、未来的工程师，居然无法去上这三门对工程师来说最重要的课程！

---

### 我的室友名叫迈克。我踏入宿舍的第一眼就发现他已经在墙上贴了至少二十张从《花花公子》杂志上撕下来的插页图片。哇，他与我非常不同！但我想迈克是个不错的小伙儿，我很喜欢听他讲自己的故事，讲他从前是军队里调皮捣蛋的小孩，在德国上过的高中，以及其他各种各样的经历。我知道他在性方面非常超前。他会告诉我有时候晚上他想独自待着，而我也知道为什么。我会说，噢，好吧。我会带上我的录音机和我的那堆卷轴磁带——西蒙和加芬克尔是我当时最喜欢的乐队组合——跑到里奇·詹克莱的宿舍去，很晚才回来。有一天深夜，我正在宿舍睡觉，迈克带了个摩门教的女孩回来。他可真了不起啊！

---

### 在那之后，兰迪不再起身敲打电视，但其他人会这样做。这正是我所希望的结果。有人敲打电视，然后我让它恢复。哈，这仿佛是一群任我摆布的豚鼠。我最希望的莫过于此。接下去的两周内，我每晚都会跑到那里看人们敲打电视。当敲打不起效时，他们就开始调来调去。那时候，电视还是调频的。我在一旁暗暗使用电视干扰机，他们一旦调对，我就让电视恢复正常。过了一阵，我又换了种玩法。当有人把手放在调谐器上调整图像清晰度时，我就让电视正常工作。但只要他们手一离开，我会让图像又变得模糊。直到他们再次接触调谐器。他们就像是活生生的木偶，我完全可以控制他们。

---

### 在那时，能上计算机课让人觉得受宠若惊。只有少数大学才开设计算机课程，并且只有研究生课程。但是进入科罗拉多大学学习工程学意味着即使是新生，只要完成了预修课程就可以去上任何一个年级的课，甚至包括研究生的课。幸运的是，这门计算机课不要求任何的先修课程，因此我也就顺利地选修了下来。这门课非常棒，在课上，老师教给我们有关计算机的所有知识：结构、程序语言、操作系统等等。这门课内容非常全面。

---

## 第五章 奶油苏打水的日子

### 我认为，最坏的并非越战本身，而是它带给人们痛苦和压力。我已经长大成人，形成了自己的道德观——深刻关注人民的生活疾苦。我开始寻找生活真谛——如今我仍在这样做——我行事做人只为在自己获得快乐和满足的同时也让别人快乐、满足。

---

### 对政府行为，我所感受的震惊和恶心非笔墨所能形容：他们将我们的人生玩弄于股掌之上，并非如父亲所说，他们关心民众疾苦。我曾经坚信政府会保护我们，但事实并非如此。自那之后，我只相信政府会为自身利益服务，并为此不惜一切手段。他们做事没有道理可言，以最糟的方式玩弄了我的人生。

---

### 小时候爸爸教给我极端的道德观，在终于看清越战真相的这几年间我已经发生了180度的转变。我变得容易怀疑，不再盲目相信，这是很重要的转变。我对各种制度都失去了信心，再也不能重拾回来。我发誓，我会以生命来保护年轻一代再也不会经历越战这样的噩梦。

---

### 我对她说：“我已经找到快乐，也知道快乐的密码。我不需要这样的课程。”我的意思是，我唯一缺的可能是位女朋友，但其他东西我都拥有了。我幽默感不错，也一直秉承着快乐生活的态度。我知道我作选择的唯一标准就是是否快乐。我认为是否快乐，只取决于自己，完全取决于自己。这些是我的价值观，从小到大以来形成的价值观。我的内心感到很宁静。直到今天，我一直都很乐天，不会疑虑重重。大多时间里，我的确是快乐的。现在依然如此。

---

### 5年后，有公司开始制造并销售与我的“奶油苏打水电脑”非常类似的小电脑，它们都具有相同大小的内存、笨拙的操作面板和开关。那时候，“奶油苏打水电脑”对我而言是个重要的起点，这个起点我到达得很早、很容易。

---

### 之后我曾多次反思，最后得出结论：尽管我们的“巴西祝福”没有成功，但它并不代表失败。有些事值得尝试，并投入大量时间和精力，即使结果不尽如人意。从恶作剧中我学到了团队合作、耐心和勤奋。同时，我还有个教训，就是不要四处宣扬自己的恶作剧。史蒂夫曾向一个学生炫耀我们的恶作剧，而一年后这个学生告诉我正是他剪断了渔线。

---

## 第六章 电话飞客

### 他是个样子古怪却又风趣的人，精力非常充沛。他坐在床边，看着我墙上奇怪的电话飞客文章、桌上杂志里的电路图以及我从自助餐馆偷来的盐脆薄饼干——每顿饭后我都偷偷包起来放进口袋——它们已经达到了20磅之多。

---

## 第七章 与乔布斯一起疯狂

### 在伯克利分校度过第三学年后，这场车祸促使我辍学开始工作。我需要赚钱，不仅需要赚回我第四年的学费，还需要赚钱买辆新车。如果我那年没有遇到车祸，就不会辍学，也就不可能创立苹果公司。事情的发展总是难以预料。

---

## 第八章 白天惠普上班，夜晚疯狂兼差

### 尽管伊智的工作很棒，但惠普提供的却堪称是世界上最完美的工作：在这家最伟大的工程公司参与王牌产品——便携式科学计算器的开发！我想，没有人能拒绝这个机会。

---

### 我也持有同样的看法。我认为公司就像一个大家庭，在这里我们要互相关心。我一向反对这类流行观点：公司由市场竞争驱动，面临困难时应首先解雇最差、最年轻和资历最浅的员工。

---

### 多年前在步行上学途中的思考就让我下定了决心，自己要始终忠于事实，从事跟实实在在的计算相关的工作。我很清楚，自己不善社交。越战征兵的经历更让我坚定了这一想法。即使是在22岁这样的年纪，我也非常确定自己不想从工程领域跳至管理层。我不愿进入管理层，去争权夺利，或是踩着别人的肩膀往上爬。我知道这一梦想在惠普能够实现——也就是说，做一辈子的工程师，永远不进入管理层。我如此确定这一点，源于我在惠普认识的年资甚高的工程师，他们不想从事管理工作。自从我遇到他们，就相信自己的愿望有可能实现。我在惠普工作了将近四年，却还没有获得本科学位。我向经理许诺，自己会在附近的圣何塞州立大学上一些夜间课程来获取学位。我无法想象辞掉工作，整天学习，因为我做的工作实在是太重要了。

<aside> 💡 在如今中国的哪里能实现呢？

</aside>

---

## 第十章 我的大主意

### 当时我并没意识到这一天有多重要。1975年6月29号，星期天，是个重要的日子。有史以来，键盘上敲打的符号第一次在屏幕上直接显示出来。

---

## 第十一章 Apple I

### 他打了几个电话，用他无与伦比的说服技巧成功地从英特尔拿到一些免费的DRAM芯片——要知道，当时这些芯片不仅十分昂贵，而且还很稀有，史蒂夫简直创造了奇迹！史蒂夫就是这样，他知道怎么跟销售代表谈话。而我永远也做不到，我实在是太害羞了。

---

### 他问我用的是合成技术公司的哪一款芯片，我告诉了他款式与编号。他指出，那些芯片比仙童的同类产品体积更大，需要更多的引脚和线路来连接，从而增加了复杂性。当时我便目瞪口呆。他让我意识到，真正简洁的计算机设计要求更少的连接，而不仅仅是少用几块芯片。所以，我的计算机设计目标从减少芯片数量转变成了缩减主板面积。

---

### 但史蒂夫有一个很好的论点。当时我们在他的车里，我直到今天还清楚地记得他当时说的话，仿佛这一切发生在昨天一样。他说：“好，就算赔钱，我们也拥有了自己的公司。我们一辈子还有比这更好的创立公司的机会吗？”这句“一生中最好的机会”打动了我，让我激动万分。两个最好的朋友开始创业啦，噢，当时我就很清楚我会迈出这一步的。我怎么能拒绝这一诱人的提议呢？

---

## 第十二章 我们自己的公司

### 我们的第一批主板完成于1976年1月。它们看上去跟阿尔泰有几分相似，但事实上我们做的东西与阿尔泰有着天壤之别。让我记忆犹新的是，在那段等待的时间里，我简直是世界上最快乐的人。我那时高兴极了。我从未想过能靠苹果赚钱，我从始至终都没有过这样的念头。当时我唯一的想法是，哇，我已经发现了一个微处理芯片能做些什么，它的应用竟然如此广泛。我知道我这一辈子要做什么了，我要为我自己制作一个计算工具！我的头脑中充满了各种关于AppleI潜能的憧憬。那时我正着迷于视频游戏，我灵机一动，说不定我的小计算机也可以用来玩游戏了？我还预想到未来的文字处理软件会彻底取代打字机。我打字很快，尽管我当时已经预见了这一前景，但我知道我们离它的实现还很遥远。我能想象计算机如何能在惠普的设计工作中帮上大忙。我能想象得到的计算机的每一项应用都有着巨大的潜在价值，我能清楚地看到它们在未来一一变成现实，这些愿景让我激动万分。

---

### 最终，当我为6502编写的解释器载入电脑时，用键盘输入小程序功能终于实现了。比如，让电脑问：“你叫什么名字？”如果你输入名字，它就能让你的名字满屏幕飞起来。今天看起来，这没有什么特别，但在当时却是极为惊人的。能用键盘输入程序，并把程序执行出来的小型计算机在当时闻所未闻。

---

### 当然，我也没有在这么多人面前讲过话。那是我有生以来经历的最大规模的公开演讲。那时，家酿计算机俱乐部已经发展了超过500名会员。这次会议在斯坦福大学线性加速器中心（SLAC）的大礼堂举行。我手里拿着印刷电路板，穿过过道，站在那儿，只是陈述了一下事实。我一生中只有两次在家酿计算机俱乐部的聚会上发言——另一次是在我介绍AppleII的时候。

---

## 第十三章 Apple II

### 两款机器都为计算机世界带来了惊人的进步。AppleI成为历史上第一台支持键盘和显示器的电脑，而AppleII则带来了彩色显示、高解析度的图像和声音，而且支持游戏控制板。它也是第一台ROM（只读内存）里内置BASIC程序的电脑，启动后即可使用。几年之后，其他电脑也添加了上述功能。后来，每家电脑公司都能提供相同的功能列表。

---

### 我想要提前检查一下AppleII是否能与投影仪兼容。我把AppleII的原型机连接上投影仪，色彩顺利地显示了出来。就在这时，那位工作人员看到了AppleII。他告诉我，在所有这些参展电脑中，他唯一愿意掏腰包购买的就是这台AppleII。我微笑着看看他——AppleII还没有公开发布呢。

---

### 任务，可能需要十年的时间。现在，可以轻轻松松地通过软件来实现。整个世界都会因此改变。”这一刻，AppleII开启了新世界。与依靠硬件制作的游戏相比，软件游戏的效能高得难以置信。

---

### 我说：“来，玩一下这个游戏。”我给他演示了如何操纵球拍。他坐在那里玩游戏，玩了15分钟，全屋子的人都看着他玩。球跑得非常快，他甚至都不知道是怎么挥动球拍的，却总能打到球。大家都以为他是个超级游戏高手。15分钟后，他赢了这一局。我们都向他道贺，简直把他当作世界上最牛的游戏玩家。我想，他应该不知道这只是个事先设好的结局。

---

### 我至今坚信，没让我参与计算机项目是惠普的巨大损失。那时，我对惠普忠心耿耿，希望毕生为之效力。我想说，如果你有一位员工说他对计算器工作感到厌倦，却在计算机方面十分在行，你应该把他安放到他最在行的领域，在那儿他才能真正感到快乐。我能想到的唯一的解释就是，计算机项目中的经理和主管感受到了威胁。我曾单枪匹马完成了整台计算机的设计，而这正是他们所顾忌的。他们也许认为：“如果我们让史蒂夫·沃兹尼亚克加入，怎么安排他的工作？难道让他做个小小的打印机界面工程师吗？”其实这样我也会觉得很快乐，但他们就是不愿意把我安排到我最喜欢、最擅长的领域里去。

---

### 第一次遇见迈克时，我就觉得他是最和善的人。他很年轻，拥有一栋可以俯瞰库比蒂诺的美丽的房子，那里视野十分开阔。他还有位迷人的妻子，这个人简直就是生活的宠儿。更幸运的是，他也喜欢我们的东西。他很诚恳，有什么就立刻表现出来，这点很重要。

---

### 所以我最终决定，放弃苹果公司，我宁愿留在惠普做我的全职工作，而把设计电脑当成业余爱好。我在最后期限的那一天来到了迈克家。在他家的阳台上，把我的决定告诉了迈克和史蒂夫。我告诉他们，我想过了，我最终决定留在惠普。我记得，迈克对此表现得很冷静。他耸耸肩，说：“嗯，好吧。”他说得非常简洁，也许他在想，再找一个苹果公司需要的人就行了。但是史蒂夫很失望，他强烈地感到AppleII是他们需要发展的产品。

---

### 几天后，我的电话开始响个不停。不管我是在公司还是家里，都会接到爸爸、妈妈、兄弟和各种朋友打来的电话。每个人都告诉我，我的决定是错误的，我应该加入苹果公司，毕竟25万美元不是一个小数目。后来我才知道，史蒂夫给他们都打过电话。显而易见，他觉得需要一些外人的干涉。但这都没有打动我，我依旧想留在惠普。

---

### 艾伦说：“史蒂夫，你知道吗，你真的应该去做这事。想想看，你可以做个工程师，之后成为经理来赚大钱，或者可以一直当工程师赚大钱。”他认为，对我而言这绝对可行，我可以一直当个工程师而永远不进入管理层。这才是我需要听到的。我需要听到别人告诉我，我可以一直在基层做一名工程师，而不必做经理。我马上给史蒂夫·乔布斯打了电话，告诉他我的新决定。他激动极了。第二天，我早早地来到惠普，走到一群老朋友们面前，告诉他们：“我决定离开惠普，去创立苹果公司。”

---

### 感谢上帝，史蒂夫和迈克没有发现我做了什么。迈克还叮嘱了几句：“别搞恶作剧，别开玩笑。这会给公司带来不良的影响。”那是职业人士的说法。但是他们是在跟史蒂夫·沃兹尼亚克打交道。我工作时也很严肃认真，做出了出色的产品。每个人都知道这一点。我也会认认真真地创业做公司，推介产品。但是对我而言，这一切应该是充满了玩笑和乐趣的，整个一生就应该这样度过。如果你想到这一点，就不难理解苹果电脑的个性中很大部分也是充满了欢乐。这都源于我喜欢开玩笑的性格。玩笑让工作更有劲。

---

### 我相信很多年后，人们都会认为是处理器技术公司所为。我在之后很多年也一直没有承认过这事，直到史蒂夫·乔布斯的生日晚会上。我送给他的相框里夹着那页传单。他一看这个，就大笑了起来。他从来没有想过这会是我干的。

---

## 第十四章 自福特上市以来最大的IPO

### 然而，是AppleII最终引发了个人电脑革命，它开创了许多先河，色彩是其中之一。我设计的AppleII能和你已经拥有的彩电一起工作。它有游戏控制板和内置音效。这是第一台实现了游戏机功能的电脑，第一台有音效和游戏控制板的电脑。AppleII甚至还有高解析度的模式，游戏程序员能在上面画一些特别的小图形。你可以通过屏幕为每个像素编程——让这个像素或开或关，显示不同的颜色——这些事情要在以前的低成本电脑上实现简直是天方夜谭。

---

### AppleII推出几个月内就涌现出几十家周边公司，他们为AppleII设计可以录在磁带上的游戏。他们都是新成立的公司，我们的设计使得以AppleII为平台的开发工作更加容易。总的来说，这些小公司大多都只有一个设计师，他在家里单枪匹马地写出简单的程序，复制在一堆盒式磁带中，然后再通过计算机专卖店进行销售。

---

### 还有一些小公司开始为AppleII的插槽接口生产线路板。为AppleII设计线路板很容易，因为我们提供了关于线路板工作情况的完整的文字介绍和记录。AppleII还囊括了许多很不错的工具，包括面向开发者开放的操作系统，还有一套我亲自编写的很好用的软件调试工具。

---

### 我问迈克，如果我在那之前完成了软盘驱动器，我能不能去拉斯维加斯？他说可以。我只有两周的时间给AppleII加上软驱，在这之前我从未见过软驱。但现在我已经有了“动力”——我渴望让人们再次被苹果震惊。（这是加引号的“动力”，因为如果我真的想去拉斯维加斯，迈克也不会反对。）

---

### 但是，备份完成后我看着两张都没贴标签的软盘，忽然心里一沉，感觉似乎是把空白盘进行了备份，覆盖了另一张盘上的所有数据。我快速检查了这两张软盘，我的担心果然成为了现实。当你精疲力竭时，常常会发生这样的事。我的“明智”犯下了一个低级错误。这意味着，我们将无法在几个小时后开幕的消费者电子展上展示自己的软盘了。我失望透顶。我们回到旅馆睡觉。上午10点左右，我醒来开始工作。我想试着重建这个系统。无论如何，至少我的脑子里都记着那些代码。我设法在中午前重建了程序，并带到展台上。我们做到了。我甚至无法用语言来描述它有多么成功，特别是跟同样来此地参展的RadioShack的TRS80以及康懋达的PET相比。

---

### 从月销量1000台猛增到10000台。天啊，这发展得太快了！1978和1979年两年间，我们如日中天。1980年，我们成为第一家售出100万台电脑的公司。我们也完成了继福特汽车之后最大规模的股票首次公开发行。我们因为在一个单日内产生百万富翁最多而载入史册。我相信，这要归功于AppleII、VisiCalc和软盘这三样东西的组合。

---

### 我想，嗯，他们抄袭了我的设计。他们会在多大程度上复制我的创意呢？我没想到他们会抄袭这么多。我认为，工程师接受的教育就是发明和设计自己的东西。工程师永远不应该抄袭别人的设计！这就是他们接受教育的目标——学会如何设计自己的东西。

---

## 第十五章 沃兹计划

### 但我想用别的方式卖掉我的股票。我很喜欢苹果公司的员工——当时苹果公司的员工已经超过了100人，这是一个很温馨的集体，我把他们看作家人一样。从我自己的第一份工作起，甚至在更早之前，我就把同事看成家人。

---

### 我决定以很低的价钱向他们售出苹果的股票，他们值得我这么做。普通员工不像管理层那样可以获得股票，我认为这不公平，于是我想出了“沃兹计划”——任何苹果员工，不管是工程师还是营销人员，都可以用5美元一股的价格向我购买苹果公司的股票，每人最多可以买2000股。基本上参与了“沃兹计划”的每一名员工在苹果上市后都买得起一栋别墅，过上舒服的生活。我对此感到非常高兴。

---

### 我们的其他产品都如此优秀，为什么偏偏AppleIII的表现就这么差劲儿呢？我可以告诉你答案。AppleIII并非由一名工程师或一个紧密的工程师团队设计，而是由公司高管们组成的委员会主导设计的。整个设计是由营销人员推动的。高管们有权力有资源去指挥电脑的工程设计，他们构想出一个电脑的样子，然后就组织人去实现他们的主意。

---

### 时至今日，AppleIII的失败都让我感到惊奇。在我看来，它的设计方式完全不是一个合格的工程师（甚至一个具有理智的普通人）会采用的。这次事件让我意识到大公司的工作方式是多么让人失望。

---

### 那时候，AppleIII面临着激烈的竞争。1981年，IBM推出了IBMPC来和AppleII抗衡。刚一面世，它就大获成功，销量增长迅速。突然间，我们遇到了我们从未预料到的严峻挑战。

---

### 诚然，苹果公司里汇集着一群优秀的工程师，但也“混”进了不少不怎么样的工程师，这几乎是所有公司快速发展时期都难以避免的。顺便提一下，也不一定是那些差劲儿的工程师的错。很可能仅是由于他们的兴趣与他们的职责不相符。总之，斯科蒂让工程部经理汤姆·惠特尼（TomWhitney）休了一周的假。在这一周的时间内，他深入工程部，和工程师一一谈话，了解了每个人的职责是什么，谁尽心尽力而谁又无所作为。然后他就解雇了一帮人。那天被称为“黑色星期一”。至少描写苹果公司历史的书都这么描述它。我想他的决定没有错，他开除的都是那些拖后腿的人。

---

### 从创立公司到在公司里与这么多不同的人共事，这段经历中我学到的经验是：在有着多年经验的人面前，千万不要伪装自己比他更懂他的专业。总的来说，我一直专注于我的工程天赋，很少过问别的事。这样保证了我和他人都在自己擅长的领域内高效工作。

---

## 第十六章 撞机事故

### 在爱丽丝和我离婚前，她告诉我，她的一个朋友雪莉想买一家剧院，一家真正的、可以运营的剧院。她看中了圣何塞的梅费尔剧院（MayfairTheater）。爱丽丝认为我应该买下它。如果爱丽丝想做什么，我永远不会让她失望。于是，我就把这家剧院买了下来。

---

### 我得告诉你，尽管我是共济会的终身会员，但我与其他共济会会员并没有多少相似之处。我的性格与他们非常不同。为了入会你必须谈论上帝、信仰上帝，仿佛他们是来自宪法一般理所当然。这种宗教思维方式与我自己的思考方式迥然不同。但我还是按照共济会的要求说了这些话，而且说得很好。如果我想做什么，我总是尽量做到最好。我之前说过，这样做只有一个原因：更多地看到爱丽丝。为了挽救婚姻，我愿意加入共济会，如果婚姻需要我这么做的话。我就是这样一个人。

---

## 第十八章 离开苹果，创建Cloud 9

### 乔·恩尼斯（JoeEnnis）正是这个项目工程师团队的一员。乔正是我喜欢的那种人，对自己正在参与制作的产品充满了激情，他对这些产品能用来做什么、能做到多少非常狂热。尽管当时已经是1985年，嬉皮士风潮早已消退，乔仍留着一头颇有嬉皮士范儿的长发。他充满了各种想法，想把AppleII延伸到各个领域，有些甚至比麦金塔部门的工程师所构想的功能设计更为深远。例如，他认为可以把AppleII通过编程改造为一个完整的电话总机。（今天，电话总机其实就是插进电话的电脑主板。）他还设想用数字化的方式储存声音——这太超前了；他甚至设想还能用数字化的方式安排它们转向其他频道。对于未来的计算机，他的想法一个接一个层出不穷，我觉得他的头脑和那些想法实在是太棒了。

---

### 我开始跟许多人谈这个想法，开始为之激动。因为我知道做到这一点有多么容易。它是个很简单的项目。在你按键的时候，一个微处理器就能够记录你输入的那些代码，然后储存信息，之后再输出同样的代码。正如你所知，我总想成为某个领域的第一人。我认为，我是第一个这么做的人。事实上，我确实也是世界上第一个设计出现在所谓的万能遥控器的人。

---

### 辞职当天，我正在收拾东西时，记者给我打了电话：“我想你要去开办一家新公司，是这样吗？”谣言已经传出去了。我告诉他，是的。他开始了解新公司的相关情况，我一一告诉了他。接着他问：“你在苹果过得不开心吗？”我告诉他真相。我说是的，还对那些一起工作过、没有得到公司应有尊重的同事表示支持。

---

### 总之，我向他大致描述了一下我们在做什么，他马上就表示他希望投资。我告诉他我并不需要钱，我自己筹好了资金。但是他当时简直就是在恳求我让他入股。好吧，当人们恳求我，说他们想参与某件事情时，我通常只好投降。

---

## 第十九章 疯帽子

### 我想，每个人一生中都会回首往事并扪心自问：我还能有什么？我还能做什么？对我来说，找到答案根本不是什么问题。如果我不做工程师，我会做一名老师，不是中学或大学老师，而是一名小学五年级老师。自从我五年级以来，就特别想成为一名五年级的老师。这是我从小就很想做的一件事，谁知道这种主意从何而来？可能是因为我四五年级时遇到的老师沙克莱克小姐对我很好，我很喜欢她，她的鼓励对我一生帮助良多。我从心底相信，教育十分重要。我记得爸爸那时候告诉我，教育能让我们提升生命的高度，也能提升人的价值。

---

### 我想，清楚地看到你想成为什么样的人、想过什么样的生活、想构建什么样的社会，这些都是生活中最重要、最崇高的目标。

---

### 这也帮助我记起，我曾渴望做一个老师的愿望。后来，无论到哪儿，我都十分关注孩子——婴儿、幼儿、小孩子、大孩子。我试着跟他们交流，向他们微笑，给他们讲笑话，成为他们的玩伴。我自幼就被灌输这样一个想法：有“坏人”会伤害或绑架孩子，所以我决定成为一个“好人”，一个任何孩子都可以依靠的“好人”。

---

### 我还记得那么一个夏天。当时我在惠普工作，史蒂夫·乔布斯告诉我，他需要找份工作来赚点儿钱。我开车把他送到迪安扎社区学院，去看看那里的招工广告。

---

### 我觉得这工作很好玩，所以，在那周里我宁愿减少工程师的工作时间，尽量腾出时间做这份只领取最低工资的工作。我喜欢孩子们看着我的脸——简直是热爱！

---

### 我在十年的教师生涯中学到了不计其数的东西。我觉得那是我一生中最重要的时光。

---

## 第二十章 一些原则

### 我们的世界并不是一个非黑即白的世界，这个世界是灰度的。作为一个发明家，你必须要用灰度的眼光来看世界。

---

### 大多数人并不觉得工程师是艺术家，在他们看来，工程师仅仅与他们所制造出的各种仪器相关。但若没有工程师的精心设计，没有那种用最少的元器件来制造出最好的东西的执着态度，我们根本无法享有那些具有智能的优美仪器。这正是工程学的精妙之处。在我一生中所认识的工程师里，只有不到20位能真正践行工程学这一充满艺术性领域的精妙之处。的确，把自己的工程作品变成艺术很难，但这应该是每个工程师为之奋斗的目标。

---

### 最近，电影《与歌同行》（WalltheLine）中的一幕深深地触动了我：制作人告诉约翰尼·卡什说，唱歌时应该把这首歌当作可以拯救世界的一首歌来用心演绎。上面这句话也适用于工程学或其他一切职业，力臻完美，用心演绎。

---

### 如果你能预测未来，发明将容易得多。但即使你和我们当初在苹果公司一样，做的都是引领计算机发展走向的产品，预测未来依然很难。20世纪七八十年代时，我还在苹果公司，我一直试着向前看，看事物向什么方向发展。看到一两年后的趋势很容易，因为设计这些产品的人跟其他公司有千丝万缕的联系。但是，一两年之后的事物就很难看清了。我唯一能依靠的是电子行业著名的摩尔定律（得名于英特尔的创立者之一戈登·摩尔）——每18个月，计算机芯片里可容纳的晶体管数量就会翻一番。这意味着电脑会变得越来越小、越来越便宜，我们已经看到了这一点。但是，我们当时很难想象谁能从这一趋势中真正获益。我们没有预料到高速调制解调器，没有预想到电脑可内置大容量的硬盘，没有预想到互联网会在阿帕网的基础上发展起来并为大众所用，也没有想到数码相机的横空出世。这些我们通通没有预料到，我们至多能看到眼前未来一两年的事物发展趋势。但是有一个例外：大约1980年，史蒂夫和我们一帮苹果公司的人到施乐公司的帕洛奥图研究中心（PARC）去了一趟。

---

### 我希望你也像我一样幸运。这个世界需要发明家——好的发明家。你可以成为这样的人。如果你热爱你做的事，愿意付出相应的劳动，你终将有所得。你在夜里独自工作，反复思考你所设计和制造的东西，为之花费的每一分钟都是值得的。我敢肯定地告诉你，你的付出是值得的。

---