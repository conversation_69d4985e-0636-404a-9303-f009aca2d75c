---
title: "bitcoinbook/ch11_blockchain.adoc at develop · bitcoinbook/bitcoinbook"
source: "https://github.com/bitcoinbook/bitcoinbook/blob/develop/ch11_blockchain.adoc"
author:
  - "[[GitHub]]"
published:
created: 2025-02-15
description: "Mastering Bitcoin 3rd Edition - Programming the Open Blockchain - bitcoinbook/ch11_blockchain.adoc at develop · bitcoinbook/bitcoinbook"
tags:
  - "clippings"
---
## The Blockchain 区块链

![mbc3 1101](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_1101.png)

### Merkle Trees 默克尔树

默克尔树在比特币中用于汇总一个区块中的所有交易，对整个交易集做出总体承诺，并允许一个非常高效的过程来验证一笔交易是否包含在一个区块中。

当 N 个数据元素在默克尔树中进行哈希和汇总时，你可以通过大约 log₂(N) 次计算来检查任何一个数据元素是否包含在树中，这使得它成为一种非常高效的数据结构。

![merkle_tree](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_1102.png)

由于默克尔树是一棵二叉树，所以它需要偶数个叶节点。如果要汇总的交易数量为奇数，则将最后一个交易哈希重复，以创建偶数个叶节点，这也被称为“平衡树”。

为了证明特定交易被包含在一个区块中，一个节点只需要生成大约 log₂(N)个 32 字节的哈希，构成一个“认证路径”或“默克尔路径”，将特定交易连接到树的根。

节点可以通过生成仅四个 32 字节哈希长度（总共 128 个字节）的默克尔路径来证明交易 K 包含在区块中。该路径由四个哈希（以阴影背景显示）HL、HIJ、HMNOP 和 HABCDEFGH 组成。有了这四个哈希作为认证路径，任何节点都可以通过计算另外四个成对哈希 HKL、HIJKL、HIJKLMNOP 和默克尔树的根（在图中以虚线轮廓显示）来证明 HK（在图底部以黑色背景显示）包含在默克尔根中。

![merkle_tree_path](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_1106.png)

### Merkle Trees and Lightweight ClientsMerkle 树和轻量级客户端

轻量级客户端没有所有的交易记录，也不下载完整的区块，只下载区块头。为了在不下载区块中所有交易的情况下验证一笔交易是否包含在一个区块中，它们使用默克尔路径。

例如，考虑一个轻量级客户端，它对其钱包中包含的地址的传入付款感兴趣。轻量级客户端将在其与对等节点的连接上建立一个布隆过滤器（见[\[布隆过滤器\]](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#bloom_filters)），以将接收到的交易限制为仅包含其感兴趣的地址的交易。当一个对等节点看到与布隆过滤器匹配的交易时，它将使用默克尔区块消息发送该区块。默克尔区块消息包含区块头以及将感兴趣的交易链接到区块中默克尔根的默克尔路径。轻量级客户端可以使用这个默克尔路径将交易连接到区块头，并验证该交易是否包含在区块中。

### Bitcoin’s Test Blockchains 比特币的测试区块链