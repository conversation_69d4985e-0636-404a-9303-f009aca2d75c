- [x] how to write allowed callback URLs: https://*.tg-662578936.byoc.tgcloud-dev.com/api/auth/oidc/callback
- [x] #task  `oidc/authnrequest` is returning cloud v2 address : ⏳ 2024-08-09 ✅ 2024-08-09
## Discussion
- 先演示正常的流程
	- Our database has built-in GUI applications such as graph studio and admin portal. We can find them in the connect option of workspace. If I hover my mouse over it, we can see the application's URL. Let me open a new tab and copy this URL. We can see that the url contains several parameters, such as the workspace host, workspace id, and so on. (Open network) If we visit this url, the front end will automatically log in using OIDC protocol. We can see this process in the network panel. It first jumps to the auth zero service for authentication. Since we've already logged in on cloud portal, there's no need for users to manually log in. Then auth zero returns an id token to the front end, and the font end is redirected to a callback endpoint of the GUI server in the workspace . And the GUI server will save the id token to the session and invokes GSQL for authentication. Eventually, the browser is redirected to the app we want to access.
- However, there is a issue with this process if we use a workspace created by byoc (demonstrated by using the byoc branch).
- As you can see here, auth zero returns an error "xxx" that tells me I need to add redirect_url to the allowed callback url setting in the auth zero application. This redirect_url is actually the callback endpoint in the workspace (demonstrate adding redirect_url).
- So I want to confirm how the domain of the workspace created by byoc is constructed. Is only the first part of the domain dynamic?