---
title: bitcoinbook/ch04_keys.adoc at develop · bitcoinbook/bitcoinbook
source: https://github.com/bitcoinbook/bitcoinbook/blob/develop/ch04_keys.adoc#table_4-2
author:
  - "[[GitHub]]"
published: 
created: 2025-02-03
description: Mastering Bitcoin 3rd Edition - Programming the Open Blockchain - bitcoinbook/ch04_keys.adoc at develop · bitcoinbook/bitcoinbook
tags:
  - clippings
  - flash
---
## 回想
- 公钥和私钥
	- 私钥签名，公钥验证
	- 基于椭圆曲线函数，K = k * G
		- 椭圆曲线的加法和乘法
		- 加法的几何学意义：两点的连线会交与椭圆曲线上的第三点，在x轴上进行反射
		- 公钥是椭圆曲线上的一个点，可以只存储x坐标和y的正负性，通过函数计算出y
- 输入和输出脚本
	- 包含输入/输出的数据，以及如何进行验证的操作码
- PTPK
	- 在输出脚本中只包含接收方的public key
	- 几乎未被使用
- PTPKH
	- 在输出脚本中包含public key的hash值，减少传输的size
- PTSK
	- 输出脚本可以指定一个redeem script
	- 用户花费输出脚本中的bitcoin时，需要给出redeen script需要的参数（如签名、公钥等）
- Base58
	- PTPKH，PTSH使用此种编码方式编码地址
- Bech32/Bech32m
	- 解决Base58的一些问题
## Keys and Addresses

### Public Key Cryptography

自公钥密码学发明以来，人们发现了一些合适的数学函数，如质数指数运算和椭圆曲线乘法。这些数学函数在一个方向上容易计算，但在另一个方向上，使用当今的计算机和算法是不可行的。基于这些数学函数，密码学能够创建不可伪造的数字签名。比特币使用椭圆曲线加法和乘法作为其密码学的基础。

公钥和私钥之间存在一种数学关系，这种关系使得私钥可用于对消息进行签名。这些签名可以根据公钥进行验证，而无需透露私钥。

#### Private Keys 私钥

私钥只是一个随机选取的数字。对私钥的控制是用户对与相应比特币公钥相关联的所有资金进行控制的根本。私钥用于创建签名，通过证明对交易中使用的资金的控制来花费比特币。私钥必须始终保密，因为向第三方透露私钥相当于让他们控制由该密钥保护的比特币。私钥还必须进行备份并防止意外丢失，因为如果丢失，它无法恢复，由它保护的资金也将永远丢失。

生成密钥的第一步也是最重要的一步是找到一个安全的随机源（计算机科学家称之为“熵”）。创建比特币密钥几乎与“在 1 到 2 的 256 次方之间选一个数”相同。你用来选那个数的确切方法并不重要，只要它不可预测或不可重复。比特币软件使用加密安全的随机数生成器来产生 256 位的熵。

#### Elliptic Curve Cryptography Explained椭圆曲线密码学解释

椭圆曲线密码学（Elliptic curve cryptography，ECC）是一种非对称或公钥密码学，基于椭圆曲线上点的加法和乘法所表示的离散对数问题。

![ecc-curve](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_0402.png)

比特币使用特定的椭圆曲线和一组数学常数，如在名为 secp256k1 的标准中所定义，该标准由美国国家标准与技术研究院（NIST）制定。secp256k1 曲线由以下函数定义，该函数生成一个椭圆曲线：

“*mod p*（模素数*p*）表示此曲线在素数阶有限域*p*上，也写作\\(\\(\\mathbb{F}\_p\\)\\)，其中*p* = 2256 – 232 – 29 – 28 – 27 – 26 –24 – 1，这是一个非常大的素数。”

由于这条曲线是在素数阶有限域上定义的，而不是在实数上定义的，因此它看起来像是二维空间中散布的点的图案，这使得它难以可视化。然而，其数学原理与实数上的椭圆曲线相同。例如，[椭圆曲线密码学：可视化素数阶为 17 的有限域上的椭圆曲线。](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#ecc-over-F17-math)展示了在小得多的素数阶为 17 的有限域上的相同椭圆曲线，呈现出网格上的点的图案。比特币的 secp256k1 椭圆曲线可以被认为是在一个大到难以想象的网格上的更为复杂的点的图案。

![ecc-over-F17-math](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_0403.png)

还有一个 + 运算符，称为“加法”，它具有一些与小学生学习的实数传统加法相似的性质。给定椭圆曲线上的两个点 P1 和 P2，有第三个点 P3 = P1 + P2，也在椭圆曲线上。

从几何角度来看，第三个点 P3 是通过在 P1 和 P2 之间画一条线来计算的。这条线将与椭圆曲线恰好相交于另外一个点。将这个点称为 P3'=(x,y)。然后在 x 轴上进行反射得到 P3=(x,-y)。

如果 P1 和 P2 是同一点，那么 P1 和 P2 之间的“线”应该延伸为该点 P1 处曲线上的切线。这条切线将与曲线恰好相交于一个新点。你可以使用微积分的技术来确定切线的斜率。令人好奇的是，即使我们将兴趣限制在具有两个整数坐标的曲线上的点，这些技术仍然有效！

事实证明，+是可结合的，这意味着(A + B)+ C = A+(B + C)。这意味着我们可以写出 A + B + C，无需括号且没有歧义。

现在我们已经定义了加法，我们可以用扩展加法的标准方式来定义乘法。对于椭圆曲线上的一个点 P，如果 k 是一个整数，那么 kP = P + P + P +…+ P（k 次）。请注意，在这种情况下，k 有时会被令人困惑地称为“指数”。

#### Public Keys 公钥

公钥是使用椭圆曲线乘法从私钥计算得出的，这是不可逆的：*K* = *k* × *G*，其中 *k* 是私钥，*G* 是一个称为*生成点*的常量点，*K*是生成的公钥。反向操作，即“求离散对数”——如果知道*K*计算*k*——就像尝试所有可能的*k*值（即暴力搜索）一样困难。在我们演示如何从私钥生成公钥之前，让我们更详细地了解一下椭圆曲线密码学。

椭圆曲线乘法是一种密码学家称为“陷门”函数的类型：在一个方向上（乘法）很容易做到，而在相反方向上（除法）则不可能做到。拥有私钥的人可以轻松创建公钥，然后与全世界共享公钥，因为他们知道没有人可以逆转这个函数并从公钥计算出私钥。这个数学技巧成为不可伪造且安全的数字签名的基础，可证明对比特币资金的控制权。

以随机生成的数字形式的私钥*k*开始，我们将其乘以曲线上预先确定的点，称为*生成点**G*，以在曲线上的其他位置生成另一个点，这就是相应的公钥*K*。生成点作为 secp256k1 标准的一部分被指定，并且对于比特币中的所有密钥始终是相同的。

### Output and Input Scripts（输出和输入脚本）
??
虽然原始比特币论文中的插图《原始比特币论文中的交易链》显示公钥（pubkeys）和签名（sigs）被直接使用，但比特币的第一个版本是将支付发送到一个名为“输出脚本”的字段，并通过一个名为“输入脚本”的字段授权这些比特币的支出。这些字段允许执行除了（或代替）验证签名是否与公钥对应之外的其他操作。例如，输出脚本可以包含两个公钥，并要求在支出输入脚本中放置两个相应的签名。
<!--SR:!2025-02-06,1,130-->

稍后，在[\[tx\_script\]](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#tx_script)中，我们将详细了解脚本。目前，我们只需要理解比特币是通过一个类似于公钥的输出脚本来接收的，而比特币的支出是由一个类似于签名的输入脚本来授权的。

使用她的节点从鲍勃的节点接收到的公钥，爱丽丝的钱包将构建一个交易输出，支付一个非常简单的输出脚本。

```
<Bob's public key> OP_CHECKSIG
```

Bob would later be able to spend that output with an input script consisting entirely of his signature:Bob 稍后可以使用完全由他的签名组成的输入脚本来花费该输出：

```
<Bob's signature>
```

要弄清楚输出脚本和输入脚本在做什么，可以将它们组合在一起（先输入脚本），然后注意到每一块数据（显示在尖括号中）都被放置在一个称为栈的项目列表的顶部。当遇到操作码（opcode）时，它会从栈顶的项目开始使用栈中的项目。让我们通过从组合脚本开始来看看这是如何工作的。

```
<Bob's signature> <Bob's public key> OP_CHECKSIG<Bob's signature> <Bob's public key> OP_CHECKSIG
```

这种类型的输出在当今被称为“支付到公钥”，简称“P2PK”。它从未被广泛用于支付，并且近十年来，没有被广泛使用的程序支持过 IP 地址支付。

### Legacy Addresses for P2PKH

这让我们回到了像鲍勃这样的接收者必须给像爱丽丝这样的发送者一个长公钥的问题。早期比特币开发者所知的最短版本的比特币公钥是 65 个字节，用十六进制书写时相当于 130 个字符。然而，比特币已经包含了几个比 65 个字节大得多的数据结构，这些数据结构需要在比特币的其他部分使用尽可能少的安全数据进行安全引用。

比特币通过*哈希函数*实现这一点，哈希函数是一种接受可能大量数据、对其进行打乱（哈希处理）并输出固定数量数据的函数。加密哈希函数在给定相同输入时总是产生相同的输出，并且安全的函数还会使某人选择产生先前见过的输出的不同输入变得不切实际。这使得输出成为对输入的*承诺*。实际上，这是一个承诺，只有输入*x*才会产生输出*X*。

出于中本聪从未说明的原因，比特币的原始版本通过先使用 SHA256 对密钥进行哈希运算，然后再对该输出使用 RIPEMD-160 进行哈希运算，从而对公钥做出承诺；这就产生了对公钥的 20 字节承诺。

现在我们了解了如何对一个公钥做出承诺，我们需要弄清楚如何在交易中使用它。考虑以下输出脚本：

```
<sig> <pubkey> OP_DUP OP_HASH160 <commitment> OP_EQUALVERIFY OP_CHECKSIG
```

虽然这个向公钥哈希（*P2PKH*）付款的过程可能看起来很复杂，但它允许爱丽丝向鲍勃付款时只包含对他公钥的一个 20 字节的承诺，而不是公钥本身，在比特币的原始版本中，公钥本来是 65 个字节。这对鲍勃来说，要传达给爱丽丝的数据就少了很多。

### Base58check Encoding 
为了以更紧凑的方式表示长数字，使用更少的符号，许多计算机系统使用基数（或进制）高于 10 的混合字母数字表示法。例如，传统的十进制系统使用 10 个数字，即 0 到 9，而十六进制系统使用 16 个，其中字母 A 到 F 作为六个额外的符号。以十六进制格式表示的数字比等效的十进制表示更短。更紧凑的是，Base64 表示法使用 26 个小写字母、26 个大写字母、10 个数字以及另外两个字符（如“+”和“/”），以便在基于文本的媒体（如电子邮件）上传输二进制数据。

Base58 是一种与 base64 类似的编码，使用大写和小写字母以及数字，但省略了一些容易相互混淆且在某些字体中显示时可能看起来相同的字符。具体来说，Base58 是没有数字 0、大写字母 O、小写字母 l、大写字母 I 以及符号“+”和“/”的 base64。或者更简单地说，它是一组小写和大写字母以及数字，但不包括刚才提到的四个（0、O、l、I）。[比特币的 Base58 字母表](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#base58alphabet)展示了完整的 Base58 字母表。

为了增加对打字错误或转录错误的额外安全性，base58check 包含了用 base58 字母表编码的*校验和*。校验和是添加到正在编码的数据末尾的额外四个字节。校验和是从编码数据的哈希值中派生出来的，因此可以用于检测转录和打字错误。当提供 base58check 代码时，解码软件将计算数据的校验和，并将其与代码中包含的校验和进行比较。如果两者不匹配，则表示引入了错误，并且 base58check 数据无效。这可以防止钱包软件将错误输入的比特币地址作为有效目的地接受，否则会导致资金损失。

要将数据（一个数字）转换为 base58check 格式，我们首先向数据添加一个前缀，称为“版本字节”，它用于轻松识别被编码的数据类型。例如，前缀零（十六进制中的 0x00）表示该数据应在传统 P2PKH 输出脚本中用作承诺（哈希）。常见版本前缀列表如[\[base58check\_versions\]](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#base58check_versions)所示。

![Base58checkEncoding](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_0406.png)

![pubkey_to_address](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_0407.png)

### Compressed Public Keys 压缩公钥
??
正如我们在[公钥](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#public_key_derivation)部分中看到的，公钥是椭圆曲线上的一个点(x, y)。因为曲线表示一个数学函数，曲线上的一个点代表方程的一个解，因此，如果我们知道*x*坐标，我们可以通过解方程 y2 mod p = (x3 + 7) mod p 来计算*y*坐标。这使我们能够只存储公钥点的*x*坐标，省略*y*坐标，并将密钥的大小和存储它所需的空间减少 256 位。在每次交易中，大小几乎减少 50%，随着时间的推移，这将节省大量数据！
<!--SR:!2025-02-06,1,130-->

![pubkey_compression](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_0408.png)

### Legacy Pay to Script Hash (P2SH) 

如我们在前面的章节中所见，接收比特币的人（如鲍勃）可以要求支付给他的款项在其输出脚本中包含某些约束条件。当鲍勃花费这些比特币时，他需要使用输入脚本来满足这些约束条件。在“IP 地址：比特币的原始地址（P2PK）”中，约束条件仅仅是输入脚本需要提供适当的签名。在“P2PKH 的传统地址”中，还需要提供适当的公钥。

2012 年对比特币协议的 BIP16 升级允许输出脚本提交一个*赎回脚本*（*redeem script*）。当鲍勃花费他的比特币时，他的输入脚本需要提供一个与承诺相匹配的赎回脚本，以及满足该赎回脚本所需的任何数据（如签名）。让我们首先想象一下，鲍勃想要花费他的比特币时需要两个签名，一个来自他的桌面钱包签名，一个来自硬件签名设备。他将这些条件放入一个赎回脚本中：

当鲍勃使用他收到的款项进行脚本承诺的支出时，他使用一个输入脚本，该脚本包括赎回脚本，赎回脚本被序列化为单个数据元素。他还提供了满足赎回脚本所需的签名，并按照操作码将使用这些签名的顺序放置它们。

当比特币全节点收到鲍勃的支出时，它们将验证序列化的赎回脚本哈希值是否与承诺值相同。然后，它们将在栈上用其反序列化的值替换它。

为了同时处理 bech32 和 bech32m，我们将研究 bech32m 比特币地址的编码和解析规则，因为它们包含解析 bech32 地址的能力，并且是目前比特币钱包推荐的地址格式。

```
<public key 1> OP_CHECKSIGVERIFY <public key 2> OP_CHECKSIG<public key 1> OP_CHECKSIGVERIFY <public key 2> OP_CHECKSIG<public 1 key> OP_CHECKSIGVERIFY <public 2 key> OP_CHECKSIG</public></public>
```

```
<signature2> <signature1> <redeem script><signature2> <signature1> <redeem script>“<signature2> <signature1> <redeem script>”</redeem></signature1></signature2>
```

```
<signature2> <signature1> <pubkey1> OP_CHECKSIGVERIFY <pubkey2> OP_CHECKSIG<signature2> <signature1> <pubkey1> OP_CHECKSIGVERIFY <pubkey2> OP_CHECKSIG<signature2> <signature1> <pubkey1> OP_CHECKSIGVERIFY <pubkey2> OP_CHECKSIG</pubkey2></pubkey1></signature1></signature2>
```

P2SH 的地址也是用 base58check 创建的。版本前缀设置为 5，这导致编码后的地址以 3 开头。一个 P2SH 地址的例子是 **********************************。

P2PKH 和 P2SH 是仅有的两种与 base58check 编码一起使用的脚本模板。它们现在被称为传统地址，并且随着时间的推移变得不那么常见了。传统地址已被 bech32 系列地址所取代。

### Bech32 Addresses

如[\[P2SH\]](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#p2sh)中所述，P2SH 输出类型的优势之一是支出方（如爱丽丝）无需知道接收方（如鲍勃）使用的脚本细节。隔离见证升级旨在利用这一机制，允许用户通过使用 P2SH 地址立即开始获得许多新优势。但是，为了让鲍勃获得所有优势，他需要爱丽丝的钱包使用不同类型的脚本来向他付款。这将要求爱丽丝的钱包进行升级以支持新脚本。

从事隔离见证地址格式开发的人员在一种名为 bech32（发音时“ch”发软音，如“besh thirty-two”）的新地址格式中为这些问题中的每一个都找到了解决方案。“bech”代表 BCH，是 1959 年和 1960 年发现 bech32 所基于的循环码的三个人名字首字母。“32”代表 bech32 字母表中的字符数量（类似于 base58check 中的 58）。

Bech32 仅使用数字和单一情况的字母（最好以小写形式呈现）。尽管其字母表的大小几乎是 base58check 字母表的一半，但用于支付见证公钥哈希（P2WPKH）脚本的 Bech32 地址仅比等效的 P2PKH 脚本的传统地址略长。

#### Bech32m Bech32m

开发人员详尽地分析了 bech32 问题，并发现更改他们算法中的一个常量将消除该问题，确保任何插入或删除最多五个字符的情况只会在不到十亿分之一的情况下未被检测到。

具有单个不同常量的 bech32 版本被称为 bech32 修改版（bech32m）。

意味着钱包需要知道正在使用哪个版本才能验证校验和，但两种地址类型都包含一个内部版本字节，这使得确定版本变得容易。

Bech32m 地址以人类可读部分（HRP）开头。

HRP 后面跟着一个分隔符，数字“1”。

bech32m 地址的另一部分称为“数据部分”。此部分有三个要素：

Witness version “见证版本”。

Witness program 见证计划

Checksum 校验和

对于 P2WPKH 输出，见证程序包含一个承诺，其构建方式与在“传统地址用于 P2PKH”中看到的 P2PKH 输出的承诺完全相同。将公钥传入 SHA256 哈希函数。生成的 32 字节摘要随后被传入 RIPEMD-160 哈希函数。该函数的摘要（承诺）被放置在见证程序中。

对于支付见证脚本哈希（P2WSH）输出，我们不使用 P2SH 算法。相反，我们将脚本传入 SHA256 哈希函数，并在见证程序中使用该函数的 32 字节摘要。对于 P2SH，SHA256 摘要再次使用 RIPEMD-160 进行哈希，但在某些情况下这可能不安全；有关详细信息，请参阅《P2SH 冲突攻击》。不使用 RIPEMD-160 而仅使用 SHA256 的结果是，P2WSH 承诺为 32 字节（256 位），而不是 20 字节（160 位）。

对于支付到 taproot（P2TR）的输出，见证程序是 secp256k1 曲线上的一个点。它可能是一个简单的公钥，但在大多数情况下，它应该是一个对公钥承诺一些额外数据的公钥。我们将在《\[taproot\]》中了解更多关于该承诺的内容。

#### Private Key Formats 私钥格式

私钥可以用多种不同的格式表示，所有这些格式都对应于同一个 256 位数字。[\[表 4-2\]](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#table_4-2)展示了用于表示私钥的几种常见格式。不同的格式在不同的情况下使用。十六进制和原始二进制格式在软件内部使用，很少展示给用户。WIF（钱包导入格式）用于在钱包之间导入/导出密钥，并且经常用于私钥的二维码（条形码）表示中。

#### Compressed Private Keys 压缩私钥

常用术语“压缩私钥”是一个错误的名称，因为当私钥以 WIF 压缩格式导出时，它实际上比“未压缩”私钥长一个字节。这是因为私钥添加了一个单字节后缀（在 \[表 4-4\]中以十六进制显示为 01），这表示私钥来自较新的钱包，并且只应用于生成压缩公钥。私钥本身不能被压缩。术语“压缩私钥”实际上意味着“只能从中导出压缩公钥的私钥”，而“未压缩私钥”实际上意味着“只能从中导出未压缩公钥的私钥”。你应该仅将导出格式称为“WIF 压缩”或“WIF”，而不要将私钥本身称为“压缩”，以避免进一步的混淆。