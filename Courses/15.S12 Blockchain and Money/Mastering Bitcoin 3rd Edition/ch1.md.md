## 引言

比特币是一系列概念和技术的集合，构成了一个数字货币生态系统的基础。比特币网络中的参与者使用称为比特币的货币单位来存储和传输价值。比特币用户主要通过互联网使用比特币协议进行通信，尽管也可以使用其他传输网络。比特币协议栈作为开源软件，可以在各种计算设备上运行，包括笔记本电脑和智能手机，使得这项技术易于访问。

|提示|在本书中，货币单位称为“bitcoin”（小写b），而系统称为“Bitcoin”（大写B）。|
|---|---|

用户可以通过网络转移比特币，完成几乎所有可以用传统货币完成的事情，包括买卖商品、向个人或组织汇款或提供信贷。比特币可以在专门的货币交易所购买、出售和兑换其他货币。比特币可以说是互联网的完美货币形式，因为它快速、安全且无国界。

与传统货币不同，比特币货币完全是虚拟的。没有物理硬币，甚至没有单独的数字硬币。硬币隐含在将价值从支出者转移到接收者的交易中。比特币用户控制着允许他们在比特币网络中证明比特币所有权的密钥。通过这些密钥，他们可以签署交易以解锁价值并通过将其转移给新所有者来花费它。密钥通常存储在用户计算机或智能手机上的数字钱包中。拥有可以签署交易的密钥是花费比特币的唯一先决条件，将控制权完全掌握在每个用户手中。

比特币是一个分布式、点对点的系统。因此，没有中央服务器或控制点。比特币单位通过称为“挖矿”的过程创建，该过程涉及反复执行一个计算任务，该任务引用最近的比特币交易列表。比特币网络中的任何参与者都可以作为矿工运行，使用他们的计算设备帮助保护交易。平均每10分钟，一个比特币矿工可以为过去的交易增加安全性，并获得全新比特币和最近交易支付的费用作为奖励。本质上，比特币挖矿将中央银行的货币发行和清算功能去中心化，并取代了对任何中央银行的需求。

比特币协议包括内置算法，用于调节整个网络的挖矿功能。矿工必须执行的计算任务的难度会动态调整，以便平均每10分钟有人成功，无论有多少矿工（以及多少处理能力）在任何时刻竞争。该协议还会定期减少新创建的比特币数量，将最终创建的比特币总数限制在略低于2100万枚的固定总量。结果是，流通中的比特币数量紧密遵循一个易于预测的曲线，每四年将剩余的一半比特币添加到流通中。大约在2035年左右的第1,411,200个区块时，99%的比特币将被发行。由于比特币的发行率逐渐降低，从长期来看，比特币货币是通缩的。此外，没有人可以强迫你接受超出预期发行率的比特币。

在幕后，比特币也是协议、点对点网络和分布式计算创新的名称。比特币建立在数十年的密码学和分布式系统研究基础上，并至少包括四项关键创新，这些创新以独特而强大的方式结合在一起。比特币包括：

- 一个去中心化的点对点网络（比特币协议）
- 一个公共交易日志（区块链）
- 一套用于独立交易验证和货币发行的规则（共识规则）
- 一种在有效区块链上达成全球去中心化共识的机制（工作量证明算法）

作为一名开发者，我认为比特币类似于货币的互联网，一个通过分布式计算传播价值并保护数字资产所有权的网络。比特币的内涵远不止表面所见。

在本章中，我们将通过解释一些主要概念和术语、获取必要的软件并使用比特币进行简单交易来开始。在接下来的章节中，我们将开始揭开使比特币成为可能的技术层，并研究比特币网络和协议的内部工作原理。

### 比特币之前的数字货币

可行数字货币的出现与密码学的发展密切相关。考虑到使用比特来表示可以交换商品和服务的价值所涉及的基本挑战，这并不令人惊讶。任何接受数字货币的人都需要回答三个基本问题：

- 我可以相信这笔钱是真实的而不是伪造的吗？
- 我可以相信这笔数字货币只能花费一次（称为“双花”问题）吗？
- 我可以确定没有其他人可以声称这笔钱属于他们而不是我吗？

纸币发行者通过使用越来越复杂的纸张和印刷技术不断与伪造问题作斗争。物理货币很容易解决双花问题，因为同一张纸币不能同时出现在两个地方。当然，传统货币也经常以数字方式存储和传输。在这些情况下，伪造和双花问题通过将所有电子交易通过具有全球货币流通视图的中央机构进行清算来处理。对于无法利用特殊墨水或全息条的数字货币，密码学提供了信任用户价值主张合法性的基础。具体来说，加密数字签名使用户能够签署数字资产或交易，证明该资产的所有权。通过适当的架构，数字签名也可以用于解决双花问题。

当密码学在20世纪80年代末开始变得更加广泛可用和理解时，许多研究人员开始尝试使用密码学构建数字货币。这些早期的数字货币项目发行了数字货币，通常由一国货币或黄金等贵金属支持。

尽管这些早期的数字货币有效，但它们是中心化的，因此容易受到政府和黑客的攻击。早期的数字货币使用中央清算所定期结算所有交易，就像传统的银行系统一样。不幸的是，在大多数情况下，这些新兴的数字货币被担忧的政府盯上，最终被诉讼出局。一些在母公司突然清算时以惊人的崩溃告终。为了抵御干预者（无论是合法政府还是犯罪元素）的攻击，需要一个_去中心化_的数字货币来避免单点攻击。比特币就是这样一个系统，设计上是去中心化的，没有任何中央权威或控制点可以被攻击或破坏。

### 比特币的历史

比特币最早在2008年通过一篇题为“比特币：一种点对点的电子现金系统”的论文被描述，[[1](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#_footnotedef_1 "查看脚注。")] 该论文以中本聪（Satoshi Nakamoto）的别名发表（见[](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#satoshi_whitepaper)）。中本聪结合了几项先前的发明，如数字签名和Hashcash，创建了一个完全去中心化的电子现金系统，该系统不依赖中央权威进行货币发行或交易的结算和验证。一个关键的创新是使用分布式计算系统（称为“工作量证明”算法）平均每10分钟进行一次全球抽奖，允许去中心化网络就交易状态达成_共识_。这优雅地解决了双花问题，即一个货币单位可以被花费两次。以前，双花问题是数字货币的一个弱点，通过中央清算所清算所有交易来解决。

比特币网络于2009年启动，基于中本聪发布的参考实现，此后由许多其他程序员修订。运行提供比特币安全和弹性的工作量证明算法（挖矿）的机器的数量和能力呈指数增长，它们的综合计算能力现在超过了世界顶级超级计算机的综合计算操作数量。

中本聪于2011年4月退出公众视野，将代码和网络开发的责任交给了一群充满活力的志愿者。比特币背后的个人或团体的身份仍然未知。然而，无论是中本聪还是其他任何人都不对比特币系统施加个人控制，该系统基于完全透明的数学原理、开源代码和参与者之间的共识运行。这项发明本身是开创性的，并且已经在分布式计算、经济学和计量经济学领域催生了新的科学。

### 分布式计算问题的解决方案

中本聪的发明也是分布式计算中一个实际且新颖的解决方案，称为“拜占庭将军问题”。简而言之，该问题在于试图让多个没有领导者的参与者通过不可靠且可能被破坏的网络交换信息来就行动方案达成一致。中本聪的解决方案使用工作量证明的概念在没有中央信任权威的情况下达成共识，代表了分布式计算的突破。

### 入门

比特币是一种可以通过使用支持该协议的应用程序访问的协议。“比特币钱包”是比特币系统最常见的用户界面，就像网络浏览器是HTTP协议最常见的用户界面一样。有许多比特币钱包的实现和品牌，就像有许多网络浏览器品牌（例如Chrome、Safari和Firefox）一样。就像我们都有自己最喜欢的浏览器一样，比特币钱包在质量、性能、安全性、隐私性和可靠性方面各不相同。还有一个比特币协议的参考实现，其中包括一个钱包，称为“比特币核心”，它源自中本聪编写的原始实现。

#### 选择比特币钱包

比特币钱包是比特币生态系统中开发最活跃的应用程序之一。竞争激烈，虽然现在可能正在开发一个新的钱包，但去年的一些钱包已经不再积极维护。许多钱包专注于特定平台或特定用途，有些更适合初学者，而另一些则充满了高级用户的功能。选择钱包是高度主观的，取决于用途和用户专业知识。因此，推荐一个特定的品牌或钱包是没有意义的。然而，我们可以根据平台和功能对比特币钱包进行分类，并提供关于所有不同类型钱包的清晰信息。值得尝试几种不同的钱包，直到找到适合您需求的钱包。

##### 比特币钱包的类型

比特币钱包可以根据平台分类如下：

桌面钱包

桌面钱包是作为参考实现创建的第一种比特币钱包。许多用户运行桌面钱包以获得其提供的功能、自主性和控制权。然而，在Windows和macOS等通用操作系统上运行存在某些安全劣势，因为这些平台通常不安全且配置不当。

移动钱包

移动钱包是最常见的比特币钱包类型。运行在Apple iOS和Android等智能手机操作系统上，这些钱包通常是新用户的不错选择。许多钱包设计简单易用，但也有为高级用户设计的全功能移动钱包。为了避免下载和存储大量数据，大多数移动钱包从远程服务器检索信息，通过向第三方披露有关比特币地址和余额的信息来降低您的隐私。

网络钱包

网络钱包通过网页浏览器访问，并将用户的钱包存储在第三方拥有的服务器上。这类似于网络邮件，因为它完全依赖于第三方服务器。其中一些服务使用在用户浏览器中运行的客户端代码，将比特币密钥的控制权保留在用户手中，尽管用户对服务器的依赖仍然会损害他们的隐私。然而，大多数服务为了易用性而从用户手中夺取比特币密钥的控制权。不建议在第三方系统上存储大量比特币。

硬件签名设备

硬件签名设备是可以存储密钥并使用专用硬件和固件签署交易的设备。它们通常通过USB电缆、近场通信（NFC）或带有QR码的摄像头连接到桌面、移动或网络钱包。通过在专用硬件上处理所有与比特币相关的操作，这些钱包不易受到许多类型的攻击。硬件签名设备有时被称为“硬件钱包”，但它们需要与全功能钱包配对以发送和接收交易，并且该配对钱包提供的安全性和隐私性在用户使用硬件签名设备时起着关键作用。

##### 全节点与轻量级

另一种对比特币钱包进行分类的方法是它们的自主程度以及它们如何与比特币网络交互：

全节点

全节点是一个验证比特币交易完整历史（每个用户的每笔交易）的程序。可选地，全节点还可以存储先前验证的交易，并向同一台计算机或互联网上的其他比特币程序提供数据。全节点使用大量的计算机资源——大约相当于每天观看一小时的流媒体视频——但全节点为其用户提供了完全的自主权。

轻量级客户端

轻量级客户端，也称为简化支付验证（SPV）客户端，连接到全节点或其他远程服务器以接收和发送比特币交易信息，但将用户钱包存储在本地，部分验证其接收的交易，并独立创建传出交易。

第三方API客户端

第三方API客户端是通过第三方API系统与比特币交互的客户端，而不是直接连接到比特币网络。钱包可能由用户或第三方服务器存储，但客户端信任远程服务器为其提供准确的信息并保护其隐私。

| 提示 | 比特币是一个点对点（P2P）网络。全节点是_对等节点：_ 每个对等节点单独验证每个确认的交易，并可以为其用户提供具有完全权威的数据。轻量级钱包和其他软件是_客户端：_ 每个客户端依赖于一个或多个对等节点为其提供有效数据。比特币客户端可以对接收到的部分数据进行二次验证，并连接到多个对等节点以减少对单个对等节点完整性的依赖，但客户端的安全性最终依赖于其对等节点的完整性。  
| --- | --- |

##### 谁控制密钥

一个非常重要的额外考虑因素是_谁控制密钥_。正如我们将在后续章节中看到的，对比特币的访问由“私钥”控制，私钥类似于非常长的PIN码。如果您是唯一控制这些私钥的人，那么您就完全控制了自己的比特币。相反，如果您没有控制权，那么您的比特币将由第三方管理，他们最终代表您控制您的资金。密钥管理软件根据控制权分为两个重要类别：_钱包_（您控制密钥）和托管账户（第三方控制密钥）。为了强调这一点，我（Andreas）创造了一个短语：_您的密钥，您的比特币。不是您的密钥，不是您的比特币_。

结合这些分类，许多比特币钱包可以分为几类，最常见的三类是桌面全节点钱包（您控制密钥）、移动轻量级钱包（您控制密钥）和基于网络的第三方账户（您不控制密钥）。不同类别之间的界限有时是模糊的，因为软件可以在多个平台上运行，并且可以以不同的方式与网络交互。

#### 快速入门

Alice 不是一个技术用户，最近才从她的朋友 Joe 那里听说了比特币。在一次聚会上，Joe 热情地向周围的人解释比特币，并提供了一个演示。Alice 对此很感兴趣，询问她如何开始使用比特币。Joe 说，对于新用户来说，移动钱包是最好的选择，并推荐了几款他最喜欢的钱包。Alice 下载了 Joe 推荐的一款钱包，并将其安装在她的手机上。

当 Alice 第一次运行她的钱包应用程序时，她选择了创建一个新的比特币钱包的选项。由于她选择的钱包是非托管钱包，Alice（且只有 Alice）将控制她的密钥。因此，她需要负责备份密钥，因为丢失密钥意味着她将无法访问她的比特币。为了帮助她完成这一操作，她的钱包生成了一个_恢复代码_，可用于恢复她的钱包。

#### 恢复代码

大多数现代非托管比特币钱包都会为用户提供恢复代码以进行备份。恢复代码通常由软件随机选择的数字、字母或单词组成，并用作钱包生成密钥的基础。参见 [](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#recovery_code_sample) 以查看示例。

恢复代码示例

|钱包|恢复代码|
|---|---|
|BlueWallet|(1) media (2) suspect (3) effort (4) dish (5) album (6) shaft (7) price (8) junk (9) pizza (10) situate (11) oyster (12) rib|
|Electrum|nephew dog crane clever quantum crazy purse traffic repeat fruit old clutch|
|Muun|LAFV TZUN V27E NU4D WPF4 BRJ4 ELLP BNFL|

|提示|恢复代码有时被称为“助记词”或“助记短语”，这意味着您应该记住这个短语，但将短语写在纸上比大多数人记忆更可靠。另一个替代名称是“种子短语”，因为它提供了生成钱包所有密钥的函数的输入（“种子”）。|
|---|---|

如果 Alice 的钱包出现问题，她可以下载钱包软件的新副本，并输入此恢复代码以重建她所有链上交易的数据库。然而，仅从恢复代码恢复并不会恢复 Alice 输入到钱包中的任何额外数据，例如她与特定地址或交易关联的标签。虽然丢失这些元数据的访问权限不像丢失资金那么重要，但它仍然有其重要性。想象一下，您需要查看旧的银行或信用卡对账单，而您支付的每个实体（或支付给您的人）的名称都被删除了。为了防止丢失元数据，许多钱包提供了除恢复代码之外的额外备份功能。

对于某些钱包来说，这种额外的备份功能现在比以往任何时候都更加重要。许多比特币支付现在使用_链下_技术，其中并非每笔支付都存储在公共区块链中。这降低了用户的成本并提高了隐私性，但这也意味着依赖于链上数据的恢复代码机制无法保证恢复用户的所有比特币。对于支持链下技术的应用程序，频繁备份钱包数据库非常重要。

值得注意的是，当第一次向新的移动钱包接收资金时，许多钱包通常会重新验证您是否已安全备份了恢复代码。这可能是一个简单的提示，也可能要求用户手动重新输入代码。

|警告|尽管许多合法的钱包会提示您重新输入恢复代码，但也有许多恶意软件应用程序模仿钱包的设计，坚持要求您输入恢复代码，然后将输入的代码发送给恶意软件开发者以窃取您的资金。这相当于试图诱骗您提供银行密码的钓鱼网站。对于大多数钱包应用程序，唯一会要求您输入恢复代码的时间是在初始设置期间（在您接收任何比特币之前）和恢复期间（在您丢失对原始钱包的访问权限之后）。如果应用程序在其他时间要求您输入恢复代码，请咨询专家以确保您没有被钓鱼。|
|---|---|

#### 比特币地址

Alice 现在准备开始使用她的新比特币钱包。她的钱包应用程序随机生成了一个私钥（在 [](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#private_keys) 中有更详细的描述），该私钥将用于生成指向她钱包的比特币地址。此时，她的比特币地址尚未被比特币网络知晓，也没有在比特币系统的任何部分“注册”。她的比特币地址只是与她的私钥对应的数字，她可以使用这些地址来控制资金的访问权限。这些地址由她的钱包独立生成，无需参考或注册任何服务。

|提示|有多种比特币地址和发票格式。地址和发票可以与其他比特币用户共享，他们可以使用这些地址或发票直接将比特币发送到您的钱包。您可以与他人共享地址或发票，而无需担心比特币的安全性。与银行账号不同，任何知道您比特币地址的人都无法从您的钱包中提取资金——您必须发起所有支出。然而，如果您向两个人提供相同的地址，他们将能够看到对方向您发送了多少比特币。如果您公开您的地址，每个人都将能够看到其他人向该地址发送了多少比特币。为了保护您的隐私，您应该在每次请求付款时生成一个新的发票和新的地址。|
|---|---|

#### 接收比特币

Alice 使用_接收_按钮，显示一个二维码，如 [Alice 使用她的移动比特币钱包的接收屏幕，并以二维码格式显示她的地址。](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#wallet_receive) 所示。

[![钱包接收屏幕，显示二维码。图片源自比特币设计指南 CC-BY](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_0101.png)](https://github.com/bitcoinbook/bitcoinbook/blob/develop/images/mbc3_0101.png)

图 1. Alice 使用她的移动比特币钱包的接收屏幕，并以二维码格式显示她的地址。

二维码是一个带有黑白点图案的方块，作为一种条形码，包含与 Joe 的智能手机摄像头可以扫描的格式相同的信息。

|警告|发送到本书中地址的任何资金都将丢失。如果您想测试发送比特币，请考虑将其捐赠给接受比特币的慈善机构。|
|---|---|

#### 获取您的第一个比特币

新用户的第一个任务是获取一些比特币。

比特币交易是不可逆的。大多数电子支付网络（如信用卡、借记卡、PayPal 和银行账户转账）是可逆的。对于出售比特币的人来说，这种差异带来了极高的风险，即买家在收到比特币后可能会撤销电子支付，从而实际上欺诈卖家。为了减轻这种风险，接受传统电子支付以换取比特币的公司通常要求买家进行身份验证和信用检查，这可能需要几天或几周的时间。作为新用户，这意味着您无法立即用信用卡购买比特币。然而，只要有一点耐心和创造性思维，您就不需要这样做。

以下是一些新用户获取比特币的方法：

- 找一个有比特币的朋友，直接从他或她那里购买一些比特币。许多比特币用户都是这样开始的。这种方法最简单。结识有比特币的人的一种方法是参加 [Meetup.com](https://meetup.com/) 上列出的本地比特币聚会。
- 通过出售产品或服务赚取比特币。如果您是程序员，出售您的编程技能。如果您是理发师，剪发换取比特币。
- 使用您所在城市的比特币 ATM。比特币 ATM 是一种接受现金并将比特币发送到您的智能手机比特币钱包的机器。
- 使用与您的银行账户关联的比特币货币交易所。许多国家现在都有货币交易所，提供买卖双方交换比特币与本地货币的市场。汇率列表服务（如 [BitcoinAverage](https://bitcoinaverage.com/)）通常显示每种货币的比特币交易所列表。

|提示|比特币相对于其他支付系统的一个优势是，如果使用得当，它可以为用户提供更多的隐私。获取、持有和花费比特币不需要您向第三方透露敏感的个人身份信息。然而，当比特币触及传统系统（如货币交易所）时，通常适用国家和国际法规。为了将比特币兑换为您的本国货币，您通常需要提供身份证明和银行信息。用户应该意识到，一旦比特币地址与身份相关联，其他相关的比特币交易也可能变得容易识别和跟踪——包括之前进行的交易。这是许多用户选择维护独立于其钱包的专用交易所账户的原因之一。|
|---|---|

Alice 是通过朋友介绍了解比特币的，因此她有一种简单的方法来获取她的第一个比特币。接下来，我们将看看她如何从她的朋友 Joe 那里购买比特币，以及 Joe 如何将比特币发送到她的钱包。

#### 查找比特币的当前价格

在 Alice 可以从 Joe 那里购买比特币之前，他们必须就比特币与美元之间的_汇率_达成一致。这引出了一个比特币新手常见的问题：“谁设定比特币的价格？”简短的答案是，价格由市场决定。

比特币与大多数其他货币一样，具有_浮动汇率_。这意味着比特币的价值根据其交易的各种市场中的供需关系而波动。例如，比特币的“价格”以美元计算，在每个市场中基于最近的比特币和美元交易计算。因此，价格往往每秒波动数次。定价服务会汇总多个市场的价格，并计算一个代表广泛市场汇率的交易量加权平均值（例如，BTC/USD）。

有数百个应用程序和网站可以提供当前的市场汇率。以下是一些最受欢迎的：

[Bitcoin Average](https://bitcoinaverage.com/)

一个提供每种货币交易量加权平均值的简单视图的网站。

[CoinCap](https://coincap.io/)

一个列出数百种加密货币（包括比特币）的市值和汇率的服务。

[芝加哥商品交易所比特币参考汇率](https://oreil.ly/ACieC)

一个可以用作机构和合同参考的参考汇率，由 CME 作为投资数据流的一部分提供。

除了这些各种网站和应用程序外，一些比特币钱包会自动在比特币和其他货币之间转换金额。

#### 发送和接收比特币

Alice 决定购买 0.001 比特币。在她和 Joe 检查汇率后，她给 Joe 适当的现金，打开她的移动钱包应用程序，并选择接收。这会显示一个包含 Alice 第一个比特币地址的二维码。

Joe 然后在他的智能手机钱包上选择发送，并打开二维码扫描器。这允许 Joe 用他的智能手机摄像头扫描条形码，这样他就不必手动输入 Alice 的比特币地址，因为地址很长。

Joe 现在将 Alice 的比特币地址设置为收款人。Joe 输入金额为 0.001 比特币（BTC）；参见 [比特币钱包发送屏幕。](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#wallet-send)。一些钱包可能会以不同的单位显示金额：0.001 BTC 是 1 毫比特币（mBTC）或 100,000 聪（sats）。

一些钱包可能还会建议 Joe 为此交易输入标签；如果是这样，Joe 输入“Alice”。几周或几个月后，这将帮助 Joe 记住他为什么发送了这 0.001 比特币。一些钱包可能还会提示 Joe 关于费用的问题。根据钱包和交易发送的方式，钱包可能会要求 Joe 输入交易费用率，或者提示他建议的费用（或费用率）。交易费用越高，交易确认的速度就越快（参见 [确认](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#confirmations)）。

[![钱包发送屏幕。图片源自比特币设计指南 CC-BY](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_0102.png)](https://github.com/bitcoinbook/bitcoinbook/blob/develop/images/mbc3_0102.png)

图 2. 比特币钱包发送屏幕。

Joe 然后仔细检查以确保他输入了正确的金额，因为他即将传输资金，错误很快就会变得不可逆转。在仔细检查地址和金额后，他按下发送以传输交易。Joe 的移动比特币钱包构建了一个交易，将 0.001 BTC 分配给 Alice 提供的地址，从 Joe 的钱包中提取资金，并使用 Joe 的私钥签署交易。这告诉比特币网络，Joe 已授权将价值转移到 Alice 的新地址。随着交易通过点对点协议传输，它迅速传播到比特币网络中。仅仅几秒钟后，网络中的大多数连接良好的节点都接收到了交易，并第一次看到了 Alice 的地址。

与此同时，Alice 的钱包不断“监听”比特币网络上的新交易，寻找与其包含的地址匹配的任何交易。在 Joe 的钱包传输交易几秒钟后，Alice 的钱包将显示她正在接收 0.001 BTC。

#### 确认

起初，Alice 的地址将显示来自 Joe 的交易为“未确认”。这意味着交易已传播到网络，但尚未记录在比特币交易日志（称为区块链）中。要确认，交易必须包含在一个区块中并添加到区块链中，这平均每 10 分钟发生一次。在传统金融术语中，这被称为_清算_。有关比特币交易传播、验证和清算（确认）的更多详细信息，请参见 [挖矿](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#mining)。

Alice 现在自豪地拥有了 0.001 BTC，她可以花费这些比特币。在接下来的几天里，Alice 通过 ATM 和交易所购买了更多的比特币。在下一章中，我们将看看她的第一次比特币购买，并更详细地研究底层交易和传播技术。