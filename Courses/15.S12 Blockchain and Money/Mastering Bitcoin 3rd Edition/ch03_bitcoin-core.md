## ch03_bitcoin-core

[[ch03_bitcoin_client]]
### Bitcoin Core: 参考实现

人们只有在相信以后能够花掉这些钱的情况下，才会接受用有价值的商品和服务来换取货币。如果货币是伪造的或意外贬值的，可能以后就无法使用，因此每个接受比特币的人都有强烈的动机去验证他们收到的比特币的完整性。比特币系统的设计使得完全在本地计算机上运行的软件能够完美地防止伪造、贬值和其他一些关键问题。**提供这种功能的软件被称为全验证节点，因为它会根据系统中的每一条规则验证每一笔已确认的比特币交易。全验证节点（简称*全节点*）还可能提供工具和数据，帮助理解比特币的工作原理以及当前网络中正在发生的事情。**

在本章中，我们将安装 Bitcoin Core，这是自比特币网络诞生以来大多数全节点操作者使用的实现。然后，我们将从你的节点中检查区块、交易和其他数据，这些数据是权威的——不是因为某个强大的实体指定了它，而是因为你的节点独立验证了它。在本书的其余部分，我们将继续使用 Bitcoin Core 来创建和检查与区块链和网络相关的数据。

### 从比特币到 Bitcoin Core

比特币是一个**开源**项目，源代码在 MIT 许可证下免费提供，可以下载并用于任何目的。不仅仅是开源，比特币是由一个开放的志愿者社区开发的。最初，这个社区只有中本聪一个人。到 2023 年，比特币的源代码已经有超过 1,000 名贡献者，其中大约有十几名开发人员几乎全职工作，还有几十名兼职开发人员。任何人都可以为代码做出贡献——包括你！

当中本聪创建比特币时，软件在发布白皮书之前已经基本完成（白皮书在 <<satoshi_whitepaper>> 中重现）。中本聪希望在发布论文之前确保实现能够正常工作。最初的实现，当时简称为“比特币”，经过大量修改和改进，演变为现在的 **Bitcoin Core**，以区别于其他实现。***Bitcoin Core* 是比特币系统的参考实现，意味着它提供了技术每个部分应如何实现的参考。Bitcoin Core 实现了比特币的所有方面，包括钱包、交易和区块验证引擎、区块构建工具以及比特币点对点通信的所有现代部分。**

<<bitcoin_core_architecture>> 展示了 Bitcoin Core 的架构。

[[bitcoin_core_architecture]]
.Bitcoin Core 架构（来源：Eric Lombrozo）。
image::images/mbc3_0301.png["Bitcoin Core 架构"]

**尽管 Bitcoin Core 作为系统中许多主要部分的参考实现，比特币白皮书描述了系统的几个早期部分。自 2011 年以来，系统的大多数主要部分都记录在一组 [比特币改进提案（BIPs）](https://oreil.ly/BCXAQ) 中**。在本书中，我们将通过编号引用 BIP 规范；例如，BIP9 描述了一种用于比特币多次重大升级的机制。

### 比特币开发环境

如果你是一名开发者，你可能会希望设置一个开发环境，包含所有编写比特币应用程序所需的工具、库和支持软件。在这个技术性很强的章节中，我们将逐步介绍这个过程。如果内容过于复杂（而你实际上并没有设置开发环境），可以跳过本章，进入下一章，下一章的技术性较低。

[[compiling_core]]
### 从源代码编译 Bitcoin Core

Bitcoin Core 的源代码可以通过下载压缩包或从 GitHub 克隆源代码库来获取。在 [Bitcoin Core 下载页面](https://oreil.ly/hN9g1) 上，选择最新版本并下载源代码的压缩包。或者，使用 Git 命令行从 [GitHub 比特币页面](https://oreil.ly/BdOwl) 创建源代码的本地副本。

[TIP]
====
在本章的许多示例中，我们将使用操作系统的命令行界面（也称为“shell”），通过“终端”应用程序访问。shell 会显示一个提示符，你输入命令，shell 会响应一些文本并显示一个新的提示符，等待你输入下一个命令。提示符在你的系统上可能看起来不同，但在以下示例中，它用 +$+ 符号表示。在示例中，当你看到 +$+ 符号后的文本时，不要输入 +$+ 符号，而是输入紧随其后的命令，然后按 Enter 键执行该命令。在示例中，每个命令下方的行是操作系统对该命令的响应。当你看到下一个 +$+ 前缀时，你就知道这是一个新命令，你应该重复这个过程。
====

在这里，我们使用 +git+ 命令创建源代码的本地副本（“克隆”）：

----
$ git clone https://github.com/bitcoin/bitcoin.git
Cloning into 'bitcoin'...
remote: Enumerating objects: 245912, done.
remote: Counting objects: 100% (3/3), done.
remote: Compressing objects: 100% (2/2), done.
remote: Total 245912 (delta 1), reused 2 (delta 1), pack-reused 245909
Receiving objects: 100% (245912/245912), 217.74 MiB | 13.05 MiB/s, done.
Resolving deltas: 100% (175649/175649), done.
----

[TIP]
====
Git 是最广泛使用的分布式版本控制系统，是任何软件开发人员工具包中必不可少的一部分。如果你还没有安装 +git+ 命令或 Git 的图形用户界面，你可能需要在你的操作系统上安装它。
====

当 Git 克隆操作完成后，你将在 _bitcoin_ 目录中拥有源代码库的完整本地副本。使用 +cd+ 命令切换到该目录：

----
$ cd bitcoin
----

#### 选择 Bitcoin Core 版本

默认情况下，本地副本将与最新代码同步，这可能是比特币的不稳定或测试版。在编译代码之前，通过检出发布**标签**来选择特定版本。这将使本地副本与代码库的特定快照同步，该快照由关键字标签标识。标签由开发人员用于按版本号标记代码的特定发布。首先，使用 +git tag+ 命令查找可用的标签：

----
$ git tag
v0.1.5
v0.1.6test1
v0.10.0
...
v0.11.2
v0.11.2rc1
v0.12.0rc1
v0.12.0rc2
...
----

标签列表显示了所有发布的比特币版本。按照惯例，用于测试的**发布候选版本**带有“rc”后缀。可以在生产系统上运行的稳定版本没有后缀。从前面的列表中，选择最高版本的发布版本，在撰写本文时是 v24.0.1。使用 +git checkout+ 命令将本地代码同步到此版本：

----
$ git checkout v24.0.1
Note: switching to 'v24.0.1'.

You are in 'detached HEAD' state. You can look around, make experimental
changes and commit them, and you can discard any commits you make in this
state without impacting any branches by switching back to a branch.

HEAD is now at b3f866a8d Merge bitcoin/bitcoin#26647: 24.0.1 final changes
----

你可以通过发出 +git status+ 命令来确认你已经“检出”了所需的版本：

----
HEAD detached at v24.0.1
nothing to commit, working tree clean
----

#### 配置 Bitcoin Core 构建

源代码包含文档，可以在多个文件中找到。查看位于 _bitcoin_ 目录中的 _README.md_ 文件中的主要文档。在本章中，我们将在 Linux（类 Unix 系统）上构建 Bitcoin Core 守护进程（服务器），也称为 +bitcoind+。通过阅读 _doc/build-unix.md_ 查看在你的平台上编译 +bitcoind+ 命令行客户端的说明。其他说明可以在 _doc_ 目录中找到；例如，_build-windows.md_ 提供了 Windows 的说明。截至本文撰写时，还提供了 Android、FreeBSD、NetBSD、OpenBSD、macOS（OSX）、Unix 和 Windows 的说明。

仔细查看构建文档的第一部分中的构建先决条件。这些是在你开始编译比特币之前必须存在于你的系统上的库。如果缺少这些先决条件，构建过程将失败并报错。如果由于你错过了某个先决条件而导致失败，你可以安装它，然后从你离开的地方继续构建过程。假设先决条件已安装，你可以通过运行 _autogen.sh_ 脚本来生成一组构建脚本，从而开始构建过程：

----
$ ./autogen.sh
libtoolize: putting auxiliary files in AC_CONFIG_AUX_DIR, 'build-aux'.
libtoolize: copying file 'build-aux/ltmain.sh'
libtoolize: putting macros in AC_CONFIG_MACRO_DIRS, 'build-aux/m4'.
 ...
configure.ac:58: installing 'build-aux/missing'
src/Makefile.am: installing 'build-aux/depcomp'
parallel-tests: installing 'build-aux/test-driver'
----

_autogen.sh_ 脚本创建了一组自动配置脚本，这些脚本将询问你的系统以发现正确的设置，并确保你拥有编译代码所需的所有必要库。其中最重要的是 +configure+ 脚本，它提供了许多不同的选项来自定义构建过程。使用 +--help+ 标志查看各种选项：

----
$ ./configure --help
`configure' configures Bitcoin Core 24.0.1 to adapt to many kinds of systems.

Usage: ./configure [OPTION]... [VAR=VALUE]...

...
Optional Features:
  --disable-option-checking  ignore unrecognized --enable/--with options
  --disable-FEATURE       do not include FEATURE (same as --enable-FEATURE=no)
  --enable-FEATURE[=ARG]  include FEATURE [ARG=yes]
  --enable-silent-rules   less verbose build output (undo: "make V=1")
  --disable-silent-rules  verbose build output (undo: "make V=0")
...
----

+configure+ 脚本允许你通过使用 +--enable-FEATURE+ 和 +--disable-FEATURE+ 标志来启用或禁用 +bitcoind+ 的某些功能，其中 pass:[<span class="keep-together"><code>FEATURE</code></span>] 替换为帮助输出中列出的功能名称。在本章中，我们将使用所有默认功能构建 +bitcoind+ 客户端。我们不会使用配置标志，但你应该查看它们以了解客户端中包含哪些可选功能。如果你在学术环境中，计算机实验室的限制可能要求你将应用程序安装到你的主目录中（例如，使用 +--prefix=$HOME+）。

以下是一些覆盖 +configure+ 脚本默认行为的有用选项：

++++
<dl>
<dt><code>--prefix=$HOME</code></dt>
<dd><p>这将覆盖生成的默认安装位置（通常是 <em>/usr/local/</em>）。使用 <code>$HOME</code> 将所有内容放在你的主目录中，或使用不同的路径。</p></dd>

<dt><code>--disable-wallet</code></dt>
<dd><p>用于禁用参考钱包实现。</p></dd>

<dt><code>--with-incompatible-bdb</code></dt>
<dd><p>如果你正在构建钱包，允许使用不兼容版本的 Berkeley DB 库。</p></dd>

<dt><code>--with-gui=no</code></dt>
<dd><p>不构建图形用户界面，这需要 Qt 库。这将仅构建服务器和命令行 Bitcoin Core。</p></dd>
</dl>
++++

接下来，运行 +configure+ 脚本以自动发现所有必要的库，并为你的系统创建自定义的构建脚本：

----
$ ./configure
checking for pkg-config... /usr/bin/pkg-config
checking pkg-config is at least version 0.9.0... yes
checking build system type... x86_64-pc-linux-gnu
checking host system type... x86_64-pc-linux-gnu
checking for a BSD-compatible install... /usr/bin/install -c
...
[许多页的配置测试]
...
----

如果一切顺利，+configure+ 命令将结束并创建允许我们编译 +bitcoind+ 的自定义构建脚本。如果有任何缺失的库或错误，+configure+ 命令将终止并报错，而不是创建构建脚本。如果发生错误，很可能是由于缺少或不兼容的库。再次查看构建文档，确保你安装了缺失的先决条件。然后再次运行 +configure+，看看是否修复了错误。

#### 构建 Bitcoin Core 可执行文件

接下来，你将编译源代码，这个过程可能需要一个小时才能完成，具体取决于你的 CPU 速度和可用内存。如果发生错误或编译过程中断，你可以随时通过再次输入 +make+ 来恢复编译。输入 *+make+* 开始编译可执行应用程序：

----
$ make
Making all in src
  CXX      bitcoind-bitcoind.o
  CXX      libbitcoin_node_a-addrdb.o
  CXX      libbitcoin_node_a-addrman.o
  CXX      libbitcoin_node_a-banman.o
  CXX      libbitcoin_node_a-blockencodings.o
  CXX      libbitcoin_node_a-blockfilter.o
[... 更多编译信息]
----

在具有多个 CPU 的快速系统上，你可能希望设置并行编译作业的数量。例如，+make -j 2+ 将使用两个核心（如果可用）。如果一切顺利，Bitcoin Core 现在已经编译完成。你应该运行单元测试套件 +make check+ 以确保链接的库没有明显的损坏。最后一步是使用 +make install+ 命令在你的系统上安装各种可执行文件。你可能需要输入用户密码，因为此步骤需要管理员权限：

----
$ make check && sudo make install
Password:
Making install in src
 ../build-aux/install-sh -c -d '/usr/local/lib'
libtool: install: /usr/bin/install -c bitcoind /usr/local/bin/bitcoind
libtool: install: /usr/bin/install -c bitcoin-cli /usr/local/bin/bitcoin-cli
libtool: install: /usr/bin/install -c bitcoin-tx /usr/local/bin/bitcoin-tx
...
----

+bitcoind+ 的默认安装将其放在 _/usr/local/bin_ 中。你可以通过询问系统可执行文件的路径来确认 Bitcoin Core 已正确安装：

----
$ which bitcoind
/usr/local/bin/bitcoind

$ which bitcoin-cli
/usr/local/bin/bitcoin-cli
----

### 运行 Bitcoin Core 节点

比特币的点对点网络由网络“节点”组成，这些节点主要由个人和一些提供比特币服务的企业运行。运行比特币节点的人可以直接且权威地查看比特币区块链，并在本地拥有所有可花费的比特币的副本，这些副本由他们自己的系统独立验证。通过运行节点，你不必依赖任何第三方来验证交易。此外，通过使用比特币节点完全验证你收到的交易，你为比特币网络做出了贡献，并使其更加健壮。

然而，运行节点最初需要下载和处理超过 500 GB 的数据，并且每天大约需要处理 400 MB 的比特币交易。这些数字是 2023 年的，随着时间的推移可能会增加。如果你关闭节点或断开互联网连接几天，你的节点将需要下载它错过的数据。例如，如果你关闭 Bitcoin Core 10 天，下次启动时你将需要下载大约 4 GB 的数据。

**根据你是否选择索引所有交易并保留区块链的完整副本，你可能还需要大量的磁盘空间——如果你计划运行 Bitcoin Core 多年，至少需要 1 TB。默认情况下，比特币节点还会将交易和区块传输到其他节点（称为“对等节点”），消耗上传互联网带宽。如果你的互联网连接有限、数据上限较低或按千兆字节计费，你可能不应该在其上运行比特币节点，或者以限制其带宽的方式运行它（参见 <<bitcoincorenode_config>>）**。你可以将你的节点连接到替代网络，例如 [Blockstream Satellite](https://oreil.ly/cIwf3) 这样的免费卫星数据提供商。

[TIP]
====
Bitcoin Core 默认保留区块链的完整副本，其中包含自 2009 年比特币网络诞生以来几乎所有已确认的交易。这个数据集有数百 GB 大小，并且会根据你的 CPU 和互联网连接速度在几小时或几天内逐步下载。Bitcoin Core 在下载完整的区块链数据集之前无法处理交易或更新账户余额。确保你有足够的磁盘空间、带宽和时间来完成初始同步。你可以配置 Bitcoin Core 通过删除旧区块来减少区块链的大小，但它仍然会下载整个数据集。
====

尽管有这些资源需求，成千上万的人仍然运行比特币节点。有些人甚至在像 Raspberry Pi（一个 35 美元的信用卡大小的计算机）这样简单的系统上运行。

[role="less_space pagebreak-before"]
**为什么你会想运行一个节点？以下是一些最常见的原因：**

- **你不想依赖任何第三方来验证你收到的交易。**

- **你不想向第三方披露哪些交易属于你的钱包。**

- **你正在开发比特币软件，需要依赖比特币节点以编程方式（API）访问网络和区块链。**

- **你正在构建必须根据比特币共识规则验证交易的应用程序。通常，比特币软件公司会运行多个节点。**

- **你想支持比特币。运行一个节点并使用它来验证你收到的交易，可以使网络更加健壮。**

如果你正在阅读本书并对强大的安全性、卓越的隐私性或开发比特币软件感兴趣，你应该运行自己的节点。

[[bitcoincorenode_config]]
### 配置 Bitcoin Core 节点

Bitcoin Core 每次启动时都会在其数据目录中查找配置文件。在本节中，我们将检查各种配置选项并设置一个配置文件。要定位配置文件，请在终端中运行 +bitcoind -printtoconsole+ 并查看前几行：

----
$ bitcoind -printtoconsole
2023-01-28T03:21:42Z Bitcoin Core version v24.0.1
2023-01-28T03:21:42Z Using the 'x86_shani(1way,2way)' SHA256 implementation
2023-01-28T03:21:42Z Using RdSeed as an additional entropy source
2023-01-28T03:21:42Z Using RdRand as an additional entropy source
2023-01-28T03:21:42Z Default data directory /home/<USER>/.bitcoin
2023-01-28T03:21:42Z Using data directory /home/<USER>/.bitcoin
2023-01-28T03:21:42Z Config file: /home/<USER>/.bitcoin/bitcoin.conf
...
[大量调试输出]
...
----

一旦你确定了配置文件的位置，你可以按 Ctrl-C 关闭节点。通常，配置文件位于用户主目录下的 _.bitcoin_ 数据目录中。在你喜欢的编辑器中打开配置文件。

Bitcoin Core 提供了超过 100 个配置选项，这些选项可以修改网络节点的行为、区块链的存储以及许多其他方面的操作。要查看这些选项的列表，请运行 +bitcoind --help+：

----
$ bitcoind --help
Bitcoin Core version v24.0.1

Usage:  bitcoind [options]                     Start Bitcoin Core

Options:

  -?
       Print this help message and exit

  -alertnotify=<cmd>
       Execute command when an alert is raised (%s in cmd is replaced by
       message)
...
[更多选项]
----

以下是一些你可以在配置文件中设置或作为 +bitcoind+ 命令行参数的最重要选项：

++alertnotify++:: 运行指定的命令或脚本以向此节点的所有者发送紧急警报。

++conf++:: 配置文件的替代位置。这只能作为 +bitcoind+ 的命令行参数使用，因为它不能位于它引用的配置文件中。

++datadir++:: 选择放置所有区块链数据的目录和文件系统。默认情况下，这是你主目录下的 _.bitcoin_ 子目录。根据你的配置，截至本文撰写时，这可能会使用从大约 10 GB 到近 1 TB 的空间，预计最大大小每年会增加几百 GB。

++prune++:: 通过删除旧区块将区块链磁盘空间需求减少到这么多兆字节。在资源受限的节点上使用此选项，该节点无法容纳完整的区块链。系统的其他部分将使用其他无法修剪的磁盘空间，因此你仍然至少需要 +datadir+ 选项中提到的最小空间。

++txindex++:: 维护所有交易的索引。这允许你通过交易 ID 以编程方式检索任何交易，前提是包含该交易的区块尚未被修剪。

[role="less_space pagebreak-before"]
++dbcache++:: UTXO 缓存的大小。默认值为 450 兆字节（MiB）。在高端硬件上增加此大小以减少从磁盘读取和写入的频率，或在低端硬件上减小此大小以节省内存，但会增加磁盘使用频率。

++blocksonly++:: 通过仅从对等节点接受已确认交易的区块来最小化你的带宽使用，而不是中继未确认的交易。

++maxmempool++:: 将交易内存池限制为此兆字节数。在内存受限的节点上使用它以减少内存使用。

[[txindex]]
.交易数据库索引和 txindex 选项
****
默认情况下，Bitcoin Core 构建的数据库仅包含与用户钱包相关的交易。如果你想能够通过 +getrawtransaction+ 等命令访问**任何**交易（参见 <<exploring_and_decoding_transactions>>），你需要配置 Bitcoin Core 构建完整的交易索引，这可以通过 +txindex+ 选项实现。在 Bitcoin Core 配置文件中设置 +txindex=1+。如果你最初没有设置此选项，后来设置为完全索引，你需要等待它重建索引。
****

<<full_index_node>> 展示了如何将前面的选项与完全索引的节点结合使用，作为比特币应用程序的 API 后端。

[[full_index_node]]
.完全索引节点的示例配置
====
----
alertnotify=myemailscript.sh "Alert: %s"
datadir=/lotsofspace/bitcoin
txindex=1
----
====

<<constrained_resources>> 展示了一个在较小服务器上运行的资源受限节点的配置。

[[constrained_resources]]
.资源受限系统的示例配置
====
----
alertnotify=myemailscript.sh "Alert: %s"
blocksonly=1
prune=5000
dbcache=150
maxmempool=150
----
====

在你编辑配置文件并设置最能代表你需求的选项后，你可以使用此配置测试 +bitcoind+。使用 +printtoconsole+ 选项运行 Bitcoin Core，以便在前台运行并将输出打印到控制台：

----
$ bitcoind -printtoconsole
2023-01-28T03:43:39Z Bitcoin Core version v24.0.1
2023-01-28T03:43:39Z Using the 'x86_shani(1way,2way)' SHA256 implementation
2023-01-28T03:43:39Z Using RdSeed as an additional entropy source
2023-01-28T03:43:39Z Using RdRand as an additional entropy source
2023-01-28T03:43:39Z Default data directory /home/<USER>/.bitcoin
2023-01-28T03:43:39Z Using data directory /lotsofspace/bitcoin
2023-01-28T03:43:39Z Config file: /home/<USER>/.bitcoin/bitcoin.conf
2023-01-28T03:43:39Z Config file arg: [main] blockfilterindex="1"
2023-01-28T03:43:39Z Config file arg: [main] maxuploadtarget="1000"
2023-01-28T03:43:39Z Config file arg: [main] txindex="1"
2023-01-28T03:43:39Z Setting file arg: wallet = ["msig0"]
2023-01-28T03:43:39Z Command-line arg: printtoconsole=""
2023-01-28T03:43:39Z Using at most 125 automatic connections 
2023-01-28T03:43:39Z Using 16 MiB out of 16 MiB requested for signature cache
2023-01-28T03:43:39Z Using 16 MiB out of 16 MiB requested for script execution 
2023-01-28T03:43:39Z Script verification uses 3 additional threads
2023-01-28T03:43:39Z scheduler thread start
2023-01-28T03:43:39Z [http] creating work queue of depth 16
2023-01-28T03:43:39Z Using random cookie authentication.
2023-01-28T03:43:39Z Generated RPC cookie /lotsofspace/bitcoin/.cookie
2023-01-28T03:43:39Z [http] starting 4 worker threads
2023-01-28T03:43:39Z Using wallet directory /lotsofspace/bitcoin/wallets
2023-01-28T03:43:39Z init message: Verifying wallet(s)…
2023-01-28T03:43:39Z Using BerkeleyDB version Berkeley DB 4.8.30
2023-01-28T03:43:39Z Using /16 prefix for IP bucketing
2023-01-28T03:43:39Z init message: Loading P2P addresses…
2023-01-28T03:43:39Z Loaded 63866 addresses from peers.dat  114ms
[... 更多启动消息 ...]
----

一旦你确认它正在加载正确的设置并按预期运行，你可以按 Ctrl-C 中断进程。

要在后台作为进程运行 Bitcoin Core，请使用 +daemon+ 选项启动它，如 +bitcoind -daemon+。

要监控比特币节点的进度和运行时状态，请以守护进程模式启动它，然后使用命令 +bitcoin-cli getblockchaininfo+：

----
$ bitcoin-cli getblockchaininfo
----

[source,json]
----
{
  "chain": "main",
  "blocks": 0,
  "headers": 83999,
  "bestblockhash": "[...]19d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f",
  "difficulty": 1,
  "time": 1673379796,
  "mediantime": 1231006505,
  "verificationprogress": 3.783041623201835e-09,
  "initialblockdownload": true,
  "chainwork": "[...]000000000000000000000000000000000000000000000100010001",
  "size_on_disk": 89087,
  "pruned": false,
  "warnings": ""
}
----

这显示了一个区块链高度为 0 个区块和 83,999 个区块头的节点。节点首先从其对等节点获取区块头，以找到具有最多工作量证明的区块链，然后继续下载完整的区块，并在下载时验证它们。

一旦你对你选择的配置选项感到满意，你应该将 Bitcoin Core 添加到操作系统的启动脚本中，以便它持续运行并在操作系统重启时重新启动。你可以在 Bitcoin Core 的源代码目录中的 _contrib/init_ 下找到各种操作系统的示例启动脚本，以及一个 _README.md_ 文件，显示哪个系统使用哪个脚本。

### Bitcoin Core API

Bitcoin Core 实现了一个 JSON-RPC 接口，也可以通过命令行助手 +bitcoin-cli+ 访问。命令行允许我们以交互方式试验 API 提供的功能。首先，调用 +help+ 命令查看可用的 Bitcoin Core RPC 命令列表：

[[bitcoind_commands]]

----
$ bitcoin-cli help
+== Blockchain ==
getbestblockhash
getblock "blockhash" ( verbosity )
getblockchaininfo
...
walletpassphrase "passphrase" timeout
walletpassphrasechange "oldpassphrase" "newpassphrase"
walletprocesspsbt "psbt" ( sign "sighashtype" bip32derivs finalize )
----

每个命令可能需要多个参数。要获取额外的帮助、详细描述和参数信息，请在 +help+ 后添加命令名称。例如，查看 +getblockhash+ RPC 命令的帮助：

----
$ bitcoin-cli help getblockhash
getblockhash height

Returns hash of block in best-block-chain at height provided.

Arguments:
1. height    (numeric, required) The height index

Result:
"hex"    (string) The block hash

Examples:
> bitcoin-cli getblockhash 1000
> curl --user myusername --data-binary '{"jsonrpc": "1.0", "id": "curltest",
  "method": "getblockhash", 
  "params": [1000]}' -H 'content-type: text/plain;' http://127.0.0.1:8332/
----

在帮助信息的末尾，你将看到两个 RPC 命令的示例，使用 +bitcoin-cli+ 助手或 HTTP 客户端 +curl+。这些示例演示了你如何调用该命令。复制第一个示例并查看结果：

----
$ bitcoin-cli getblockhash 1000
00000000c937983704a73af28acdec37b049d214adbda81d7e2a3dd146f6ed09
----

结果是一个区块哈希，我们将在后续章节中详细描述。但现在，这个命令应该在你的系统上返回相同的结果，证明你的 Bitcoin Core 节点正在运行，接受命令，并且有关于区块 1,000 的信息返回给你。

在接下来的部分中，我们将演示一些非常有用的 RPC 命令及其预期的输出。

#### 获取 Bitcoin Core 的状态信息

Bitcoin Core 通过 JSON-RPC 接口提供不同模块的状态报告。最重要的命令包括 +getblockchaininfo+、+getmempoolinfo+、+getnetworkinfo+ 和 +getwalletinfo+。

Bitcoin 的 +getblockchaininfo+ RPC 命令之前已经介绍过。+getnetworkinfo+ 命令显示有关比特币网络节点状态的基本信息。使用 +bitcoin-cli+ 运行它：

----
$ bitcoin-cli getnetworkinfo
----
[source,json]
----
{
  "version": 240001,
  "subversion": "/Satoshi:24.0.1/",
  "protocolversion": 70016,
  "localservices": "0000000000000409",
  "localservicesnames": [
    "NETWORK",
    "WITNESS",
    "NETWORK_LIMITED"
  ],
  "localrelay": true,
  "timeoffset": -1,
  "networkactive": true,
  "connections": 10,
  "connections_in": 0,
  "connections_out": 10,
  "networks": [
    "...detailed information about all networks..."
  ],
  "relayfee": 0.00001000,
  "incrementalfee": 0.00001000,
  "localaddresses": [
  ],
  "warnings": ""
}
----

数据以 JavaScript 对象表示法（JSON）返回，这是一种所有编程语言都可以轻松“消费”的格式，但也非常易于人类阅读。在这些数据中，我们看到 Bitcoin Core 软件和比特币协议的版本号。我们还看到当前连接数以及有关比特币网络和此节点设置的各种信息。

[TIP]
====
+bitcoind+ 可能需要一些时间（可能超过一天）才能赶上当前的区块链高度，因为它从其他比特币节点下载区块并验证这些区块中的每一笔交易——截至本文撰写时，几乎有十亿笔交易。你可以使用 +getblockchaininfo+ 检查其进度，查看已知区块的数量。本章其余部分的示例假设你至少处于区块 775,072。由于比特币交易的安全性取决于区块，以下示例中的一些信息将根据你的节点拥有的区块数量略有变化。
====

[[exploring_and_decoding_transactions]]
#### 探索和解码交易

在 <<spending_bitcoin>> 中，Alice 从 Bob 的商店购买了一些东西。她的交易被记录在区块链上。让我们使用 API 通过传递交易 ID（txid）作为参数来检索并检查该交易：

[[alice_tx_serialized]]
.Alice 的序列化交易
----
$ bitcoin-cli getrawtransaction 466200308696215bbc949d5141a49a41\
38ecdfdfaa2a8029c1f9bcecd1f96177

01000000000101eb3ae38f27191aa5f3850dc9cad00492b88b72404f9da13569
8679268041c54a0100000000ffffffff02204e0000000000002251203b41daba
4c9ace578369740f15e5ec880c28279ee7f51b07dca69c7061e07068f8240100
000000001600147752c165ea7be772b2c0acb7f4d6047ae6f4768e0141cf5efe
2d8ef13ed0af21d4f4cb82422d6252d70324f6f4576b727b7d918e521c00b51b
e739df2f899c49dc267c0ad280aca6dab0d2fa2b42a45182fc83e81713010000
0000
----

[TIP]
====
交易 ID（txid）不是权威的。区块链中缺少 txid 并不意味着交易未被处理。这被称为“交易延展性”，因为交易在确认到区块之前可以被修改，从而改变其 txid。交易被包含在区块中后，其 txid 不能更改，除非发生区块链重组，该区块从最佳区块链中移除。在交易有多次确认后，重组是罕见的。
====

+getrawtransaction+ 命令返回一个十六进制表示的序列化交易。要解码它，我们使用 +decoderawtransaction+ 命令，将十六进制数据作为参数传递。你可以复制 +getrawtransaction+ 返回的十六进制数据并将其作为参数粘贴到 +decoderawtransaction+ 中：

++++
<pre data-type="programlisting">
$ bitcoin-cli decoderawtransaction 01000000000101eb3ae38f27191aa5f3850dc9cad0\
0492b88b72404f9da135698679268041c54a0100000000ffffffff02204e00000000000022512\
03b41daba4c9ace578369740f15e5ec880c28279ee7f51b07dca69c7061e07068f82401000000\
00001600147752c165ea7be772b2c0acb7f4d6047ae6f4768e0141cf5efe2d8ef13ed0af21d4f\
4cb82422d6252d70324f6f4576b727b7d918e521c00b51be739df2f899c49dc267c0ad280aca6\
dab0d2fa2b42a45182fc83e817130100000000
</pre>
++++

++++
<pre data-type="programlisting" data-code-language="json">
{
  "txid": "466200308696215bbc949d5141a49a4138ecdfdfaa2a8029c1f9bcecd1f96177",
  "hash": "f7cdbc7cf8b910d35cc69962e791138624e4eae7901010a6da4c02e7d238cdac",
  "version": 1,
  "size": 194,
  "vsize": 143,
  "weight": 569,
  "locktime": 0,
  "vin": [
    {
      "txid": "4ac541802679866935a19d4f40728bb89204d0cac90d85f3a51a19...aeb",
      "vout": 1,
      "scriptSig": {
        "asm": "",
        "hex": ""
      },
      "txinwitness": [
        "cf5efe2d8ef13ed0af21d4f4cb82422d6252d70324f6f4576b727b7d918e5...301"
      ],
      "sequence": 4294967295
    }
  ],
  "vout": [
    {
      "value": 0.00020000,
      "n": 0,
      "scriptPubKey": {
        "asm": "1 3b41daba4c9ace578369740f15e5ec880c28279ee7f51b07dca...068",
        "desc": "rawtr(3b41daba4c9ace578369740f15e5ec880c282### 继续翻译

```markdown
        "hex": "51203b41daba4c9ace578369740f15e5ec880c28279ee7f51b07d...068",
        "address": "bc1p8dqa4wjvnt890qmfws83te0v3qxzsfu7ul63kp7u56w8q...5qn",
        "type": "witness_v1_taproot"
      }
    },
    {
      "value": 0.00075000,
      "n": 1,
      "scriptPubKey": {
        "asm": "0 7752c165ea7be772b2c0acb7f4d6047ae6f4768e",
        "desc": "addr(******************************************)#qq404gts",
        "hex": "00147752c165ea7be772b2c0acb7f4d6047ae6f4768e",
        "address": "******************************************",
        "type": "witness_v0_keyhash"
      }
    }
  ]
}
</pre>
++++

交易解码显示了该交易的所有组成部分，包括交易的输入和输出。在这个例子中，我们看到该交易使用了一个输入并生成了两个输出。该交易的输入来自之前确认的交易（显示为输入 +txid+）。两个输出分别对应支付给 Bob 的金额和返回给 Alice 的找零。

我们可以通过使用相同的命令（例如，[.keep-together]#+getrawtransaction+）# 进一步探索区块链，检查该交易中引用的前一笔交易。通过从一个交易跳到另一个交易，我们可以追踪比特币从一个所有者传递到下一个所有者的过程。

#### 探索区块

探索区块与探索交易类似。然而，区块可以通过区块**高度**或区块**哈希**来引用。首先，让我们通过区块高度找到一个区块。我们使用 +getblockhash+ 命令，该命令以区块高度为参数并返回该区块的**区块头哈希**：

++++
<pre data-type="programlisting">
$ bitcoin-cli getblockhash 123456
0000000000002917ed80650c6174aac8dfc46f5fe36480aaef682ff6cd83c3ca
</pre>
++++

现在我们知道所选区块的区块头哈希，我们可以查询该区块。我们使用 +getblock+ 命令，以区块哈希为参数：

++++
<pre data-type="programlisting">
$ bitcoin-cli getblock 0000000000002917ed80650c6174aac8dfc46f5fe36480aaef682f\
f6cd83c3ca
</pre>
++++

++++
<pre data-type="programlisting" data-code-language="json">
{
  "hash": "0000000000002917ed80650c6174aac8dfc46f5fe36480aaef682ff6cd83c3ca",
  "confirmations": 651742,
  "height": 123456,
  "version": 1,
  "versionHex": "00000001",
  "merkleroot": "0e60651a9934e8f0decd1c[...]48fca0cd1c84a21ddfde95033762d86c",
  "time": 1305200806,
  "mediantime": 1305197900,
  "nonce": 2436437219,
  "bits": "1a6a93b3",
  "difficulty": 157416.4018436489,
  "chainwork": "[...]00000000000000000000000000000000000000541788211ac227bc",
  "nTx": 13,
  "previousblockhash": "[...]60bc96a44724fd72daf9b92cf8ad00510b5224c6253ac40095",
  "nextblockhash": "[...]00129f5f02be247070bf7334d3753e4ddee502780c2acaecec6d66",
  "strippedsize": 4179,
  "size": 4179,
  "weight": 16716,
  "tx": [
    "5b75086dafeede555fc8f9a810d8b10df57c46f9f176ccc3dd8d2fa20edd685b",
    "e3d0425ab346dd5b76f44c222a4bb5d16640a4247050ef82462ab17e229c83b4",
    "137d247eca8b99dee58e1e9232014183a5c5a9e338001a0109df32794cdcc92e",
    "5fd167f7b8c417e59106ef5acfe181b09d71b8353a61a55a2f01aa266af5412d",
    "60925f1948b71f429d514ead7ae7391e0edf965bf5a60331398dae24c6964774",
    "d4d5fc1529487527e9873256934dfb1e4cdcb39f4c0509577ca19bfad6c5d28f",
    "7b29d65e5018c56a33652085dbb13f2df39a1a9942bfe1f7e78e97919a6bdea2",
    "0b89e120efd0a4674c127a76ff5f7590ca304e6a064fbc51adffbd7ce3a3deef",
    "603f2044da9656084174cfb5812feaf510f862d3addcf70cacce3dc55dab446e",
    "9a4ed892b43a4df916a7a1213b78e83cd83f5695f635d535c94b2b65ffb144d3",
    "dda726e3dad9504dce5098dfab5064ecd4a7650bfe854bb2606da3152b60e427",
    "e46ea8b4d68719b65ead930f07f1f3804cb3701014f8e6d76c4bdbc390893b94",
    "864a102aeedf53dd9b2baab4eeb898c5083fde6141113e0606b664c41fe15e1f"
  ]
}
</pre>
++++

+confirmations+ 条目告诉我们该区块的**深度**——有多少区块建立在它之上，表明更改该区块中任何交易的难度。+height+ 告诉我们该区块之前有多少区块。我们看到区块的版本、创建时间（根据矿工的时间）、前 11 个区块的中位时间（矿工更难操纵的时间测量值）以及区块大小的三种不同测量值（其传统的剥离大小、完整大小和权重单位大小）。我们还看到一些用于安全性和工作量证明的字段（默克尔根、随机数、难度和链工作量）；我们将在 <<mining>> 中详细研究这些内容。

#### 使用 Bitcoin Core 的编程接口

+bitcoin-cli+ 助手对于探索 Bitcoin Core API 和测试功能非常有用。但 API 的整个意义在于以编程方式访问功能。在本节中，我们将演示如何从另一个程序访问 Bitcoin Core。

Bitcoin Core 的 API 是一个 JSON-RPC 接口。JSON 是一种非常方便的格式，可以轻松地被所有编程语言“消费”，同时也非常易于人类阅读。RPC 代表远程[.keep-together]过程调用，这意味着我们通过网络协议调用远程（在 Bitcoin Core 节**点上）的过程（函数）。在**这种情况下，网络协议是 HTTP。

当我们使用 +bitcoin-cli+ 命令获取命令的帮助时，它向我们展示了如何使用 +curl+（多功能命令行 HTTP 客户端）构建这些 JSON-RPC 调用之一：

----
$ curl --user myusername --data-binary '{"jsonrpc": "1.0", "id":"curltest",
  "method": "getblockchaininfo", 
  "params": [] }' -H 'content-type: text/plain;' http://127.0.0.1:8332/
----

此命令显示 +curl+ 向本地主机（127.0.0.1）提交 HTTP 请求，连接到默认的比特币 RPC 端口（8332），并使用 +text/plain+ 编码提交 +jsonrpc+ 请求以调用 +getblockchaininfo+ 方法。

你可能会注意到 +curl+ 会要求随请求一起发送凭据。Bitcoin Core 每次启动时都会创建一个随机密码，并将其放在数据目录下的 +.cookie+ 文件中。+bitcoin-cli+ 助手可以根据数据目录读取此密码文件。同样，你可以复制密码并将其传递给 +curl+（或任何更高级的 Bitcoin Core RPC 包装器），如 <<cookie_auth>> 所示。

[[cookie_auth]]
.使用基于 cookie 的身份验证与 Bitcoin Core
====
----
$ cat .bitcoin/.cookie
  __cookie__:17c9b71cef21b893e1a019f4bc071950c7942f49796ed061b274031b17b19cd0

$ curl 
  --user __cookie__:17c9b71cef21b893e1a019f4bc071950c7942f49796ed061b274031b17b19cd0 
  --data-binary '{"jsonrpc": "1.0", "id":"curltest", 
  "method": "getblockchaininfo", 
  "params": [] }' -H 'content-type: text/plain;' http://127.0.0.1:8332/

{"result":{"chain":"main","blocks":799278,"headers":799278,
"bestblockhash":"000000000000000000018387c50988ec705a95d6f765b206b6629971e6978879",
"difficulty":53911173001054.59,"time":1689703111,"mediantime":1689701260,
"verificationprogress":0.9999979206082515,"initialblockdownload":false,
"chainwork":"00000000000000000000000000000000000000004f3e111bf32bcb47f9dfad5b",
"size_on_disk":563894577967,"pruned":false,"warnings":""},"error":null,
"id":"curltest"}
----
====

或者，你可以使用 Bitcoin Core 源代码目录中的 [.keep-together]_./share/rpcauth/rpcauth.py_# 提供的帮助脚本创建一个静态密码。

如果你在自己的程序中实现 JSON-RPC 调用，可以使用通用的 HTTP 库来构建调用，类似于前面的 +curl+ 示例。

**然而，大多数流行的编程语言中都有库可以“包装”Bitcoin Core API，使其更加简单。我们将使用 +python-bitcoinlib+ 库来简化 API 访问。**该库不属于 Bitcoin Core 项目，需要像安装其他 Python 库一样安装。请记住，这需要你有一个正在运行的 Bitcoin Core 实例，该实例将用于进行 JSON-RPC 调用。

<<rpc_example>> 中的 Python 脚本进行了一个简单的 +getblockchaininfo+ 调用，并打印了 Bitcoin Core 返回的数据中的 +block+ 参数。

[[rpc_example]]
.通过 Bitcoin Core 的 JSON-RPC API 运行 +getblockchaininfo+
====
[source,python]
----
include::code/rpc_example.py[]
----
====

运行它得到以下结果：

----
$ python rpc_example.py
773973
----

它告诉我们本地 Bitcoin Core 节点在其区块链中有多少个区块。虽然不是一个惊人的结果，但它展示了该库作为 Bitcoin Core JSON-RPC API 简化接口的基本用法。

接下来，让我们使用 +getrawtransaction+ 和 +decodetransaction+ 调用来检索 Alice 支付给 Bob 的交易详情。在 <<rpc_transaction>> 中，我们检索 Alice 的交易并列出交易的输出。对于每个输出，我们显示接收地址和金额。提醒一下，Alice 的交易有一个输出支付给 Bob，另一个输出是返回给 Alice 的找零。

[[rpc_transaction]]
.检索交易并迭代其输出
====
[source,python]
----
include::code/rpc_transaction.py[]
----
====

运行此代码，我们得到：

----
$ python rpc_transaction.py
************************************************************** 0.00020000
****************************************** 0.00075000
----

前面的两个示例都比较简单。你实际上并不需要一个程序来运行它们；你可以轻松地使用 +bitcoin-cli+ 助手。然而，下一个示例需要进行数百次 RPC 调用，更清楚地展示了编程接口的使用。

在 <<rpc_block>> 中，我们首先检索一个区块，然后通过引用每个交易 ID 检索其中的所有交易。接下来，我们遍历每个交易的输出并累加其金额。

[[rpc_block]]
.检索区块并累加所有交易的输出
====
[source,python]
----
include::code/rpc_block.py[]
----
====

运行此代码，我们得到：

----
$ python rpc_block.py

Total value in block:  10322.07722534
----

我们的示例代码计算出该区块中交易的总价值为 10,322.07722534 BTC（包括 25 BTC 的奖励和 0.0909 BTC 的手续费）。将其与区块浏览器网站报告的金额进行比较，搜索区块哈希或高度。一些区块浏览器报告的总价值不包括奖励和手续费。看看你是否能发现差异。

[[alt_libraries]]
### 替代客户端、库和工具包

**比特币生态系统中有许多替代客户端、库、工具包，甚至全节点实现**。这些实现使用各种编程语言，为程序员提供了他们首选语言的本地接口。

以下部分列出了一些最好的库、客户端和工具包，按编程语言组织。

#### C/C++
[Bitcoin Core](https://oreil.ly/BdOwl) :: 比特币的参考实现

#### JavaScript
[bcoin](https://bcoin.io) :: 一个模块化且可扩展的全节点实现，带有 API  
[Bitcore](https://bitcore.io) :: 由 Bitpay 提供的全节点、API 和库  
[BitcoinJS](https://oreil.ly/4iqf2) :: 一个纯 JavaScript 比特币库，适用于 node.js 和浏览器

#### Java
[bitcoinj](https://bitcoinj.github.io) :: 一个 Java 全节点客户端库

#### Python
[python-bitcoinlib](https://oreil.ly/xn_rg) :: 由 Peter Todd 提供的 Python 比特币库、共识库和节点  
[pycoin](https://oreil.ly/wcpXP) :: 由 Richard Kiss 提供的 Python 比特币库

#### Go
[btcd](https://oreil.ly/h5MEI) :: 一个 Go 语言的全节点比特币客户端

#### Rust
[rust-bitcoin](https://oreil.ly/me6gf) :: 用于序列化、解析和 API 调用的 Rust 比特币库

#### Scala
[bitcoin-s](https://bitcoin-s.org) :: 一个用 Scala 实现的比特币

#### C#
[NBitcoin](https://oreil.ly/Qfjgq) :: 一个全面的 .NET 框架比特币库

还有许多其他编程语言的库，而且新的库还在不断创建。

如果你按照本章的说明操作，你现在已经运行了 Bitcoin Core，并开始使用你自己的全节点探索网络和区块链。从现在开始，你可以独立使用你控制的软件——在你控制的计算机上——来验证你收到的任何比特币是否遵循比特币系统中的每一条规则，而不必信任任何外部权威。在接下来的章节中，我们将更多地了解系统的规则，以及你的节点和钱包如何使用它们来保护你的资金、保护你的隐私，并使支出和接收[.keep-together]
```