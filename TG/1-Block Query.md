## Where
```sql
GSQL > use graph financialGraph
GSQL > SELECT s FROM (s:Account) LIMIT 10
GSQL > SELECT s FROM (s:Account {name: "<PERSON>"})
GSQL > SELECT s FROM (s:Account WHERE s.isBlocked)
GSQL > SELECT s FROM (s:Account) WHERE s.name IN ("<PERSON>", "<PERSON>")
GSQL > SELECT s, e, t FROM (s:Account) -[e:transfer]-> (t:Account) WHERE s <> t
```
## Having
```sql
GSQL > use graph financialGraph
GSQL > SELECT s FROM (s:Account) -[e:transfer]-> (t:Account) having s.isBlocked
GSQL > SELECT s FROM (s:Account) -[e:transfer]-> (t:Account) having s.isBlocked AND s.name = "<PERSON>"
```
## Aggregation
```sql
GSQL > use graph financialGraph
GSQL > SELECT COUNT(s) FROM (s:_)
GSQL > SELECT COUNT(*) FROM (s:Account:City)
GSQL > SELECT COUNT(DISTINCT t) FROM (s:Account)-[e]->(t)
GSQL > SELECT COUNT(e), STDEV(e.amount), AVG(e.amount) FROM (s:Account)-[e:transfer|isLocatedIn]->(t)
GSQL > SELECT a, sum(e.amount) as amount1 , sum(e2.amount) as amount2 FROM (a:Account)-[e:transfer]->(b:Account)-[e2:transfer]->(c:Account) GROUP BY a;
```
## Expression in select result
```sql
# Using mathematical expressions
GSQL > use graph financialGraph
GSQL > SELECT s, e.amount*0.01 AS amt FROM (s:Account {name: "Scott"})- [e:transfer]-> (t)

# Using CASE expression
GSQL > BEGIN
GSQL >  SELECT s, CASE WHEN e.amount*0.01 > 80 THEN true ELSE false END AS status 
GSQL >  FROM (s:Account {name: "Scott"})- [e:transfer]-> (t)
GSQL > END
```
## Let: define variables
```sql
GSQL > use graph financialGraph 
GSQL > BEGIN 
GSQL > LET
GSQL >  DOUBLE a = 500.0; 
GSQL >  STRING b = "Jenny"; 
GSQL >  BOOL c = false; 
GSQL > IN 
GSQL >  SELECT s, e.amount AS amt, t
GSQL >  FROM (s:Account) - [e:transfer]-> (t:Account) 
GSQL >  WHERE s.isBlocked = c AND t.name <> b 
GSQL >  HAVING amt > a; 
GSQL > END
```
## Accumulator
```sql
GSQL> use graph financialGraph
GSQL> BEGIN
GSQL> LET  
GSQL>  SetAccum<string> @transferNames;  
GSQL>  IN  
GSQL>   SELECT s FROM (s:Account where s.isBlocked) -[:transfer*1..3]- (t:Account)  
GSQL>   ACCUM s.@transferNames += t.name;
GSQL> END
```

```sql
GSQL> use graph financialGraph
GSQL> BEGIN 
GSQL> LET   
GSQL>  double ratio = 0.01;  
GSQL>  SumAccum<double> @totalAmt;  // local accumulator for total amount 
GSQL>  SumAccum<INT> @@cnt;  // global accumulator for count
GSQL> IN  
GSQL>  SELECT s FROM (s:Account {name:"Ed"}) - [e:transfer]-> (t:Account) 
GSQL>  ACCUM s.@totalAmt += ratio * e.amount, // Accumulate total amount for s 
GSQL>      @@cnt += 1; // Accumulate count of transfers
GSQL> END
```