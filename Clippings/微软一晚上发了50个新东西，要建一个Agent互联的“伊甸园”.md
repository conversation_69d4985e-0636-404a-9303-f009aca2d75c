---
title: "微软一晚上发了50个新东西，要建一个Agent互联的“伊甸园”"
source: "https://mp.weixin.qq.com/s/foe144cf9ORbaIapZOJS2w"
author:
  - "[[晓静]]"
published:
created: 2025-05-20
description: "微软争抢Agent话语权。"
tags:
  - "clippings"
---
原创 晓静 *2025年05月20日 08:50*

![图片](https://mmbiz.qpic.cn/sz_mmbiz_png/ow6przZuPIHJVHREibrdJawsrVuicf58qCCia8XVibzzZmc4ftugzbafKdJWDgicDMkeOn2F3O0OVCH9v5kibIspN0vw/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1)

**腾讯科技特约作者｜晓静、苏扬**

**编辑｜萌萌**

北京时间5月20日凌晨，微软Build 2025开幕，主题为“开放的 Agentic Web”。 **微软一口气发布了 50 余个新产品/新服务，宣称要为开发者铺设“从模型到智能体”的高速路。**

这50多项发布包括 **全新平台 Windows AI Foundry、开放协议 Model Context Protocol (MCP) 与 NLWeb、企业级多智能体管控工具，以及把 xAI Grok 3 系列等 1,900+ 模型接入 Azure AI Foundry 的“模型工厂”。**

回头看这两年的Build的大会，变化速度很快，这一点在后面的内容也会有提及——Build 2024的主题是“Copilot 无处不在”， **今年微软已鲜少提及“Copilot”这一概念，取而代之的是全面渗透的“Agent”。**

**微软CEO纳德拉在主题演讲中宣布：“我们已经进入了AI Agent时代，正在见证AI系统如何以全新方式帮助我们解决问题。”**

微软在Build 2025中明确提出了“开放的智能体网络”（Open Agentic Web）的愿景，旨在实现AI Agent跨场景自主运作，但“Agent伊甸园”这一愿景前路漫漫，它将面临四大挑战：

- Anthropic等主导的MCP与NLWeb开放标准尚未统一，存在碎片化风险；
- Copilot+ NPU电脑普及不足制约本地推理优势；
- 多智能体链式调用加剧安全风险，缺乏成熟应对方案；
- 谷歌、苹果侧重端侧私有云，若拒绝MCP将削弱微软网络效应。

也正因为此，在 Build 2025 的三场“顶流对话”中， **萨提亚·纳德拉试图通过目前AI圈“最有分量”的三个人——山姆·奥特曼、黄仁勋和马斯克，将“Open Agentic Web”理念拉进了“产业实战”。**

Open AI CEO山姆·奥特曼宣布，推出面向开发者的新 Codex 智能体，称“这可能是编程史上最大的变革，它让开发者第一次拥有‘真正的虚拟队友’，可以把任务扔给它，几天后直接收成果”；

英伟达创始人黄仁勋强调“加速计算正在重新定义一切”，并将 CUDA 比作“AI 时代的电力网”，只有让 GPU、云和代理协议无缝呼应，才能把推理速度再提升一个数量级；

xAI 创始人埃隆·马斯克一面确认 Grok 3/3 mini 正式登陆 Azure AI Foundry，一面重申他的“物理学底座”方法论——“Physics is the law，其他都是建议”，并直言“最好的安全策略就是彻底的透明与诚实”。

![图片](https://mp.weixin.qq.com/s/www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3E%3C/title%3E%3Cg%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%20fill-opacity='0'%3E%3Cg%20transform='translate(-249.000000,%20-126.000000)'%20fill='%23FFFFFF'%3E%3Crect%20x='249'%20y='126'%20width='1'%20height='1'%3E%3C/rect%3E%3C/g%3E%3C/g%3E%3C/svg%3E)

**Build大会省流版：**

- **开放的 Agentic Web：** 微软提出让智能体像网页一样互联互通，依托 MCP 形成“AI 的 USB-C”，并以 NLWeb 为网站提供自然语言端点，方便智能体读取与调用。
- **端-云一体化：** Windows AI Foundry 把模型本地训练/推理与 Azure AI Foundry 的云端治理打通，开发者可用统一 API 在 PC、服务器乃至浏览器中复用模型与智能体。
- **多智能体编排：** Azure AI Foundry Agent Service 正式GA（全面可用），Copilot Studio 引入多智能体编排，企业可在同一工作流中让多角色智能体协同。
- **NLWeb+MCP：** 构建智能体时代的 HTML。
- **安全与治理：** Entra Agent ID 为每个智能体分配“数字身份证”，Foundry Observability 与 Prompt Shields 监控质量、成本与越权风险。
![图片](https://mp.weixin.qq.com/s/www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3E%3C/title%3E%3Cg%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%20fill-opacity='0'%3E%3Cg%20transform='translate(-249.000000,%20-126.000000)'%20fill='%23FFFFFF'%3E%3Crect%20x='249'%20y='126'%20width='1'%20height='1'%3E%3C/rect%3E%3C/g%3E%3C/g%3E%3C/svg%3E)

#### “Open Agentic Web”蓝图

![图片](https://mp.weixin.qq.com/s/www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3E%3C/title%3E%3Cg%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%20fill-opacity='0'%3E%3Cg%20transform='translate(-249.000000,%20-126.000000)'%20fill='%23FFFFFF'%3E%3Crect%20x='249'%20y='126'%20width='1'%20height='1'%3E%3C/rect%3E%3C/g%3E%3C/g%3E%3C/svg%3E)

微软Build 2025大会上提出的“智能体网络”（agentic web） 概念，远远超出了当今人工智能助手的范畴。

在微软的愿景中，智能体会积极主动地发起任务、独立做出决策、与其他人工智能系统协调，并在极少的人类监督下完成复杂的工作流程。这标志着人工智能系统运行及与用户和其他技术交互方式的根本转变。

微软首席技术官凯文·斯科特称“智能体网络”（agentic web）从根本上改变了人类与技术的交互方式：“推理能力将持续提升，我们有望在这一领域取得显著进展。然而，若想让智能体承担更为复杂的工作任务，一些关键要素必须尽快实现。”

**其中一个关键的缺失要素是记忆，斯科特表示：“为了解决这个问题，微软正在引入几种与记忆相关的技术，包括结构化检索增强生成（RAG），它可以帮助人工智能系统更精准地从海量数据中回忆信息。”**

微软副总裁史蒂文·巴蒂奇在一场关于智能体的演讲中解释说：“你可能会拥有一个专属于个人的智能体以及一个工作专用智能体。其中，工作专用智能体将关联您与雇主的各类信息。”

巴蒂奇强调，这种情境感知对于创造智能体至关重要，它能够很好地理解用户、对用户所处的情境进行情境化，以便用户可以少点几次按钮。这种 **从纯粹的响应式人工智能向具有持久记忆的系统转变，代表着智能体革命最深刻的方面之一。**

![图片](https://mp.weixin.qq.com/s/www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3E%3C/title%3E%3Cg%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%20fill-opacity='0'%3E%3Cg%20transform='translate(-249.000000,%20-126.000000)'%20fill='%23FFFFFF'%3E%3Crect%20x='249'%20y='126'%20width='1'%20height='1'%3E%3C/rect%3E%3C/g%3E%3C/g%3E%3C/svg%3E)

#### Azure AI Foundry 平台

#### 端云一体化

微软在活动中对Azure AI Foundry平台进行了重要更新，该平台用于开发与管理人工智能应用程序及智能体。

微软负责人工智能智能体的副总裁雷·史密斯在接受媒体采访时强调了多智能体系统的重要性：“多智能体调用、调试以及深入分析这些智能体是关键，这不仅延伸至Copilot Studio，更涵盖了即将推出的Azure AI Foundry智能体。我们的客户一直强调这种多智能体能力对他们的业务至关重要。”

史密斯解释了将任务分配给多个智能体的原因—— **开发者很难将一个可靠的流程压缩到一个智能体中。将其拆分为多个部分，有助于提高可维护性，简化解决方案的构建，同时显著增强可靠性** 。

Azure AI Foundry Agent Service目前已全面上线，支持开发企业级人工智能智能体，兼容多智能体工作流及开放协议，兼容A2A和MCP协议，让企业能够编排多个专业智能体，共同处理复杂任务。

尽管基于云端的人工智能占据新闻头条，但微软正大力推进本地设备端人工智能的发展，针对希望在用户设备端直接部署人工智能的开发者发布多项重要成果。

Windows AI Foundry作为Windows Copilot Runtime的进化版，为Windows平台的本地人工智能开发提供统一平台。其集成Windows ML及模型优化工具，支持在Windows 11与macOS设备上直接运行AI模型、工具与智能体。

微软副总裁史蒂文·巴蒂奇在演讲中谈及客户端AI的爆发式增长：“我们正忙于预测并引领趋势，多数预测在三到四个月内即成真，这与过去习惯预测一两年后的节奏大相径庭。尽管压力巨大，但也充满机遇。”

![图片](https://mp.weixin.qq.com/s/www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3E%3C/title%3E%3Cg%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%20fill-opacity='0'%3E%3Cg%20transform='translate(-249.000000,%20-126.000000)'%20fill='%23FFFFFF'%3E%3Crect%20x='249'%20y='126'%20width='1'%20height='1'%3E%3C/rect%3E%3C/g%3E%3C/g%3E%3C/svg%3E)

#### NLWeb+MCP

#### 智能体网络时代的 HTML

微软愿景的核心是通过开放标准实现不同平台与服务间的智能体互操作性，其中模型上下文协议扮演关键角色 **。**

微软宣布加入MCP指导委员会（the MCP Steering Committee ），并为生态系统贡献两项新成果： **更新的授权规范与MCP服务器注册服务设计。**

微软Core AI团队负责人杰伊·帕里克强调了开放与互操作性的重要性：“在微软内部，快速学习是关键。技术、应用与竞争格局的快速变化，要求我们以开放生态加速创新。”

![图片](https://mp.weixin.qq.com/s/www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3E%3C/title%3E%3Cg%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%20fill-opacity='0'%3E%3Cg%20transform='translate(-249.000000,%20-126.000000)'%20fill='%23FFFFFF'%3E%3Crect%20x='249'%20y='126'%20width='1'%20height='1'%3E%3C/rect%3E%3C/g%3E%3C/g%3E%3C/svg%3E)

此外，微软还推出新开源项目NLWeb **，** 其定位为 “智能体网络时代的 HTML”，允许网站为用户提供灵活对话界面，支持自定义模型与数据交互。

微软还宣布， **将埃隆·马斯克旗下xAI公司开发的Grok系列模型正式纳入其Azure AI Foundry平台，其中包括2025年2月发布的第三代旗舰模型Grok 3及其轻量级版本Grok 3 mini。**

通过此次合作，微软Azure云服务的人工智能模型库已扩展至超过1900种模型变体，覆盖OpenAI、Meta Platforms和DeepSeek等主流合作伙伴的技术生态。

Grok系列模型具有多重应用场景：在技术架构上，Grok 3基于20万块GPU集群训练完成，其推理版本Grok 3 Reasoning通过自我事实核查机制提升了专业领域的准确性；在服务形态上，除支持Azure平台外，Grok系列模型还为马斯克旗下社交平台X的聊天机器人提供核心技术支持。

此前，该聊天机器人曾因传播争议性言论引发关注，xAI回应称系模型遭遇“未经授权的修改”，并承诺将通过提升提示代码透明度加强内容治理。

在微软首席执行官萨提亚·纳德拉主持的活动中，马斯克通过全息投影强调：“开发者社区的反馈对AI模型进化至关重要，我们始终保持开放迭代的态度——既会快速修正已发现的错误，也将持续优化尚未暴露的潜在缺陷。”目前xAI团队正加速推进Grok 3.5版本的优化调试，预计近期将开放测试通道。

![图片](https://mp.weixin.qq.com/s/www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3E%3C/title%3E%3Cg%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%20fill-opacity='0'%3E%3Cg%20transform='translate(-249.000000,%20-126.000000)'%20fill='%23FFFFFF'%3E%3Crect%20x='249'%20y='126'%20width='1'%20height='1'%3E%3C/rect%3E%3C/g%3E%3C/g%3E%3C/svg%3E)

#### 安全与身份管理

#### 应对企业AI治理挑战

随着智能体在企业中的普及，微软通过多项新功能解决安全、治理与合规性核心需求，防范 “智能体扩散” 风险。

根据官方公告，预览版Microsoft Entra Agent ID可自动为开发者在Copilot Studio或Azure AI Foundry中创建的智能体分配唯一身份标识，帮助企业从源头安全管理智能体，避免因无序扩展导致的监管盲区。

微软同时将Purview数据安全与合规控制深度集成至AI平台，支持开发者构建具备企业级安全能力的AI解决方案。例如，针对Microsoft 365 Copilot智能体的数据丢失防护（DLP）控制，以及在AI交互中检测敏感数据的全新功能。

微软专家雷·史密斯向企业IT团队建议：“从头构建解决方案虽具灵活性，但需自行添加大量管控框架。而Copilot Studio的优势在于提供托管基础设施框架，内置生命周期管理、治理及可观测性能力。”

![图片](https://mp.weixin.qq.com/s/www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3E%3C/title%3E%3Cg%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%20fill-opacity='0'%3E%3Cg%20transform='translate(-249.000000,%20-126.000000)'%20fill='%23FFFFFF'%3E%3Crect%20x='249'%20y='126'%20width='1'%20height='1'%3E%3C/rect%3E%3C/g%3E%3C/g%3E%3C/svg%3E)

#### 智能体战略

#### 定义下一代计算范式

在本届大会上，人工智能智能体最具突破性的应用之一是微软Discovery平台，其目标是加速制药、材料科学等行业的科研与开发进程。

微软战略任务与技术副总裁杰森·赞德在接受专访时透露，该平台仅用200小时便发现一种非PFAS数据中心浸没式冷却剂—这一过程传统研发需耗时数年。“作为超大规模云服务提供商，数据中心对我们至关重要，”赞德表示，“通过平台框架，我们在200小时内筛选了36.7万种潜在物质，并与合作伙伴完成合成验证。”

他进一步指出，这一突破显著缩短传统研发周期：“此类任务通常需数年甚至十年，而受监管限制，企业必须快速替换被禁产品。如何压缩开发周期成为现实课题，AI智能体正为此提供颠覆性解决方案。”

“上一次让我如此兴奋还是在上世纪90年代初，” 微软首席技术官凯文·斯科特在Build大会上表示。“这些技术组件如 ‘糖果店’ 般对开发者充满吸引力—即使是非技术专家，也能理解其原理与组合逻辑，并迅速付诸实践。”

随着从 “信息网络” 向 “智能体网络” 的转型，微软的战略延续了其早期云计算布局思路—提供全栈工具、平台与基础设施，同时推动开放标准。当下的核心问题已非 “AI智能体是否会改变业务”，而是 “企业能否快速适应一个机器不仅响应指令，更能预见需求、自主决策并重塑工作方式的新世界。”

![图片](https://mp.weixin.qq.com/s/www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3E%3C/title%3E%3Cg%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%20fill-opacity='0'%3E%3Cg%20transform='translate(-249.000000,%20-126.000000)'%20fill='%23FFFFFF'%3E%3Crect%20x='249'%20y='126'%20width='1'%20height='1'%3E%3C/rect%3E%3C/g%3E%3C/g%3E%3C/svg%3E)

#### 奥特曼、马斯克、黄仁勋

#### 在线“打Call”

由于和微软的特殊关系，会前行业内都在猜测，山姆·奥特曼将不再出现在今年Build大会上。

结果，纳德拉还是以远程的形式与奥特曼连线，两人围绕OpenAI新发布的Codex——被定义为云端软件工程师Agent，开源等动作，对软件工程和开发工作效率的提升展开讨论。

在奥特曼看来， **Codex这样的Agent产品，已经是软件开发团队标配的虚拟编制** ，“在你有了一个可以真正指派工作的虚拟团队成员，”奥特曼说，“你可以同时发出很多请求，这样你就可以修bug、实现新功能，还能回答关于代码的问题。这就像是真正的软件工程任务分配。”

回到本次大会的核心主题Agent上，纳德拉说，开发者又要搭建框架，部署模型，还要做Agent协同，适应这种变化速度是最难的一点。

在这个话题上，山姆·奥特曼的看法是应该回头看，看看过去两年模型能力变化给业界带来的可能性，再以这个视角去探索未来，“未来在公司里构建产品和软件，真的会倾向于新工具以及新工作流程所带来的可能性。”

除了奥特曼，马斯克的出现也是今年Build大会的亮点之一。

在此之前，微软已经官宣将xAI的Grok系列模型正式纳入其Azure AI Foundry平台，马斯克同样远程到场助力， **暖场环节纳德拉甚至“爆料”马斯克曾在Windows团队实习** 。

当然，核心还是让马斯克给自家的Grok打分，谈效率提升以及未来版本迭代的能力和愿景，马斯克的老梗还是“第一性原理”，接下来的Grok 3.5将围绕该原理，将物理学方法应用到模型的思维过程中，“如果你想探寻基本真理，就需要把问题拆解到最可能正确的公理层面，然后从那里开始推理。”

邀请马斯克出场，给大会背书是一个目的，试图以行业视角来讲述复杂AI应用的构建，多Agent协作也是纳德拉的目的。

**马斯克的核心观点还是第一性原理，强调任何AI都要以物理定律为基础** ，马斯克说，“我见过很多人违反人类的法律，却从未见过任何人违反物理定律。”

今年大会的远程嘉宾中，黄仁勋是最特殊的一位，他刚在大洋彼岸的Computex上参加活动，也被纳德拉远程拉到了Build大会的现场。

两人的对话围绕英伟达的AI超级计算机的进展及其背后的技术革新与产业趋势展开，其中最具代表性的就是英伟达的GB200系统进入大规模生产，并在微软Azure的AI基础设施中大规模部署。

“结合我们新的 CUDA 算法、Azure 上的新型 AI 基础设施中的模型技术，共同实现了比 Hopper 架构 40 倍的性能提升。”黄仁勋说。

硬件话题是抛砖，软硬件如何结合则是纳德拉想要引出的玉。所以，他问了黄仁勋一个问题：“当我审视一个应用的性能时，延迟、成本、智能技术（如提示缓存）等因素都会产生巨大影响。能否谈谈软件的复合效应如何增强我们的协同成果？”

黄仁勋直接给了一个数据——40倍——Hopper架构芯片过去两年性能提升了40倍，Blackwell芯片又在Hopper架构的基础上提升了40倍，而这些进步都是软件算法更新的价值，当然还有架构的兼容。

黄仁勋说，“ **从 Pascal 到 Ampere，从 Hopper 到 Blackwell，再到下一代架构，我们的软件架构始终保持兼容。** 这促使开发者深入优化模型、算法，这些工作将惠及整个集群，现有集群能够受益于全栈的软件升级。”

（腾讯科技特约编译无忌对本文亦有贡献）

| **ima知识库**  ![图片](https://mp.weixin.qq.com/s/www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3E%3C/title%3E%3Cg%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%20fill-opacity='0'%3E%3Cg%20transform='translate(-249.000000,%20-126.000000)'%20fill='%23FFFFFF'%3E%3Crect%20x='249'%20y='126'%20width='1'%20height='1'%3E%3C/rect%3E%3C/g%3E%3C/g%3E%3C/svg%3E)  AI能量站汇集AI应用实践的基础科普与教程，覆盖全球热门公司、顶尖科学家、研究员以及市场机构输出的人工智能的基础理论、技术研究、价值对齐理论和产业发展报告，以及全球的AI监管政策。帮助AI小白入门，替进阶选手跟踪最新的AI知识。 |
| --- |

  

**推荐阅读**

[![图片](https://mp.weixin.qq.com/s/www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3E%3C/title%3E%3Cg%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%20fill-opacity='0'%3E%3Cg%20transform='translate(-249.000000,%20-126.000000)'%20fill='%23FFFFFF'%3E%3Crect%20x='249'%20y='126'%20width='1'%20height='1'%3E%3C/rect%3E%3C/g%3E%3C/g%3E%3C/svg%3E)](https://mp.weixin.qq.com/s?__biz=Mjc1NjM3MjY2MA==&mid=2691558483&idx=1&sn=70ee2390e313d645a7eaf76a21390fea&scene=21#wechat_redirect) 打字即出图！混元图像2.0让AI生图进入“零延迟”时代

![图片](https://mp.weixin.qq.com/s/www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3E%3C/title%3E%3Cg%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%20fill-opacity='0'%3E%3Cg%20transform='translate(-249.000000,%20-126.000000)'%20fill='%23FFFFFF'%3E%3Crect%20x='249'%20y='126'%20width='1'%20height='1'%3E%3C/rect%3E%3C/g%3E%3C/g%3E%3C/svg%3E) ![图片](https://mp.weixin.qq.com/s/www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3E%3C/title%3E%3Cg%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%20fill-opacity='0'%3E%3Cg%20transform='translate(-249.000000,%20-126.000000)'%20fill='%23FFFFFF'%3E%3Crect%20x='249'%20y='126'%20width='1'%20height='1'%3E%3C/rect%3E%3C/g%3E%3C/g%3E%3C/svg%3E)

[阅读原文](https://mp.weixin.qq.com/s/)

继续滑动看下一个

腾讯科技

向上滑动看下一个