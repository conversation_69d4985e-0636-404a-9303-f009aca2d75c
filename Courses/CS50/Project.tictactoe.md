---
id: 0b8w8z8ped9vk0uijbtl78m
title: tictactoe
desc: ''
updated: 1706429360967
created: 1706422703959
---

## Code
```py
"""
Tic Tac Toe Player
"""

import math
import copy

X = "X"
O = "O"
EMPTY = None


def initial_state():
    """
    Returns starting state of the board.
    """
    return [[EMPTY, EMPTY, EMPTY],
            [EMPTY, EMPTY, EMPTY],
            [EMPTY, EMPTY, EMPTY]]


def player(board):
    """
    Returns player who has the next turn on a board.
    """
    count_X = 0
    count_O = 0
    for i in range(3):
        for j in range(3):
            if board[i][j] == X:
                count_X += 1
            if board[i][j] == O:
                count_O += 1
    if count_X <= count_O:
        return X
    return O


def actions(board):
    """
    Returns set of all possible actions (i, j) available on the board.
    """
    s = set()
    for i in range(3):
        for j in range(3):
            if board[i][j] == EMPTY:
                s.add((i, j))
    return s


def result(board, action):
    """
    Returns the board that results from making move (i, j) on the board.
    """
    if board[action[0]][action[1]] != EMPTY:
        raise Exception("Invalid action")

    b = copy.deepcopy(board) 
    b[action[0]][action[1]] = player(b)
    return b


def winner(board):
    """
    Returns the winner of the game, if there is one.
    """
    # check rows
    for i in range(3):
        if all(a == X for a in board[i]):
            return X
        if all(a == O for a in board[i]):
            return O
    # check cols
    for j in range(3):
        col = [board[0][j], board[1][j], board[2][j]]
        if all(a == X for a in col):
            return X
        if all(a == O for a in col):
            return O 
    # other
    line = [board[0][0], board[1][1], board[2][2]]
    if all(a == X for a in line):
            return X
    if all(a == O for a in line):
        return O 
    line = [board[0][2], board[1][1], board[2][0]]
    if all(a == X for a in line):
            return X
    if all(a == O for a in line):
        return O
    return None

def terminal(board):
    """
    Returns True if game is over, False otherwise.
    """
    all_filled = True
    for i in range(3):
        for j in range(3):
            if board[i][j] == EMPTY:
                all_filled = False
                break
    if winner(board) != None:
        return True
    return all_filled

def utility(board):
    """
    Returns 1 if X has won the game, -1 if O has won, 0 otherwise.
    """
    w = winner(board)
    if w == X:
        return 1
    if w == O:
        return -1
    return 0


def minimax(board):
    """
    Returns the optimal action for the current player on the board.
    """
    if player(board) == X:
        return minimax_X(board)[1]
    return minimax_O(board)[1]

def minimax_X(board):
    if terminal(board):
        return (utility(board), None)
    moves = actions(board)
    res = (-2, None)
    for move in moves:
        score = minimax_O(result(board, move))[0]
        if score > res[0]:
            res = (score, move)
    return res
    

def minimax_O(board):
    if terminal(board):
        return (utility(board), None)
    moves = actions(board)
    res = (2, None)
    for move in moves:
        score = minimax_X(result(board, move))[0]
        if score < res[0]:
            res = (score, move)
    return res
```