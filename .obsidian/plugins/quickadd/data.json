{"choices": [{"id": "2c58b4e2-645a-4fbc-9ca4-f1e2685b6ba9", "name": "Add TG task", "type": "Capture", "command": false, "appendLink": false, "captureTo": "TG/TG Tasks.md", "captureToActiveFile": false, "createFileIfItDoesntExist": {"enabled": false, "createWithTemplate": false, "template": ""}, "format": {"enabled": true, "format": "```js quickadd\nreturn await this.app.plugins.plugins['obsidian-tasks-plugin'].apiV1.createTaskLineModal() + '\\n';\n```"}, "insertAfter": {"enabled": true, "after": "## Tasks", "insertAtEnd": false, "considerSubsections": false, "createIfNotFound": true, "createIfNotFoundLocation": "top"}, "prepend": false, "task": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": false, "openFileInMode": "default"}, {"id": "da2c4182-93ba-4279-aa61-ec40f49e18d3", "name": "Add task", "type": "Capture", "command": false, "appendLink": false, "captureTo": "Notes/All tasks.md", "captureToActiveFile": false, "createFileIfItDoesntExist": {"enabled": false, "createWithTemplate": false, "template": ""}, "format": {"enabled": true, "format": "```js quickadd\nreturn await this.app.plugins.plugins['obsidian-tasks-plugin'].apiV1.createTaskLineModal() + '\\n';\n```"}, "insertAfter": {"enabled": true, "after": "## Tasks", "insertAtEnd": false, "considerSubsections": false, "createIfNotFound": true, "createIfNotFoundLocation": "top"}, "prepend": false, "task": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": false, "openFileInMode": "default"}, {"id": "6c9b1918-be40-48d9-987f-897811a027d6", "name": "Add guitar article", "type": "Capture", "command": false, "appendLink": false, "captureTo": "Guitar.md", "captureToActiveFile": false, "createFileIfItDoesntExist": {"enabled": false, "createWithTemplate": false, "template": ""}, "format": {"enabled": true, "format": "- {{value}}\n"}, "insertAfter": {"enabled": true, "after": "## Articles", "insertAtEnd": false, "considerSubsections": false, "createIfNotFound": false, "createIfNotFoundLocation": "top"}, "prepend": false, "task": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": false, "openFileInMode": "default"}, {"id": "4d503b76-f09a-44cb-ac01-6d804311a2f9", "name": "Add article", "type": "Capture", "command": false, "appendLink": false, "captureTo": "Tasks.md", "captureToActiveFile": false, "createFileIfItDoesntExist": {"enabled": false, "createWithTemplate": false, "template": ""}, "format": {"enabled": true, "format": "- [ ] {{value}}\n"}, "insertAfter": {"enabled": true, "after": "## Things I can read", "insertAtEnd": false, "considerSubsections": false, "createIfNotFound": true, "createIfNotFoundLocation": "top"}, "prepend": false, "task": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": false, "openFileInMode": "default"}, {"id": "7da03358-d1b9-4013-a7b5-cd93c6b3d9a2", "name": "Add News", "type": "Template", "command": false, "templatePath": "Template/News.md", "fileNameFormat": {"enabled": false, "format": ""}, "folder": {"enabled": true, "folders": [], "chooseWhenCreatingNote": false, "createInSameFolderAsActiveFile": false, "chooseFromSubfolders": false}, "appendLink": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": false, "openFileInMode": "default", "fileExistsMode": "Increment the file name", "setFileExistsBehavior": false}], "macros": [{"name": "task", "id": "1d185954-8e04-4734-882a-4c1567b7c9d0", "commands": [], "runOnStartup": false}], "inputPrompt": "single-line", "devMode": false, "templateFolderPath": "Template", "announceUpdates": true, "version": "1.11.5", "disableOnlineFeatures": true, "enableRibbonIcon": false, "ai": {"defaultModel": "Ask me", "defaultSystemPrompt": "As an AI assistant within Obsidian, your primary goal is to help users manage their ideas and knowledge more effectively. Format your responses using Markdown syntax. Please use the [[Obsidian]] link format. You can write aliases for the links by writing [[Obsidian|the alias after the pipe symbol]]. To use mathematical notation, use LaTeX syntax. LaTeX syntax for larger equations should be on separate lines, surrounded with double dollar signs ($$). You can also inline math expressions by wrapping it in $ symbols. For example, use $$w_{ij}^{\text{new}}:=w_{ij}^{\text{current}}+etacdotdelta_jcdot x_{ij}$$ on a separate line, but you can write \"($eta$ = learning rate, $delta_j$ = error term, $x_{ij}$ = input)\" inline.", "promptTemplatesFolderPath": "", "showAssistant": true, "providers": [{"name": "OpenAI", "endpoint": "https://api.openai.com/v1", "apiKey": "", "models": [{"name": "text-davinci-003", "maxTokens": 4096}, {"name": "gpt-3.5-turbo", "maxTokens": 4096}, {"name": "gpt-3.5-turbo-16k", "maxTokens": 16384}, {"name": "gpt-3.5-turbo-1106", "maxTokens": 16385}, {"name": "gpt-4", "maxTokens": 8192}, {"name": "gpt-4-32k", "maxTokens": 32768}, {"name": "gpt-4-1106-preview", "maxTokens": 128000}, {"name": "gpt-4-turbo", "maxTokens": 128000}, {"name": "gpt-4o", "maxTokens": 128000}, {"name": "gpt-4o-mini", "maxTokens": 128000}]}]}, "migrations": {"migrateToMacroIDFromEmbeddedMacro": true, "useQuickAddTemplateFolder": true, "incrementFileNameSettingMoveToDefaultBehavior": true, "mutualExclusionInsertAfterAndWriteToBottomOfFile": true, "setVersionAfterUpdateModalRelease": true, "addDefaultAIProviders": true}}