/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __markAsModule = (target) => __defProp(target, "__esModule", { value: true });
var __export = (target, all) => {
  __markAsModule(target);
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __reExport = (target, module2, desc) => {
  if (module2 && typeof module2 === "object" || typeof module2 === "function") {
    for (let key of __getOwnPropNames(module2))
      if (!__hasOwnProp.call(target, key) && key !== "default")
        __defProp(target, key, { get: () => module2[key], enumerable: !(desc = __getOwnPropDesc(module2, key)) || desc.enumerable });
  }
  return target;
};
var __toModule = (module2) => {
  return __reExport(__markAsModule(__defProp(module2 != null ? __create(__getProtoOf(module2)) : {}, "default", module2 && module2.__esModule && "default" in module2 ? { get: () => module2.default, enumerable: true } : { value: module2, enumerable: true })), module2);
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};

// main.ts
__export(exports, {
  default: () => MinimalTheme
});
var import_obsidian = __toModule(require("obsidian"));
var MinimalTheme = class extends import_obsidian.Plugin {
  onload() {
    return __async(this, null, function* () {
      yield this.loadSettings();
      this.addSettingTab(new MinimalSettingTab(this.app, this));
      this.loadRules();
      let settingsUpdate = () => {
        const fontSize = this.app.vault.getConfig("baseFontSize");
        this.settings.textNormal = fontSize;
        if (this.app.vault.getConfig("foldHeading")) {
          this.settings.folding = true;
          this.saveData(this.settings);
          console.log("Folding is on");
        } else {
          this.settings.folding = false;
          this.saveData(this.settings);
          console.log("Folding is off");
        }
        document.body.classList.toggle("minimal-folding", this.settings.folding);
        if (this.app.vault.getConfig("showLineNumber")) {
          this.settings.lineNumbers = true;
          this.saveData(this.settings);
          console.log("Line numbers are on");
        } else {
          this.settings.lineNumbers = false;
          this.saveData(this.settings);
          console.log("Line numbers are off");
        }
        document.body.classList.toggle("minimal-line-nums", this.settings.lineNumbers);
        if (this.app.vault.getConfig("readableLineLength")) {
          this.settings.readableLineLength = true;
          this.saveData(this.settings);
          console.log("Readable line length is on");
        } else {
          this.settings.readableLineLength = false;
          this.saveData(this.settings);
          console.log("Readable line length is off");
        }
        document.body.classList.toggle("minimal-readable", this.settings.readableLineLength);
        document.body.classList.toggle("minimal-readable-off", !this.settings.readableLineLength);
      };
      let sidebarUpdate = () => {
        const sidebarEl = document.getElementsByClassName("mod-left-split")[0];
        const ribbonEl = document.getElementsByClassName("side-dock-ribbon")[0];
        if (sidebarEl && ribbonEl && document.body.classList.contains("theme-light") && this.settings.lightStyle == "minimal-light-contrast") {
          sidebarEl.addClass("theme-dark");
          ribbonEl.addClass("theme-dark");
        } else if (sidebarEl && ribbonEl) {
          sidebarEl.removeClass("theme-dark");
          ribbonEl.removeClass("theme-dark");
        }
      };
      this.registerEvent(app.vault.on("config-changed", settingsUpdate));
      this.registerEvent(app.workspace.on("css-change", sidebarUpdate));
      settingsUpdate();
      app.workspace.onLayoutReady(() => {
        sidebarUpdate();
      });
      const lightStyles = ["minimal-light", "minimal-light-tonal", "minimal-light-contrast", "minimal-light-white"];
      const darkStyles = ["minimal-dark", "minimal-dark-tonal", "minimal-dark-black"];
      const imgGridStyles = ["img-grid", "img-grid-ratio", "img-nogrid"];
      const tableWidthStyles = ["table-100", "table-default-width", "table-wide", "table-max"];
      const iframeWidthStyles = ["iframe-100", "iframe-default-width", "iframe-wide", "iframe-max"];
      const imgWidthStyles = ["img-100", "img-default-width", "img-wide", "img-max"];
      const mapWidthStyles = ["map-100", "map-default-width", "map-wide", "map-max"];
      const chartWidthStyles = ["chart-100", "chart-default-width", "chart-wide", "chart-max"];
      this.addCommand({
        id: "increase-body-font-size",
        name: "Increase body font size",
        callback: () => {
          this.settings.textNormal = this.settings.textNormal + 0.5;
          this.saveData(this.settings);
          this.setFontSize();
        }
      });
      this.addCommand({
        id: "decrease-body-font-size",
        name: "Decrease body font size",
        callback: () => {
          this.settings.textNormal = this.settings.textNormal - 0.5;
          this.saveData(this.settings);
          this.setFontSize();
        }
      });
      this.addCommand({
        id: "toggle-minimal-dark-cycle",
        name: "Cycle between dark mode styles",
        callback: () => {
          this.settings.darkStyle = darkStyles[(darkStyles.indexOf(this.settings.darkStyle) + 1) % darkStyles.length];
          this.saveData(this.settings);
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-light-cycle",
        name: "Cycle between light mode styles",
        callback: () => {
          this.settings.lightStyle = lightStyles[(lightStyles.indexOf(this.settings.lightStyle) + 1) % lightStyles.length];
          this.saveData(this.settings);
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-hidden-borders",
        name: "Toggle sidebar borders",
        callback: () => {
          this.settings.bordersToggle = !this.settings.bordersToggle;
          this.saveData(this.settings);
          this.refresh();
        }
      });
      this.addCommand({
        id: "toggle-colorful-headings",
        name: "Toggle colorful headings",
        callback: () => {
          this.settings.colorfulHeadings = !this.settings.colorfulHeadings;
          this.saveData(this.settings);
          this.refresh();
        }
      });
      this.addCommand({
        id: "toggle-minimal-focus-mode",
        name: "Toggle focus mode",
        callback: () => {
          this.settings.focusMode = !this.settings.focusMode;
          this.saveData(this.settings);
          this.refresh();
        }
      });
      this.addCommand({
        id: "toggle-minimal-colorful-frame",
        name: "Toggle colorful window frame",
        callback: () => {
          this.settings.colorfulFrame = !this.settings.colorfulFrame;
          this.saveData(this.settings);
          this.refresh();
        }
      });
      this.addCommand({
        id: "cycle-minimal-table-width",
        name: "Cycle between table width options",
        callback: () => {
          this.settings.tableWidth = tableWidthStyles[(tableWidthStyles.indexOf(this.settings.tableWidth) + 1) % tableWidthStyles.length];
          this.saveData(this.settings);
          this.refresh();
        }
      });
      this.addCommand({
        id: "cycle-minimal-image-width",
        name: "Cycle between image width options",
        callback: () => {
          this.settings.imgWidth = imgWidthStyles[(imgWidthStyles.indexOf(this.settings.imgWidth) + 1) % imgWidthStyles.length];
          this.saveData(this.settings);
          this.refresh();
        }
      });
      this.addCommand({
        id: "cycle-minimal-iframe-width",
        name: "Cycle between iframe width options",
        callback: () => {
          this.settings.iframeWidth = iframeWidthStyles[(iframeWidthStyles.indexOf(this.settings.iframeWidth) + 1) % iframeWidthStyles.length];
          this.saveData(this.settings);
          this.refresh();
        }
      });
      this.addCommand({
        id: "cycle-minimal-chart-width",
        name: "Cycle between chart width options",
        callback: () => {
          this.settings.chartWidth = chartWidthStyles[(chartWidthStyles.indexOf(this.settings.chartWidth) + 1) % chartWidthStyles.length];
          this.saveData(this.settings);
          this.refresh();
        }
      });
      this.addCommand({
        id: "cycle-minimal-map-width",
        name: "Cycle between map width options",
        callback: () => {
          this.settings.mapWidth = mapWidthStyles[(mapWidthStyles.indexOf(this.settings.mapWidth) + 1) % mapWidthStyles.length];
          this.saveData(this.settings);
          this.refresh();
        }
      });
      this.addCommand({
        id: "toggle-minimal-img-grid",
        name: "Toggle image grids",
        callback: () => {
          this.settings.imgGrid = !this.settings.imgGrid;
          this.saveData(this.settings);
          this.refresh();
        }
      });
      this.addCommand({
        id: "toggle-minimal-switch",
        name: "Switch between light and dark mode",
        callback: () => {
          this.updateTheme();
        }
      });
      this.addCommand({
        id: "toggle-minimal-light-default",
        name: "Use light mode (default)",
        callback: () => {
          this.settings.lightStyle = "minimal-light";
          this.saveData(this.settings);
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-light-white",
        name: "Use light mode (all white)",
        callback: () => {
          this.settings.lightStyle = "minimal-light-white";
          this.saveData(this.settings);
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-light-tonal",
        name: "Use light mode (low contrast)",
        callback: () => {
          this.settings.lightStyle = "minimal-light-tonal";
          this.saveData(this.settings);
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-light-contrast",
        name: "Use light mode (high contrast)",
        callback: () => {
          this.settings.lightStyle = "minimal-light-contrast";
          this.saveData(this.settings);
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-dark-default",
        name: "Use dark mode (default)",
        callback: () => {
          this.settings.darkStyle = "minimal-dark";
          this.saveData(this.settings);
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-dark-tonal",
        name: "Use dark mode (low contrast)",
        callback: () => {
          this.settings.darkStyle = "minimal-dark-tonal";
          this.saveData(this.settings);
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-dark-black",
        name: "Use dark mode (true black)",
        callback: () => {
          this.settings.darkStyle = "minimal-dark-black";
          this.saveData(this.settings);
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-atom-light",
        name: "Switch light color scheme to Atom (light)",
        callback: () => {
          this.settings.lightScheme = "minimal-atom-light";
          this.saveData(this.settings);
          this.updateLightScheme();
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-ayu-light",
        name: "Switch light color scheme to Ayu (light)",
        callback: () => {
          this.settings.lightScheme = "minimal-ayu-light";
          this.saveData(this.settings);
          this.updateLightScheme();
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-catppuccin-light",
        name: "Switch light color scheme to Catppuccin (light)",
        callback: () => {
          this.settings.lightScheme = "minimal-catppuccin-light";
          this.saveData(this.settings);
          this.updateLightScheme();
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-default-light",
        name: "Switch light color scheme to default (light)",
        callback: () => {
          this.settings.lightScheme = "minimal-default-light";
          this.saveData(this.settings);
          this.updateLightScheme();
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-gruvbox-light",
        name: "Switch light color scheme to Gruvbox (light)",
        callback: () => {
          this.settings.lightScheme = "minimal-gruvbox-light";
          this.saveData(this.settings);
          this.updateLightScheme();
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-eink-light",
        name: "Switch light color scheme to E-ink (light)",
        callback: () => {
          this.settings.lightScheme = "minimal-eink-light";
          this.saveData(this.settings);
          this.updateLightScheme();
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-everforest-light",
        name: "Switch light color scheme to Everforest (light)",
        callback: () => {
          this.settings.lightScheme = "minimal-everforest-light";
          this.saveData(this.settings);
          this.updateLightScheme();
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-flexoki-light",
        name: "Switch light color scheme to Flexoki (light)",
        callback: () => {
          this.settings.lightScheme = "minimal-flexoki-light";
          this.saveData(this.settings);
          this.updateLightScheme();
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-macos-light",
        name: "Switch light color scheme to macOS (light)",
        callback: () => {
          this.settings.lightScheme = "minimal-macos-light";
          this.saveData(this.settings);
          this.updateLightScheme();
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-notion-light",
        name: "Switch light color scheme to Sky (light)",
        callback: () => {
          this.settings.lightScheme = "minimal-notion-light";
          this.saveData(this.settings);
          this.updateLightScheme();
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-nord-light",
        name: "Switch light color scheme to Nord (light)",
        callback: () => {
          this.settings.lightScheme = "minimal-nord-light";
          this.saveData(this.settings);
          this.updateLightScheme();
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-rose-pine-light",
        name: "Switch light color scheme to Ros\xE9 Pine (light)",
        callback: () => {
          this.settings.lightScheme = "minimal-rose-pine-light";
          this.saveData(this.settings);
          this.updateLightScheme();
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-solarized-light",
        name: "Switch light color scheme to Solarized (light)",
        callback: () => {
          this.settings.lightScheme = "minimal-solarized-light";
          this.saveData(this.settings);
          this.updateLightScheme();
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-things-light",
        name: "Switch light color scheme to Things (light)",
        callback: () => {
          this.settings.lightScheme = "minimal-things-light";
          this.saveData(this.settings);
          this.updateLightScheme();
          this.updateLightStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-atom-dark",
        name: "Switch dark color scheme to Atom (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-atom-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-ayu-dark",
        name: "Switch dark color scheme to Ayu (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-ayu-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-catppuccin-dark",
        name: "Switch dark color scheme to Catppuccin (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-catppuccin-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-dracula-dark",
        name: "Switch dark color scheme to Dracula (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-dracula-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-default-dark",
        name: "Switch dark color scheme to default (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-default-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-eink-dark",
        name: "Switch dark color scheme to E-ink (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-eink-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-everforest-dark",
        name: "Switch dark color scheme to Everforest (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-everforest-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-flexoki-dark",
        name: "Switch dark color scheme to Flexoki (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-flexoki-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-gruvbox-dark",
        name: "Switch dark color scheme to Gruvbox (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-gruvbox-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-macos-dark",
        name: "Switch dark color scheme to macOS (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-macos-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-nord-dark",
        name: "Switch dark color scheme to Nord (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-nord-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-notion-dark",
        name: "Switch dark color scheme to Sky (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-notion-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-rose-pine-dark",
        name: "Switch dark color scheme to Ros\xE9 Pine (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-rose-pine-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-solarized-dark",
        name: "Switch dark color scheme to Solarized (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-solarized-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-things-dark",
        name: "Switch dark color scheme to Things (dark)",
        callback: () => {
          this.settings.darkScheme = "minimal-things-dark";
          this.saveData(this.settings);
          this.updateDarkScheme();
          this.updateDarkStyle();
        }
      });
      this.addCommand({
        id: "toggle-minimal-dev-block-width",
        name: "Dev \u2014 Show block widths",
        callback: () => {
          this.settings.devBlockWidth = !this.settings.devBlockWidth;
          this.saveData(this.settings);
          this.refresh();
        }
      });
      this.refresh();
    });
  }
  onunload() {
    console.log("Unloading Minimal Theme Settings plugin");
    const sidebarEl = document.getElementsByClassName("mod-left-split")[0];
    const ribbonEl = document.getElementsByClassName("side-dock-ribbon")[0];
    sidebarEl.removeClass("theme-dark");
    ribbonEl.removeClass("theme-dark");
    this.unloadRules();
    this.removeStyle();
    this.removeSettings();
    this.removeLightScheme();
    this.removeDarkScheme();
  }
  loadSettings() {
    return __async(this, null, function* () {
      this.settings = Object.assign(DEFAULT_SETTINGS, yield this.loadData());
    });
  }
  saveSettings() {
    return __async(this, null, function* () {
      yield this.saveData(this.settings);
    });
  }
  refresh() {
    this.updateStyle();
  }
  loadRules() {
    const css = document.createElement("style");
    css.id = "minimal-theme";
    document.getElementsByTagName("head")[0].appendChild(css);
    document.body.classList.add("minimal-theme");
    this.updateStyle();
  }
  unloadRules() {
    const styleElement = document.getElementById("minimal-theme");
    if (styleElement) {
      styleElement.parentNode.removeChild(styleElement);
    }
    document.body.classList.remove("minimal-theme");
  }
  setFontSize() {
    this.app.vault.setConfig("baseFontSize", this.settings.textNormal);
    this.app.updateFontSize();
  }
  updateStyle() {
    this.removeStyle();
    this.removeSettings();
    document.body.addClass(this.settings.lightStyle, this.settings.lightScheme, this.settings.darkStyle, this.settings.darkScheme);
    document.body.classList.toggle("borders-none", !this.settings.bordersToggle);
    document.body.classList.toggle("colorful-headings", this.settings.colorfulHeadings);
    document.body.classList.toggle("colorful-frame", this.settings.colorfulFrame);
    document.body.classList.toggle("colorful-active", this.settings.colorfulActiveStates);
    document.body.classList.toggle("minimal-focus-mode", this.settings.focusMode);
    document.body.classList.toggle("links-int-on", this.settings.underlineInternal);
    document.body.classList.toggle("links-ext-on", this.settings.underlineExternal);
    document.body.classList.toggle("full-width-media", this.settings.fullWidthMedia);
    document.body.classList.toggle("img-grid", this.settings.imgGrid);
    document.body.classList.toggle("minimal-dev-block-width", this.settings.devBlockWidth);
    document.body.classList.toggle("minimal-status-off", !this.settings.minimalStatus);
    document.body.classList.toggle("full-file-names", !this.settings.trimNames);
    document.body.classList.toggle("labeled-nav", this.settings.labeledNav);
    document.body.classList.toggle("minimal-folding", this.settings.folding);
    document.body.addClass(this.settings.chartWidth, this.settings.tableWidth, this.settings.imgWidth, this.settings.iframeWidth, this.settings.mapWidth);
    const el = document.getElementById("minimal-theme");
    if (!el)
      throw "minimal-theme element not found!";
    else {
      el.innerText = "body.minimal-theme{--font-ui-small:" + this.settings.textSmall + "px;--line-height:" + this.settings.lineHeight + ";--line-width:" + this.settings.lineWidth + "rem;--line-width-wide:" + this.settings.lineWidthWide + "rem;--max-width:" + this.settings.maxWidth + "%;--font-editor-override:" + this.settings.editorFont + ";";
    }
  }
  updateDarkStyle() {
    document.body.removeClass("theme-light", "minimal-dark", "minimal-dark-tonal", "minimal-dark-black");
    document.body.addClass("theme-dark", this.settings.darkStyle);
    if (this.app.vault.getConfig("theme") !== "system") {
      this.app.setTheme("obsidian");
      this.app.vault.setConfig("theme", "obsidian");
    }
    this.app.workspace.trigger("css-change");
  }
  updateLightStyle() {
    document.body.removeClass("theme-dark", "minimal-light", "minimal-light-tonal", "minimal-light-contrast", "minimal-light-white");
    document.body.addClass("theme-light", this.settings.lightStyle);
    if (this.app.vault.getConfig("theme") !== "system") {
      this.app.setTheme("moonstone");
      this.app.vault.setConfig("theme", "moonstone");
    }
    this.app.workspace.trigger("css-change");
  }
  updateDarkScheme() {
    this.removeDarkScheme();
    document.body.addClass(this.settings.darkScheme);
  }
  updateLightScheme() {
    this.removeLightScheme();
    document.body.addClass(this.settings.lightScheme);
  }
  updateTheme() {
    if (this.app.vault.getConfig("theme") === "system") {
      if (document.body.classList.contains("theme-light")) {
        document.body.removeClass("theme-light");
        document.body.addClass("theme-dark");
      } else {
        document.body.removeClass("theme-dark");
        document.body.addClass("theme-light");
      }
    } else {
      if (document.body.classList.contains("theme-light")) {
        document.body.removeClass("theme-light");
        document.body.addClass("theme-dark");
      } else {
        document.body.removeClass("theme-dark");
        document.body.addClass("theme-light");
      }
      const currentTheme = this.app.vault.getConfig("theme");
      const newTheme = currentTheme === "moonstone" ? "obsidian" : "moonstone";
      this.app.setTheme(newTheme);
      this.app.vault.setConfig("theme", newTheme);
    }
    this.app.workspace.trigger("css-change");
  }
  removeSettings() {
    document.body.removeClass("borders-none", "colorful-headings", "colorful-frame", "colorful-active", "minimal-focus-mode", "links-int-on", "links-ext-on", "full-width-media", "img-grid", "minimal-dev-block-width", "minimal-status-off", "full-file-names", "labeled-nav", "minimal-folding");
    document.body.removeClass("table-wide", "table-max", "table-100", "table-default-width", "iframe-wide", "iframe-max", "iframe-100", "iframe-default-width", "img-wide", "img-max", "img-100", "img-default-width", "chart-wide", "chart-max", "chart-100", "chart-default-width", "map-wide", "map-max", "map-100", "map-default-width");
  }
  removeStyle() {
    document.body.removeClass("minimal-light", "minimal-light-tonal", "minimal-light-contrast", "minimal-light-white", "minimal-dark", "minimal-dark-tonal", "minimal-dark-black");
  }
  removeDarkScheme() {
    document.body.removeClass("minimal-atom-dark", "minimal-ayu-dark", "minimal-catppuccin-dark", "minimal-default-dark", "minimal-dracula-dark", "minimal-eink-dark", "minimal-everforest-dark", "minimal-flexoki-dark", "minimal-gruvbox-dark", "minimal-macos-dark", "minimal-nord-dark", "minimal-notion-dark", "minimal-rose-pine-dark", "minimal-solarized-dark", "minimal-things-dark");
  }
  removeLightScheme() {
    document.body.removeClass("minimal-atom-light", "minimal-ayu-light", "minimal-catppuccin-light", "minimal-default-light", "minimal-eink-light", "minimal-everforest-light", "minimal-flexoki-light", "minimal-gruvbox-light", "minimal-macos-light", "minimal-nord-light", "minimal-notion-light", "minimal-rose-pine-light", "minimal-solarized-light", "minimal-things-light");
  }
};
var DEFAULT_SETTINGS = {
  lightStyle: "minimal-light",
  darkStyle: "minimal-dark",
  lightScheme: "minimal-default-light",
  darkScheme: "minimal-default-dark",
  editorFont: "",
  lineHeight: 1.5,
  lineWidth: 40,
  lineWidthWide: 50,
  maxWidth: 88,
  textNormal: 16,
  textSmall: 13,
  imgGrid: false,
  imgWidth: "img-default-width",
  tableWidth: "table-default-width",
  iframeWidth: "iframe-default-width",
  mapWidth: "map-default-width",
  chartWidth: "chart-default-width",
  colorfulHeadings: false,
  colorfulFrame: false,
  colorfulActiveStates: false,
  trimNames: true,
  labeledNav: false,
  fullWidthMedia: true,
  bordersToggle: true,
  minimalStatus: true,
  focusMode: false,
  underlineInternal: true,
  underlineExternal: true,
  folding: true,
  lineNumbers: false,
  readableLineLength: false,
  devBlockWidth: false
};
var MinimalSettingTab = class extends import_obsidian.PluginSettingTab {
  constructor(app2, plugin) {
    super(app2, plugin);
    this.plugin = plugin;
  }
  display() {
    let { containerEl } = this;
    containerEl.empty();
    const colorSection = containerEl.createEl("div", { cls: "setting-item setting-item-heading" });
    const colorSectionInfo = colorSection.createEl("div", { cls: "setting-item-info" });
    colorSectionInfo.createEl("div", { text: "Color scheme", cls: "setting-item-name" });
    const colorDesc = colorSectionInfo.createEl("div", { cls: "setting-item-description" });
    colorDesc.appendChild(createEl("span", {
      text: "To create a custom color scheme use the "
    }));
    colorDesc.appendChild(createEl("a", {
      text: "Style Settings",
      href: "obsidian://show-plugin?id=obsidian-style-settings"
    }));
    colorDesc.appendText(" plugin. See ");
    colorDesc.appendChild(createEl("a", {
      text: "documentation",
      href: "https://minimal.guide/features/color-schemes"
    }));
    colorDesc.appendText(" for details.");
    new import_obsidian.Setting(containerEl).setName("Light mode color scheme").setDesc("Preset color options for light mode.").addDropdown((dropdown) => dropdown.addOption("minimal-default-light", "Default").addOption("minimal-atom-light", "Atom").addOption("minimal-ayu-light", "Ayu").addOption("minimal-catppuccin-light", "Catppuccin").addOption("minimal-eink-light", "E-ink (beta)").addOption("minimal-everforest-light", "Everforest").addOption("minimal-flexoki-light", "Flexoki").addOption("minimal-gruvbox-light", "Gruvbox").addOption("minimal-macos-light", "macOS").addOption("minimal-nord-light", "Nord").addOption("minimal-rose-pine-light", "Ros\xE9 Pine").addOption("minimal-notion-light", "Sky").addOption("minimal-solarized-light", "Solarized").addOption("minimal-things-light", "Things").setValue(this.plugin.settings.lightScheme).onChange((value) => {
      this.plugin.settings.lightScheme = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.updateLightScheme();
    }));
    new import_obsidian.Setting(containerEl).setName("Light mode background contrast").setDesc("Level of contrast between sidebar and main content.").addDropdown((dropdown) => dropdown.addOption("minimal-light", "Default").addOption("minimal-light-white", "All white").addOption("minimal-light-tonal", "Low contrast").addOption("minimal-light-contrast", "High contrast").setValue(this.plugin.settings.lightStyle).onChange((value) => {
      this.plugin.settings.lightStyle = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.updateLightStyle();
    }));
    new import_obsidian.Setting(containerEl).setName("Dark mode color scheme").setDesc("Preset colors options for dark mode.").addDropdown((dropdown) => dropdown.addOption("minimal-default-dark", "Default").addOption("minimal-atom-dark", "Atom").addOption("minimal-ayu-dark", "Ayu").addOption("minimal-catppuccin-dark", "Catppuccin").addOption("minimal-dracula-dark", "Dracula").addOption("minimal-eink-dark", "E-ink (beta)").addOption("minimal-everforest-dark", "Everforest").addOption("minimal-flexoki-dark", "Flexoki").addOption("minimal-gruvbox-dark", "Gruvbox").addOption("minimal-macos-dark", "macOS").addOption("minimal-nord-dark", "Nord").addOption("minimal-rose-pine-dark", "Ros\xE9 Pine").addOption("minimal-notion-dark", "Sky").addOption("minimal-solarized-dark", "Solarized").addOption("minimal-things-dark", "Things").setValue(this.plugin.settings.darkScheme).onChange((value) => {
      this.plugin.settings.darkScheme = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.updateDarkScheme();
    }));
    new import_obsidian.Setting(containerEl).setName("Dark mode background contrast").setDesc("Level of contrast between sidebar and main content.").addDropdown((dropdown) => dropdown.addOption("minimal-dark", "Default").addOption("minimal-dark-tonal", "Low contrast").addOption("minimal-dark-black", "True black").setValue(this.plugin.settings.darkStyle).onChange((value) => {
      this.plugin.settings.darkStyle = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.updateDarkStyle();
    }));
    containerEl.createEl("br");
    const featuresSection = containerEl.createEl("div", { cls: "setting-item setting-item-heading" });
    const featuresSectionInfo = featuresSection.createEl("div", { cls: "setting-item-info" });
    featuresSectionInfo.createEl("div", { text: "Features", cls: "setting-item-name" });
    const featuresSectionDesc = featuresSectionInfo.createEl("div", { cls: "setting-item-description" });
    featuresSectionDesc.appendChild(createEl("span", {
      text: "See "
    }));
    featuresSectionDesc.appendChild(createEl("a", {
      text: "documentation",
      href: "https://minimal.guide"
    }));
    featuresSectionDesc.appendText(" for details.");
    new import_obsidian.Setting(containerEl).setName("Text labels for primary navigation").setDesc("Navigation items in the left sidebar uses text labels.").addToggle((toggle) => toggle.setValue(this.plugin.settings.labeledNav).onChange((value) => {
      this.plugin.settings.labeledNav = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Colorful window frame").setDesc("The top area of the app uses your accent color.").addToggle((toggle) => toggle.setValue(this.plugin.settings.colorfulFrame).onChange((value) => {
      this.plugin.settings.colorfulFrame = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Colorful active states").setDesc("Active file and menu items use your accent color.").addToggle((toggle) => toggle.setValue(this.plugin.settings.colorfulActiveStates).onChange((value) => {
      this.plugin.settings.colorfulActiveStates = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Colorful headings").setDesc("Headings use a different color for each size.").addToggle((toggle) => toggle.setValue(this.plugin.settings.colorfulHeadings).onChange((value) => {
      this.plugin.settings.colorfulHeadings = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Minimal status bar").setDesc("Turn off to use full-width status bar.").addToggle((toggle) => toggle.setValue(this.plugin.settings.minimalStatus).onChange((value) => {
      this.plugin.settings.minimalStatus = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Trim file names in sidebars").setDesc("Use ellipses to fit file names on a single line.").addToggle((toggle) => toggle.setValue(this.plugin.settings.trimNames).onChange((value) => {
      this.plugin.settings.trimNames = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Workspace borders").setDesc("Display divider lines between workspace elements.").addToggle((toggle) => toggle.setValue(this.plugin.settings.bordersToggle).onChange((value) => {
      this.plugin.settings.bordersToggle = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Focus mode").setDesc("Hide tab bar and status bar, hover to display. Can be toggled via hotkey.").addToggle((toggle) => toggle.setValue(this.plugin.settings.focusMode).onChange((value) => {
      this.plugin.settings.focusMode = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Underline internal links").setDesc("Show underlines on internal links.").addToggle((toggle) => toggle.setValue(this.plugin.settings.underlineInternal).onChange((value) => {
      this.plugin.settings.underlineInternal = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Underline external links").setDesc("Show underlines on external links.").addToggle((toggle) => toggle.setValue(this.plugin.settings.underlineExternal).onChange((value) => {
      this.plugin.settings.underlineExternal = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Maximize media").setDesc("Images and videos fill the width of the line.").addToggle((toggle) => toggle.setValue(this.plugin.settings.fullWidthMedia).onChange((value) => {
      this.plugin.settings.fullWidthMedia = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    containerEl.createEl("br");
    const layoutSection = containerEl.createEl("div", { cls: "setting-item setting-item-heading" });
    const layoutSectionInfo = layoutSection.createEl("div", { cls: "setting-item-info" });
    layoutSectionInfo.createEl("div", { text: "Layout", cls: "setting-item-name" });
    const layoutSectionDesc = layoutSectionInfo.createEl("div", { cls: "setting-item-description" });
    layoutSectionDesc.appendChild(createEl("span", {
      text: "These options can also be defined on a per-file basis, see "
    }));
    layoutSectionDesc.appendChild(createEl("a", {
      text: "documentation",
      href: "https://minimal.guide/features/block-width"
    }));
    layoutSectionDesc.appendText(" for details.");
    new import_obsidian.Setting(containerEl).setName("Image grids").setDesc("Turn consecutive images into columns \u2014 to make a new row, add an extra line break between images.").addToggle((toggle) => toggle.setValue(this.plugin.settings.imgGrid).onChange((value) => {
      this.plugin.settings.imgGrid = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Chart width").setDesc("Default width for chart blocks.").addDropdown((dropdown) => dropdown.addOption("chart-default-width", "Default").addOption("chart-wide", "Wide line width").addOption("chart-max", "Maximum line width").addOption("chart-100", "100% pane width").setValue(this.plugin.settings.chartWidth).onChange((value) => {
      this.plugin.settings.chartWidth = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Iframe width").setDesc("Default width for iframe blocks.").addDropdown((dropdown) => dropdown.addOption("iframe-default-width", "Default").addOption("iframe-wide", "Wide line width").addOption("iframe-max", "Maximum line width").addOption("iframe-100", "100% pane width").setValue(this.plugin.settings.iframeWidth).onChange((value) => {
      this.plugin.settings.iframeWidth = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Image width").setDesc("Default width for image blocks.").addDropdown((dropdown) => dropdown.addOption("img-default-width", "Default").addOption("img-wide", "Wide line width").addOption("img-max", "Maximum line width").addOption("img-100", "100% pane width").setValue(this.plugin.settings.imgWidth).onChange((value) => {
      this.plugin.settings.imgWidth = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Map width").setDesc("Default width for map blocks.").addDropdown((dropdown) => dropdown.addOption("map-default-width", "Default").addOption("map-wide", "Wide line width").addOption("map-max", "Maximum line width").addOption("map-100", "100% pane width").setValue(this.plugin.settings.mapWidth).onChange((value) => {
      this.plugin.settings.mapWidth = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Table width").setDesc("Default width for table and Dataview blocks.").addDropdown((dropdown) => dropdown.addOption("table-default-width", "Default").addOption("table-wide", "Wide line width").addOption("table-max", "Maximum line width").addOption("table-100", "100% pane width").setValue(this.plugin.settings.tableWidth).onChange((value) => {
      this.plugin.settings.tableWidth = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    containerEl.createEl("br");
    containerEl.createEl("div", { text: "Typography", cls: "setting-item setting-item-heading" });
    new import_obsidian.Setting(containerEl).setName("Text font size").setDesc("Used for the main text (default 16).").addText((text) => text.setPlaceholder("16").setValue((this.plugin.settings.textNormal || "") + "").onChange((value) => {
      this.plugin.settings.textNormal = parseFloat(value);
      this.plugin.saveData(this.plugin.settings);
      this.plugin.setFontSize();
    }));
    new import_obsidian.Setting(containerEl).setName("Small font size").setDesc("Used for text in the sidebars and tabs (default 13).").addText((text) => text.setPlaceholder("13").setValue((this.plugin.settings.textSmall || "") + "").onChange((value) => {
      this.plugin.settings.textSmall = parseFloat(value);
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Line height").setDesc("Line height of text (default 1.5).").addText((text) => text.setPlaceholder("1.5").setValue((this.plugin.settings.lineHeight || "") + "").onChange((value) => {
      this.plugin.settings.lineHeight = parseFloat(value);
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Normal line width").setDesc("Number of characters per line (default 40).").addText((text) => text.setPlaceholder("40").setValue((this.plugin.settings.lineWidth || "") + "").onChange((value) => {
      this.plugin.settings.lineWidth = parseInt(value.trim());
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Wide line width").setDesc("Number of characters per line for wide elements (default 50).").addText((text) => text.setPlaceholder("50").setValue((this.plugin.settings.lineWidthWide || "") + "").onChange((value) => {
      this.plugin.settings.lineWidthWide = parseInt(value.trim());
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Maximum line width %").setDesc("Percentage of space inside a pane that a line can fill (default 88).").addText((text) => text.setPlaceholder("88").setValue((this.plugin.settings.maxWidth || "") + "").onChange((value) => {
      this.plugin.settings.maxWidth = parseInt(value.trim());
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
    new import_obsidian.Setting(containerEl).setName("Editor font").setDesc("Overrides the text font defined in Obsidian Appearance settings when in edit mode.").addText((text) => text.setPlaceholder("").setValue((this.plugin.settings.editorFont || "") + "").onChange((value) => {
      this.plugin.settings.editorFont = value;
      this.plugin.saveData(this.plugin.settings);
      this.plugin.refresh();
    }));
  }
};
//# sourceMappingURL=data:application/json;base64,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
