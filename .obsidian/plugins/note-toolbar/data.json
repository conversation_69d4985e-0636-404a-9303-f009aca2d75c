{"debugEnabled": false, "emptyViewToolbar": null, "export": {"includeIcons": true, "replaceVars": true, "useDataEls": true, "useIds": true}, "folderMappings": [{"folder": "*", "toolbar": "f12cfbf3-721f-4067-b252-e64fcb889018"}], "icon": "circle-ellipsis", "keepPropsState": false, "onboarding": {}, "ribbonAction": "toolbar", "scriptingEnabled": true, "showEditInFabMenu": true, "showLaunchpad": false, "showToolbarIn": {"audio": false, "bases": false, "canvas": false, "image": false, "kanban": false, "pdf": false, "video": false}, "showToolbarInFileMenu": false, "showToolbarInOther": "", "toolbarProp": "notetoolbar", "toolbars": [{"uuid": "f12cfbf3-721f-4067-b252-e64fcb889018", "customClasses": "", "defaultItem": null, "defaultStyles": ["border", "even", "sticky"], "hasCommand": false, "items": [{"uuid": "4dec25a7-a99a-430b-9a25-ca11f0953e52", "hasCommand": false, "icon": "table-properties", "inGallery": false, "label": "", "link": "", "linkAttr": {"commandCheck": false, "commandId": "note-toolbar:toggle-properties", "hasVars": false, "target": "", "type": "command"}, "plugin": "", "tooltip": "Toggle Properties", "visibility": {"desktop": {"allViews": {"components": ["icon", "label"]}}, "mobile": {"allViews": {"components": ["icon", "label"]}}, "tablet": {"allViews": {"components": ["icon", "label"]}}}}, {"uuid": "cd155ddc-4a23-4782-b78f-ab25ac678a0e", "hasCommand": false, "icon": "focus", "inGallery": false, "label": "", "link": "", "linkAttr": {"commandCheck": false, "commandId": "", "hasVars": false, "target": "", "type": "javascript"}, "plugin": "", "scriptConfig": {"expression": "const h = activeDocument.querySelector('.view-header'), s = activeDocument.querySelector('.status-bar');\nif (h && s) {\n if (h.style.display === 'none') {\n h.style.display = '';\n s.style.display = '';\n app.workspace.leftSplit?.expand();\n // app.workspace.rightSplit?.expand();\n await app.commands.executeCommandById('note-toolbar:show-properties');\n }\n else {\n h.style.display = 'none';\n s.style.display = 'none';\n app.workspace.leftSplit?.collapse();\n // app.workspace.rightSplit?.collapse();\n await app.commands.executeCommandById('note-toolbar:hide-properties');\n }\n }", "pluginFunction": "evaluate"}, "tooltip": "Toggle UI", "visibility": {"desktop": {"allViews": {"components": ["icon", "label"]}}, "mobile": {"allViews": {"components": ["icon", "label"]}}, "tablet": {"allViews": {"components": ["icon", "label"]}}}}, {"uuid": "067ae5ac-d0de-423f-a09f-f807e5f1af62", "hasCommand": false, "icon": "venetian-mask", "inGallery": false, "label": "", "link": "", "linkAttr": {"commandCheck": false, "commandId": "", "hasVars": false, "target": "", "type": "javascript"}, "plugin": "", "scriptConfig": {"expression": "const i = ntb.getActiveItem();\nconst t = [...Object.values(app.customCss.themes), { name: '' }];\nconst n = t[(t.findIndex(theme => theme.name === app.vault.getConfig('cssTheme')) + 1) % t.length].name;\napp.customCss.setTheme(n);\nif (n) new Notice(n);", "pluginFunction": "evaluate"}, "tooltip": "Cycle themes", "visibility": {"desktop": {"allViews": {"components": ["icon", "label"]}}, "mobile": {"allViews": {"components": ["icon", "label"]}}, "tablet": {"allViews": {"components": ["icon", "label"]}}}}, {"uuid": "8f93789c-e5cc-49aa-8154-454b413067bb", "hasCommand": false, "icon": "bookmark", "inGallery": false, "label": "", "link": "", "linkAttr": {"commandCheck": false, "commandId": "bookmarks:open", "hasVars": false, "target": "", "type": "command"}, "plugin": "", "tooltip": "Show bookmarks", "visibility": {"desktop": {"allViews": {"components": ["icon", "label"]}}, "mobile": {"allViews": {"components": ["icon", "label"]}}, "tablet": {"allViews": {"components": ["icon", "label"]}}}}, {"uuid": "7c1e3714-493f-46a6-b794-f1fb8381a4f9", "hasCommand": false, "icon": "", "inGallery": false, "label": "", "link": "", "linkAttr": {"commandCheck": false, "commandId": "", "hasVars": false, "type": "separator"}, "tooltip": "", "visibility": {"desktop": {"allViews": {"components": ["icon", "label"]}}, "mobile": {"allViews": {"components": ["icon", "label"]}}, "tablet": {"allViews": {"components": ["icon", "label"]}}}}, {"uuid": "470b540c-88fc-45a0-a0f1-331a9a314f8f", "hasCommand": false, "icon": "square-arrow-left", "inGallery": false, "label": "", "link": "", "linkAttr": {"commandCheck": false, "commandId": "", "hasVars": false, "target": "", "type": "javascript"}, "plugin": "", "scriptConfig": {"expression": "const cf = app.workspace.getActiveFile(); if (!cf) return; const cfp = app.vault.getAbstractFileByPath(cf.parent.path), cfl = cfp.children.filter(f=>f.basename), sfl = cfl.sort((a,b)=>a.basename.localeCompare(b.basename,undefined,{numeric:true,sensitivity:'base'})).map(f=>f.path),ci=sfl.findIndex(e=>e===cf.path);\nif (sfl.length>1) { const pf = ci===0?sfl[sfl.length-1]:sfl[ci-1]; app.workspace.activeLeaf.openFile(app.vault.getAbstractFileByPath(pf)); }", "pluginFunction": "evaluate"}, "tooltip": "Previous file", "visibility": {"desktop": {"allViews": {"components": ["icon", "label"]}}, "mobile": {"allViews": {"components": ["icon", "label"]}}, "tablet": {"allViews": {"components": ["icon", "label"]}}}}, {"uuid": "b1d51e35-19fa-41b5-8458-195d9e9c7481", "hasCommand": false, "icon": "square-arrow-right", "inGallery": false, "label": "", "link": "", "linkAttr": {"commandCheck": false, "commandId": "", "hasVars": false, "target": "", "type": "javascript"}, "plugin": "", "scriptConfig": {"expression": "const cf = app.workspace.getActiveFile(); if (!cf) return; const cfp = app.vault.getAbstractFileByPath(cf.parent.path), cfl = cfp.children.filter(f=>f.basename), sfl = cfl.sort((a,b)=>a.basename.localeCompare(b.basename,undefined,{numeric:true,sensitivity:'base'})).map(f=>f.path),ci=sfl.findIndex(e=>e==cf.path);\nif (sfl.length>1) { let nf = ci==sfl.length-1?sfl[0]:sfl[ci+1]; app.workspace.activeLeaf.openFile(app.vault.getAbstractFileByPath(nf)); }", "pluginFunction": "evaluate"}, "tooltip": "Next file", "visibility": {"desktop": {"allViews": {"components": ["icon", "label"]}}, "mobile": {"allViews": {"components": ["icon", "label"]}}, "tablet": {"allViews": {"components": ["icon", "label"]}}}}, {"uuid": "d92568bf-6bd0-4566-a128-ce8a196eeb3b", "hasCommand": false, "icon": "clipboard-check", "inGallery": false, "label": "", "link": "", "linkAttr": {"commandCheck": false, "commandId": "", "hasVars": false, "target": "", "type": "javascript"}, "plugin": "", "scriptConfig": {"expression": "const e = app.workspace.activeLeaf.view?.editor;\nif (!e) return;\nconst c = e.getValue();\nconst b = c.startsWith('---\\n') ? c.replace(/^---\\n[\\s\\S]*?\\n---\\n*/, '') : c;\nawait navigator.clipboard.writeText(b);\nnew Notice(ntb.t('api.msg.clipboard-copied'));", "pluginFunction": "evaluate"}, "tooltip": "Copy note content", "visibility": {"desktop": {"allViews": {"components": ["icon", "label"]}}, "mobile": {"allViews": {"components": ["icon", "label"]}}, "tablet": {"allViews": {"components": ["icon", "label"]}}}}, {"uuid": "51a92a57-f762-479f-b398-7c7815fdd788", "hasCommand": false, "icon": "folder-input", "inGallery": false, "label": "", "link": "", "linkAttr": {"commandCheck": false, "commandId": "file-explorer:move-file", "hasVars": false, "target": "", "type": "command"}, "plugin": "", "tooltip": "Move file to folder", "visibility": {"desktop": {"allViews": {"components": ["icon", "label"]}}, "mobile": {"allViews": {"components": ["icon", "label"]}}, "tablet": {"allViews": {"components": ["icon", "label"]}}}}, {"uuid": "b91726bd-a734-495f-a36e-e790b998d682", "hasCommand": false, "icon": "folder-tree", "inGallery": false, "label": "", "link": "", "linkAttr": {"commandCheck": false, "commandId": "file-explorer:reveal-active-file", "hasVars": false, "target": "", "type": "command"}, "plugin": "", "tooltip": "Show file in navigation", "visibility": {"desktop": {"allViews": {"components": ["icon", "label"]}}, "mobile": {"allViews": {"components": ["icon", "label"]}}, "tablet": {"allViews": {"components": ["icon", "label"]}}}}, {"uuid": "cfb4363c-2172-4e4c-b487-2043429fc831", "hasCommand": false, "icon": "", "inGallery": false, "label": "", "link": "", "linkAttr": {"commandCheck": false, "commandId": "", "hasVars": false, "type": "separator"}, "tooltip": "", "visibility": {"desktop": {"allViews": {"components": ["icon", "label"]}}, "mobile": {"allViews": {"components": ["icon", "label"]}}, "tablet": {"allViews": {"components": ["icon", "label"]}}}}, {"uuid": "a9170e3f-8f82-4d7e-ace7-80627a90d3b5", "hasCommand": false, "icon": "check-square-2", "inGallery": false, "label": "", "link": "", "linkAttr": {"commandCheck": false, "commandId": "obsidian-tasks-plugin:edit-task", "hasVars": false, "target": "", "type": "command"}, "plugin": "", "tooltip": "Create/Edit task", "visibility": {"desktop": {"allViews": {"components": ["icon", "label"]}}, "mobile": {"allViews": {"components": ["icon", "label"]}}, "tablet": {"allViews": {"components": ["icon", "label"]}}}}, {"uuid": "5ee0f815-65a6-4a4b-b7ee-d39b3b30544c", "hasCommand": false, "icon": "code-square", "inGallery": false, "label": "", "link": "", "linkAttr": {"commandCheck": true, "commandId": "editor:insert-codeblock", "focus": "editor", "hasVars": false, "target": "", "type": "command"}, "plugin": "", "tooltip": "Insert code block", "visibility": {"desktop": {"allViews": {"components": ["icon", "label"]}}, "mobile": {"allViews": {"components": ["icon", "label"]}}, "tablet": {"allViews": {"components": ["icon", "label"]}}}}, {"uuid": "62e80b83-9ad0-4b59-bb09-d9c3042a72fa", "hasCommand": false, "icon": "quote", "inGallery": false, "label": "", "link": "", "linkAttr": {"commandCheck": true, "commandId": "editor:insert-callout", "focus": "editor", "hasVars": false, "target": "", "type": "command"}, "plugin": "", "tooltip": "Insert callout", "visibility": {"desktop": {"allViews": {"components": ["icon", "label"]}}, "mobile": {"allViews": {"components": ["icon", "label"]}}, "tablet": {"allViews": {"components": ["icon", "label"]}}}}, {"uuid": "b34bb138-1862-4e75-89c3-493f7facdcab", "hasCommand": false, "icon": "line-horizontal", "inGallery": false, "label": "", "link": "", "linkAttr": {"commandCheck": true, "commandId": "editor:insert-horizontal-rule", "focus": "editor", "hasVars": false, "target": "", "type": "command"}, "plugin": "", "tooltip": "Insert horizontal rule", "visibility": {"desktop": {"allViews": {"components": ["icon", "label"]}}, "mobile": {"allViews": {"components": ["icon", "label"]}}, "tablet": {"allViews": {"components": ["icon", "label"]}}}}, {"uuid": "4344b7ce-cc5e-4c8d-8020-da379c2dd2cf", "hasCommand": false, "icon": "brackets", "inGallery": false, "label": "", "link": "", "linkAttr": {"commandCheck": true, "commandId": "editor:insert-wikilink", "focus": "editor", "hasVars": false, "target": "", "type": "command"}, "plugin": "", "tooltip": "Add internal link", "visibility": {"desktop": {"allViews": {"components": ["icon", "label"]}}, "mobile": {"allViews": {"components": ["icon", "label"]}}, "tablet": {"allViews": {"components": ["icon", "label"]}}}}, {"uuid": "876b0ae0-b8a8-49ec-9fab-4069aa2977df", "hasCommand": false, "icon": "", "inGallery": false, "label": "", "link": "", "linkAttr": {"commandCheck": false, "commandId": "", "hasVars": false, "type": "separator"}, "tooltip": "", "visibility": {"desktop": {"allViews": {"components": ["icon", "label"]}}, "mobile": {"allViews": {"components": ["icon", "label"]}}, "tablet": {"allViews": {"components": ["icon", "label"]}}}}, {"uuid": "1176360e-571b-4dbc-bac3-1199111cd395", "hasCommand": false, "icon": "percent-square", "inGallery": false, "label": "", "link": "", "linkAttr": {"commandCheck": false, "commandId": "templater-obsidian:create-new-note-from-template", "hasVars": false, "target": "", "type": "command"}, "plugin": "", "tooltip": "New from template", "visibility": {"desktop": {"allViews": {"components": ["icon", "label"]}}, "mobile": {"allViews": {"components": ["icon", "label"]}}, "tablet": {"allViews": {"components": ["icon", "label"]}}}}, {"uuid": "6fa6be85-154f-4ce3-95aa-ab6bcb1e8c22", "hasCommand": false, "icon": "templater-icon", "inGallery": false, "label": "", "link": "", "linkAttr": {"commandCheck": false, "commandId": "templater-obsidian:insert-templater", "hasVars": false, "target": "", "type": "command"}, "plugin": "", "tooltip": "Insert template", "visibility": {"desktop": {"allViews": {"components": ["icon", "label"]}}, "mobile": {"allViews": {"components": ["icon", "label"]}}, "tablet": {"allViews": {"components": ["icon", "label"]}}}}, {"uuid": "5b1227c0-14b0-482c-96fd-9c459f74742f", "hasCommand": false, "icon": "settings-2", "inGallery": false, "label": "", "link": "", "linkAttr": {"commandCheck": false, "commandId": "note-toolbar:open-toolbar-settings", "hasVars": false, "target": "", "type": "command"}, "plugin": "", "tooltip": "Toolbar settings", "visibility": {"desktop": {"allViews": {"components": ["icon", "label"]}}, "mobile": {"allViews": {"components": ["icon", "label"]}}, "tablet": {"allViews": {"components": ["icon", "label"]}}}}, {"uuid": "0952c55a-e51d-4274-8a13-c3262d013f50", "hasCommand": false, "icon": "file-stack", "inGallery": false, "label": "", "link": "", "linkAttr": {"commandCheck": false, "commandId": "", "hasVars": false, "target": "", "type": "javascript"}, "plugin": "", "scriptConfig": {"expression": "const src = '.obsidian/plugins/note-toolbar/data.json';\nconst date = new Date().toISOString().slice(0, 10).replace(/-/g, '');\nconst dest = `note-toolbar-${date}-data.json`;\napp.vault.adapter.read(src).then(data => { app.vault.adapter.write(dest, data); new Notice(ntb.t('api.msg.file-created', {filename: dest})); });", "pluginFunction": "evaluate"}, "tooltip": "Backup Note <PERSON><PERSON><PERSON> config", "visibility": {"desktop": {"allViews": {"components": ["icon", "label"]}}, "mobile": {"allViews": {"components": ["icon", "label"]}}, "tablet": {"allViews": {"components": ["icon", "label"]}}}}], "mobileStyles": [], "name": "Default toolbar", "position": {"desktop": {"allViews": {"position": "props"}}, "mobile": {"allViews": {"position": "props"}}, "tablet": {"allViews": {"position": "props"}}}, "updated": "2025-07-05T07:38:53.175Z"}], "version": 20250313.1, "whatsnew_version": "1.24"}