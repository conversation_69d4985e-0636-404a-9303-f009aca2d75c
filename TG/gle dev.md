### Ref

- [dev workflow](https://graphsql.atlassian.net/wiki/spaces/GLTKB/pages/2096496643/Workflow)
- [use vscode](https://graphsql.atlassian.net/wiki/spaces/GLTKB/pages/1357611054/VS+Code+Extensions+for+GLE)
- [testing](https://graphsql.atlassian.net/wiki/spaces/GLTKB/pages/2586837011/Testing)
- [work with engine/loader(C++)](https://graphsql.atlassian.net/wiki/spaces/GLTKB/pages/2110292282/Work+with+Engine+Loader+C)

### [Setup](https://graphsql.atlassian.net/wiki/spaces/GLTKB/pages/2580316277/Getting+started#IDE)

```bash
./tools/gle_dev_env_setup.sh
source ~/.profile
```

vscode config:

```json
{
    "java.configuration.runtimes": [
        {
            "name": "JavaSE-17",
            "path": "/home/<USER>/.glocal/jdk-17.0.12+7",
            "default": true
        },
    ],
    "java.jdt.ls.java.home": "/home/<USER>/.glocal/jdk-17.0.12+7",
    "java.import.gradle.java.home": "/home/<USER>/.glocal/jdk-17.0.12+7"
}
```

### Build

```bash
# running in the container to apply your changes directly
gle_compile

# running in the host machine to apply your changes to a remote host, e.g. tiger1
gle_compile -PremoteHostname=tiger1

# update gle jars to tigergraph
./gradlew -Psubmodule=true -Pobfus=false :assemble :updatePkgJars :gsql-server:enableDebug
```

### [Debug](https://graphsql.atlassian.net/wiki/spaces/GLTKB/pages/2067365977/Debug+GSQL)

### Tests

- run unit tests
    
    ```go
    gle_test -f class_name
    ```
    
- run regress tests
    
    ```go
    gtest shell.sh regress_number
    ```