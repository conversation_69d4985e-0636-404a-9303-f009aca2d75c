## Tasks
- [ ] demo video
## Script
- We have added a new tab called 'queries' in the GSQL Editor. (切换到tab) When users switch to this tab, the file list area will display all the queries in the current workspace. (选择一个installed query) By selecting a query on the left, users can perform some operations on it, such as edit, install, and delete. This section shows the RESTPP endpoint generated after the query is installed. <u>By clicking the code snippets button, users can copy the code here to call the query programmatically. </u>Currently, we support three languages: curl, JavaScript, and Python.
- If you click edit, we will automatically create a file containing the query content and switch back to the file tab (edit一个query). If we run this file, the query will be recreated. (点击query tab) <u>To run the query, we can first click this question mark to view the query's parameter types, and then fill in the query's payload in this JSON editor.</u>
- If we click the run query button (点击run query), since the query is not installed, it will run in interpreted mode, and the results will appear in the panel below, similar to running a file. Since result is of graph type, we will visualize the graph by default. Clicking other operation buttons, such as install, delete, will <u>execute</u> the corresponding GSQL commands on the backend. We can also install all queries by one click of the install all button.
- That's the demo of custom queries. have any questions or comments?
## Notes
- insights_shortest_path: **********
- prepare before demo: workspace, solution Mule Account Detection