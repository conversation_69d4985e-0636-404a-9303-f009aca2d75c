#habit
## Goal
make outputs from input we consume
## Process
1. capture notes: fleeting notes and literature notes
	- folders are unnecessary
	- don't need to consider the formats，以及是否原子
2. process notes
	1. make the note atomic
		- 进行扩充，包括each evidence, question(also a note)
	2. make it search friendly
		- add tags
			- `question`
			- different status
3. connect notes
	- 尝试找到相关的ideas
	- 重点是以这种方式思考

- 笔记的类型
	- 学习笔记
		- 最终可以分解成多个原子笔记，通过问题、观点进行关联
		- 主题 ~ 子主题 ~ 问题 ~ 结论/证据
	- 日常笔记
		- 类型
			- 日志、总结
			- 收藏
			- 记录：书籍、电影、podcast、工作
			- Projects
		- 关联到home
	- 笔记的tag
		- `build`: 需要进一步学习、构建的
		- `building`: 正在学习的
		- `process`: 要处理的
		- `habit`: 正在构建的习惯
## Notes
- 模板很重要
## Tasks
- [x] project template
- [ ] quick add碎片知识