## Scripts
- intro: 今天我将向大家demo一个用deepseek v3模型来生成gsql query的poc。在这个poc中，我们给GSQL editor增加了一个按钮。点击这个按钮后，用户可以在输入框中输入他们想生成query的描述，点击提交后，应用将会把当前的graph schema和对query的描述发送给大模型，要求大模型返回一个gsql query。
- 为了节省等待install query的时间，我准备了两个demo的视频。我将播放视频来为大家演示上述的功能。
- 在视频中，我使用的graph schema是这样的，它包含city、account、phone三种节点类型，并且有另外三种边的类型。
- 让我们试试这一样一个query: "Given an account name, find all the accounts that have transferred money to this account and record the total amount of transfers for each account"。
- 这会花费大约10s的时间。AI返回query后，我们会调用gsql的接口来对query进行code check。如果有任何错误，它将显示在屏幕上。我们也提供了使用AI来自动修复错误的功能。因为这个query是符合正确的gsql语法的，所以没有出现任何错误提示。
- 我们看到query成功地安装并执行了，返回的输出结果为空是因为调用时传入的account name是不存在的。
- 我们来看看发送给大模型的prompt是如何构造的。它基本上包含三个部分，第一个部分是graph的schema，然后是我们期望ai返回的格式，最后是一些query的例子。由于时间的关系，我只是从官方的transaction fraud solution中随意选取了一些query。在初始的版本中，我没有在prompt中包含这些示例，发现即便是对于我刚才演示的这样一个简单的query，ai生成的结果也会包含语法错误。在加上这些例子后，结果变好了一些。
- 我们再来看看第二个demo视频，这次我们使用这样的描述："For each blocked account, find its 1-hop-neighbor account who has not been blocked. The find and print out all the blocked mobile phones these unblocked accounts own."
- 可以看到AI使用了三个select语句来实现这个功能。我们可以将其写得更简练，不过从功能上AI生成的query是正确的。另外一个小问题是，AI选择使用一个Set accumulator来放置结果并最终输出它。如果能直接输出最后一个select语句的结果，会是更好的做法。
- AI生成query的已知问题：
	- AI好像不知道“Select ...into.."这个语法特性
	- 不一定能生成符合语法的gsql query，如使用`Target`关键字作为变量名，在POST-ACCUM clause中使用`print`关键字...
	- 生成的内容不够简练，如使用过多的select语句，声明不必要的变量...

## English version

Today, I’m going to demo a POC that uses the DeepSeek V3 model to generate GSQL queries. In this POC, we added a button to the GSQL editor. When the user clicks this button, they can input a description of the query they want to generate. After submitting, the application sends the current graph schema along with the query description to the large language model, asking it to return a GSQL query.

- To save the time required for installing queries, I’ve prepared two demo videos. I’ll play these videos to demonstrate the above functionalities.
    
- In the video, the graph schema includes three types of vertices: city, account, and phone, along with three types of edges.
    
- I input an description here, “Given an account name, find all the accounts that have transferred money to this account and record the total amount of transfers to each account.”
    
- This takes about 10 seconds. After the AI returns the query, we call the GSQL API to perform a code check. If there are any errors, they will be displayed on the screen. We also offer a feature to let the AI automatically fix the errors. In this case, since the query follows correct GSQL syntax, no error was shown.
    
- We can see that the query was successfully installed and executed. The output result is empty because the account name passed in during execution does not exist.
    
- Now let’s look at the second demo video. This time, the input description is: “For each blocked account, find its 1-hop-neighbor account who has not been blocked. Then find and print out all the blocked mobile phones these unblocked accounts own.”
    
- We can see that the AI used three `SELECT` statements to finish this task. While the query could be written more concisely, the functionality is correct. One minor issue is that the AI used a `Set` accumulator to store the results. It would be better to directly output the result of the last `SELECT` statement.
    
- I also want to share some known issues with AI-generated queries I found recently:
    
    - The AI doesn’t seem to know about the “SELECT ... INTO ...” syntax structure.
        
    - It may not always generate syntactically correct GSQL queries. I encountered some cases such as using `Target`(which is a keyword) as a variable name or using the `print` keyword in a POST-ACCUM clause.
        
    - The generated queries are not always concise — for instance, using too many `SELECT` statements or declaring unnecessary variables.
        
## Example1
```
Given an account name, find all the accounts that have transferred money to this account and record the total amount of transfers for each account
```
- AI
```sql
USE GRAPH financialGraph
CREATE OR REPLACE DISTRIBUTED QUERY find_transfer_in_accounts(STRING account_name) FOR GRAPH financialGraph {
  SumAccum<UINT> @total_amount;
  
  Start = {Account.*};
   
  Result = SELECT t FROM Start:s -(transfer:e)-> Account:t
           WHERE t.name == account_name
           ACCUM s.@total_amount += e.amount;
   
  PRINT Result[Result.name, Result.@total_amount AS total_transferred_amount];
}
INSTALL QUERY find_transfer_in_accounts
RUN QUERY find_transfer_in_accounts("YourAccountName")

```
## Example 2
- prompt: For each blocked account, find its 1-hop-neighbor account who has not been blocked. The find and print out all the blocked mobile phones these unblocked accounts own.
- Answer
```sql
USE GRAPH financialGraph

CREATE OR REPLACE DISTRIBUTED QUERY q10() SYNTAX V3 {

 SumAccum<int> @cnt = 0;

 //for each blocked account, find its 1-hop-neighbor who has not been blocked.
 tgtAccnts = SELECT y
             FROM (x:Account)- [e:transfer] -> (y:Account)
             WHERE x.isBlocked == TRUE AND y.isBlocked == FALSE
             ACCUM y.@cnt +=1;


  PRINT tgtAccnts;
}

```
- AI generated
```sql
USE GRAPH financialGraph
CREATE OR REPLACE DISTRIBUTED QUERY find_unblocked_neighbors_of_blocked_accounts() FOR GRAPH financialGraph {
  SetAccum<VERTEX<Account>> @@blocked_accounts;
  SetAccum<VERTEX<Account>> @@unblocked_neighbors;
  
  // First find all blocked accounts
  blocked = SELECT a FROM Account:a WHERE a.isBlocked == TRUE
            ACCUM @@blocked_accounts += a;
   
  // Then find their unblocked neighbors through transfer edges
  unblocked_neighbors = SELECT n FROM blocked:a - ((transfer|transfer_reverse):e) - Account:n
                       WHERE n.isBlocked == FALSE
                       ACCUM @@unblocked_neighbors += n;
  
  PRINT @@blocked_accounts AS BlockedAccounts;
  PRINT @@unblocked_neighbors AS UnblockedNeighbors;
}
INSTALL QUERY find_unblocked_neighbors_of_blocked_accounts
RUN QUERY find_unblocked_neighbors_of_blocked_accounts()
```