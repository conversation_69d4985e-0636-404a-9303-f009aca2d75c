---
title: What are LLMs? - Hugging Face Agents Course
source: https://huggingface.co/learn/agents-course/unit1/tools
author: 
published: 
created: 2025-03-27
description: We’re on a journey to advance and democratize artificial intelligence through open source and open science.
tags:
  - clippings
  - huggingface-ai-agent
---
Partial conversion completed with errors. Original HTML:

<div><h1 class="relative group" data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><a id="what-are-tools" class="header-link block pr-1.5 text-lg no-hover:hidden with-hover:absolute with-hover:p-1.5 with-hover:opacity-0 with-hover:group-hover:opacity-100 with-hover:right-full" href="https://huggingface.co/learn/agents-course/unit1/#what-are-tools" data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><span data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><svg class="" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" preserveAspectRatio="xMidYMid meet" viewBox="0 0 256 256"><path d="M167.594 88.393a8.001 8.001 0 0 1 0 11.314l-67.882 67.882a8 8 0 1 1-11.314-11.315l67.882-67.881a8.003 8.003 0 0 1 11.314 0zm-28.287 84.86l-28.284 28.284a40 40 0 0 1-56.567-56.567l28.284-28.284a8 8 0 0 0-11.315-11.315l-28.284 28.284a56 56 0 0 0 79.196 79.197l28.285-28.285a8 8 0 1 0-11.315-11.314zM212.852 43.14a56.002 56.002 0 0 0-79.196 0l-28.284 28.284a8 8 0 1 0 11.314 11.314l28.284-28.284a40 40 0 0 1 56.568 56.567l-28.285 28.285a8 8 0 0 0 11.315 11.314l28.284-28.284a56.065 56.065 0 0 0 0-79.196z" fill="currentColor"></path></svg></span></a> <span data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f">What are Tools?</span></h1></div><div><h2 class="relative group" data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><a id="what-are-ai-tools" class="header-link block pr-1.5 text-lg no-hover:hidden with-hover:absolute with-hover:p-1.5 with-hover:opacity-0 with-hover:group-hover:opacity-100 with-hover:right-full" href="https://huggingface.co/learn/agents-course/unit1/#what-are-ai-tools" data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><span data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><svg class="" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" preserveAspectRatio="xMidYMid meet" viewBox="0 0 256 256"><path d="M167.594 88.393a8.001 8.001 0 0 1 0 11.314l-67.882 67.882a8 8 0 1 1-11.314-11.315l67.882-67.881a8.003 8.003 0 0 1 11.314 0zm-28.287 84.86l-28.284 28.284a40 40 0 0 1-56.567-56.567l28.284-28.284a8 8 0 0 0-11.315-11.315l-28.284 28.284a56 56 0 0 0 79.196 79.197l28.285-28.285a8 8 0 1 0-11.315-11.314zM212.852 43.14a56.002 56.002 0 0 0-79.196 0l-28.284 28.284a8 8 0 1 0 11.314 11.314l28.284-28.284a40 40 0 0 1 56.568 56.567l-28.285 28.285a8 8 0 0 0 11.315 11.314l28.284-28.284a56.065 56.065 0 0 0 0-79.196z" fill="currentColor"></path></svg></span></a> <span data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f">What are AI Tools?</span></h2></div><div><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">工具是赋予LLM的一项功能，该功能应服务于明确的用途。</font></div><div><p data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f">Here are some commonly used tools in AI agents:</p></div><div><table data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><thead><tr><th>Tool</th> <th>Description</th></tr></thead> <tbody data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><tr data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><td data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f">Web Search</td> <td data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f" data-immersive-translate-paragraph="1">Allows the agent to fetch up-to-date information from the internet.<font class="notranslate immersive-translate-target-wrapper" data-immersive-translate-translation-element-mark="1" lang="zh-CN"><br><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-block-wrapper-theme-none immersive-translate-target-translation-block-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">允许智能体从互联网获取最新信息。</font></font></font></td></tr> <tr data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><td data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f">Image Generation</td> <td data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f">Creates images based on text descriptions.</td></tr> <tr data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><td data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f">Retrieval</td> <td data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f">Retrieves information from an external source.</td></tr> <tr data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><td data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f">API Interface</td> <td data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f" data-immersive-translate-paragraph="1">Interacts with an external API (GitHub, YouTube, Spotify, etc.).<font class="notranslate immersive-translate-target-wrapper" data-immersive-translate-translation-element-mark="1" lang="zh-CN"><br><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-block-wrapper-theme-none immersive-translate-target-translation-block-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">与外部 API（如 GitHub、YouTube、Spotify 等）交互。</font></font></font></td></tr></tbody></table></div><div><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">例如，若需进行算术运算，为你的LLM提供一个计算器工具，将比依赖模型原生能力获得更优结果。</font></div><div><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-block-wrapper-theme-none immersive-translate-target-translation-block-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">此外，LLMs根据其训练数据预测提示的完成情况，这意味着它们的内部知识仅包含训练前的事件。因此，如果您的代理需要最新数据，您必须通过某些工具提供这些数据。</font></font></div><div><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">例如，如果您直接（不使用搜索工具）询问LLM今天的天气，LLM可能会虚构出随机的天气情况。</font></div><div><li data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><p data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f">A Tool should contain:</p> <ul data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><li data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f">A <strong data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f">textual description of what the function does</strong>.</li> <li data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f">A <em data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f">Callable</em> (something to perform an action).</li> <li data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><em data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f">Arguments</em> with typings.</li> <li data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f">(Optional) Outputs with typings.</li></ul></li></div><div><h2 class="relative group" data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><a id="how-do-tools-work" class="header-link block pr-1.5 text-lg no-hover:hidden with-hover:absolute with-hover:p-1.5 with-hover:opacity-0 with-hover:group-hover:opacity-100 with-hover:right-full" href="https://huggingface.co/learn/agents-course/unit1/#how-do-tools-work" data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><span data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><svg class="" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" preserveAspectRatio="xMidYMid meet" viewBox="0 0 256 256"><path d="M167.594 88.393a8.001 8.001 0 0 1 0 11.314l-67.882 67.882a8 8 0 1 1-11.314-11.315l67.882-67.881a8.003 8.003 0 0 1 11.314 0zm-28.287 84.86l-28.284 28.284a40 40 0 0 1-56.567-56.567l28.284-28.284a8 8 0 0 0-11.315-11.315l-28.284 28.284a56 56 0 0 0 79.196 79.197l28.285-28.285a8 8 0 1 0-11.315-11.314zM212.852 43.14a56.002 56.002 0 0 0-79.196 0l-28.284 28.284a8 8 0 1 0 11.314 11.314l28.284-28.284a40 40 0 0 1 56.568 56.567l-28.285 28.285a8 8 0 0 0 11.315 11.314l28.284-28.284a56.065 56.065 0 0 0 0-79.196z" fill="currentColor"></path></svg></span></a> <span data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f">How do tools work?</span></h2></div><div><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-block-wrapper-theme-none immersive-translate-target-translation-block-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">工具调用的输出是对话中的另一种消息类型。工具调用步骤通常不会展示给用户：代理检索对话、调用工具、获取输出，将其作为新对话消息添加，并将更新后的对话再次发送给LLM。从用户角度看，仿佛是LLM使用了工具，但实际上是由我们的应用程序代码（代理）完成的。</font></font></div><div><h2 class="relative group" data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><a id="how-do-we-give-tools-to-an-llm" class="header-link block pr-1.5 text-lg no-hover:hidden with-hover:absolute with-hover:p-1.5 with-hover:opacity-0 with-hover:group-hover:opacity-100 with-hover:right-full" href="https://huggingface.co/learn/agents-course/unit1/#how-do-we-give-tools-to-an-llm" data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><span data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><svg class="" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" preserveAspectRatio="xMidYMid meet" viewBox="0 0 256 256"><path d="M167.594 88.393a8.001 8.001 0 0 1 0 11.314l-67.882 67.882a8 8 0 1 1-11.314-11.315l67.882-67.881a8.003 8.003 0 0 1 11.314 0zm-28.287 84.86l-28.284 28.284a40 40 0 0 1-56.567-56.567l28.284-28.284a8 8 0 0 0-11.315-11.315l-28.284 28.284a56 56 0 0 0 79.196 79.197l28.285-28.285a8 8 0 1 0-11.315-11.314zM212.852 43.14a56.002 56.002 0 0 0-79.196 0l-28.284 28.284a8 8 0 1 0 11.314 11.314l28.284-28.284a40 40 0 0 1 56.568 56.567l-28.285 28.285a8 8 0 0 0 11.315 11.314l28.284-28.284a56.065 56.065 0 0 0 0-79.196z" fill="currentColor"></path></svg></span></a> <span data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f">How do we give tools to an LLM?</span></h2></div><div><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">完整的答案可能看起来令人望而生畏，但我们本质上是通过系统提示向模型提供可用工具的文本描述：</font></div><div><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">为了实现这一点，我们必须非常精确和准确地做到：</font></div><div><li data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><strong data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f">What the tool does</strong></li></div><div><li data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><strong data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f">What exact inputs it expects</strong></li></div><div><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">我们将实现一个简化的计算器工具，仅用于两个整数的乘法运算。以下是我们的 Python 实现示例：</font></div><div><pre class=""><span class="hljs-keyword">def</span> <span class="hljs-title function_">calculator</span>(<span class="hljs-params">a: <span class="hljs-built_in">int</span>, b: <span class="hljs-built_in">int</span></span>) -&gt; <span class="hljs-built_in">int</span>:
    <span class="hljs-string">"""Multiply two integers."""</span>
    <span class="hljs-keyword">return</span> a * b</pre></div><div><pre class="">Tool Name: calculator, Description: Multiply two integers., Arguments: a: int, b: int, Outputs: int</pre></div><div><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-block-wrapper-theme-none immersive-translate-target-translation-block-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">如果我们想提供额外的工具，就必须保持一致并始终使用相同的格式。这个过程可能很脆弱，我们可能会无意中忽略一些细节。</font></font></div><div><h3 class="relative group" data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><a id="auto-formatting-tool-sections" class="header-link block pr-1.5 text-lg no-hover:hidden with-hover:absolute with-hover:p-1.5 with-hover:opacity-0 with-hover:group-hover:opacity-100 with-hover:right-full" href="https://huggingface.co/learn/agents-course/unit1/#auto-formatting-tool-sections" data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><span data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><svg class="" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" preserveAspectRatio="xMidYMid meet" viewBox="0 0 256 256"><path d="M167.594 88.393a8.001 8.001 0 0 1 0 11.314l-67.882 67.882a8 8 0 1 1-11.314-11.315l67.882-67.881a8.003 8.003 0 0 1 11.314 0zm-28.287 84.86l-28.284 28.284a40 40 0 0 1-56.567-56.567l28.284-28.284a8 8 0 0 0-11.315-11.315l-28.284 28.284a56 56 0 0 0 79.196 79.197l28.285-28.285a8 8 0 1 0-11.315-11.314zM212.852 43.14a56.002 56.002 0 0 0-79.196 0l-28.284 28.284a8 8 0 1 0 11.314 11.314l28.284-28.284a40 40 0 0 1 56.568 56.567l-28.285 28.285a8 8 0 0 0 11.315 11.314l28.284-28.284a56.065 56.065 0 0 0 0-79.196z" fill="currentColor"></path></svg></span></a> <span data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f">Auto-formatting Tool sections</span></h3></div><div><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-block-wrapper-theme-none immersive-translate-target-translation-block-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">我们将利用 Python 的内省功能来利用源代码，并自动为我们构建工具描述。我们只需要工具实现使用类型提示、文档字符串和合理的函数名称。我们将编写一些代码从源代码中提取相关部分。</font></font></div><div><pre class=""><span class="hljs-meta">@tool</span>
<span class="hljs-keyword">def</span> <span class="hljs-title function_">calculator</span>(<span class="hljs-params">a: <span class="hljs-built_in">int</span>, b: <span class="hljs-built_in">int</span></span>) -&gt; <span class="hljs-built_in">int</span>:
    <span class="hljs-string">"""Multiply two integers."""</span>
    <span class="hljs-keyword">return</span> a * b

<span class="hljs-built_in">print</span>(calculator.to_string())</pre></div><div><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">注意函数定义前的 <code data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f">@tool</code> 装饰器。</font></div><div><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">通过接下来要实现的方案，我们将能够利用装饰器提供的 <code data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f">to_string()</code> 函数自动从源代码中检索以下文本：</font></div><div><pre class="">Tool Name: calculator, Description: Multiply two integers., Arguments: a: int, b: int, Outputs: int</pre></div><div><h3 class="relative group" data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><a id="generic-tool-implementation" class="header-link block pr-1.5 text-lg no-hover:hidden with-hover:absolute with-hover:p-1.5 with-hover:opacity-0 with-hover:group-hover:opacity-100 with-hover:right-full" href="https://huggingface.co/learn/agents-course/unit1/#generic-tool-implementation" data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><span data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><svg class="" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" width="1em" height="1em" preserveAspectRatio="xMidYMid meet" viewBox="0 0 256 256"><path d="M167.594 88.393a8.001 8.001 0 0 1 0 11.314l-67.882 67.882a8 8 0 1 1-11.314-11.315l67.882-67.881a8.003 8.003 0 0 1 11.314 0zm-28.287 84.86l-28.284 28.284a40 40 0 0 1-56.567-56.567l28.284-28.284a8 8 0 0 0-11.315-11.315l-28.284 28.284a56 56 0 0 0 79.196 79.197l28.285-28.285a8 8 0 1 0-11.315-11.314zM212.852 43.14a56.002 56.002 0 0 0-79.196 0l-28.284 28.284a8 8 0 1 0 11.314 11.314l28.284-28.284a40 40 0 0 1 56.568 56.567l-28.285 28.285a8 8 0 0 0 11.315 11.314l28.284-28.284a56.065 56.065 0 0 0 0-79.196z" fill="currentColor"></path></svg></span></a> <span data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f">Generic Tool implementation</span></h3></div><div><pre class=""><span class="hljs-keyword">class</span> <span class="hljs-title class_">Tool</span>:
    <span class="hljs-string">"""
    A class representing a reusable piece of code (Tool).

    Attributes:
        name (str): Name of the tool.
        description (str): A textual description of what the tool does.
        func (callable): The function this tool wraps.
        arguments (list): A list of argument.
        outputs (str or list): The return type(s) of the wrapped function.
    """</span>
    <span class="hljs-keyword">def</span> <span class="hljs-title function_">__init__</span>(<span class="hljs-params">self,
                 name: <span class="hljs-built_in">str</span>,
                 description: <span class="hljs-built_in">str</span>,
                 func: <span class="hljs-built_in">callable</span>,
                 arguments: <span class="hljs-built_in">list</span>,
                 outputs: <span class="hljs-built_in">str</span></span>):
        self.name = name
        self.description = description
        self.func = func
        self.arguments = arguments
        self.outputs = outputs

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">to_string</span>(<span class="hljs-params">self</span>) -&gt; <span class="hljs-built_in">str</span>:
        <span class="hljs-string">"""
        Return a string representation of the tool,
        including its name, description, arguments, and outputs.
        """</span>
        args_str = <span class="hljs-string">", "</span>.join([
            <span class="hljs-string">f"<span class="hljs-subst">{arg_name}</span>: <span class="hljs-subst">{arg_type}</span>"</span> <span class="hljs-keyword">for</span> arg_name, arg_type <span class="hljs-keyword">in</span> self.arguments
        ])

        <span class="hljs-keyword">return</span> (
            <span class="hljs-string">f"Tool Name: <span class="hljs-subst">{self.name}</span>,"</span>
            <span class="hljs-string">f" Description: <span class="hljs-subst">{self.description}</span>,"</span>
            <span class="hljs-string">f" Arguments: <span class="hljs-subst">{args_str}</span>,"</span>
            <span class="hljs-string">f" Outputs: <span class="hljs-subst">{self.outputs}</span>"</span>
        )

    <span class="hljs-keyword">def</span> <span class="hljs-title function_">__call__</span>(<span class="hljs-params">self, *args, **kwargs</span>):
        <span class="hljs-string">"""
        Invoke the underlying function (callable) with provided arguments.
        """</span>
        <span class="hljs-keyword">return</span> self.func(*args, **kwargs)</pre></div><div><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">我们可以用类似下面的代码来创建一个该类的工具：</font></div><div><pre class="">calculator_tool = Tool(
    <span class="hljs-string">"calculator"</span>,                   <span class="hljs-comment"># name</span>
    <span class="hljs-string">"Multiply two integers."</span>,       <span class="hljs-comment"># description</span>
    calculator,                     <span class="hljs-comment"># function to call</span>
    [(<span class="hljs-string">"a"</span>, <span class="hljs-string">"int"</span>), (<span class="hljs-string">"b"</span>, <span class="hljs-string">"int"</span>)],   <span class="hljs-comment"># inputs (names and types)</span>
    <span class="hljs-string">"int"</span>,                          <span class="hljs-comment"># output</span>
)</pre></div><div><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">但我们也可以使用 Python 的 <code data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f">inspect</code> 模块来为我们检索所有信息！这就是 <code data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f">@tool</code> 装饰器的作用。</font></div><div><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">Model Context Protocol (MCP): 统一的工具接口</font></div><div><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">模型上下文协议（MCP）是一个开放协议，它标准化了应用程序如何向LLMs提供工具。MCP 提供：</font></div><div><ul data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f"><li data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f" data-immersive-translate-paragraph="1">A growing list of pre-built integrations that your LLM can directly plug into<font class="notranslate immersive-translate-target-wrapper" data-immersive-translate-translation-element-mark="1" lang="zh-CN"><br><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-block-wrapper-theme-none immersive-translate-target-translation-block-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">越来越多的预构建集成，您的LLM可以直接接入</font></font></font></li> <li data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f" data-immersive-translate-paragraph="1">The flexibility to switch between LLM providers and vendors<font class="notranslate immersive-translate-target-wrapper" data-immersive-translate-translation-element-mark="1" lang="zh-CN"><br><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-block-wrapper-theme-none immersive-translate-target-translation-block-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">在LLM提供商和供应商之间灵活切换的能力</font></font></font></li> <li data-immersive-translate-walked="cc9ba4f4-47f1-4226-92f9-25ca501a5f2f" data-immersive-translate-paragraph="1">Best practices for securing your data within your infrastructure<font class="notranslate immersive-translate-target-wrapper" data-immersive-translate-translation-element-mark="1" lang="zh-CN"><br><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-block-wrapper-theme-none immersive-translate-target-translation-block-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">基础设施内保护数据的最佳实践</font></font></font></li></ul></div><div><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">这意味着任何实现 MCP 的框架都可以利用协议中定义的工具，无需为每个框架重新实现相同的工具接口。</font></div>