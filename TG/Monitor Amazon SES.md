## [Monitor email sending using Amazon SES event publishing - Amazon Simple Email Service](https://docs.aws.amazon.com/ses/latest/dg/monitor-using-event-publishing.html)
- 以细粒度级别跟踪电子邮件发送情况，您可以设置 Amazon SES 将电子邮件发送事件发布到 Amazon CloudWatch
- 可以追踪多种类型的电子邮件发送事件，包括发送、投递、打开、点击、退回、投诉、拒绝、渲染失败和投递延迟
- 使用事件发布功能，需新建配置集
	- 定义事件发布的目的地
	- 要发布的事件类型
- 发送邮件时
	- 提供配置集名称、一个或多个消息标签
	- 或者使用自动标签：ses:from-domain
## Monitor Amazon SES
- 以下步骤涉及设置事件发布，相关主题如下：
1. 您必须使用 Amazon SES 控制台或 API 创建配置集。
2. 向配置集添加一个或多个事件目的地（CloudWatch、Firehose、Pinpoint 或 SNS），并配置特定于事件目的地的参数。
3. 发送电子邮件时，需指定包含您事件目的地的配置集以供使用。
## SES -> SNS -> Lambda -> dynamodb
- Create an SNS Topic
	- SESEventsTopic
	- `arn:aws:sns:us-west-1:966275272565:SESEventsTopic`
- Create a [DynamoDB Table](https://us-west-1.console.aws.amazon.com/dynamodbv2/home?region=us-west-1#table?name=EmailEvents)
	- `arn:aws:dynamodb:us-west-1:966275272565:table/EmailEvents`
- Create a [Lambda Function](https://us-west-1.console.aws.amazon.com/lambda/home?region=us-west-1#/functions/ProcessSESEvents?newFunction=true&tab=code)
```json
{
  "Version": "2012-10-17",
  "Statement": [{
    "Effect": "Allow",
    "Action": "dynamodb:PutItem",
    "Resource": "arn:aws:dynamodb:us-west-1:966275272565:table/EmailEvents"
  }]
}
```
- Subscribe Lambda to SNS
- Configure SES Event Tracking
	- EmailTrackingConfigSet