---
title: "bitcoinbook/ch06_transactions.adoc at develop · bitcoinbook/bitcoinbook"
source: "https://github.com/bitcoinbook/bitcoinbook/blob/develop/ch06_transactions.adoc#inputs"
author:
  - "[[GitHub]]"
published:
created: 2025-02-05
description: "Mastering Bitcoin 3rd Edition - Programming the Open Blockchain - bitcoinbook/ch06_transactions.adoc at develop · bitcoinbook/bitcoinbook"
tags:
  - "clippings"
---
### A Serialized Bitcoin Transaction 序列化的比特币交易

```
$ bitcoin-cli getrawtransaction 466200308696215bbc949d5141a49a41\
38ecdfdfaa2a8029c1f9bcecd1f96177

01000000000101eb3ae38f27191aa5f3850dc9cad00492b88b72404f9da13569
8679268041c54a0100000000ffffffff02204e0000000000002251203b41daba
4c9ace578369740f15e5ec880c28279ee7f51b07dca69c7061e07068f8240100
000000001600147752c165ea7be772b2c0acb7f4d6047ae6f4768e0141cf5efe
2d8ef13ed0af21d4f4cb82422d6252d70324f6f4576b727b7d918e521c00b51b
e739df2f899c49dc267c0ad280aca6dab0d2fa2b42a45182fc83e81713010000
0000$ bitcoin-cli getrawtransaction 466200308696215bbc949d5141a49a41\
38ecdfdfaa2a8029c1f9bcecd1f96177

01000000000101eb3ae38f27191aa5f3850dc9cad00492b88b72404f9da13569
8679268041c54a0100000000ffffffff02204e0000000000002251203b41daba
4c9ace578369740f15e5ec880c28279ee7f51b07dca69c7061e07068f8240100
000000001600147752c165ea7be772b2c0acb7f4d6047ae6f4768e0141cf5efe
2d8ef13ed0af21d4f4cb82422d6252d70324f6f4576b727b7d918e521c00b51b
e739df2f899c49dc267c0ad280aca6dab0d2fa2b42a45182fc83e81713010000
0000$ bitcoin-cli getrawtransaction 466200308696215bbc949d5141a49a41\38ecdfdfaa2a8029c1f9bcecd1f9617701000000000101eb3ae38f27191aa5f3850dc9cad00492b88b72404f9da135698679268041c54a0100000000ffffffff02204e0000000000002251203b41daba4c9ace578369740f15e5ec880c28279ee7f51b07dca69c7061e07068f8240100000000001600147752c165ea7be772b2c0acb7f4d6047ae6f4768e0141cf5efe2d8ef13ed0af21d4f4cb82422d6252d70324f6f4576b727b7d918e521c00b51be739df2f899c49dc267c0ad280aca6dab0d2fa2b42a45182fc83e817130100000000
```

![A byte map of Alice’s transaction](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_0601.png)

### Version 版本

### Extended Marker and Flag 扩展标记和旗帜

### Inputs Inputs

![map of bytes in the inputs field of Alice’s transaction](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_0602.png)

图 2. 爱丽丝交易的输入字段中的字节映射。

#### Length of Transaction Input List 交易输入列表的长度

该数字被编码为一个紧凑大小的无符号整数。

每笔交易中的每个输入必须包含三个字段：一个*输出点*字段、一个长度前缀的*输入脚本*字段和一个*序列号*。

Outpoint

她首先需要告诉全节点如何找到她之前接收那些比特币的交易。由于对比特币的控制权是在交易输出中分配的，爱丽丝使用“输出点”字段指向之前的“输出”。每个输入必须包含一个单一的输出点。

包含一个 32 字节的交易 ID（txid）

因为交易可能包含多个输出，爱丽丝还需要确定使用该交易中的哪个特定输出，这被称为其“输出索引”。输出索引是从 0 开始的 4 字节无符号整数。

在找到先前的输出时，全节点从其中获得了几个关键信息。

分配给上一个输出的比特币数量。

该先前输出的授权条件。这些是为了花费分配给该先前输出的比特币而必须满足的条件。

对于已确认的交易，确认它的区块高度以及该区块的时间中位数（MTP）。这是相对时间锁（在[序列作为共识强制执行的相对时间锁](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#relative_timelocks)中描述）和币基交易输出（在[币基交易](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#coinbase_transactions)中描述）所必需的。

明前一个输出存在于区块链中（或作为已知的未确认交易），并且没有其他交易花费过它。

#### Input Script Input Script

输入脚本字段是遗留交易格式的残留物。我们的示例交易输入花费一个原生隔离见证输出，该输出在输入脚本中不需要任何数据，因此输入脚本的长度前缀设置为零（0x00）。

#### Sequence 序列

输入的最后四个字节是其“序列号”。这个字段的用途和含义随着时间的推移发生了变化。

基于原始序列的交易替换

选择性加入交易替换信号

### Outputs Outputs

![A byte map of the outputs field from Alice’s transaction](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_0604.png)

图 4. 爱丽丝交易输出字段的字节图。

#### Outputs Count 

输出字段以一个计数开始，表示此交易中的输出数量。

#### Amount

这是一个 8 字节的有符号整数，表示要转移的聪数量。聪是可以在比特币链上交易中表示的最小比特币单位。

Uneconomical outputs and disallowed dust

#### Output Scripts 输出脚本

输出金额后面跟着一个整数 compactSize，表示*输出脚本*的长度，该脚本包含了花费比特币所需满足的条件。

### Witness Structure 见证结构

以下脚本包含一个公钥和一个操作码，该操作码需要对支出交易中的数据进行相应的签名提交。就像我们简单示例中的数字*2*一样，签名是我们的见证。

```
<public key> OP_CHECKSIG<public key> OP_CHECKSIG <public key> OP_CHECKSIG</public>
```

在用于所有早期比特币交易的传统交易格式中，签名和其他数据被放置在输入脚本字段中。然而，当开发人员开始在比特币上实现合约协议时，如我们在[基于原始序列的交易替换](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#original_tx_replacement)中看到的那样，他们发现将证人放在输入脚本字段中存在几个重大问题。

#### Circular Dependencies 循环依赖

#### Second-Party Transaction Malleability 第三方交易可锻性

#### Segregated Witness 隔离见证

早在 2011 年，协议开发者就知道如何解决循环依赖、第三方可延展性和第二方可延展性的问题。其想法是避免在生成交易的交易 ID（txid）的计算中包含输入脚本。

#### Witness Structure Serialization 见证结构序列化

![A byte map of the witness from Alice’s transaction](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_0605.png)

图 5. 来自爱丽丝交易的见证结构的字节图。

### Lock Time 锁定时间

序列化交易中的最后一个字段是其锁定时间。

### Coinbase Transactions Coinbase 交易

每个区块中的第一笔交易是一种特殊情况。大多数较早的文档将其称为“创世交易”，但大多数较新的文档将其称为“币基交易”