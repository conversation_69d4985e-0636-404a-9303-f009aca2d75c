``` shell
# insights
curl 'https://s3/insights-cloud.tar.gz' -o 'insights.tar.gz'
tar -xf insights.tar.gz

cd tigergraph/app/4.2.0/bin/gui
rm -rf tools
rm -rf gap
wget https://tigergraph-build-artifacts.s3-us-west-1.amazonaws.com/tigergraph/tools/c20c75d984d237f6a3c8c472a6874229944e6f3c/release/family_of_tools.tar.gz
wget https://tigergraph-build-artifacts.s3-us-west-1.amazonaws.com/tigergraph/tools/c20c75d984d237f6a3c8c472a6874229944e6f3c/release/gap.tar.gz
tar -xf gap.tar.gz
tar -xf family_of_tools.tar.gz
```