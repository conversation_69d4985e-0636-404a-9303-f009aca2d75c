---
title: What are LLMs? - Hugging Face Agents Course
source: https://huggingface.co/learn/agents-course/unit1/thoughts
author: 
published: 
created: 2025-03-28
description: We’re on a journey to advance and democratize artificial intelligence through open source and open science.
tags:
  - clippings
---
代理的思维负责访问当前观察结果并决定下一步应采取哪些行动。

  
这三个组件在一个持续循环中协同工作。用编程中的类比来说，代理使用了一个 while 循环：该循环会持续进行，直到代理的目标达成。

## The Thought-Action-Observation Cycle

![Think, Act, Observe cycle](https://huggingface.co/datasets/agents-course/course-images/resolve/main/en/unit1/AgentCycle.gif)

| Type of Thought | Example |
| --- | --- |
| Planning | “我需要将此任务分为三个步骤：1) 收集数据，2) 分析趋势，3) 生成报告” |
| Analysis | “根据错误信息，问题似乎出在数据库连接参数上” |
| Decision Making | “考虑到用户的预算限制，我应该推荐中档选项” |
| Problem Solving | “为了优化这段代码，我应该先进行性能分析以找出瓶颈” |
| Memory Integration | “用户之前提到过他们偏好 Python，所以我将提供 Python 示例” |
| Self-Reflection | “我上次的方法效果不佳，应该尝试不同的策略” |
| Goal Setting | “要完成这项任务，我首先需要确立验收标准” |
| Prioritization | “在添加新功能之前，应先解决安全漏洞问题” |

  
ReAct 是一种简单的提示技术，它在让LLM解码下一个令牌之前附加“让我们一步步思考”。

简化版中，我们的系统提示可能如下所示：

![Think, Act, Observe cycle](https://huggingface.co/datasets/agents-course/course-images/resolve/main/en/unit1/system_prompt_cycle.png)

  
这使得模型能更细致地考虑子步骤，通常比直接生成最终解决方案产生更少的错误。

![ReAct](https://huggingface.co/datasets/agents-course/course-images/resolve/main/en/unit1/ReAct.png)

  
(d)是 Re-Act 方法的一个示例，其中我们提示“让我们一步步思考”。

## Types of Agent Actions