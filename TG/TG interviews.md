### 面试题
- [vue3 中的响应式设计原理](https://fe.ecool.fun/topic/ea676360-c8f5-4ce4-bc66-5c3e4f7eddb6?orderBy=updateTime&order=desc&tagId=14)
- [Vue3.0 性能提升主要是通过哪几方面体现的？](https://fe.ecool.fun/topic/9f19f171-7071-42e6-82d5-35b19bd83928?orderBy=updateTime&order=desc&tagId=14)
- vite对比webpack有哪些优势，如何实现的
- [web常见的攻击方式有哪些，以及如何进行防御？](https://fe.ecool.fun/topic/947278fd-7485-4e8c-a704-83d48280e05a?orderBy=updateTime&order=desc&tagId=21)
## 2024-05-08
### Project
- 为什么会转行？如何转的，在哪里学习？测试的经验有哪些帮助？
- 把项目从 vue2+webpack 升级到 vue3+vite: 有哪些工作，为什么会带来提升？
- vue3的改进、如何实现响应式的
- vue3的新特性
- web常见的攻击方式有哪些，以及如何进行防御？
- 项目
### Coding
- 井字棋游戏
## 2024-05-15
### Question
- React Concurrent Rendering
## 2024-05-17
- Vue的响应式设计原理
- Vite为什么这么快
- Vue组件的key属性
- event loop
## 2024-05-22
- React-query解决了什么问题
- React有什么避免组件re-render的方法
- 为什么在使用react setState()后能马上拿到state的新值吗
- useLayoutEffect的作用是
- 讲讲Vue的响应式是如何实现的
## 2024-05-28
- 介绍一下硕士论文: Community detection in complex network by network embedding anddensity clustering
	- graph如何表示
	- community detection算法
- Composition API vs Options API
- 使用vue3时遇到的坑
- code exercise: file tree, create, search
## 2024-05-31
- 页面检测工具项目
- react vue设计理念的区别
- react18的新特性
- 对函数式编程的理解
- 设计一个drag drop组件