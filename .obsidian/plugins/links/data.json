{"linkReplacements": [], "titleSeparator": " • ", "showPerformanceNotification": false, "removeLinksFromHeadingsInternalWikilinkWithoutTextReplacement": "Destination", "deleteUnreferencedLinkTarget": true, "removeLinksFromHeadingsInternalWikilinkWithoutTextAction": "ReplaceWithDestination", "onConvertToMdlinkAppendMdExtension": false, "autoselectWordOnCreateLink": true, "skipFrontmatterInNoteWideCommands": true, "ffReplaceLink": false, "ffExtractSection": false, "ffWrapNoteInFolder": false, "ffConvertLinksInFolder": false, "ffObsidianUrlSupport": false, "ffHighlightBrokenLinks": false, "ffSetLinkDestinationFromClipbard": false, "ffSkipFrontmatterInNoteWideCommands": false, "ffCopyLinkToObject": false, "contexMenu": {"editLinkText": true, "setLinkText": true, "setLinkTextFromClipboard": true, "editLinkDestination": true, "setLinkDestinationFromClipboard": true, "copyLinkDestination": true, "unlink": true, "convertToWikilink": true, "convertToAutolink": true, "convertToMakrdownLink": true, "convertToHtmlLink": false, "replaceLink": true, "embedUnembedLink": true, "deleteLink": true, "createLink": true, "createLinkFromClipboard": true, "convertAllLinksToMdLinks": false, "convertWikilinkToMdLinks": false, "convertUrlsToMdlinks": false, "convertAutolinksToMdlinks": false, "convertHtmllinksToMdlinks": false, "extractSection": false, "wrapNoteInFolder": false, "copyLinkToClipboard": true, "copyLinkToHeadingToClipboard": true, "copyLinkToBlockToClipboard": false, "cutLinkToClipboard": true}}