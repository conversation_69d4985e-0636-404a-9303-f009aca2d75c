## [My Obsidian Template — 2025 Update \| by <PERSON><PERSON><PERSON> \| Medium](https://medium.com/@munafsheikh/my-obsidian-template-2025-update-848195ee1b05)
- 在创建特定格式的笔记时，应确保所有管理操作已完成。自动添加标签、正确归类文件夹、建立索引
- 条目类型（笔记、信息、参考等）提示用户选择笔记类别（混沌或清晰、梦境日志等）
## [How to Take Smart Notes - Book on a Page - YouTube](https://www.youtube.com/watch?v=o49C8jQIsvs)
- **writing is not a chore, it's the thinking process**
- fleeting note: when you have a idea
- permanent note: one idea per note, precise reference
	- 不要创建太多，遗忘是一项重要的技能如果我们想要记住一些事
- overview note
- **practice every day**
- focus on distillation, not quantity
- Ask questions
- study = research
	- elaboration, writing, summary
	- tests
- **praise the workflow not the talent**
### [VISUAL SUMMARY: Feel-Good Productivity by <PERSON> - The Periodic Table of Productivity - YouTube](https://www.youtube.com/watch?v=Z-ksCSK-1mk)
### [The Excalidraw-Obsidian Showcase: 57 key features in just 17 minutes - YouTube](https://www.youtube.com/watch?v=P_Q6avJGoWI)
- Add links to note
- Add link to a block reference: text transporter plugin
- text to diagram
- get elements link in the canvas, then use it even in a markdown note
- insert images
	- drag from website
	- cmd: insert Any file
- embed the drawing to markdown note
	- or embed an specific element
	- cmd: embed recent drawing
- deconstruct a part of drawing
### [[unit1-Tools]]
### [[messages-and-special-tokens]]
### [[What are LLMs? - Hugging Face Agents Course]]
### [Unit 1 What are agents](https://huggingface.co/learn/agents-course/unit1/what-are-agents)
- 这就是智能体的定义：一个能够推理、规划并与环境交互的人工智能模型。
- [Introduction - Hugging Face NLP Course](https://huggingface.co/learn/nlp-course/chapter1/1)
### [agents-course (Hugging Face Agents Course)](https://huggingface.co/agents-course)
### [Welcome To The Agents Course! Introduction to the Course and Q&A - YouTube](https://www.youtube.com/watch?v=iLVyYDbdSmM&t=16s)
- 推荐的前置课程：nlp course part 3/4, talk about tokenizers
- HF Agents hackathon $10000 prize
### [Ollama cli reference](https://github.com/ollama/ollama?tab=readme-ov-file#cli-reference)
### [Dev Containers - Visual Studio Marketplace](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers)
### Building Custom MCP Servers From Scratch Using Cline: A Comprehensive Guide
- MCP (Model Context Protocol): LLM应用与其他外部工具间的通信
	- MCP hosts
	- MCP server
### [My 17 Minute AI Workflow To Stand Out At Work - YouTube](https://www.youtube.com/watch?v=yqq_U2fxd2U)
- 让AI基于高质量的资料做出回复，能得到更好的答案
- workflow
	1. Elicit: 获取某个topic的学术文献
	2. NotebookLM: 上传文献，总结，获得观点
	3. claude: 根据上一步的观点询问更具体的问题
### [The FUN and EFFICIENT note-taking system I use in my PhD - YouTube](https://www.youtube.com/watch?v=L9SLlxaEEXY)
- 用自己的话总结
- trust the system
### [My FULL Obsidian Zettelkasten Workflow in 10 minutes - YouTube](https://www.youtube.com/watch?v=GRA_fwbdrtc)
- reference note
- literature note
- permanent note
### [Zettelkasten Method Explained: A Beginner's Guide - YouTube](https://www.youtube.com/watch?v=GpV47rUYk8I)

### [How To Take Smart Notes (3 methods no one's talking about) - YouTube](https://www.youtube.com/watch?v=5O46Rqh5zHE)

### [2023 Zettelkasten Obsidian Workflow ⚡️ Simple Set Up - YouTube](https://www.youtube.com/watch?v=HSTOSWOhNo4)
