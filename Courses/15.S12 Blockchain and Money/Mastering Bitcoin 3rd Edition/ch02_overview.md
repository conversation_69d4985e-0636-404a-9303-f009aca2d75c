# ch02_overview

[[ch02_bitcoin_overview]]
## 比特币的工作原理

与传统的银行和支付系统不同，比特币系统不需要依赖第三方信任。比特币系统中，每个用户都可以使用运行在自己计算机上的软件来验证比特币系统的每个方面的正确操作。在本章中，我们将通过跟踪比特币系统中的一笔交易，观察它如何被记录在区块链（所有交易的分布式账本）上，从高层次上审视比特币。后续章节将深入探讨交易、网络和挖矿背后的技术。

### 比特币概述

比特币系统由拥有包含密钥的钱包的用户、在网络中传播的交易以及通过竞争性计算生成共识区块链的矿工组成。区块链是所有交易的权威账本。

本章中的每个示例都基于比特币网络上的实际交易，通过从一个钱包向另一个钱包发送资金来模拟多个用户之间的交互。在跟踪交易通过比特币网络到区块链的过程中，我们将使用**区块链浏览器**网站来可视化每个步骤。区块链浏览器是一个作为比特币搜索引擎的Web应用程序，允许你搜索地址、交易和区块，并查看它们之间的关系和资金流动。

流行的区块链浏览器包括：

* [Blockstream Explorer](https://blockstream.info)
* [Mempool.Space](https://mempool.space)
* [BlockCypher Explorer](https://live.blockcypher.com)

每个区块链浏览器都有一个搜索功能，可以输入比特币地址、交易哈希、区块号或区块哈希，并从比特币网络中检索相应的信息。对于每个交易或区块示例，我们将提供一个URL，以便你可以自己查找并详细研究。

[[block-explorer-privacy]]
### 区块链浏览器隐私警告

[WARNING]
====
在区块链浏览器上搜索信息可能会向运营商透露你对该信息感兴趣，使他们能够将其与你的IP地址、浏览器详细信息、过去的搜索记录或其他可识别信息关联起来。如果你查找本书中的交易，区块链浏览器的运营商可能会猜测你正在学习比特币，这应该不是问题。但如果你查找自己的交易，运营商可能会猜测你收到了多少比特币、花费了多少以及当前拥有多少比特币。
====

[[spending_bitcoin]]
### 从在线商店购买

Alice是前一章中介绍的新用户，她刚刚获得了她的第一批比特币。在<<getting_first_bitcoin>>中，Alice与她的朋友Joe见面，用现金交换了一些比特币。自那以后，Alice又购买了更多的比特币。现在，Alice将进行她的第一笔消费交易，从Bob的在线商店购买一个高级播客剧集的访问权限。

Bob的网店最近开始接受比特币支付，在其网站上添加了比特币支付选项。Bob商店的价格以当地货币（美元）列出，但在结账时，顾客可以选择用美元或比特币支付。

Alice找到了她想购买的播客剧集，并进入结账页面。在结账时，Alice除了通常的支付选项外，还可以选择用比特币支付。购物车显示了以美元和比特币（BTC）计价的价格，按照比特币的当前汇率计算。

Bob的电子商务系统会自动生成一个包含**发票**的二维码（<<invoice-QR>>）。

[[invoice-QR]]
### 发票二维码
![payment-request](mbc3_0201.png)

与仅包含目标比特币地址的二维码不同，此发票是一个编码了目标地址、支付金额和描述的URI。这使得比特币钱包应用程序可以预填用于发送支付的信息，同时向用户显示可读的描述。你可以用比特币钱包应用程序扫描二维码，看看Alice会看到什么：

[[invoice-URI]]
### 发票二维码编码的URI，定义在BIP21中：
----
bitcoin:******************************************?amount=0.01577764&
label=Bob%27s%20Store&
message=Purchase%20at%20Bob%27s%20Store

URI的组成部分

比特币地址: "******************************************"
支付金额: "0.01577764"
收款地址的标签: "Bob's Store"
支付的描述: "Purchase at Bob's Store"
----

[TIP]
====
尝试用你的钱包扫描此二维码以查看地址和金额，但**不要发送资金**。
====

Alice用她的智能手机扫描显示的条形码。她的智能手机显示了一笔支付给+Bob's Store+的正确金额，她选择发送以授权支付。几秒钟内（大约与信用卡授权时间相同），Bob在收银台上看到了这笔交易。

[NOTE]
====
比特币网络可以处理小数值的交易，例如从毫比特币（1/1000比特币）到1/100,000,000比特币（称为**聪**）。本书在谈论大于1比特币的金额和使用小数表示法时，使用与传统货币（如美元）相同的复数规则，例如“10比特币”或“0.001比特币”。同样的规则也适用于其他比特币记账单位，如毫比特币和聪。
====

你可以使用区块链浏览器查看区块链数据，例如Alice向Bob支付的[交易](https://oreil.ly/hAeyh)。

在接下来的部分中，我们将更详细地研究这笔交易。我们将看到Alice的钱包是如何构建它的，它是如何在网络中传播的，如何被验证的，以及最终Bob如何在后续交易中花费这笔金额。

### 比特币交易

简单来说，交易告诉网络，某些比特币的所有者已授权将该价值转移给另一个所有者。新所有者现在可以通过创建另一笔交易来花费比特币，授权将其转移给另一个所有者，依此类推，形成一个所有权链。

#### 交易输入和输出

交易就像复式记账分类账中的条目。每笔交易包含一个或多个**输入**，这些输入花费资金。在交易的另一侧，有一个或多个**输出**，这些输出接收资金。输入和输出的金额不一定相同。相反，输出的总和略小于输入的总和，差额代表隐含的**交易费用**，这是由将交易包含在区块链中的矿工收取的小额费用。比特币交易在<<transaction-double-entry>>中显示为复式记账分类账条目。

交易还包含每个比特币金额（输入）的所有权证明，形式是来自所有者的数字签名，任何人都可以独立验证。在比特币术语中，花费是签署一笔交易，将价值从前一笔交易转移到一个由比特币地址标识的新所有者。

[[transaction-double-entry]]
### 交易作为复式记账
![Transaction Double-Entry](mbc3_0202.png)

#### 交易链

Alice向Bob商店的支付使用前一笔交易的输出作为其输入。在前一章中，Alice从她的朋友Joe那里收到了比特币以换取现金。我们在<<transaction-chain>>中将其标记为**交易1**（Tx1）。

Tx1将0.001比特币（100,000聪）发送到由Alice的密钥锁定的输出。她向Bob商店的新交易（Tx2）引用了前一个输出作为输入。在图中，我们使用箭头并通过将输入标记为“Tx1:0”来显示该引用。在实际交易中，引用是Alice从Joe那里收到钱的交易的32字节交易标识符（txid）。“:0”表示Alice收到钱的输出位置；在这种情况下，是第一个位置（位置0）。

如图所示，实际的比特币交易并不显式包含其输入的价值。为了确定输入的价值，软件需要使用输入的引用来查找正在花费的前一笔交易输出。

Alice的Tx2包含两个新输出，一个支付75,000聪用于播客，另一个支付20,000聪返回给Alice作为找零。

[[transaction-chain]]
### 交易链，其中一笔交易的输出是下一笔交易的输入
![Transaction chain](mbc3_0203.png)

[TIP]
====
序列化的比特币交易——软件用于发送交易的数据格式——使用最小的链上价值单位的整数来编码要转移的价值。当比特币首次创建时，这个单位没有名称，一些开发者简单地称其为**基本单位**。后来，许多用户开始称这个单位为**聪**（sat），以纪念比特币的创造者。在<<transaction-chain>>和本书中的一些其他插图中，我们使用聪值，因为这是协议本身使用的单位。
====

#### 找零

除了支付给比特币接收者的一个或多个输出外，许多交易还会包括一个支付给比特币花费者的输出，称为**找零**输出。这是因为交易输入，就像货币票据一样，不能部分花费。如果你在商店购买价值5美元的商品，但使用20美元的钞票支付，你期望收到15美元的找零。同样的概念适用于比特币交易输入。如果你购买价值5比特币的商品，但只有一个价值20比特币的输入可用，你将向商店所有者发送一个5比特币的输出，并向自己发送一个15比特币的输出作为找零（不包括你的交易费用）。

在比特币协议层面，找零输出（及其支付的地址，称为**找零地址**）与支付输出没有区别。

重要的是，找零地址不必与输入的地址相同，出于隐私原因，通常是所有者钱包中的新地址。在理想情况下，输出的两种不同用途都使用从未见过的地址，并且看起来完全相同，防止任何第三方确定哪些输出是找零，哪些是支付。然而，为了说明目的，我们在<<transaction-chain>>中为找零输出添加了阴影。

并非每笔交易都有找零输出。那些没有找零输出的交易被称为**无找零交易**，它们只能有一个输出。无找零交易只有在花费的金额大致等于交易输入中的可用金额减去预期的交易费用时才是一个实际的选择。在<<transaction-chain>>中，我们看到Bob创建了Tx3作为一笔无找零交易，花费了他在Tx2中收到的输出。

#### 币选择

不同的钱包在选择用于支付的输入时使用不同的策略，称为**币选择**。

它们可能会聚合许多小输入，或者使用一个等于或大于所需支付的输入。除非钱包能够以某种方式聚合输入以精确匹配所需的支付加上交易费用，否则钱包将需要生成一些找零。这与人们处理现金的方式非常相似。如果你总是使用口袋里最大的钞票，你最终会得到一堆零钱。如果你只使用零钱，你通常会只有大额钞票。人们潜意识里在这两个极端之间找到平衡，比特币钱包开发者努力编程实现这种平衡。

#### 常见交易形式

一种非常常见的交易形式是简单支付。这种类型的交易有一个输入和两个输出，如<<transaction-common>>所示。

[[transaction-common]]
### 最常见的交易
![Common Transaction](mbc3_0204.png)

另一种常见的交易形式是**合并交易**，它将多个输入花费到一个输出中（<<transaction-consolidating>>）。这相当于将一堆硬币和纸币兑换成一张更大的纸币。这种交易有时由钱包和企业生成，以清理大量小额资金。

[[transaction-consolidating]]
### 合并交易聚合资金
![Aggregating Transaction](mbc3_0205.png)

最后，另一种在区块链上常见的交易形式是**批量支付**，它支付给代表多个接收者的多个输出（<<transaction-distributing>>）。这种类型的交易有时被商业实体用于分配资金，例如在处理多个员工的工资支付时。

[[transaction-distributing]]
### 批量交易分配资金
![Distributing Transaction](mbc3_0206.png)

### 构建交易

Alice的钱包应用程序包含所有用于选择输入和生成输出以构建符合Alice规范的交易的逻辑。Alice只需要选择一个目的地、金额和交易费用，其余的事情在钱包应用程序中完成，而无需她看到细节。重要的是，如果钱包已经知道它控制的输入，它可以在完全离线的情况下构建交易。就像在家写支票然后将其装入信封发送到银行一样，交易不需要在连接到比特币网络时构建和签名。

#### 获取正确的输入

Alice的钱包应用程序首先必须找到可以支付她想要发送给Bob的金额的输入。大多数钱包会跟踪属于钱包中地址的所有可用输出。因此，Alice的钱包将包含Joe交易中的交易输出副本，该交易是用现金交换创建的（见<<getting_first_bitcoin>>）。运行在完整节点上的比特币钱包应用程序实际上包含每个已确认交易的未花费输出副本，称为**未花费交易输出**（UTXO）。然而，由于完整节点使用更多资源，许多用户钱包运行轻量级客户端，仅跟踪用户自己的UTXO。

在这种情况下，这个单一的UTXO足以支付播客的费用。如果不是这种情况，Alice的钱包应用程序可能需要组合几个较小的UTXO，就像从钱包里挑选硬币一样，直到找到足够的金额来支付播客。在这两种情况下，可能需要一些找零，我们将在下一节中看到，钱包应用程序创建交易输出（支付）。

#### 创建输出

交易输出是使用一个脚本创建的，该脚本类似于“此输出支付给能够提供与Bob的公共地址对应的密钥签名的人”。因为只有Bob拥有与该地址对应的密钥的钱包，只有Bob的钱包可以提供这样的签名以稍后花费此输出。因此，Alice将**锁定**输出值，要求Bob的签名。

此交易还将包括第二个输出，因为Alice的资金包含的金额超过了播客的费用。Alice的找零输出与支付给Bob的输出在同一笔交易中创建。本质上，Alice的钱包将她的资金分成两个输出：一个给Bob，一个返回给她自己。然后她可以在后续交易中花费找零输出。

最后，为了使交易能够及时被网络处理，Alice的钱包应用程序将添加一笔小额费用。费用并未在交易中明确说明；它由输入和输出之间的价值差异暗示。这笔交易费用由矿工收取，作为将交易包含在记录在区块链上的区块中的费用。

[[transaction-alice-url]]
[TIP]
====
查看[Alice向Bob商店的交易](https://oreil.ly/GwBq1)。
====

#### 将交易添加到区块链

由Alice的钱包应用程序创建的交易包含确认资金所有权和分配新所有者所需的一切。现在，交易必须被传输到比特币网络，成为区块链的一部分。在下一节中，我们将看到交易如何成为新区块的一部分，以及区块是如何被挖出的。最后，我们将看到新区块一旦被添加到区块链中，随着更多区块的添加，网络对其的信任度逐渐增加。

##### 传输交易

由于交易包含所有必要的信息以供处理，因此无论它如何或在哪里传输到比特币网络都无关紧要。比特币网络是一个点对点网络，每个比特币节点通过连接到其他几个比特币节点来参与。比特币网络的目的是将交易和区块传播给所有参与者。

##### 传播方式

比特币点对点网络中的节点是既包含软件逻辑又包含数据的程序，能够完全验证新交易的正确性。节点之间的连接通常被可视化为图中的边（线），节点本身是图中的点（节点）。因此，比特币节点通常被称为“完全验证节点”或简称**全节点**。

Alice的钱包应用程序可以通过任何类型的连接（有线、WiFi、移动网络等）将新交易发送到任何比特币节点。它还可以将交易发送到另一个程序（例如区块链浏览器），该程序会将其转发给节点。她的比特币钱包不需要直接连接到Bob的比特币钱包，也不需要使用Bob提供的互联网连接，尽管这两种选择也是可能的。任何比特币节点在接收到它之前未见过的新交易时，都会将其转发给所有连接的其他节点，这种传播技术被称为**八卦传播**。因此，交易迅速传播到整个点对点网络，几秒钟内就能到达大部分节点。

##### Bob的视角

如果Bob的比特币钱包应用程序直接连接到Alice的钱包应用程序，Bob的钱包应用程序可能是第一个接收到交易的。然而，即使Alice的钱包通过其他节点发送交易，它也会在几秒钟内到达Bob的钱包。Bob的钱包会立即识别Alice的交易为入账支付，因为它包含一个可由Bob的密钥赎回的输出。Bob的钱包应用程序还可以独立验证交易是否格式正确。如果Bob使用自己的全节点，他的钱包可以进一步验证Alice的交易是否只花费了有效的UTXO。

### 比特币挖矿

Alice的交易现在已经在比特币网络上传播。它只有在被挖矿过程包含在一个区块中并且该区块被全节点验证后，才能成为**区块链**的一部分。有关挖矿的详细解释，请参见<<mining>>。

比特币的防伪系统基于计算。交易被打包成**区块**。区块有一个非常小的头部，必须以非常特定的方式形成，这需要大量的计算才能正确完成——但只需要少量的计算来验证其正确性。挖矿过程在比特币中具有两个目的：

* 矿工只能通过创建遵循比特币所有**共识规则**的区块来获得诚实收入。因此，矿工通常有动力只在其区块中包含有效交易，并且他们构建的区块也基于有效的区块。这使得用户可以选择性地假设区块中的任何交易都是有效交易。

* 挖矿目前在每个区块中创建新的比特币，几乎像中央银行印刷新货币一样。每个区块中创建的比特币数量是有限的，并且随着时间的推移逐渐减少，遵循固定的发行计划。

挖矿在成本和回报之间实现了微妙的平衡。挖矿使用电力来解决计算问题。成功的矿工将以新比特币和交易费用的形式获得**奖励**。然而，只有在矿工仅包含有效交易的情况下，才能获得奖励，比特币协议的**共识**规则决定了什么是有效的。这种微妙的平衡为比特币提供了安全性，而无需中央权威。

挖矿被设计为一种去中心化的彩票。每个矿工可以通过创建一个**候选区块**来生成自己的彩票，该区块包含他们想要挖矿的新交易以及一些额外的数据字段。矿工将他们的候选区块输入到一个专门设计的算法中，该算法对数据进行**哈希**（或“散列”），生成与输入数据完全不同的输出。这个**哈希函数**对于相同的输入总是产生相同的输出——但没有人能预测新输入的输出会是什么样子，即使它与之前的输入只有微小的不同。如果哈希函数的输出与比特币协议确定的模板匹配，矿工就赢得了彩票，比特币用户将接受该区块及其交易为有效区块。如果输出不匹配模板，矿工对其候选区块进行微小的更改并再次尝试。截至撰写本文时，矿工在找到获胜组合之前需要尝试的候选区块数量约为168亿亿次。这也是哈希函数需要运行的次数。

然而，一旦找到获胜组合，任何人都可以通过运行哈希函数一次来验证区块的有效性。这使得有效区块的创建需要大量的工作，但验证只需要微不足道的工作量。简单的验证过程能够概率性地证明工作已经完成，因此生成该证明所需的数据——在这种情况下是区块——被称为**工作量证明（PoW）**。

交易被添加到新区块中，优先考虑费用率最高的交易以及其他一些标准。每个矿工在从网络接收到前一个区块后，立即开始挖矿一个新的候选交易区块，知道其他矿工赢得了这一轮彩票。他们立即创建一个新的候选区块，承诺前一个区块，填充交易，并开始计算候选区块的PoW。每个矿工在其候选区块中包含一个特殊交易，该交易支付给他们的比特币地址区块奖励加上候选区块中所有交易的交易费用总和。如果他们找到一个使候选区块成为有效区块的解决方案，他们将获得此奖励，前提是他们的成功区块被添加到全球区块链中，并且他们包含的奖励交易变得可花费。参与矿池的矿工已经设置了他们的软件，以创建将奖励分配给矿池地址的候选区块。从那里，奖励的一部分按矿工贡献的工作量比例分配给矿池成员。

Alice的交易被网络接收并包含在未验证交易池中。一旦被全节点验证，它就被包含在一个候选区块中。大约在Alice的钱包首次传输交易五分钟后，一名矿工找到了该区块的解决方案，并向网络宣布。在其他矿工验证了获胜区块后，他们开始新一轮的彩票以生成下一个区块。

包含Alice交易的获胜区块成为区块链的一部分。包含Alice交易的区块被计为该交易的**一次确认**。在包含Alice交易的区块通过网络传播后，创建一个包含不同版本Alice交易的替代区块（例如不支付Bob的交易）将需要执行与所有比特币矿工创建一个全新区块相同的工作量。当有多个替代区块可供选择时，比特币全节点选择具有最多总PoW的有效区块链，称为**最佳区块链**。为了让整个网络接受替代区块，需要在替代区块之上再挖出一个新区块。

这意味着矿工有一个选择。他们可以与Alice合作，创建一个替代交易，其中Alice支付Bob，也许Alice会向矿工支付她之前支付给Bob的一部分钱。这种不诚实的行为将需要他们花费创建两个新区块的努力。相反，诚实的矿工可以创建一个新区块，并获得他们包含在其中的所有交易的费用，加上区块补贴。通常情况下，不诚实地创建两个区块以获得少量额外支付的代价远不如诚实地创建一个新区块有利可图，这使得已确认的交易不太可能被故意更改。对于Bob来说，这意味着他可以开始相信Alice的支付是可靠的。

[TIP]
====
你可以查看包含[Alice交易](https://oreil.ly/7v_lH)的区块。
====

大约在包含Alice交易的区块广播19分钟后，另一名矿工挖出了一个新的区块。由于这个新区块建立在包含Alice交易的区块之上（为Alice的交易提供了两次确认），现在只有在挖出两个替代区块并在其上构建一个新的区块——总共需要挖出三个区块——Alice才能收回她发送给Bob的钱。每个建立在包含Alice交易的区块之上的新区块都算作一次额外的确认。随着区块的不断堆叠，逆转交易的难度越来越大，从而使Bob对Alice的支付安全性越来越有信心。

在<<block-alice1>>中，我们可以看到包含Alice交易的区块。其下方是数十万个区块，它们相互链接成一个区块链，一直追溯到区块#0，即**创世区块**。随着时间的推移，新区块的“高度”增加，整个链的计算难度也随之增加。按照惯例，任何具有超过六次确认的区块都被认为非常难以更改，因为重新计算六个区块（加上一个新的区块）需要巨大的计算量。我们将在<<mining>>中更详细地研究挖矿过程以及它如何建立信任。

[[block-alice1]]
### Alice的交易包含在一个区块中
![Alice's transaction included in a block](mbc3_0207.png)

### 花费交易

现在，Alice的交易已经作为区块的一部分嵌入到区块链中，它对所有比特币应用程序都是可见的。每个比特币全节点都可以独立验证交易的有效性和可花费性。全节点验证从比特币首次在区块中生成的那一刻起，直到它们到达Bob的地址为止的每一笔资金转移。轻量级客户端可以通过确认交易在区块链中并且在其后有多个区块被挖出来部分验证支付，从而提供矿工为其投入了大量努力的保证（见<<spv_nodes>>）。

Bob现在可以花费这笔交易和其他交易的输出。例如，Bob可以通过将Alice的播客支付转移到这些新所有者来支付承包商或供应商。随着Bob花费从Alice和其他客户那里收到的支付，他扩展了交易链。假设Bob向他的网页设计师Gopesh支付了新网页的费用。现在交易链将如<<block-alice2>>所示。

[[block-alice2]]
### Alice的交易作为从Joe到Gopesh的交易链的一部分
![Alice's transaction as part of a transaction chain](mbc3_0208.png)

在本章中，我们看到了交易如何构建一个将价值从一个所有者转移到另一个所有者的链。我们还跟踪了Alice的交易从她的钱包中创建的那一刻起，通过比特币网络，到矿工将其记录在区块链上的过程。在本书的其余部分，我们将深入研究钱包、地址、签名、交易、网络以及最终的挖矿背后的具体技术。