/* Basic button styling */
#multi-properties-modal a {
	display: inline-block;
	padding: var(--size-4-1) var(--size-4-2);
	text-decoration: none;
	background-color: var(--interactive-normal);
	box-shadow: var(--input-shadow);
	color: var(--text-normal);
	border-radius: 4px; /* Rounded corners */
	cursor: pointer;
	text-align: center;
	font-weight: var(--input-font-weight);
	font-size: var(--font-ui-small);
}

/* Hover and focus styles */
#multi-properties-modal a:hover,
#multi-properties-modal a:focus {
	outline: grey solid 2px;
}

/* Disabled style */
#multi-properties-modal a:disabled {
	background-color: #ccc; /* Disabled background color */
	border-color: #ccc; /* Disabled border color */
	color: #999; /* Disabled text color */
	cursor: not-allowed;
}
