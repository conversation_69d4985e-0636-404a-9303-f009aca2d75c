"use strict";var e,t,i,n=require("obsidian");!function(e){e[e.DEFINED=1]="DEFINED",e[e.INFERRED=2]="INFERRED"}(e||(e={})),function(e){e[e.PARENT=0]="PARENT",e[e.CHILD=1]="CHILD",e[e.LEFT=2]="LEFT",e[e.RIGHT=3]="RIGHT"}(t||(t={})),function(e){e[e.TO=1]="TO",e[e.FROM=2]="FROM",e[e.BOTH=3]="BOTH"}(i||(i={}));const s=/(?:\[([^[\]]+)\]\()((?:(?:ftp|https?|sftp|shttp|tftp):(?:\/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>"']|\([^\s()<>]*\))+(?:\([^\s()<>]*\)|[^\s`*!()\[\]{};:'".,<>?«»“”‘’]))\)|\b()((?:(?:ftp|https?|sftp|shttp|tftp):(?:\/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>"']|\([^\s()<>]*\))+(?:\([^\s()<>]*\)|[^\s`*!()\[\]{};:'".,<>?«»“”‘’]))\b/gi;class r{constructor(e){this.plugin=e,this.fileToUrlMap=new Map,this.fileUrlInverseMap=new Map,this.hosts=[],this.initalized=!1,this.app=e.app}async init(){const e=Date.now(),t=this.app.vault.getMarkdownFiles();for(const e of t)await this.parseFileURLs(e);this.registerFileEvents(),this.initalized=!0,console.log(`ExcaliBrain indexed ${this.fileUrlInverseMap.size} URLs from ${this.hosts.length} unique hosts in ${this.fileToUrlMap.size} of ${t.length} markdown files in ${((Date.now()-e)/1e3).toFixed(1)} seconds`)}getOrigin(e,t){try{return new URL(e).origin}catch(i){return console.log(`ExcaliBrain URLParser: Invalid URL ${e} in file ${t.path}`),":Unknown Origin:"}}async parseFileURLs(e){var t,i,r,a;if(!(e&&e instanceof n.TFile))return;let u="";try{u=await this.app.vault.cachedRead(e)}catch(t){return void console.log(`ExcaliBrain URLParser: Failed to read file ${e.path}`,t)}const o=new Map;let l;for(;null!==(l=s.exec(u));){const n=null!==(i=null!==(t=l[1])&&void 0!==t?t:l[3])&&void 0!==i?i:"";let s=null!==(a=null!==(r=l[2])&&void 0!==r?r:l[4])&&void 0!==a?a:"";if(s.match(/^(?:ftp|https?|sftp|shttp|tftp):/)||(s="https://"+s),o.has(s)){if(""!==n){const e=o.get(s);""===e.alias&&o.set(s,Object.assign(Object.assign({},e),{alias:n}))}}else{const t=this.getOrigin(s,e);o.set(s,{url:s,alias:n,origin:t}),this.updateInverseMap(s,e,t)}}const D=Array.from(o.values());D.length>0&&this.fileToUrlMap.set(e,D)}updateInverseMap(e,t,i){this.fileUrlInverseMap.has(e)||this.fileUrlInverseMap.set(e,{files:[],origin:i});const n=this.fileUrlInverseMap.get(e);n&&(n.files.includes(t)||n.files.push(t),this.hosts.includes(n.origin)||this.hosts.push(n.origin))}registerFileEvents(){const e=e=>{t(e),this.parseFileURLs(e)},t=e=>{const t=this.fileToUrlMap.get(e);this.fileToUrlMap.delete(e),t&&t.forEach((t=>{const i=this.fileUrlInverseMap.get(t.url);if(i){if(i.files=i.files.filter((t=>t!==e)),0===i.files.length)return this.fileUrlInverseMap.delete(t.url),void(this.hosts=this.hosts.filter((e=>e!==i.origin)));this.fileUrlInverseMap.set(t.url,i)}}))};this.plugin.registerEvent(this.app.vault.on("create",e)),this.plugin.registerEvent(this.app.vault.on("modify",e)),this.plugin.registerEvent(this.app.vault.on("delete",t))}}const a=(e,t,i)=>{const n=e.metadataCache.getFirstLinkpathDest(t,i);return n?n.path:t},u=(e,t)=>{var i,n,r,u,o;const l=new Set,D=e.matchAll(/[^[]*\[\[(?<wikiLink>[^#\]\|]*)[^\]]*]]|\[[^\]]*]\((?<mdLink>[^)]*)\)/g);let d,h;for(;!(d=D.next()).done;){if(null===(n=null===(i=null==d?void 0:d.value)||void 0===i?void 0:i.groups)||void 0===n?void 0:n.wikiLink){const e=a(app,d.value.groups.wikiLink,t.path);e&&l.add(e)}if(null===(u=null===(r=null==d?void 0:d.value)||void 0===r?void 0:r.groups)||void 0===u?void 0:u.mdLink){const e=a(app,decodeURIComponent(d.value.groups.mdLink),t.path);e&&l.add(e)}}for(;null!==(h=s.exec(e));)l.add(null!==(o=h[2])&&void 0!==o?o:h[4]);return Array.from(l)},o=(e,t,i)=>{var s;const r=new Set;if(t.values){if(t.hasOwnProperty("conversionAccuracy"))return[];const l=Array.from(t.values());let D;try{l.filter((e=>(null==e?void 0:e.type)&&("file"===e.type||"header"===e.type||"block"==e.type))).forEach((t=>{const n=a(e.app,t.path,i.path);n&&r.add(n)})),l.filter((e=>Boolean(null==e?void 0:e.values))).forEach((t=>{D=t;const n=Array.from(t.values());o(e,n,i).forEach((e=>r.add(e)))}))}catch(e){console.log(e),console.log(l),console.log(D),console.log(null==D?void 0:D.values)}const d=u(l.filter((e=>"string"==typeof e)).join(" "),i),h=null===(s=l.filter((e=>(null==e?void 0:e.values)&&"object"==typeof e&&"string"==typeof e.values[0])))||void 0===s?void 0:s.map((t=>a(e.app,t.values[0],i.path)));
//! currently there is an issue with case sensitivity. DataView retains case sensitivity of links for the front matter, but not the others
h||console.log({error:"objectLinks is undefined which is unexpected",errorLocation:"readDVField",field:t,file:i});const c=((e,t,i)=>{const s=new Set;return t.forEach((t=>{var i;(null===(i=null==t?void 0:t.hasOwnProperty)||void 0===i?void 0:i.call(t,"ts"))&&s.add(n.moment(t.ts).format(e.dailyNoteSettings.format))})),Array.from(s)})(e,l);return Array.from(r).concat(d).concat(null!=h?h:[]).concat(c)}if(t.path){const n=a(e.app,t.path,i.path);return n?[n]:[]}return"string"==typeof t?u(t,i):[]},l=(e,t,i)=>{const n=[],s=new Set;return i.forEach((i=>{const r=t[i];r&&!s.has(i)&&(s.add(i),o(e,r,t.file).forEach((e=>n.push({link:e,field:i}))))})),n},D=(e,t)=>{var i;const n=d(e,t);if(!n)return[null,null];if(e[t.primaryTagFieldLowerCase]){const s=null===(i=e[t.primaryTagFieldLowerCase].match(/#([^\s\])$"'\\]*)(?:$|\s)/g))||void 0===i?void 0:i.map((e=>e.trim())).filter((e=>t.tagStyleList.some((t=>e.startsWith(t))))),r=s&&s.length>0?s[0]:n[0];return[r,n.filter((e=>e!=r))]}return[n[0],n.slice(1)]},d=(e,t)=>{var i,n,s;return e?(null!==(s=null===(n=null===(i=e.file)||void 0===i?void 0:i.tags)||void 0===n?void 0:n.values)&&void 0!==s?s:[]).filter((e=>t.tagStyleList.some((t=>e.startsWith(t))))):null},h=(e,t)=>{const[i,n]=e;if(!i)return{};const s=t.tagNodeStyles[t.tagStyleList.filter((e=>i.startsWith(e)))[0]];if(s&&t.displayAllStylePrefixes){const e=Object.keys(t.tagNodeStyles).filter((e=>n.includes(e))),i=new Set;s.prefix&&i.add(s.prefix),null==e||e.map((e=>t.tagNodeStyles[e].prefix)).filter((e=>Boolean(e))).forEach((e=>i.add(e)));const r=Array.from(i).join("");return Object.assign(Object.assign({},s),{prefix:r})}return s},c=e=>{console.error(Object.assign({plugin:"ExcaliBrain"},e))};console.log.bind(window.console),console.log.bind(window.console);const g=e=>`data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(e.replaceAll("&nbsp;"," "))))}`,E=(e,t)=>{if(!e.DEVICE.isDesktop)return;let i=!0;if(!t){const n=e.targetView;if(!n)return;i=app.workspace.activeLeaf===n.leaf,t=n.ownerWindow}i&&(t.electronWindow.isAlwaysOnTop()||(t.electronWindow.setAlwaysOnTop(!0),setTimeout((()=>{t.electronWindow.setAlwaysOnTop(!1)}),500)))};function p(e){const t=e.lastIndexOf("/"),i=-1==t?e:e.substring(t+1);return{folderpath:n.normalizePath(e.substring(0,t)),filename:i,basename:i.replace(/\.[^/.]+$/,"")}}function f(e,t){return!!e&&["pdf","mp4","mp3","webm","md"].includes(e.extension)&&!t.isExcalidrawFile(e)}class F{constructor(e){this.pages=new Map,this.forEach=this.pages.forEach.bind(this.pages),this.app=e.app,this.plugin=e}add(e,t){this.pages.set(e,t)}has(e){return this.pages.has(e)}get(e){return this.pages.get(e)}getPages(){return Array.from(this.pages.values())}get size(){return this.pages.size}delete(e){const t=this.pages.get(e);t&&(t.neighbours.forEach(((t,i)=>{const n=this.pages.get(i);n&&(n.unlinkNeighbour(e),n.file||0!==n.neighbours.size||this.pages.delete(i))})),this.pages.delete(e))}addInferredParentChild(t,n){this.plugin.settings.inferAllLinksAsFriends?(n.addLeftFriend(t,e.INFERRED,i.TO),t.addLeftFriend(n,e.INFERRED,i.FROM)):this.plugin.settings.inverseInfer?(n.addChild(t,e.INFERRED,i.TO),t.addParent(n,e.INFERRED,i.FROM)):(n.addParent(t,e.INFERRED,i.TO),t.addChild(n,e.INFERRED,i.FROM))}addResolvedLinks(e){const t=this.app.metadataCache.resolvedLinks;Object.keys(t).forEach((i=>{if(e&&e.path!==i)return;const n=this.pages.get(i);Object.keys(t[i]).forEach((e=>{let t=this.pages.get(e);t||(t=this.pages.get(this.plugin.lowercasePathMap.get(e.toLowerCase()))),this.addInferredParentChild(n,t)}))}))}addPageURLs(){this.plugin.urlParser.fileToUrlMap.forEach(((t,n)=>{const s=this.get(n.path);s&&t.forEach((t=>{let n=this.get(t.url);n||(n=new A(this,t.url,null,this.plugin,!1,!1,t.alias,t.url),this.add(t.url,n)),""===t.alias||""!==n.name&&n.name!==t.url||(n.name=t.alias),this.addInferredParentChild(s,n);const r=this.get(t.origin);r&&(n.addParent(r,e.INFERRED,i.FROM),r.addChild(n,e.INFERRED,i.TO))}))}))}addUnresolvedLinks(e){if(e&&(e.isFolder||e.isTag))return;const t=this.app.metadataCache.unresolvedLinks;Object.keys(t).forEach((i=>{if(e&&e.path!==i)return;let n=this.pages.get(i);n&&i!==this.plugin.settings.excalibrainFilepath&&Object.keys(t[i]).forEach((e=>m(e,n,this.plugin,this)))}))}}const m=(t,n,s,r)=>{var a;const u=null!==(a=r.get(t))&&void 0!==a?a:new A(r,t,null,s);return s.settings.inferAllLinksAsFriends?(u.addLeftFriend(n,e.INFERRED,i.TO),n.addLeftFriend(u,e.INFERRED,i.FROM)):s.settings.inverseInfer?(u.addChild(n,e.INFERRED,i.TO),n.addParent(u,e.INFERRED,i.FROM)):(u.addParent(n,e.INFERRED,i.TO),n.addChild(u,e.INFERRED,i.FROM)),r.add(t,u),u},C={target:null,isHidden:!1,isParent:!1,isChild:!1,isLeftFriend:!1,isRightFriend:!1,isNextFriend:!1,isPreviousFriend:!1,direction:null},y=(e,t)=>e&&t?e+", "+t:e||t,S=(e,t)=>e?e===i.BOTH||e===t?e:i.BOTH:t,b=(t,i)=>t===e.DEFINED?e.DEFINED:t===e.INFERRED?i===e.DEFINED?e.DEFINED:e.INFERRED:i;class A{constructor(e,t,i,n,s=!1,r=!1,a,u=null){this.pages=e,this.path=t,this.file=i,this.plugin=n,this.isFolder=s,this.isTag=r,this.name=a,this.url=u,this.dvIndexReady=!1,a||(this.name=i?"md"===i.extension?i.basename:i.name:Boolean(u)?u:(e=>{const t=e.endsWith(".md"),i=e.substring(e.lastIndexOf("/")+1);return t?i.slice(0,-3):i})(t)),this.mtime=i?i.stat.mtime:null,this.neighbours=new Map}addDVFieldLinksToPage(){var t,n,s;if(this.dvIndexReady||this.isFolder||!this.pages)return;if(this.dvIndexReady=!0,this.isTag){const t=this.plugin.DVAPI.index.etags.getInverse("#"+this.path.substring(4));if(!t)return;return void t.forEach((t=>{const n=this.pages.get(t);n&&(this.neighbours.has(t)||(n.addParent(this,e.DEFINED,i.TO,"tag-tree"),this.addChild(n,e.DEFINED,i.FROM,"tag-tree")))}))}if(!this.file)return;const r=this.plugin.DVAPI.page(this.file.path);if(!r)return;if(this.dvPage=r,!r)return;[this.primaryStyleTag,this.styleTags]=D(this.dvPage,this.plugin.settings),(null!==(s=null===(n=null===(t=r.file)||void 0===t?void 0:t.etags)||void 0===n?void 0:n.values)&&void 0!==s?s:[]).forEach((t=>{t="tag:"+t.substring(1);const n=this.pages.get(t);n&&(this.addParent(n,e.DEFINED,i.FROM,"tag-tree"),n.addChild(this,e.DEFINED,i.TO,"tag-tree"))}));const a=this.plugin.hierarchyLowerCase.hidden;l(this.plugin,r,a).forEach((e=>{let t=this.pages.get(e.link);t||(t=m(e.link,this,this.plugin,this.plugin.pages)),this.addHidden(t)}));const u=this.plugin.hierarchyLowerCase.parents;l(this.plugin,r,u).forEach((t=>{let n=this.pages.get(t.link);n||(n=m(t.link,this,this.plugin,this.plugin.pages)),this.addParent(n,e.DEFINED,i.FROM,t.field),n.addChild(this,e.DEFINED,i.TO,t.field)}));const o=this.plugin.hierarchyLowerCase.children;l(this.plugin,r,o).forEach((t=>{let n=this.pages.get(t.link);n||(n=m(t.link,this,this.plugin,this.plugin.pages)),this.addChild(n,e.DEFINED,i.FROM,t.field),n.addParent(this,e.DEFINED,i.TO,t.field)}));const d=this.plugin.hierarchyLowerCase.leftFriends;l(this.plugin,r,d).forEach((t=>{let n=this.pages.get(t.link);n||(n=m(t.link,this,this.plugin,this.plugin.pages)),this.addLeftFriend(n,e.DEFINED,i.FROM,t.field),n.addLeftFriend(this,e.DEFINED,i.TO,t.field)}));const h=this.plugin.hierarchyLowerCase.rightFriends;l(this.plugin,r,h).forEach((t=>{let n=this.pages.get(t.link);n||(n=m(t.link,this,this.plugin,this.plugin.pages)),this.addRightFriend(n,e.DEFINED,i.FROM,t.field),n.addRightFriend(this,e.DEFINED,i.TO,t.field)}));const c=this.plugin.hierarchyLowerCase.previous;l(this.plugin,r,c).forEach((t=>{let n=this.pages.get(t.link);n||(n=m(t.link,this,this.plugin,this.plugin.pages)),this.addPreviousFriend(n,e.DEFINED,i.FROM,t.field),n.addNextFriend(this,e.DEFINED,i.TO,t.field)}));const g=this.plugin.hierarchyLowerCase.next;l(this.plugin,r,g).forEach((t=>{let n=this.pages.get(t.link);n||(n=m(t.link,this,this.plugin,this.plugin.pages)),this.addNextFriend(n,e.DEFINED,i.FROM,t.field),n.addPreviousFriend(this,e.DEFINED,i.TO,t.field)}))}getTitle(){var e,t,i,n,s,r;if(this.isURL)return this.plugin.settings.renderAlias&&this.name&&""!==this.name?this.name:this.url;const a=this.file&&this.plugin.settings.renderAlias&&null!==(n=null===(i=null===(t=null===(e=this.dvPage)||void 0===e?void 0:e.file)||void 0===t?void 0:t.aliases)||void 0===i?void 0:i.values)&&void 0!==n?n:[];let u=a.length>0?a[0]:this.name;if("[object Object]"===u&&(u=(null===(s=this.dvPage.aliases)||void 0===s?void 0:s[0])?Object.entries(this.dvPage.aliases[0])[0].join(": "):this.name),(null===(r=this.dvPage)||void 0===r?void 0:r.file)&&this.plugin.customNodeLabel)try{return this.plugin.customNodeLabel(this.dvPage,u)}catch(e){c({fn:this.getTitle,message:"Error executing cutomer node label function. The script is: "+this.plugin.settings.nodeTitleScript,data:this.dvPage,where:"Page.getTitle()",error:e})}return u}getRelationVector(t){return{pi:t.isParent&&t.parentType===e.INFERRED,pd:t.isParent&&t.parentType===e.DEFINED,ci:t.isChild&&t.childType===e.INFERRED,cd:t.isChild&&t.childType===e.DEFINED,lfd:!this.plugin.settings.inferAllLinksAsFriends&&t.isLeftFriend||this.plugin.settings.inferAllLinksAsFriends&&t.isLeftFriend&&!(t.parentType===e.DEFINED||t.childType===e.DEFINED||t.rightFriendType===e.DEFINED||t.nextFriendType===e.DEFINED||t.previousFriendType===e.DEFINED),rfd:t.isRightFriend&&t.rightFriendType===e.DEFINED,pfd:t.isPreviousFriend&&t.previousFriendType===e.DEFINED,nfd:t.isNextFriend&&t.nextFriendType===e.DEFINED}}getNeighbours(){this.addDVFieldLinksToPage(),this.neighbours.forEach((e=>e.target.addDVFieldLinksToPage()));const{showVirtualNodes:e,showAttachments:t,showFolderNodes:i,showTagNodes:n,showPageNodes:s,showURLNodes:r}=this.plugin.settings;return Array.from(this.neighbours).filter((a=>!a[1].isHidden&&(e||!a[1].target.isVirtual)&&(t||!a[1].target.isAttachment)&&(i||!a[1].target.isFolder)&&(n||!a[1].target.isTag)&&(s||a[1].target.isFolder||a[1].target.isTag||a[1].target.isAttachment||a[1].target.isURL)&&(r||!a[1].target.isURL)))}get isVirtual(){return null===this.file&&!this.isFolder&&!this.isTag&&!this.isURL}get isURL(){return Boolean(this.url)}get isAttachment(){return!!this.file&&"md"!==this.file.extension}get isMarkdown(){var e;return"md"===(null===(e=this.file)||void 0===e?void 0:e.extension)||!this.file}addHidden(t){if(t.path===this.plugin.settings.excalibrainFilepath||t.path===this.path)return;const i=this.neighbours.get(t.path);i?i.isHidden=!0:this.neighbours.set(t.path,Object.assign(Object.assign({},C),{target:t,isHidden:!0,parentType:e.DEFINED}))}addParent(e,t,i,n){var s;if(e.path===this.plugin.settings.excalibrainFilepath||e.path===this.path)return;const r=this.neighbours.get(e.path);if(r)return r.isParent=!0,r.parentType=b(r.parentType,t),n&&!(null===(s=r.parentTypeDefinition)||void 0===s?void 0:s.contains(n))&&(r.parentTypeDefinition=y(n,r.parentTypeDefinition)),void(r.direction=S(r.direction,i));this.neighbours.set(e.path,Object.assign(Object.assign({},C),{target:e,isParent:!0,parentType:t,parentTypeDefinition:n,direction:i}))}addChild(e,t,i,n){var s;if(e.path===this.plugin.settings.excalibrainFilepath||e.path===this.path)return;const r=this.neighbours.get(e.path);if(r)return r.isChild=!0,r.childType=b(r.childType,t),n&&!(null===(s=r.childTypeDefinition)||void 0===s?void 0:s.contains(n))&&(r.childTypeDefinition=y(n,r.childTypeDefinition)),void(r.direction=S(r.direction,i));this.neighbours.set(e.path,Object.assign(Object.assign({},C),{target:e,isChild:!0,childType:t,childTypeDefinition:n,direction:i}))}addLeftFriend(e,t,i,n){var s;if(e.path===this.plugin.settings.excalibrainFilepath||e.path===this.path)return;const r=this.neighbours.get(e.path);if(r)return r.isLeftFriend=!0,r.leftFriendType=b(r.leftFriendType,t),n&&!(null===(s=r.leftFriendTypeDefinition)||void 0===s?void 0:s.contains(n))&&(r.leftFriendTypeDefinition=y(n,r.leftFriendTypeDefinition)),void(r.direction=S(r.direction,i));this.neighbours.set(e.path,Object.assign(Object.assign({},C),{target:e,isLeftFriend:!0,leftFriendType:t,leftFriendTypeDefinition:n,direction:i}))}addRightFriend(e,t,i,n){var s;if(e.path===this.plugin.settings.excalibrainFilepath||e.path===this.path)return;const r=this.neighbours.get(e.path);if(r)return r.isRightFriend=!0,r.rightFriendType=b(r.rightFriendType,t),n&&!(null===(s=r.rightFriendTypeDefinition)||void 0===s?void 0:s.contains(n))&&(r.rightFriendTypeDefinition=y(n,r.rightFriendTypeDefinition)),void(r.direction=S(r.direction,i));this.neighbours.set(e.path,Object.assign(Object.assign({},C),{target:e,isRightFriend:!0,rightFriendType:t,rightFriendTypeDefinition:n,direction:i}))}addNextFriend(e,t,i,n){var s;if(e.path===this.plugin.settings.excalibrainFilepath||e.path===this.path)return;const r=this.neighbours.get(e.path);if(r)return r.isNextFriend=!0,r.nextFriendType=b(r.nextFriendType,t),n&&!(null===(s=r.nextFriendTypeDefinition)||void 0===s?void 0:s.contains(n))&&(r.nextFriendTypeDefinition=y(n,r.nextFriendTypeDefinition)),void(r.direction=S(r.direction,i));this.neighbours.set(e.path,Object.assign(Object.assign({},C),{target:e,isNextFriend:!0,nextFriendType:t,nextFriendTypeDefinition:n,direction:i}))}addPreviousFriend(e,t,i,n){var s;if(e.path===this.plugin.settings.excalibrainFilepath||e.path===this.path)return;const r=this.neighbours.get(e.path);if(r)return r.isPreviousFriend=!0,r.previousFriendType=b(r.previousFriendType,t),n&&!(null===(s=r.previousFriendTypeDefinition)||void 0===s?void 0:s.contains(n))&&(r.previousFriendTypeDefinition=y(n,r.previousFriendTypeDefinition)),void(r.direction=S(r.direction,i));this.neighbours.set(e.path,Object.assign(Object.assign({},C),{target:e,isPreviousFriend:!0,previousFriendType:t,previousFriendTypeDefinition:n,direction:i}))}unlinkNeighbour(e){this.neighbours.delete(e)}isChild(t){const{pi:i,pd:n,ci:s,cd:r,lfd:a,rfd:u,nfd:o,pfd:l}=this.getRelationVector(t);return!r||n||a||u||o||l?i||n||!s||r||a||u||o||l?null:e.INFERRED:e.DEFINED}childrenCount(){return this.getNeighbours().reduce(((t,i)=>{const n=this.isChild(i[1]);return t+(n&&this.plugin.settings.showInferredNodes||n===e.DEFINED?1:0)}),0)}hasChildren(){return this.getNeighbours().some((t=>{const i=this.isChild(t[1]);return i&&this.plugin.settings.showInferredNodes||i===e.DEFINED}))}getChildren(){return this.getNeighbours().filter((t=>{const i=this.isChild(t[1]);return i&&this.plugin.settings.showInferredNodes||i===e.DEFINED})).map((e=>({page:e[1].target,relationType:e[1].childType,typeDefinition:e[1].childTypeDefinition,linkDirection:e[1].direction})))}isParent(t){const{pi:i,pd:n,ci:s,cd:r,lfd:a,rfd:u,nfd:o,pfd:l}=this.getRelationVector(t);return r||!n||a||u||o||l?!i||n||s||r||a||u||o||l?null:e.INFERRED:e.DEFINED}parentCount(){return this.getNeighbours().reduce(((t,i)=>{const n=this.isParent(i[1]);return t+(n&&this.plugin.settings.showInferredNodes||n===e.DEFINED?1:0)}),0)}hasParents(){return this.getNeighbours().some((t=>{const i=this.isParent(t[1]);return i&&this.plugin.settings.showInferredNodes||i===e.DEFINED}))}getParents(){return this.getNeighbours().filter((t=>{const i=this.isParent(t[1]);return i&&this.plugin.settings.showInferredNodes||i===e.DEFINED})).map((e=>({page:e[1].target,relationType:e[1].parentType,typeDefinition:e[1].parentTypeDefinition,linkDirection:e[1].direction})))}isLeftFriend(t){const{pi:i,pd:n,ci:s,cd:r,lfd:a,rfd:u,nfd:o,pfd:l}=this.getRelationVector(t);return a?e.DEFINED:i&&!n&&s&&!r&&!a&&!u&&!o&&!l||[n,r,a,u,o,l].filter(Boolean).length>=2?e.INFERRED:null}leftFriendCount(){return this.getNeighbours().reduce(((t,i)=>{const n=this.isLeftFriend(i[1]);return t+(n&&this.plugin.settings.showInferredNodes||n===e.DEFINED?1:0)}),0)}hasLeftFriends(){return this.getNeighbours().some((t=>{const i=this.isLeftFriend(t[1]);return i&&this.plugin.settings.showInferredNodes||i===e.DEFINED}))}getLeftFriends(){return this.getNeighbours().filter((t=>{const i=this.isLeftFriend(t[1]);return i&&this.plugin.settings.showInferredNodes||i===e.DEFINED})).map((t=>{var i;return{page:t[1].target,relationType:null!==(i=t[1].leftFriendType)&&void 0!==i?i:t[1].parentType===e.DEFINED&&t[1].childType===e.DEFINED?e.DEFINED:e.INFERRED,typeDefinition:t[1].leftFriendTypeDefinition,linkDirection:t[1].direction}}))}isRightFriend(t){const{pd:i,cd:n,lfd:s,rfd:r,nfd:a,pfd:u}=this.getRelationVector(t);return i||n||s||!r||a||u?null:e.DEFINED}rightFriendCount(){return this.getNeighbours().reduce(((t,i)=>{const n=this.isRightFriend(i[1]);return t+(n&&this.plugin.settings.showInferredNodes||n===e.DEFINED?1:0)}),0)}hasRightFriends(){return this.getNeighbours().some((t=>{const i=this.isRightFriend(t[1]);return i&&this.plugin.settings.showInferredNodes||i===e.DEFINED}))}getRightFriends(){return this.getNeighbours().filter((t=>{const i=this.isRightFriend(t[1]);return i&&this.plugin.settings.showInferredNodes||i===e.DEFINED})).map((t=>({page:t[1].target,relationType:e.DEFINED,typeDefinition:t[1].rightFriendTypeDefinition,linkDirection:t[1].direction})))}isPreviousFriend(t){const{pd:i,cd:n,lfd:s,rfd:r,nfd:a,pfd:u}=this.getRelationVector(t);return i||n||s||r||!u||a?null:e.DEFINED}previousFriendCount(){return this.getNeighbours().reduce(((t,i)=>{const n=this.isPreviousFriend(i[1]);return t+(n&&this.plugin.settings.showInferredNodes||n===e.DEFINED?1:0)}),0)}hasPreviousFriends(){return this.getNeighbours().some((t=>{const i=this.isPreviousFriend(t[1]);return i&&this.plugin.settings.showInferredNodes||i===e.DEFINED}))}getPreviousFriends(){return this.getNeighbours().filter((t=>{const i=this.isPreviousFriend(t[1]);return i&&this.plugin.settings.showInferredNodes||i===e.DEFINED})).map((t=>({page:t[1].target,relationType:e.DEFINED,typeDefinition:t[1].previousFriendTypeDefinition,linkDirection:t[1].direction})))}isNextFriend(t){const{pd:i,cd:n,lfd:s,rfd:r,nfd:a,pfd:u}=this.getRelationVector(t);return i||n||s||r||u||!a?null:e.DEFINED}nextFriendCount(){return this.getNeighbours().reduce(((t,i)=>{const n=this.isNextFriend(i[1]);return t+(n&&this.plugin.settings.showInferredNodes||n===e.DEFINED?1:0)}),0)}hasNextFriends(){return this.getNeighbours().some((t=>{const i=this.isNextFriend(t[1]);return i&&this.plugin.settings.showInferredNodes||i===e.DEFINED}))}getNextFriends(){return this.getNeighbours().filter((t=>{const i=this.isNextFriend(t[1]);return i&&this.plugin.settings.showInferredNodes||i===e.DEFINED})).map((t=>({page:t[1].target,relationType:e.DEFINED,typeDefinition:t[1].nextFriendTypeDefinition,linkDirection:t[1].direction})))}getSiblings(){const t=new Map;return this.getParents().forEach((i=>i.page.getChildren().forEach((i=>{t.has(i.page.path)?i.relationType===e.DEFINED&&(t.get(i.page.path).relationType=e.DEFINED):t.set(i.page.path,i)})))),Array.from(t.values())}}var T="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},v={};Object.defineProperty(v,"__esModule",{value:!0});const N=e=>{try{return window.ExcalidrawAutomate.getAPI(e)}catch(e){return console.log({message:"Excalidraw not available",fn:N}),null}};var w=v.getEA=N;const L="ExcaliBrain",O="excalibrain",_="1.9.14",I=["base","inferred","file-tree","tag-tree"],k={strokeColor:"#696969FF",strokeWidth:1,strokeStyle:"solid",roughness:0,startArrowHead:"none",endArrowHead:"none",showLabel:!1,fontSize:10,fontFamily:3,textColor:"#ffffffff"},B={prefix:"",backgroundColor:"#00000066",fillStyle:"solid",textColor:"#ffffffff",borderColor:"#00000000",fontSize:20,fontFamily:3,maxLabelLength:30,roughness:0,strokeShaprness:"round",strokeWidth:1,strokeStyle:"solid",padding:10,gateRadius:5,gateOffset:15,gateStrokeColor:"#ffffffff",gateBackgroundColor:"#ffffffff",gateFillStyle:"solid"},x={exclusions:["excalidraw-font","excalidraw-font-color","excalidraw-css","excalidraw-plugin","excalidraw-link-brackets","excalidraw-link-prefix","excalidraw-border-color","excalidraw-default-mode","excalidraw-export-dark","excalidraw-export-transparent","excalidraw-export-svgpadding","excalidraw-export-pngscale","excalidraw-url-prefix","excalidraw-linkbutton-opacity","excalidraw-onload-script","kanban-plugin"],parents:["Parent","Parents","up","u","North","origin","inception","source","parent domain"],children:["Children","Child","down","d","South","leads to","contributes to","nurtures"],leftFriends:["Friends","Friend","Jump","Jumps","j","similar","supports","alternatives","advantages","pros"],rightFriends:["opposes","disadvantages","missing","cons"],previous:["Previous","Prev","West","w","Before"],next:["Next","n","East","e","After"],hidden:["hidden"]};var R={JSON_MALFORMED:"Malformed JSON",JSON_MISSING_KEYS:'JSON must have these 4 keys: "parents", "children", "friends", "nextFriends"',JSON_VALUES_NOT_STRING_ARRAYS:'Key values must be a non-empty array of strings. e.g. "parents": ["Parent", "Parents", "up"]',EXCALIBRAIN_FILE_NAME:"Filepath of Excalibrain drawing",EXCALIBRAIN_FILE_DESC:"⚠ This file will be overwritten by the plugin. If you stop the script and make changes to the graph, you should rename the file so your edits are preserved, because the next time you initiate ExcaliBrain your edits will be overwritten by the automatically generated ExcaliBrain graph.",INDEX_REFRESH_FREQ_NAME:"Index refresh frequency",INDEX_REFRESH_FREQ_DESC:"ExcaliBrain will update its index whenever you switch work panes, in case a file has changed in your Vault since the last index update. <br>This setting is thus only relevant when you are typing in a markdown editor (not switching files or panes) and you still want ExcaliBrain to update it's graph as you type. Because frequent background index updates can be resource intensive you have an option to increase the time interval for the index-updates which in turn will reduce the overhead on your system.",HIERARCHY_HEAD:"Ontology",HIERARCHY_DESC:"Ontology, in the the heart of Excalibrain. It is the context of our knowledge graph and refers to a system for organizing and defining the relationships between different nodes in the graph. It allows us to add semantic meaning to connections by associating them with specific Dataview fields, such as 'author' or 'chapter,' which influence the way nodes are positioned relative to each other on the graph. This approach enables a more structured and meaningful representation of information, making it easier to understand and explore the interconnectedness of concepts within the markdown documents in your Vault.<br><br>Enter the field names separated by comma (,) that you will use to define links in your graph.<br><br>You can also add fields to the ontology on the fly from the markdown editor by typing the new field (e.g.: 'Consits of::') and then calling one of the command palette actions to <code>Add dataview field to ontology as ...</code>, or by opening the context menu.",INFER_NAME:"Infer all implicit relationships as Friend",INFER_DESC:"<b>Toggle On:</b> All implicit links in the document are interpreted as FRIENDS.<br><b>Toggle Off:</b> The following logic is used:<ul><li>A forward link is inferred as a CHILD</li><li>A backlink is inferred as a PARENT</li><li>If files mutually link to each other, they are FRIENDS</li></ul>",REVERSE_NAME:"Reverse infer logic",REVERSE_DESC:"<b>Toggle ON:</b> Treat backlinks as children and forward links as parents.<br><b>Toggle OFF:</b> Treat backlinks as parents and forward links as children</b>",INVERSE_ARROW_DIRECTION_NAME:"Inverse arrow direction",INVERSE_ARROW_DIRECTION_DESC:"<b>Toggle ON:</b> Display arrow heads in the opposite direction of the link direction.<br><b>Toggle OFF:</b> Display arrow heads in the same direction as the link direction</b>",HIDDEN_NAME:"Hidden",HIDDEN_DESC:"Dataview or YAML fields that are hidden in the graph.",PARENTS_NAME:"Parents",CHILDREN_NAME:"Children",LEFT_FRIENDS_NAME:"Left-Side Friends",RIGHT_FRIENDS_NAME:"Right-Side Friends",PREVIOUS_NAME:"Previous (Friends)",NEXT_NAME:"Next (Friends)",EXCLUSIONS_NAME:"Excluded",EXCLUSIONS_DESC:"Dataview or YAML fields that are never used for ontology. These fields will not show up in the ontology suggester in the markdown editor, and will not be shown in the unassigned list.",UNASSIGNED_NAME:"Unassigned",UNASSIGNED_DESC:"Fields in your Vault that are neither excluded nor part of the defined ontology.",ONTOLOGY_SUGGESTER_NAME:"Ontology Suggester",ONTOLOGY_SUGGESTER_DESC:"Activate ontology suggester in the markdown editor. If enabled then typing the trigger sequence at the beginning of a paragraph will activate the suggester listing your ontology fields defined above.",ONTOLOGY_SUGGESTER_ALL_NAME:"Character sequence to trigger generic suggester. The Generic suggester will include all the ontology fields regardless of their direction.",ONTOLOGY_SUGGESTER_PARENT_NAME:"Character sequence to trigger parent suggester",ONTOLOGY_SUGGESTER_CHILD_NAME:"Character sequence to trigger child suggester",ONTOLOGY_SUGGESTER_LEFT_FRIEND_NAME:"Character sequence to trigger left-side friend suggester",ONTOLOGY_SUGGESTER_RIGHT_FRIEND_NAME:"Character sequence to trigger right-side friend suggester",ONTOLOGY_SUGGESTER_PREVIOUS_NAME:"Character sequence to trigger pr<u>e</u>vious (friend) suggester",ONTOLOGY_SUGGESTER_NEXT_NAME:"Character sequence to trigger next (friend) suggester",MID_SENTENCE_SUGGESTER_TRIGGER_NAME:"Mid-sentence dataview field suggester trigger",MID_SENTENCE_SUGGESTER_TRIGGER_DESC:"You may add fields mid-way in sentences following one of these two formats:<br><code>We met at [location:: [[XYZ restaurant]]] with [candidate:: [[John Doe]]]</code><br><code>We met at (location:: [[XYZ restaurant]]) with (candidate:: [[John Doe]])</code><br>If you set this trigger to e.g. <code>(</code> then typing <code>(:::</code> anywhere in the sentence will activate the suggester (assuming you are using the default generic suggester trigger commbination of <code>:::</code> - see setting above).<br>More info on inline fields: [DataView Help](https://blacksmithgu.github.io/obsidian-dataview/data-annotation/)",BOLD_FIELDS_NAME:"Add selected field with BOLD",BOLD_FIELDS_DESC:"Add selected field to text with bold typeface, i.e. (**field name**:: ) resulting in (<b>field name</b>:: )",DISPLAY_HEAD:"Display",COMPACT_VIEW_NAME:"Compact view",COMPACT_VIEW_DESC:"Controls the width of the graph by setting the maximum number of columns that are displayed for children and parent nodes.<br><b>Toggle ON:</b>The max number of child columns is 3, and the max number of parent columns is 2<br><b>Toggle OFF:</b>The max number of child columns is 5, max number of parent columns is 3",COMPACTING_FACTOR_NAME:"Compacting factor",COMPACTING_FACTOR_DESC:"The higher the number the more compact the graph will be. The lower the number the more spread out the graph will be.",MINLINKLENGTH_NAME:"Minimum center-friend distance",MINLINKLENGTH_DESC:"The minimum distance betweeen the central node and the friend nodes. The higher the number the furhter away the friends will be from the parent, leaving more space for the link ontology labels.",NODETITLE_SCRIPT_NAME:"Javascript for rendering node names",NODETITLE_SCRIPT_DESC:"Javascript code to render the node title. If you don't need it, just leave this field empty.<br>Function definition: <code>customNodeLabel: (dvPage: Literal, defaultName:string) => string</code><br>In your script you may refer to the dataview page object via the <code>dvPage</code> variable; and the default page name (filename or alias if available) via the <code>defaultName</code> variable. Use the following expression syntax:<br><code>dvPage['field 1']??defaultName</code> - this example will display the vaule of 'field 1' if available else the defaultName<br>⚠ Your line of code will be executed as is, make sure you add proper exception handling. Beyond <code>defaultName</code> and dataview field names, you also have the freedom to use any javascript function (e.g. <code>defaultName.toLowerCase()</code>) and any value that appears on the <code>dvPage</code> object, e.g. <code>dvPage.file.path</code>, etc. <br> To explore the dataview page object open Developer Console and enter the following code:<br><code>DataviewAPI.page('full filepath including extension')</code><br>Here's an example code that will display the value of the title field if available, else the filename, followed by the state (if available): <br><code>dvPage.title??defaultName & (dvPage.state ? ' - ' & dvPage.state : '')</code>",BEHAVIOR_HEAD:"Behavior",EXCLUDE_PATHLIST_NAME:"Filepaths to exclude",EXCLUDE_PATHLIST_DESC:"Enter comma-separated list of filepaths to exclude from the index.",STYLE_HEAD:"Styling",STYLE_DESC:"Styles are applied in sequence.<br><ol><li><b>Base</b> node style</li><li><b>Inferred</b> node style (only applied if the node is inferred)</li><li><b>Virtual</b> node style (only applied if the node is virtual)</li> <li><b>Central</b> node style (only applied if the node is in the center)</li><li><b>Sibling</b> node style (only applied if the node is a sibling)</li> <li><b>Attachment</b> node style (only applied if the node is an attachment)</li><li><b>Optional</b> tag based style</li></ol>All the attributes of the base node style must be specified. All other styles may have partial definitions. e.g. You may add a prefix and override the base node-background color in the tag-based style, override the font color in the inferred-node style and set the border stroke style to dotted in the virtual-node style.",CANVAS_BGCOLOR:"Canvas color",SHOW_FULL_TAG_PATH_NAME:"Display full tag name",SHOW_FULL_TAG_PATH_DESC:"<b>Toggle on:</b> will display the full tag e.g. #reading/books/sci-fi</br><b>Toggle off:</b> will display the current section of the tag, e.g. assuming the tag above, the graph will display only #reading, #books, #sci-fi respectively as you navigate the tag hierarchy.",SHOW_COUNT_NAME:"Display neighbor count",SHOW_COUNT_DESC:"Show the number of children, parents, friends next to the node gate",ALLOW_AUTOZOOM_NAME:"Autozoom",ALLOW_AUTOZOOM_DESC:"<b>Toggle ON:</b> Allow autozoom<br><b>Toggle OFF:</b> Disable autozoom",MAX_AUTOZOOM_NAME:"Maximum autozoom level [%]",MAX_AUTOZOOM_DESC:"Maximum zoom level to apply when autozoom is enabled. The higher the number the more zoomed in the graph will be.",ALLOW_AUTOFOCUS_ON_SEARCH_NAME:"Autofocus on search",ALLOW_AUTOFOCUS_ON_SEARCH_DESC:"<b>Toggle ON:</b> Allow autofocus on Search<br><b>Toggle OFF:</b> Disable autofocus",ALWAYS_ON_TOP_NAME:"Popout default 'always on top' behavior",ALWAYS_ON_TOP_DESC:"<b>Toggle ON:</b> When opening ExcaliBrain in a popout window, it will open with the new window in 'always on top' mode.<br><b>Toggle OFF:</b> The new window will not be in 'always on top' mode.",EMBEDDED_FRAME_WIDTH_NAME:"Embedded frame width",EMBEDDED_FRAME_HEIGHT_NAME:"Embedded frame height",TAGLIST_NAME:"Formatted tags",TAGLIST_DESC:"You can specify special formatting rules for Nodes based on tags. If there are multiple tags present in a note, and 'note type::' is not defined, the page the first matching a specification will be used. <br>Tagnames should start with <mark>#</mark> and may be incomplete. i.e. <code>#book</code> will match #books, #book/fiction, etc.<br>tAg NaMeS are CaSE sensiTIve<br>Enter a comma separated list of tags here, then select from the dropdown list to change the formatting.",NOTE_STYLE_TAG_NAME:"Note style tag field",NOTE_STYLE_TAG_DESC:"The dataview field to designate the primary tag for styling the page. This tag will be used as the base style. If other tags on the page also have defined styles and those style definitions include a prefix character those prefixes will be also added to the note title.",ALL_STYLE_PREFIXES_NAME:"Display all tags styles",ALL_STYLE_PREFIXES_DESC:"Display tag prefixes for all tags included in the note",MAX_ITEMCOUNT_DESC:"Maximum node count",MAX_ITEMCOUNT_NAME:"Maximum number of nodes to display in a given area of the layout.i.e. the maximum number of parents, the maximum number of children, the maximum number of friends, and the maximum number of siblings to display. If there are more items, they will be ommitted from the drawing.",NODESTYLE_INCLUDE_TOGGLE:"Toggle ON: override base node style for this attribute; OFF: apply base node style for this attribute",NODESTYLE_PREFIX_NAME:"Prefix",NODESTYLE_PREFIX_DESC:"Prefix character or emoji to display in front of the node's label",NODESTYLE_BGCOLOR:"Background color",NODESTYLE_BG_FILLSTYLE:"Background fill-style",NODESTYLE_TEXTCOLOR:"Text color",NODESTYLE_BORDERCOLOR:"Border color",NODESTYLE_FONTSIZE:"Font size",NODESTYLE_FONTFAMILY:"Font family",NODESTYLE_MAXLABELLENGTH_NAME:"Max label length",NODESTYLE_MAXLABELLENGTH_DESC:"Maximum number of characters to display from node title. Longer nodes will end with '...'",NODESTYLE_ROUGHNESS:"Stroke roughness",NODESTYLE_SHARPNESS:"Stroke sharpness",NODESTYLE_STROKEWIDTH:"Stroke width",NODESTYLE_STROKESTYLE:"Stroke style",NODESTYLE_RECTANGLEPADDING:"Padding of the node rectangle",NODESTYLE_GATE_RADIUS_NAME:"Gate radius",NODESTYLE_GATE_RADIUS_DESC:"The radius of the 3 small circles (alias: gates) serving as connection points for nodes",NODESTYLE_GATE_OFFSET_NAME:"Gate offset",NODESTYLE_GATE_OFFSET_DESC:"The offset to the left and right of the parent and child gates.",NODESTYLE_GATE_COLOR:"Gate border color",NODESTYLE_GATE_BGCOLOR_NAME:"Gate background color",NODESTYLE_GATE_BGCOLOR_DESC:"The fill color of the gate if it has children",NODESTYLE_GATE_FILLSTYLE:"Gate background fill-style",NODESTYLE_BASE:"Base node style",NODESTYLE_CENTRAL:"Style of central node",NODESTYLE_INFERRED:"Style of inferred nodes",NODESTYLE_URL:"Style of web page nodes",NODESTYLE_VIRTUAL:"Style of virtual nodes",NODESTYLE_SIBLING:"Style of sibling nodes",NODESTYLE_ATTACHMENT:"Style of attachment nodes",NODESTYLE_FOLDER:"Style of folder nodes",NODESTYLE_TAG:"Style of tag nodes",LINKSTYLE_COLOR:"Color",LINKSTYLE_WIDTH:"Width",LINKSTYLE_STROKE:"Stroke style",LINKSTYLE_ROUGHNESS:"Roughness",LINKSTYLE_ARROWSTART:"Start arrow head",LINKSTYLE_ARROWEND:"End arrow head",LINKSTYLE_SHOWLABEL:"Show label on link",LINKSTYLE_FONTSIZE:"Label font size",LINKSTYLE_FONTFAMILY:"Label font family",LINKSTYLE_BASE:"Base link style",LINKSTYLE_INFERRED:"Style of inferred link",LINKSTYLE_FOLDER:"Style of folder link",LINKSTYLE_TAG:"Style of tag link",DATAVIEW_NOT_FOUND:`Dataview plugin not found. Please install or enable Dataview then try restarting ${L}.`,DATAVIEW_UPGRADE:`Please upgrade Dataview to 0.5.31 or newer. Please update Dataview then try restarting ${L}.`,EXCALIDRAW_NOT_FOUND:`Excalidraw plugin not found. Please install or enable Excalidraw then try restarting ${L}.`,EXCALIDRAW_MINAPP_VERSION:`ExcaliBrain requires Excalidraw ${_} or higher. Please upgrade Excalidraw then try restarting ${L}.`,COMMAND_ADD_HIDDEN_FIELD:"Add dataview field to ontology as HIDDEN",COMMAND_ADD_PARENT_FIELD:"Add dataview field to ontology as PARENT",COMMAND_ADD_CHILD_FIELD:"Add dataview field to ontology as CHILD",COMMAND_ADD_LEFT_FRIEND_FIELD:"Add dataview field to ontology as LEFT-SIDE FRIEND",COMMAND_ADD_RIGHT_FRIEND_FIELD:"Add dataview field to ontology as RIGHT-SIDE FRIEND",COMMAND_ADD_PREVIOUS_FIELD:"Add dataview field to ontology as PREVIOUS",COMMAND_ADD_NEXT_FIELD:"Add dataview field to ontology as NEXT",COMMAND_ADD_ONTOLOGY_MODAL:"Add dataview field to ontology: Open Ontology Modal",COMMAND_START:"ExcaliBrain Normal",COMMAND_START_HOVER:"ExcaliBrain Hover-Editor",COMMAND_START_POPOUT:"ExcaliBrain Popout Window",COMMAND_STOP:"Stop ExcaliBrain",HOVER_EDITOR_ERROR:"I am sorry. Something went wrong. Most likely there was a version update to Hover Editor which I haven't addressed properly in ExcaliBrain. Normally I should get this fixed within few days",OPEN_DRAWING:"Save snapshot for editing",SEARCH_IN_VAULT:"Starred items will be listed in empty search.\nSearch for a file, a folder or a tag in your Vault.\nToggle folders and tags on/off to show in the list.",SHOW_HIDE_ATTACHMENTS:"Show/Hide attachments",SHOW_HIDE_VIRTUAL:"Show/Hide virtual nodes",SHOW_HIDE_INFERRED:"Show/Hide inferred relationships",SHOW_HIDE_ALIAS:"Show/Hide document alias",SHOW_HIDE_SIBLINGS:"Show/Hide siblings",SHOW_HIDE_POWERFILTER:"Enable/Disable Power Filter",SHOW_HIDE_EMBEDDEDCENTRAL:"Display central node as embedded frame",SHOW_HIDE_URLS:"Show/Hide URLs in central notes as graph nodes",SHOW_HIDE_FOLDER:"Show/Hide folder nodes",SHOW_HIDE_TAG:"Show/Hide tag nodes",SHOW_HIDE_PAGES:"Show/Hide page nodes (incl. defined, inferred, virtual and attachments)",PIN_LEAF:"Link ExcaliBrain to the most recent active leaf. When linked, ExcaliBrain will only monitor changes of the pinned leaf and open synchronized pages only on the pinned leaf.",NAVIGATE_BACK:"Navigate back",NAVIGATE_FORWARD:"Navigate forward",REFRESH_VIEW:"Refresh",AUTO_OPEN_DOCUMENT:"Synchronize navigation. When plugs are connected, changes to ExcaliBrain focus will be reflected in the active  Obsidian tab and vice versa.\n\nYou can link/unlink this button to the '<> Display central node as embedded frame' button in the ExcaliBrain settings.",TOGGLE_AUTOOPEN_WHEN_EMBED_TOGGLE_NAME:"Synchronize navigation on Embed toggle",TOGGLE_AUTOOPEN_WHEN_EMBED_TOGGLE_DESC:"<b>Toggle ON</b>: When you toggle the <i>'<kbd>&lt;&thinsp;&gt;</kbd> Display central node as embedded frame'</i> button, ExcaliBrain will automatically turn navigation synchronization on<br><b>Toggle OFF</b>: When you toggle the <i>'<kbd>&lt;&thinsp;&gt;</kbd> Display central node as embedded frame'</i> button, ExcaliBrain will not automatically turn navigation synchronization on",ADD_TO_ONTOLOGY_MODAL_DESC:"Select the direction of the ontology. If one of the buttons is highlighted, then the field is already part of the ontology in that direction."};const M={en:R,ar:{},cs:{},da:{},de:{JSON_MALFORMED:"Ungültiges JSON-Format",JSON_MISSING_KEYS:'JSON muss diese 4 Schlüssel enthalten: "parents", "children", "friends", "nextFriends"',JSON_VALUES_NOT_STRING_ARRAYS:'Die Schlüsselwerte müssen ein nicht-leeres Array von Zeichenketten sein. z.B. "parents": ["Eltern", "Elternteile", "hoch"]',EXCALIBRAIN_FILE_NAME:"Dateipfad der Excalibrain-Zeichnung",EXCALIBRAIN_FILE_DESC:"⚠ Diese Datei wird durch das Plugin überschrieben. Wenn Sie das Skript stoppen und Änderungen am Graphen vornehmen, sollten Sie die Datei umbenennen, damit Ihre Änderungen erhalten bleiben. Denn beim nächsten Start von ExcaliBrain werden Ihre Änderungen durch den automatisch generierten ExcaliBrain-Graphen überschrieben.",INDEX_REFRESH_FREQ_NAME:"Index-Aktualisierungsfrequenz",INDEX_REFRESH_FREQ_DESC:"ExcaliBrain wird seinen Index immer dann aktualisieren, wenn Sie zwischen Arbeitsbereichen wechseln, falls eine Datei in Ihrer Vault seit der letzten Index-Aktualisierung geändert wurde. <br>Diese Einstellung ist nur relevant, wenn Sie in einem Markdown-Editor tippen (keine Datei- oder Bereichswechsel vornehmen) und dennoch möchten, dass ExcaliBrain den Graphen während des Schreibens aktualisiert. Da häufige Hintergrund-Index-Updates ressourcenintensiv sein können, haben Sie die Möglichkeit, das Zeitintervall für die Index-Updates zu vergrößern, um die Auswirkungen auf Ihr System zu reduzieren.",HIERARCHY_HEAD:"Ontologie",HIERARCHY_DESC:"Geben Sie die Dataview-Feldnamen durch Kommas getrennt ein, die Sie verwenden möchten, um Link-Richtungen in Ihrem Graphen zu definieren.<br>Sie können auch Felder dynamisch von Ihrem Markdown-Editor aus zur Ontologie hinzufügen, indem Sie das neue Feld am Anfang eines Absatzes eingeben (z.B. 'Besteht aus::') und dann eine der Befehlspalettenaktionen aufrufen, um das Dataview-Feld als ELTERN, KIND, FREUND oder RECHTER FREUND zur Ontologie hinzuzufügen.",INFER_NAME:"Alle impliziten Beziehungen als Freund interpretieren",INFER_DESC:"<b>Ein:</b> Alle impliziten Verknüpfungen im Dokument werden als FREUNDE interpretiert.<br><b>Aus:</b> Die folgende Logik wird verwendet:<ul><li>Eine Vorwärtsverknüpfung wird als KIND interpretiert</li><li>Eine Rückverknüpfung wird als ELTERN interpretiert</li><li>Wenn Dateien sich gegenseitig verknüpfen, sind sie FREUNDE</li></ul>",REVERSE_NAME:"Logik für implizite Beziehungen umkehren",REVERSE_DESC:"<b>Ein:</b> Rückverknüpfungen als KINDER und Vorwärtsverknüpfungen als ELTERN behandeln.<br><b>Aus:</b> Rückverknüpfungen als ELTERN und Vorwärtsverknüpfungen als KINDER behandeln</b>",PARENTS_NAME:"Eltern",CHILDREN_NAME:"Kinder",LEFT_FRIENDS_NAME:"Freunde (links)",RIGHT_FRIENDS_NAME:"Freunde (rechts)",PREVIOUS_NAME:"Vorherige (Freunde)",NEXT_NAME:"Nächste (Freunde)",EXCLUSIONS_NAME:"Ausgeschlossen",EXCLUSIONS_DESC:"Dataview- oder YAML-Felder, die niemals für die Ontologie verwendet werden.",UNASSIGNED_NAME:"Nicht zugewiesen",UNASSIGNED_DESC:"Felder in Ihrer Vault, die weder ausgeschlossen noch Teil der definierten Ontologie sind.",ONTOLOGY_SUGGESTER_NAME:"Ontologie-Vorschläge",ONTOLOGY_SUGGESTER_DESC:"Aktivieren Sie den Ontologie-Vorschläger im Markdown-Editor. Wenn aktiviert, wird das Auslösemuster am Anfang eines Absatzes den Ontologie-Feldern angezeigt, die oben definiert sind.",ONTOLOGY_SUGGESTER_ALL_NAME:"Zeichenkette zum Auslösen des generischen Vorschlägers. Der generische Vorschläger enthält alle Ontologie-Felder unabhängig von ihrer Richtung.",ONTOLOGY_SUGGESTER_PARENT_NAME:"Zeichenkette zum Auslösen des Vorschlägers für ELTERN",ONTOLOGY_SUGGESTER_CHILD_NAME:"Zeichenkette zum Auslösen des Vorschlägers für KINDER",ONTOLOGY_SUGGESTER_LEFT_FRIEND_NAME:"Zeichenkette zum Auslösen des Vorschlägers für linke FREUNDE",ONTOLOGY_SUGGESTER_RIGHT_FRIEND_NAME:"Zeichenkette zum Auslösen des Vorschlägers für rechte FREUNDE",ONTOLOGY_SUGGESTER_PREVIOUS_NAME:"Zeichenkette zum Auslösen des Vorschlägers für vorherige (FREUNDE)",ONTOLOGY_SUGGESTER_NEXT_NAME:"Zeichenkette zum Auslösen des Vorschlägers für nächste (FREUNDE)",MID_SENTENCE_SUGGESTER_TRIGGER_NAME:"Auslösemuster für Dataview-Feldvorschläge inmitten von Sätzen",MID_SENTENCE_SUGGESTER_TRIGGER_DESC:"Sie können Felder inmitten von Sätzen hinzufügen, indem Sie einem der beiden Formate folgen:<br><code>We met at [location:: [[XYZ restaurant]]] with [candidate:: [[John Doe]]]</code><br><code>We met at (location:: [[XYZ restaurant]]) with (candidate:: [[John Doe]])</code><br>Wenn Sie das Auslösemuster z.B. auf <code>(</code> setzen, wird das Eingeben von <code>(:::</code> an einer beliebigen Stelle im Satz den Vorschläger aktivieren (sofern Sie das Standard-Auslösemuster für generische Vorschläge von <code>:::</code> verwenden - siehe Einstellung oben).<br>Weitere Informationen zu Inline-Feldern finden Sie unter [DataView-Hilfe](https://blacksmithgu.github.io/obsidian-dataview/data-annotation/)",BOLD_FIELDS_NAME:"Ausgewähltes Feld fett hervorheben",BOLD_FIELDS_DESC:"Fügt das ausgewählte Feld mit fetter Schriftart zum Text hinzu, z.B. (**Feldname**:: ) ergibt (<b>Feldname</b>:: )",DISPLAY_HEAD:"Darstellung",COMPACT_VIEW_NAME:"Kompakte Ansicht",COMPACT_VIEW_DESC:"Zeigt den Graphen in einer kompakten Ansicht an",EXCLUDE_PATHLIST_NAME:"Auszuschließende Dateipfade",EXCLUDE_PATHLIST_DESC:"Geben Sie eine kommagetrennte Liste von Dateipfaden ein, die vom Index ausgeschlossen werden sollen.",RENDERALIAS_NAME:"Alias anzeigen, wenn verfügbar",RENDERALIAS_DESC:"Zeigt den Seitennamen anstelle des Dateinamens an, wenn dieser in den Metadaten der Seite angegeben ist.",NODETITLE_SCRIPT_NAME:"Javascript zum Rendern von Knotennamen",NODETITLE_SCRIPT_DESC:"Javascript-Code zum Rendern des Knotentitels. Wenn Sie es nicht benötigen, lassen Sie dieses Feld einfach leer.<br>Funktionsdefinition: <code>customNodeLabel: (dvPage: Literal, defaultName:string) => string</code><br>In Ihrem Skript können Sie auf das Dataview-Objekt der Seite über die Variable <code>dvPage</code> und den Standardseitennamen (Dateiname oder Alias, sofern vorhanden) über die Variable <code>defaultName</code> zugreifen. Verwenden Sie die folgende Ausdruckssyntax:<br><code>dvPage['Feld 1']??defaultName</code> - dieses Beispiel zeigt den Wert von 'Feld 1', falls verfügbar, andernfalls den Standardnamen.<br>⚠ Ihr Code wird wie eingegeben ausgeführt, stellen Sie sicher, dass Sie eine ordnungsgemäße Fehlerbehandlung hinzufügen. Neben <code>defaultName</code> und Dataview-Feldnamen haben Sie auch die Freiheit, beliebige JavaScript-Funktionen zu verwenden (z.B. <code>defaultName.toLowerCase()</code>) und beliebige Werte, die im <code>dvPage</code>-Objekt erscheinen, z.B. <code>dvPage.file.path</code>, etc.<br> Um das Dataview-Objekt der Seite zu erkunden, öffnen Sie die Entwicklerkonsole und geben Sie folgenden Code ein:<br><code>DataviewAPI.page('vollständiger Dateipfad einschließlich Erweiterung')</code><br>Hier ist ein Beispielcode, der den Wert des Titelfelds anzeigt, sofern verfügbar, gefolgt vom Dateinamen und dem Status (sofern verfügbar): <br><code>dvPage.title??defaultName & (dvPage.state ? ' - ' & dvPage.state : '')</code>",SHOWINFERRED_NAME:"Implizite Beziehungen anzeigen",SHOWINFERRED_DESC:"<b>Ein:</b> Zeigt sowohl explizit definierte als auch implizierte Verknüpfungen an. Vorwärtsverknüpfungen sind Kinder, Rückverknüpfungen sind Eltern, wenn sich zwei Seiten gegenseitig beziehen, wird die Beziehung als Freundschaft interpretiert. Explizit definierte Beziehungen haben immer Vorrang.<br><b>Aus:</b> Zeigt nur explizit definierte Beziehungen an.",SHOWVIRTUAL_NAME:"Virtuelle Kindknoten anzeigen",SHOWVIRTUAL_DESC:"<b>Ein:</b> Zeigt nicht aufgelöste Verknüpfungen an.<br><b>Aus:</b> Zeigt nicht aufgelöste Verknüpfungen nicht an.",SHOWATTACHMENTS_NAME:"Anhänge einbeziehen",SHOWATTACHMENTS_DESC:"<b>Ein:</b> Zeigt alle Dateitypen im Graphen an. <br><b>Aus:</b> Zeigt nur Markdown-Dateien an.",STYLE_HEAD:"Stil",STYLE_DESC:"Stile werden in der Reihenfolge angewendet.<br><ol><li><b>Basis-</b>Knotenstil</li><li><b>Implizierter</b> Knotenstil (wird nur angewendet, wenn der Knoten impliziert ist)</li><li><b>Virtueller</b> Knotenstil (wird nur angewendet, wenn der Knoten virtuell ist)</li> <li><b>Zentraler</b> Knotenstil (wird nur angewendet, wenn der Knoten in der Mitte ist)</li><li><b>Geschwister</b> Knotenstil (wird nur angewendet, wenn der Knoten ein Geschwister ist)</li> <li><b>Anlagen</b> Knotenstil (wird nur angewendet, wenn der Knoten ein Anhang ist)</li><li><b>Optionaler</b> stichwortbasierter Stil</li></ol>Alle Attribute des Basis-Knotenstils müssen angegeben werden. Alle anderen Stile können teilweise definiert sein. Sie können beispielsweise einen Präfix hinzufügen und die Hintergrundfarbe des Basis-Knotens überschreiben oder die Schriftfarbe im implizierten Knotenstil ändern und den Randstrichstil im virtuellen Knotenstil auf gestrichelt setzen.",CANVAS_BGCOLOR:"Hintergrundfarbe der Leinwand",SHOW_FULL_TAG_PATH_NAME:"Vollständigen Tag-Namen anzeigen",SHOW_FULL_TAG_PATH_DESC:"<b>Ein:</b> Der vollständige Tag wird angezeigt, z.B. #lesen/bücher/sci-fi</br><b>Aus:</b> Die aktuelle Sektion des Tags wird angezeigt. Angenommen, der obige Tag lautet #lesen/bücher/sci-fi, dann werden im Graphen nur #lesen, #bücher, #sci-fi angezeigt, wenn Sie die Tag-Hierarchie durchlaufen.",SHOW_COUNT_NAME:"Anzahl der Nachbarn anzeigen",SHOW_COUNT_DESC:"Zeigt die Anzahl der Kinder, Eltern, Freunde neben dem Knoten-Gate an",ALLOW_AUTOZOOM_NAME:"Autozoom erlauben",ALLOW_AUTOZOOM_DESC:"<b>Ein:</b> Erlaubt Autozoom<br><b>Aus:</b> Deaktiviert Autozoom",ALLOW_AUTOFOCUS_ON_SEARCH_NAME:"Autofokus bei Suche erlauben",ALLOW_AUTOFOCUS_ON_SEARCH_DESC:"<b>Ein:</b> Erlaubt Autofokus bei Suche<br><b>Aus:</b> Deaktiviert Autofokus",ALWAYS_ON_TOP_NAME:"Standardmäßiges 'immer im Vordergrund' - Verhalten für Popout",ALWAYS_ON_TOP_DESC:"<b>Ein:</b> Wenn ExcaliBrain in einem Popout-Fenster geöffnet wird, wird es im 'immer im Vordergrund'-Modus geöffnet.<br><b>Aus:</b> Das neue Fenster wird nicht im 'immer im Vordergrund'-Modus geöffnet.",EMBEDDED_FRAME_WIDTH_NAME:"Breite des eingebetteten Rahmens",EMBEDDED_FRAME_HEIGHT_NAME:"Höhe des eingebetteten Rahmens",TAGLIST_NAME:"Formatierte Tags",TAGLIST_DESC:"Sie können spezielle Formatierungsregeln für Knoten basierend auf Tags festlegen. Wenn mehrere Tags auf der Seite vorhanden sind, wird die erste passende Spezifikation verwendet. <br>Tagnamen sollten mit einem <mark>#</mark> beginnen und können unvollständig sein. Zum Beispiel wird <code>#buch</code> zu #bücher, #buch/fiction usw. passen.<br>Geben Sie hier eine kommagetrennte Liste von Tags ein und wählen Sie aus der Dropdown-Liste, um die Formatierung zu ändern.",MAX_ITEMCOUNT_DESC:"Maximale Anzahl von Knoten",MAX_ITEMCOUNT_NAME:"Maximale Anzahl von Knoten, die in einem bestimmten Bereich der Anordnung angezeigt werden.D.h. die maximale Anzahl von Eltern, die maximale Anzahl von Kindern, die maximale Anzahl von Freunden und die maximale Anzahl von Geschwistern, die angezeigt werden sollen. Wenn es mehr Elemente gibt, werden sie aus der Zeichnung ausgelassen.",NODESTYLE_INCLUDE_TOGGLE:"Ein: Überschreibt den Basis-Knotenstil für dieses Attribut; Aus: Wendet den Basis-Knotenstil für dieses Attribut an",NODESTYLE_PREFIX_NAME:"Präfix",NODESTYLE_PREFIX_DESC:"Präfixzeichen oder Emoji, das vor dem Knotenlabel angezeigt wird",NODESTYLE_BGCOLOR:"Hintergrundfarbe",NODESTYLE_BG_FILLSTYLE:"Hintergrund-Füllstil",NODESTYLE_TEXTCOLOR:"Textfarbe",NODESTYLE_BORDERCOLOR:"Randfarbe",NODESTYLE_FONTSIZE:"Schriftgröße",NODESTYLE_FONTFAMILY:"Schriftart",NODESTYLE_MAXLABELLENGTH_NAME:"Maximale Label-Länge",NODESTYLE_MAXLABELLENGTH_DESC:"Maximale Anzahl von Zeichen, die vom Knotentitel angezeigt werden. Längere Knoten enden mit '...'",NODESTYLE_ROUGHNESS:"Strichrauheit",NODESTYLE_SHARPNESS:"Strichschärfe",NODESTYLE_STROKEWIDTH:"Strichstärke",NODESTYLE_STROKESTYLE:"Strichstil",NODESTYLE_RECTANGLEPADDING:"Polsterung des Knotenrechtecks",NODESTYLE_GATE_RADIUS_NAME:"Radius des Gates",NODESTYLE_GATE_RADIUS_DESC:"Der Radius der 3 kleinen Kreise (Alias: Gates), die als Verbindungspunkte für Knoten dienen",NODESTYLE_GATE_OFFSET_NAME:"Offset des Gates",NODESTYLE_GATE_OFFSET_DESC:"Der Abstand nach links und rechts von den Eltern- und Kind-Gates.",NODESTYLE_GATE_COLOR:"Randfarbe des Gates",NODESTYLE_GATE_BGCOLOR_NAME:"Hintergrundfarbe des Gates",NODESTYLE_GATE_BGCOLOR_DESC:"Die Füllfarbe des Gates, wenn es Kinder hat",NODESTYLE_GATE_FILLSTYLE:"Hintergrund-Füllstil des Gates",NODESTYLE_BASE:"Basis-Knotenstil",NODESTYLE_CENTRAL:"Stil des zentralen Knotens",NODESTYLE_INFERRED:"Stil der implizierten Knoten",NODESTYLE_VIRTUAL:"Stil der virtuellen Knoten",NODESTYLE_SIBLING:"Stil der Geschwister-Knoten",NODESTYLE_ATTACHMENT:"Stil der Anlagen-Knoten",NODESTYLE_FOLDER:"Stil der Ordner-Knoten",NODESTYLE_TAG:"Stil der Tag-Knoten",LINKSTYLE_COLOR:"Farbe",LINKSTYLE_WIDTH:"Breite",LINKSTYLE_STROKE:"Strichstil",LINKSTYLE_ROUGHNESS:"Strichrauheit",LINKSTYLE_ARROWSTART:"Pfeilspitze am Anfang",LINKSTYLE_ARROWEND:"Pfeilspitze am Ende",LINKSTYLE_SHOWLABEL:"Label auf Verbindung anzeigen",LINKSTYLE_FONTSIZE:"Label-Schriftgröße",LINKSTYLE_FONTFAMILY:"Label-Schriftart",LINKSTYLE_BASE:"Basis-Verbindungsstil",LINKSTYLE_INFERRED:"Stil der implizierten Verbindung",LINKSTYLE_FOLDER:"Stil der Ordner-Verbindung",LINKSTYLE_TAG:"Stil der Tag-Verbindung",DATAVIEW_NOT_FOUND:`Das Dataview-Plugin wurde nicht gefunden. Bitte installieren oder aktivieren Sie Dataview und starten Sie ${L} neu.`,DATAVIEW_UPGRADE:`Bitte aktualisieren Sie Dataview auf Version 0.5.31 oder höher. Bitte aktualisieren Sie Dataview und starten Sie ${L} neu.`,EXCALIDRAW_NOT_FOUND:`Das Excalidraw-Plugin wurde nicht gefunden. Bitte installieren oder aktivieren Sie Excalidraw und starten Sie ${L} neu.`,EXCALIDRAW_MINAPP_VERSION:`ExcaliBrain erfordert Excalidraw Version ${_} oder höher. Bitte aktualisieren Sie Excalidraw und starten Sie ${L} neu.`,COMMAND_ADD_PARENT_FIELD:"Dataview-Feld der Ontologie als ELTERN hinzufügen",COMMAND_ADD_CHILD_FIELD:"Dataview-Feld der Ontologie als KIND hinzufügen",COMMAND_ADD_LEFT_FRIEND_FIELD:"Dataview-Feld der Ontologie als LINKSFREUND hinzufügen",COMMAND_ADD_RIGHT_FRIEND_FIELD:"Dataview-Feld der Ontologie als RECHTSFREUND hinzufügen",COMMAND_ADD_PREVIOUS_FIELD:"Dataview-Feld der Ontologie als VORHERIGE hinzufügen",COMMAND_ADD_NEXT_FIELD:"Dataview-Feld der Ontologie als NÄCHSTE hinzufügen",COMMAND_START:"ExcaliBrain Normal starten",COMMAND_START_HOVER:"ExcaliBrain Hover-Editor starten",COMMAND_START_POPOUT:"ExcaliBrain Popout-Fenster starten",COMMAND_STOP:"ExcaliBrain beenden",HOVER_EDITOR_ERROR:"Entschuldigung. Etwas ist schiefgegangen. Wahrscheinlich gab es ein Versionsupdate von Hover Editor, das ich in ExcaliBrain nicht richtig berücksichtigt habe. Normalerweise werde ich das innerhalb weniger Tage beheben.",OPEN_DRAWING:"Snapshot zum Bearbeiten speichern",SEARCH_IN_VAULT:"Markierte Elemente werden in der leeren Suche aufgelistet.\nSuchen Sie nach einer Datei, einem Ordner oder einem Tag in Ihrem Tresor.\nSchalten Sie Ordner und Tags ein/aus, um sie in der Liste anzuzeigen.",SHOW_HIDE_ATTACHMENTS:"Anlagen anzeigen/ausblenden",SHOW_HIDE_VIRTUAL:"Virtuelle Knoten anzeigen/ausblenden",SHOW_HIDE_INFERRED:"Implizierte Knoten anzeigen/ausblenden",SHOW_HIDE_ALIAS:"Dokument-Alias anzeigen/ausblenden",SHOW_HIDE_SIBLINGS:"Geschwister anzeigen/ausblenden",SHOW_HIDE_EMBEDDEDCENTRAL:"Zentralen Knoten als eingebetteten Rahmen anzeigen",SHOW_HIDE_FOLDER:"Ordner-Knoten anzeigen/ausblenden",SHOW_HIDE_TAG:"Tag-Knoten anzeigen/ausblenden",SHOW_HIDE_PAGES:"Seiten-Knoten anzeigen/ausblenden (einschließlich definierter, implizierter, virtueller und Anlagen)",PIN_LEAF:"ExcaliBrain mit dem zuletzt aktiven Blatt verbinden"},"en-gb":{},es:{JSON_MALFORMED:"JSON mal formado",JSON_MISSING_KEYS:'JSON debe contener estas 4 claves: "parents", "children", "friends", "nextFriends"',JSON_VALUES_NOT_STRING_ARRAYS:'Los valores de las claves deben ser una matriz no vacía de cadenas. Ejemplo: "parents": ["Padre", "Padres", "arriba"]',EXCALIBRAIN_FILE_NAME:"Ruta del archivo de dibujo de Excalibrain",EXCALIBRAIN_FILE_DESC:"⚠ Este archivo será sobrescrito por el complemento. Si detienes el script y realizas cambios en el grafo, debes renombrar el archivo para conservar tus ediciones, porque la próxima vez que inicies ExcaliBrain, tus ediciones serán sobrescritas por el grafo generado automáticamente.",INDEX_REFRESH_FREQ_NAME:"Frecuencia de actualización del índice",INDEX_REFRESH_FREQ_DESC:"ExcaliBrain actualizará su índice cada vez que cambies los paneles de trabajo, en caso de que un archivo haya cambiado en tu Vault desde la última actualización del índice. <br>Esta configuración solo es relevante cuando estás escribiendo en un editor de markdown (sin cambiar de archivo o paneles) y aún deseas que ExcaliBrain actualice su grafo mientras escribes. Debido a que las actualizaciones frecuentes del índice en segundo plano pueden ser intensivas en recursos, tienes la opción de aumentar el intervalo de tiempo para las actualizaciones del índice, lo que reducirá la carga en tu sistema.",HIERARCHY_HEAD:"Ontología",HIERARCHY_DESC:"Ingresa los nombres de campo de Dataview separados por comas (,) que usarás para definir las direcciones de los enlaces en tu grafo.<br>También puedes agregar campos a la ontología sobre la marcha desde el editor de markdown escribiendo el nuevo campo al comienzo de un párrafo (por ejemplo, 'Consta de::') y luego llamando a una de las acciones del menú de comandos para <code>Agregar campo de Dataview a la ontología como PADRE</code>, o <code>como HIJO</code>, <code>como AMIGO</code>, o <code>como AMIGO DERECHO</code>",INFER_NAME:"Inferir todas las relaciones implícitas como Amigos",INFER_DESC:"<b>Activado:</b> Todos los enlaces implícitos en el documento se interpretan como AMIGOS.<br><b>Desactivado:</b> Se utiliza la siguiente lógica:<ul><li>Un enlace hacia adelante se infiere como HIJO</li><li>Un enlace de retroceso se infiere como PADRE</li><li>Si los archivos se vinculan mutuamente, son AMIGOS</li></ul>",REVERSE_NAME:"Invertir lógica de inferencia",REVERSE_DESC:"<b>Activado:</b> Tratar los enlaces de retroceso como hijos y los enlaces hacia adelante como padres.<br><b>Desactivado:</b> Tratar los enlaces de retroceso como padres y los enlaces hacia adelante como hijos</b>",PARENTS_NAME:"Padres",CHILDREN_NAME:"Hijos",LEFT_FRIENDS_NAME:"Amigos del Lado Izquierdo",RIGHT_FRIENDS_NAME:"Amigos del Lado Derecho",PREVIOUS_NAME:"Anterior (Amigos)",NEXT_NAME:"Siguiente (Amigos)",EXCLUSIONS_NAME:"Excluidos",EXCLUSIONS_DESC:"Campos de Dataview o YAML que nunca se utilizan para la ontología",UNASSIGNED_NAME:"Sin Asignar",UNASSIGNED_DESC:"Campos en tu Vault que no están excluidos ni forman parte de la ontología definida.",ONTOLOGY_SUGGESTER_NAME:"Sugeridor de Ontología",ONTOLOGY_SUGGESTER_DESC:"Activa el sugeridor de ontología en el editor de markdown. Si está habilitado, al escribir la secuencia de activación al comienzo de un párrafo activará el sugeridor que muestra los campos de ontología definidos anteriormente.",ONTOLOGY_SUGGESTER_ALL_NAME:"Secuencia de caracteres para activar el sugeridor genérico. El sugeridor genérico incluirá todos los campos de ontología sin importar su dirección.",ONTOLOGY_SUGGESTER_PARENT_NAME:"Secuencia de caracteres para activar el sugeridor de padres",ONTOLOGY_SUGGESTER_CHILD_NAME:"Secuencia de caracteres para activar el sugeridor de hijos",ONTOLOGY_SUGGESTER_LEFT_FRIEND_NAME:"Secuencia de caracteres para activar el sugeridor de amigos del lado izquierdo",ONTOLOGY_SUGGESTER_RIGHT_FRIEND_NAME:"Secuencia de caracteres para activar el sugeridor de amigos del lado derecho",ONTOLOGY_SUGGESTER_PREVIOUS_NAME:"Secuencia de caracteres para activar el sugeridor de anter<u>ior</u> (amigos)",ONTOLOGY_SUGGESTER_NEXT_NAME:"Secuencia de caracteres para activar el sugeridor de sigu<u>iente</u> (amigos)",MID_SENTENCE_SUGGESTER_TRIGGER_NAME:"Activador de sugeridor de campos de Dataview en medio de oraciones",MID_SENTENCE_SUGGESTER_TRIGGER_DESC:"Puedes agregar campos a mitad de las oraciones siguiendo uno de estos dos formatos:<br><code>Nos encontramos en [lugar:: [[Restaurante XYZ]]] con [candidato:: [[John Doe]]]</code><br><code>Nos encontramos en (lugar:: [[Restaurante XYZ]]) con (candidato:: [[John Doe]])</code><br>Si configuras este activador como por ejemplo <code>(</code>, entonces al escribir <code>(:::</code> en cualquier parte de la oración se activará el sugeridor (asumiendo que estás utilizando la combinación predeterminada de activador de sugeridor <code>:::</code> - ver configuración anterior).<br>Más información sobre campos en línea: [Ayuda de DataView](https://blacksmithgu.github.io/obsidian-dataview/data-annotation/)",BOLD_FIELDS_NAME:"Agregar campo seleccionado en negrita",BOLD_FIELDS_DESC:"Agregar el campo seleccionado al texto en negrita, es decir, (**nombre del campo**:: ) resultando en (<b>nombre del campo</b>:: )",DISPLAY_HEAD:"Visualización",COMPACT_VIEW_NAME:"Vista compacta",COMPACT_VIEW_DESC:"Mostrar el grafo en una vista compacta",EXCLUDE_PATHLIST_NAME:"Rutas de archivos a excluir",EXCLUDE_PATHLIST_DESC:"Ingresa una lista de rutas de archivos separadas por comas que se deben excluir del índice.",RENDERALIAS_NAME:"Mostrar alias si está disponible",RENDERALIAS_DESC:"Muestra el alias de la página en lugar del nombre de archivo si está especificado en el front matter de la página.",NODETITLE_SCRIPT_NAME:"Javascript para renderizar nombres de nodos",NODETITLE_SCRIPT_DESC:"Código Javascript para renderizar el título del nodo. Si no lo necesitas, simplemente deja este campo vacío.<br>Definición de la función: <code>customNodeLabel: (dvPage: Literal, defaultName:string) => string</code><br>En tu script, puedes referirte al objeto de página Dataview a través de la variable <code>dvPage</code>; y el nombre de página predeterminado (nombre de archivo o alias si está disponible) a través de la variable <code>defaultName</code>. Utiliza la siguiente sintaxis de expresión:<br><code>dvPage['campo 1']??defaultName</code> - este ejemplo mostrará el valor de 'campo 1' si está disponible, de lo contrario mostrará defaultName<br>⚠ Tu línea de código se ejecutará tal como está, asegúrate de agregar un manejo adecuado de excepciones. Además de <code>defaultName</code> y los nombres de campo de dataview, también tienes la libertad de usar cualquier función de javascript (por ejemplo, <code>defaultName.toLowerCase()</code>) y cualquier valor que aparezca en el objeto <code>dvPage</code>, como <code>dvPage.file.path</code>, etc. <br> Para explorar el objeto de página de Dataview, abre la Consola de Desarrollador e ingresa el siguiente código:<br><code>DataviewAPI.page('ruta completa del archivo incluyendo extensión')</code><br>Aquí tienes un ejemplo de código que mostrará el valor del campo 'title' si está disponible, de lo contrario mostrará el nombre de archivo, seguido del estado (si está disponible): <br><code>dvPage.title??defaultName & (dvPage.state ? ' - ' & dvPage.state : '')</code>",SHOWINFERRED_NAME:"Mostrar relaciones inferidas",SHOWINFERRED_DESC:"<b>Activado:</b> Mostrar tanto los enlaces explícitamente definidos como los inferidos. Los enlaces hacia adelante son hijos, los enlaces de retroceso son padres, si dos páginas se refieren mutuamente, se infiere que existe una amistad. Las relaciones definidas explícitamente siempre tienen prioridad.<br><b>Desactivado:</b> Mostrar solo relaciones definidas explícitamente.",SHOWVIRTUAL_NAME:"Mostrar nodos virtuales hijos",SHOWVIRTUAL_DESC:"<b>Activado:</b> Mostrar enlaces no resueltos.<br><b>Desactivado:</b> No mostrar enlaces no resueltos.",SHOWATTACHMENTS_NAME:"Incluir adjuntos",SHOWATTACHMENTS_DESC:"<b>Activado:</b> Mostrar todo tipo de archivos en el grafo. <br><b>Desactivado:</b> Mostrar solo archivos de markdown.",STYLE_HEAD:"Estilo",STYLE_DESC:"Los estilos se aplican en secuencia.<br><ol><li>Estilo de nodo <b>base</b></li><li>Estilo de nodo <b>inferido</b> (solo se aplica si el nodo es inferido)</li><li>Estilo de nodo <b>virtual</b> (solo se aplica si el nodo es virtual)</li> <li>Estilo de nodo <b>central</b> (solo se aplica si el nodo está en el centro)</li><li>Estilo de nodo <b>hermano</b> (solo se aplica si el nodo es un hermano)</li> <li>Estilo de nodo <b>adjunto</b> (solo se aplica si el nodo es un adjunto)</li><li>Estilo basado en etiquetas <b>opcional</b></li></ol>Todos los atributos del estilo de nodo base deben especificarse. Todos los demás estilos pueden tener definiciones parciales. Por ejemplo, puedes agregar un prefijo y sobrescribir el color de fondo del nodo en el estilo basado en etiquetas, sobrescribir el color de fuente en el estilo de nodo inferido y establecer el estilo del borde como punteado en el estilo de nodo virtual.",CANVAS_BGCOLOR:"Color del lienzo",SHOW_FULL_TAG_PATH_NAME:"Mostrar nombre completo de la etiqueta",SHOW_FULL_TAG_PATH_DESC:"<b>Activado:</b> mostrará el nombre completo de la etiqueta, por ejemplo, #lectura/libros/ciencia ficción</br><b>Desactivado:</b> mostrará la sección actual de la etiqueta, por ejemplo, asumiendo la etiqueta anterior, el grafo mostrará solo #lectura, #libros, #ciencia ficción respectivamente a medida que navegas la jerarquía de etiquetas.",SHOW_COUNT_NAME:"Mostrar conteo de vecinos",SHOW_COUNT_DESC:"Mostrar el número de hijos, padres, amigos junto a la puerta del nodo",ALLOW_AUTOZOOM_NAME:"Autozoom",ALLOW_AUTOZOOM_DESC:"<b>Activado:</b> Permitir autozoom<br><b>Desactivado:</b> Deshabilitar autozoom",ALLOW_AUTOFOCUS_ON_SEARCH_NAME:"Autofocus en búsqueda",ALLOW_AUTOFOCUS_ON_SEARCH_DESC:"<b>Activado:</b> Permitir enfoque automático en la búsqueda<br><b>Desactivado:</b> Deshabilitar enfoque automático",ALWAYS_ON_TOP_NAME:"Comportamiento predeterminado de 'siempre arriba' en ventana emergente",ALWAYS_ON_TOP_DESC:"<b>Activado:</b> Cuando se abre ExcaliBrain en una ventana emergente, se abrirá con la nueva ventana en el modo 'siempre arriba'.<br><b>Desactivado:</b> La nueva ventana no estará en el modo 'siempre arriba'.",EMBEDDED_FRAME_WIDTH_NAME:"Ancho del marco incorporado",EMBEDDED_FRAME_HEIGHT_NAME:"Altura del marco incorporado",TAGLIST_NAME:"Etiquetas formateadas",TAGLIST_DESC:"Puedes especificar reglas de formato especial para nodos basadas en etiquetas. Si hay varias etiquetas en la página, se utilizará la primera que coincida con una especificación. <br>Los nombres de las etiquetas deben comenzar con <mark>#</mark> y pueden ser incompletas. Es decir, <code>#libro</code> coincidirá con #libros, #libro/ficción, etc.<br>Ingresa una lista separada por comas de etiquetas aquí, luego selecciona en la lista desplegable para cambiar el formato.",MAX_ITEMCOUNT_DESC:"Recuento máximo de nodos",MAX_ITEMCOUNT_NAME:"Número máximo de nodos para mostrar en un área determinada del diseño.es decir, el número máximo de padres, el número máximo de hijos, el número máximo de amigos y el número máximo de hermanos para mostrar. Si hay más elementos, se omitirán del dibujo.",NODESTYLE_INCLUDE_TOGGLE:"Activar: sobrescribir estilo base del nodo para este atributo; Desactivar: aplicar estilo base del nodo para este atributo",NODESTYLE_PREFIX_NAME:"Prefijo",NODESTYLE_PREFIX_DESC:"Carácter o emoji de prefijo que se mostrará delante de la etiqueta del nodo",NODESTYLE_BGCOLOR:"Color de fondo",NODESTYLE_BG_FILLSTYLE:"Estilo de relleno de fondo",NODESTYLE_TEXTCOLOR:"Color de texto",NODESTYLE_BORDERCOLOR:"Color del borde",NODESTYLE_FONTSIZE:"Tamaño de fuente",NODESTYLE_FONTFAMILY:"Fuente",NODESTYLE_MAXLABELLENGTH_NAME:"Longitud máxima de etiqueta",NODESTYLE_MAXLABELLENGTH_DESC:"Número máximo de caracteres a mostrar del título del nodo. Los nodos más largos se truncarán con '...'",NODESTYLE_ROUGHNESS:"Rugosidad del trazo",NODESTYLE_SHARPNESS:"Nitidez del trazo",NODESTYLE_STROKEWIDTH:"Ancho del trazo",NODESTYLE_STROKESTYLE:"Estilo del trazo",NODESTYLE_RECTANGLEPADDING:"Relleno del rectángulo del nodo",NODESTYLE_GATE_RADIUS_NAME:"Radio de la puerta",NODESTYLE_GATE_RADIUS_DESC:"El radio de los 3 pequeños círculos (alias: puertas) que sirven como puntos de conexión para los nodos",NODESTYLE_GATE_OFFSET_NAME:"Desplazamiento de la puerta",NODESTYLE_GATE_OFFSET_DESC:"El desplazamiento a la izquierda y derecha de las puertas de los padres e hijos.",NODESTYLE_GATE_COLOR:"Color del borde de la puerta",NODESTYLE_GATE_BGCOLOR_NAME:"Color de fondo de la puerta",NODESTYLE_GATE_BGCOLOR_DESC:"El color de relleno de la puerta si tiene hijos",NODESTYLE_GATE_FILLSTYLE:"Estilo de relleno de fondo de la puerta",NODESTYLE_BASE:"Estilo base del nodo",NODESTYLE_CENTRAL:"Estilo del nodo central",NODESTYLE_INFERRED:"Estilo de los nodos inferidos",NODESTYLE_VIRTUAL:"Estilo de los nodos virtuales",NODESTYLE_SIBLING:"Estilo de los nodos hermanos",NODESTYLE_ATTACHMENT:"Estilo de los nodos de adjunto",NODESTYLE_FOLDER:"Estilo de los nodos de carpeta",NODESTYLE_TAG:"Estilo de los nodos de etiqueta",LINKSTYLE_COLOR:"Color",LINKSTYLE_WIDTH:"Ancho",LINKSTYLE_STROKE:"Estilo del trazo",LINKSTYLE_ROUGHNESS:"Rugosidad",LINKSTYLE_ARROWSTART:"Cabeza de flecha de inicio",LINKSTYLE_ARROWEND:"Cabeza de flecha de fin",LINKSTYLE_SHOWLABEL:"Mostrar etiqueta en el enlace",LINKSTYLE_FONTSIZE:"Tamaño de fuente de la etiqueta",LINKSTYLE_FONTFAMILY:"Fuente de la etiqueta",LINKSTYLE_BASE:"Estilo base del enlace",LINKSTYLE_INFERRED:"Estilo del enlace inferido",LINKSTYLE_FOLDER:"Estilo del enlace de carpeta",LINKSTYLE_TAG:"Estilo del enlace de etiqueta",DATAVIEW_NOT_FOUND:`Plugin Dataview no encontrado. Por favor, instala o habilita Dataview y luego intenta reiniciar ${L}.`,DATAVIEW_UPGRADE:`Por favor, actualiza Dataview a la versión 0.5.31 o superior. Actualiza Dataview y luego intenta reiniciar ${L}.`,EXCALIDRAW_NOT_FOUND:`Plugin Excalidraw no encontrado. Por favor, instala o habilita Excalidraw y luego intenta reiniciar ${L}.`,EXCALIDRAW_MINAPP_VERSION:`ExcaliBrain requiere Excalidraw ${_} o superior. Por favor, actualiza Excalidraw y luego intenta reiniciar ${L}.`,COMMAND_ADD_PARENT_FIELD:"Agregar campo de Dataview a la ontología como PADRE",COMMAND_ADD_CHILD_FIELD:"Agregar campo de Dataview a la ontología como HIJO",COMMAND_ADD_LEFT_FRIEND_FIELD:"Agregar campo de Dataview a la ontología como AMIGO LADO IZQUIERDO",COMMAND_ADD_RIGHT_FRIEND_FIELD:"Agregar campo de Dataview a la ontología como AMIGO LADO DERECHO",COMMAND_ADD_PREVIOUS_FIELD:"Agregar campo de Dataview a la ontología como ANTERIOR",COMMAND_ADD_NEXT_FIELD:"Agregar campo de Dataview a la ontología como SIGUIENTE",COMMAND_START:"ExcaliBrain Normal",COMMAND_START_HOVER:"ExcaliBrain Editor Emergente",COMMAND_START_POPOUT:"ExcaliBrain Ventana Emergente",COMMAND_STOP:"Detener ExcaliBrain",HOVER_EDITOR_ERROR:"Lo siento. Algo salió mal. Lo más probable es que haya habido una actualización de versión en el Editor Emergente que no he abordado adecuadamente en ExcaliBrain. Normalmente debería solucionarlo en unos pocos días",OPEN_DRAWING:"Guardar instantánea para editar",SEARCH_IN_VAULT:"Los elementos marcados serán listados en una búsqueda vacía.\nBusca un archivo, una carpeta o una etiqueta en tu Vault.\nAlterna entre carpetas y etiquetas para mostrar/ocultar en la lista.",SHOW_HIDE_ATTACHMENTS:"Mostrar/Ocultar adjuntos",SHOW_HIDE_VIRTUAL:"Mostrar/Ocultar nodos virtuales",SHOW_HIDE_INFERRED:"Mostrar/Ocultar nodos inferidos",SHOW_HIDE_ALIAS:"Mostrar/Ocultar alias del documento",SHOW_HIDE_SIBLINGS:"Mostrar/Ocultar hermanos",SHOW_HIDE_EMBEDDEDCENTRAL:"Mostrar el nodo central como marco incorporado",SHOW_HIDE_FOLDER:"Mostrar/Ocultar nodos de carpeta",SHOW_HIDE_TAG:"Mostrar/Ocultar nodos de etiqueta",SHOW_HIDE_PAGES:"Mostrar/Ocultar nodos de página (incluye definidos, inferidos, virtuales y adjuntos)",PIN_LEAF:"Conectar ExcaliBrain a la hoja activa más reciente"},fr:{},hi:{},id:{},it:{},ja:{},ko:{},nl:{},nn:{},pl:{},pt:{},"pt-br":{},ro:{},ru:{},tr:{},"zh-cn":{JSON_MALFORMED:"JSON 格式错误",JSON_MISSING_KEYS:'JSON 必须包含以下四个键："parents"（父节点）、"children"（子节点）、"friends"（友好节点）、"nextFriends"（下一友好节点）',JSON_VALUES_NOT_STRING_ARRAYS:'键的值必须是非空字符串数组。例如："parents": ["Parent", "Parents", "up"]',EXCALIBRAIN_FILE_NAME:"Excalibrain 图绘制的文件路径",EXCALIBRAIN_FILE_DESC:"⚠ 此文件将被插件覆盖。如果您停止脚本并对图表进行更改，应该重新命名文件以保留您的编辑内容，因为下次启动 ExcaliBrain 时，您的编辑内容将被自动生成的 ExcaliBrain 图表覆盖。",INDEX_REFRESH_FREQ_NAME:"索引刷新频率",INDEX_REFRESH_FREQ_DESC:"每当您切换工作窗格时，ExcaliBrain 将更新其索引，以防止您的 Vault 中的文件自上次索引更新以来发生了更改。<br>因此，此设置仅在您在 Markdown 编辑器中输入时有效（不切换文件或窗格），并且您仍希望在输入时更新 ExcaliBrain 图表。由于频繁的后台索引更新可能会占用资源，您可以选择增加索引更新的时间间隔，从而减少系统开销。",HIERARCHY_HEAD:"本体论",HIERARCHY_DESC:"输入您将使用的 Dataview 字段名称，用逗号（,）分隔，以定义图表中的链接方向。<br>您还可以通过在 Markdown 编辑器中以段落开头键入新字段（例如：'Consits of::'）并调用命令面板操作之一来即时添加字段到本体论：<code>添加 dataview 字段到父节点</code>、<code>添加 dataview 字段到子节点</code>、<code>添加 dataview 字段到友好节点</code> 或 <code>添加 dataview 字段到右侧友好节点</code>",INFER_NAME:"将所有隐式关系推断为友好节点",INFER_DESC:"<b>打开：</b> 将文档中的所有隐式链接解释为友好节点。<br><b>关闭：</b> 将使用以下逻辑：<ul><li>前向链接被推断为子节点</li><li>反向链接被推断为父节点</li><li>如果文件相互链接，则它们是友好节点</li></ul>",REVERSE_NAME:"反向推断逻辑",REVERSE_DESC:"<b>打开：</b> 将反向链接视为子节点，将前向链接视为父节点。<br><b>关闭：</b> 将反向链接视为父节点，将前向链接视为子节点</b>",PARENTS_NAME:"父节点",CHILDREN_NAME:"子节点",LEFT_FRIENDS_NAME:"左侧友好节点",RIGHT_FRIENDS_NAME:"右侧友好节点",PREVIOUS_NAME:"上一个（友好节点）",NEXT_NAME:"下一个（友好节点）",EXCLUSIONS_NAME:"排除",EXCLUSIONS_DESC:"永远不用于本体论的 Dataview 或 YAML 字段",UNASSIGNED_NAME:"未指定",UNASSIGNED_DESC:"在您的 Vault 中，既不是排除项也不是已定义本体论的字段。",ONTOLOGY_SUGGESTER_NAME:"本体论建议器",ONTOLOGY_SUGGESTER_DESC:"在 Markdown 编辑器中激活本体论建议器。如果启用，然后在段落开头键入触发序列，将会触发建议器，显示您上面定义的本体论字段。",ONTOLOGY_SUGGESTER_ALL_NAME:"触发通用建议器的字符序列。通用建议器将包含所有本体论字段，而不考虑它们的方向。",ONTOLOGY_SUGGESTER_PARENT_NAME:"触发父节点建议器的字符序列",ONTOLOGY_SUGGESTER_CHILD_NAME:"触发子节点建议器的字符序列",ONTOLOGY_SUGGESTER_LEFT_FRIEND_NAME:"触发左侧友好节点建议器的字符序列",ONTOLOGY_SUGGESTER_RIGHT_FRIEND_NAME:"触发右侧友好节点建议器的字符序列",ONTOLOGY_SUGGESTER_PREVIOUS_NAME:"触发上一个（友好节点）建议器的字符序列",ONTOLOGY_SUGGESTER_NEXT_NAME:"触发下一个（友好节点）建议器的字符序列",MID_SENTENCE_SUGGESTER_TRIGGER_NAME:"中间位置的 Dataview 字段建议器触发字符序列",MID_SENTENCE_SUGGESTER_TRIGGER_DESC:"您可以在句子中间添加字段，遵循以下两种格式之一：<br><code>我们在 [地点:: [[XYZ restaurant]]] 遇见了 [候选人:: [[John Doe]]]</code><br><code>我们在 (地点:: [[XYZ restaurant]]) 遇见了 (候选人:: [[John Doe]])</code><br>如果您将此触发器设置为例如 <code>(</code>，则在句子中的任何位置键入 <code>(:::</code> 将会触发建议器（假设您正在使用默认的通用建议器触发组合 <code>:::</code> - 请参阅上面的设置）。更多有关内联字段的信息：[DataView 帮助](https://blacksmithgu.github.io/obsidian-dataview/data-annotation/)",BOLD_FIELDS_NAME:"使用粗体添加选定的字段",BOLD_FIELDS_DESC:"使用粗体类型添加选定的字段，即（**字段名**:: ），结果为（<b>字段名</b>:: ）",DISPLAY_HEAD:"显示",COMPACT_VIEW_NAME:"紧凑视图",COMPACT_VIEW_DESC:"以紧凑的视图显示图表",EXCLUDE_PATHLIST_NAME:"要排除的文件路径",EXCLUDE_PATHLIST_DESC:"输入要从索引中排除的文件路径，用逗号分隔。",RENDERALIAS_NAME:"如果可用，显示别名",RENDERALIAS_DESC:"如果页面的 Front Matter 中指定了页面别名，则显示别名而不是文件名。",NODETITLE_SCRIPT_NAME:"用于渲染节点名称的 JavaScript",NODETITLE_SCRIPT_DESC:"用于渲染节点标题的 JavaScript 代码。如果不需要，请将此字段留空。<br>函数定义：<code>customNodeLabel: (dvPage: Literal, defaultName:string) => string</code><br>在您的脚本中，您可以通过变量 <code>dvPage</code> 引用 dataview 页面对象；通过变量 <code>defaultName</code> 引用默认页面名称（文件名或别名，如果有）。使用以下表达式语法：<br><code>dvPage['field 1']??defaultName</code> - 该示例将在可用时显示 'field 1' 的值，否则显示 defaultName。<br>⚠ 您的代码将按原样执行，请确保添加适当的异常处理。除了 <code>defaultName</code> 和 dataview 字段名称之外，您还可以自由使用任何 JavaScript 函数（例如 <code>defaultName.toLowerCase()</code>）和出现在 <code>dvPage</code> 对象上的任何值，例如 <code>dvPage.file.path</code> 等。<br>要查看 dataview 页面对象，请打开开发者控制台并输入以下代码：<br><code>DataviewAPI.page('完整文件路径，包括扩展名')</code><br>以下是一个示例代码，如果可用，将显示 title 字段的值，否则显示文件名，后跟状态（如果可用）：<br><code>dvPage.title??defaultName & (dvPage.state ? ' - ' & dvPage.state : '')</code>",SHOWINFERRED_NAME:"显示推断关系",SHOWINFERRED_DESC:"<b>打开：</b> 显示显式定义的关系和推断的关系。前向链接为子节点，反向链接为父节点，如果两个页面相互引用，则推断为友好关系。显式定义的关系始终优先。<br><b>关闭：</b> 仅显示显式定义的关系。",SHOWVIRTUAL_NAME:"显示虚拟子节点",SHOWVIRTUAL_DESC:"<b>打开：</b> 显示未解析的链接。<br><b>关闭：</b> 不显示未解析的链接。",SHOWATTACHMENTS_NAME:"包括附件",SHOWATTACHMENTS_DESC:"<b>打开：</b> 在图表上显示所有类型的文件。<br><b>关闭：</b> 仅显示 Markdown 文件。",STYLE_HEAD:"样式",STYLE_DESC:"样式按顺序应用。<br><ol><li><b>基本</b> 节点样式</li><li><b>推断</b> 节点样式（仅在节点是推断的情况下应用）</li><li><b>虚拟</b> 节点样式（仅在节点是虚拟的情况下应用）</li> <li><b>中心</b> 节点样式（仅在节点位于中心时应用）</li><li><b>兄弟</b> 节点样式（仅在节点是兄弟节点时应用）</li> <li><b>附件</b> 节点样式（仅在节点是附件时应用）</li><li><b>基于标签</b> 的样式</li></ol>必须指定基本节点样式的所有属性。其他样式可以部分定义。例如，您可以在基于标签的样式中添加前缀并覆盖基本节点背景颜色，在推断节点样式中覆盖字体颜色，在虚拟节点样式中设置边框线样式为虚线。",CANVAS_BGCOLOR:"画布颜色",SHOW_FULL_TAG_PATH_NAME:"显示完整标签名称",SHOW_FULL_TAG_PATH_DESC:"<b>打开：</b> 将显示完整的标签，例如 #reading/books/sci-fi</br><b>关闭：</b> 将根据标签层级显示当前部分标签，例如在上面的标签中，导航标签层级时分别只显示 #reading、#books、#sci-fi。",SHOW_COUNT_NAME:"显示邻居数量",SHOW_COUNT_DESC:"显示子节点、父节点、友好节点旁边的数量",ALLOW_AUTOZOOM_NAME:"自动缩放",ALLOW_AUTOZOOM_DESC:"<b>打开：</b> 允许自动缩放<br><b>关闭：</b> 禁用自动缩放",ALLOW_AUTOFOCUS_ON_SEARCH_NAME:"搜索时自动聚焦",ALLOW_AUTOFOCUS_ON_SEARCH_DESC:"<b>打开：</b> 允许搜索时自动聚焦<br><b>关闭：</b> 禁用搜索时自动聚焦",ALWAYS_ON_TOP_NAME:"弹出窗口默认“置顶”行为",ALWAYS_ON_TOP_DESC:"<b>打开：</b> 在弹出窗口中打开 ExcaliBrain 时，新窗口将以“始终置顶”模式打开。<br><b>关闭：</b> 新窗口将不会以“始终置顶”模式打开。",EMBEDDED_FRAME_WIDTH_NAME:"嵌入帧宽度",EMBEDDED_FRAME_HEIGHT_NAME:"嵌入帧高度",TAGLIST_NAME:"格式化标签",TAGLIST_DESC:"您可以为节点指定基于标签的特殊格式规则。如果页面上存在多个标签，则将使用第一个匹配规范的标签。<br>标签名应以 <mark>#</mark> 开头，可以是不完整的。例如，<code>#book</code> 将匹配 #books、#book/fiction 等。<br>在此处输入逗号分隔的标签列表，然后从下拉列表中选择以更改格式。",MAX_ITEMCOUNT_DESC:"最大节点数量",MAX_ITEMCOUNT_NAME:"在布局中显示的节点的最大数量。例如：最大父节点数、最大子节点数、最大友好节点数和最大兄弟节点数。如果有更多节点，则它们将从图中省略。",NODESTYLE_INCLUDE_TOGGLE:"打开：覆盖此属性的基本节点样式；关闭：应用此属性的基本节点样式",NODESTYLE_PREFIX_NAME:"前缀",NODESTYLE_PREFIX_DESC:"节点标签前显示的前缀字符或表情符号",NODESTYLE_BGCOLOR:"背景颜色",NODESTYLE_BG_FILLSTYLE:"背景填充样式",NODESTYLE_TEXTCOLOR:"文本颜色",NODESTYLE_BORDERCOLOR:"边框颜色",NODESTYLE_FONTSIZE:"字体大小",NODESTYLE_FONTFAMILY:"字体族",NODESTYLE_MAXLABELLENGTH_NAME:"最大标签长度",NODESTYLE_MAXLABELLENGTH_DESC:"要从节点标题中显示的最大字符数。较长的节点标题将以 '...' 结尾",NODESTYLE_ROUGHNESS:"笔触粗糙度",NODESTYLE_SHARPNESS:"笔触锐化度",NODESTYLE_STROKEWIDTH:"笔触宽度",NODESTYLE_STROKESTYLE:"笔触样式",NODESTYLE_RECTANGLEPADDING:"节点矩形的填充",NODESTYLE_GATE_RADIUS_NAME:"连接点半径",NODESTYLE_GATE_RADIUS_DESC:"作为节点连接点的 3 个小圆（别名：连接点）的半径",NODESTYLE_GATE_OFFSET_NAME:"连接点偏移",NODESTYLE_GATE_OFFSET_DESC:"父节点和子节点连接点的左右偏移量。",NODESTYLE_GATE_COLOR:"连接点边框颜色",NODESTYLE_GATE_BGCOLOR_NAME:"连接点背景颜色",NODESTYLE_GATE_BGCOLOR_DESC:"连接点的填充颜色（如果具有子节点）",NODESTYLE_GATE_FILLSTYLE:"连接点背景填充样式",NODESTYLE_BASE:"基本节点样式",NODESTYLE_CENTRAL:"中心节点样式",NODESTYLE_INFERRED:"推断节点样式",NODESTYLE_VIRTUAL:"虚拟节点样式",NODESTYLE_SIBLING:"兄弟节点样式",NODESTYLE_ATTACHMENT:"附件节点样式",NODESTYLE_FOLDER:"文件夹节点样式",NODESTYLE_TAG:"标签节点样式",LINKSTYLE_COLOR:"颜色",LINKSTYLE_WIDTH:"宽度",LINKSTYLE_STROKE:"笔触样式",LINKSTYLE_ROUGHNESS:"粗糙度",LINKSTYLE_ARROWSTART:"起始箭头头部",LINKSTYLE_ARROWEND:"结束箭头头部",LINKSTYLE_SHOWLABEL:"在连接上显示标签",LINKSTYLE_FONTSIZE:"标签字体大小",LINKSTYLE_FONTFAMILY:"标签字体族",LINKSTYLE_BASE:"基本连接样式",LINKSTYLE_INFERRED:"推断连接样式",LINKSTYLE_FOLDER:"文件夹连接样式",LINKSTYLE_TAG:"标签连接样式",DATAVIEW_NOT_FOUND:`未找到 Dataview 插件。请安装或启用 Dataview，然后尝试重新启动 ${L}。`,DATAVIEW_UPGRADE:`请升级 Dataview 到 0.5.31 或更高版本。请更新 Dataview，然后尝试重新启动 ${L}。`,EXCALIDRAW_NOT_FOUND:`未找到 Excalidraw 插件。请安装或启用 Excalidraw，然后尝试重新启动 ${L}。`,EXCALIDRAW_MINAPP_VERSION:`ExcaliBrain 需要 Excalidraw ${_} 或更高版本。请升级 Excalidraw，然后尝试重新启动 ${L}。`,COMMAND_ADD_PARENT_FIELD:"将 dataview 字段添加到本体作为父节点",COMMAND_ADD_CHILD_FIELD:"将 dataview 字段添加到本体作为子节点",COMMAND_ADD_LEFT_FRIEND_FIELD:"将 dataview 字段添加到本体作为左侧友好节点",COMMAND_ADD_RIGHT_FRIEND_FIELD:"将 dataview 字段添加到本体作为右侧友好节点",COMMAND_ADD_PREVIOUS_FIELD:"将 dataview 字段添加到本体作为上一个节点",COMMAND_ADD_NEXT_FIELD:"将 dataview 字段添加到本体作为下一个节点",COMMAND_START:"ExcaliBrain 普通模式",COMMAND_START_HOVER:"ExcaliBrain 悬停编辑器模式",COMMAND_START_POPOUT:"ExcaliBrain 弹出窗口模式",COMMAND_STOP:"停止 ExcaliBrain",HOVER_EDITOR_ERROR:"对不起，发生了一些错误。很可能是 Hover 编辑器更新了版本，而我在 ExcaliBrain 中没有适当处理。通常我会在几天内解决此问题",OPEN_DRAWING:"保存用于编辑的快照",SEARCH_IN_VAULT:"收藏夹中的项目将列在空搜索中。\n在您的 Vault 中搜索文件、文件夹或标签。\n切换文件夹和标签的显示/隐藏以在列表中显示。",SHOW_HIDE_ATTACHMENTS:"显示/隐藏附件",SHOW_HIDE_VIRTUAL:"显示/隐藏虚拟节点",SHOW_HIDE_INFERRED:"显示/隐藏推断节点",SHOW_HIDE_ALIAS:"显示/隐藏文档别名",SHOW_HIDE_SIBLINGS:"显示/隐藏兄弟节点",SHOW_HIDE_EMBEDDEDCENTRAL:"将中心节点显示为嵌入式框架",SHOW_HIDE_FOLDER:"显示/隐藏文件夹节点",SHOW_HIDE_TAG:"显示/隐藏标签节点",SHOW_HIDE_PAGES:"显示/隐藏页面节点（包括已定义、推断、虚拟和附件节点）",PIN_LEAF:"链接 ExcaliBrain 到最近的活动叶子"},"zh-tw":{},hu:{JSON_MALFORMED:"Hibás JSON",JSON_MISSING_KEYS:'A JSON-nak rendelkeznie kell az alábbi 4 kulccsal: "parents", "children", "friends", "nextFriends"',JSON_VALUES_NOT_STRING_ARRAYS:'A kulcsok értékeinek nem üres string tömbnek kell lenniük. Példa: "parents": ["Szülő", "Szülők", "feljebb"]',EXCALIBRAIN_FILE_NAME:"Excalibrain rajz fájl elérési útvonala",EXCALIBRAIN_FILE_DESC:"⚠ Ez a fájl felül lesz írva a bővítmény által. Ha leállítod a szkriptet és változtatsz a gráfban, akkor át kell nevezned a fájlt, hogy a módosításaid megmaradjanak. Mert amikor újra elindítod az ExcaliBrain-t, a módosításaidat felülírja az automatikusan generált ExcaliBrain gráf.",INDEX_REFRESH_FREQ_NAME:"Index frissítési gyakorisága",INDEX_REFRESH_FREQ_DESC:"Az ExcaliBrain frissíti az indexét, amikor váltasz munkatérre, abban az esetben, ha az előző index frissítése óta megváltozott egy fájl a Vault-odban. <br>Ez a beállítás csak akkor érvényes, ha egy markdown szerkesztőben írsz (nem váltasz fájlokat vagy térképeket), és mégis azt szeretnéd, hogy az ExcaliBrain frissítse a gráfodat ahogy gépelsz. Mivel a gyakori háttérindex frissítések erőforrásigényesek lehetnek, van lehetőséged növelni az index-frissítés időközét, amely csökkenti a rendszer terhelését.",HIERARCHY_HEAD:"Ontológia",HIERARCHY_DESC:"Add meg a Dataview mezőneveket vesszővel elválasztva (,) úgy, hogy ezeket fogod használni a link irányok meghatározásához a gráfodban.<br>Az ontológiát a markdown szerkesztőben a mező elé írva is bővítheted (például: 'Tartalmazza::') majd valamelyik parancspaletta parancs segítségével hozzáadhatod a dataview mezőt az ontológiához, mint SZÜLŐ, GYERMEK, BARÁT, vagy JOBBSZÉL BARÁT",INFER_NAME:"Az összes implicit kapcsolat barátként való megjelenítése",INFER_DESC:"<b>Be:</b> Az összes implcita linket a dokumentumban barátként értelmezi.<br><b>Ki:</b> Az alábbi logikát alkalmazza:<ul><li>Egy előre mutató linket gyermekként értelmez</li><li>Egy visszamutató linket szülőként értelmez</li><li>Ha két fájl kölcsönösen hivatkozik egymásra, azok barátok</li></ul>",REVERSE_NAME:"Fordított következtető logika",REVERSE_DESC:"<b>Be:</b> A visszamutató linkeket gyerekként, az előre mutatókat szülőkként kezeli.<br><b>Ki:</b> A visszamutató linkeket szülőkként, az előre mutatókat gyerekként kezeli</b>",PARENTS_NAME:"Szülők",CHILDREN_NAME:"Gyermekek",LEFT_FRIENDS_NAME:"Baloldali barátok",RIGHT_FRIENDS_NAME:"Jobboldali barátok",PREVIOUS_NAME:"Előző (barátok)",NEXT_NAME:"Következő (barátok)",EXCLUSIONS_NAME:"Kizárt",EXCLUSIONS_DESC:"Dataview vagy YAML mezők, amelyek sosem kerülnek felhasználásra az ontológiában",UNASSIGNED_NAME:"Nem hozzárendelt",UNASSIGNED_DESC:"A Vault-odban található mezők, amelyek sem az ontológia részei, sem kizárt mezők.",ONTOLOGY_SUGGESTER_NAME:"Ontológia javasoló",ONTOLOGY_SUGGESTER_DESC:"Aktiválja az ontológia javasolót a markdown szerkesztőben. Ha engedélyezve van, akkor a paragrafus elején írt trigger szekvenciával aktiválhatod a javasolót, ami felsorolja az előzőleg meghatározott ontológiai mezőket.",ONTOLOGY_SUGGESTER_ALL_NAME:"Karakter szekvencia a generikus javasoló aktiválásához. A generikus javasoló az összes ontológiai mezőt tartalmazza függetlenül az irányuktól.",ONTOLOGY_SUGGESTER_PARENT_NAME:"Karakter szekvencia a szülő javasoló aktiválásához",ONTOLOGY_SUGGESTER_CHILD_NAME:"Karakter szekvencia a gyermek javasoló aktiválásához",ONTOLOGY_SUGGESTER_LEFT_FRIEND_NAME:"Karakter szekvencia a baloldali barát javasoló aktiválásához",ONTOLOGY_SUGGESTER_RIGHT_FRIEND_NAME:"Karakter szekvencia a jobboldali barát javasoló aktiválásához",ONTOLOGY_SUGGESTER_PREVIOUS_NAME:"Karakter szekvencia az előző (barát) javasoló aktiválásához",ONTOLOGY_SUGGESTER_NEXT_NAME:"Karakter szekvencia a következő (barát) javasoló aktiválásához",MID_SENTENCE_SUGGESTER_TRIGGER_NAME:"Köztes adatmező javasoló kiváltó",MID_SENTENCE_SUGGESTER_TRIGGER_DESC:"Lehetőség van mezőket köztes helyen a mondatokban hozzáadni a következő két formátum valamelyikét használva:<br><code>We met at [location:: [[XYZ restaurant]]] with [candidate:: [[John Doe]]]</code><br><code>We met at (location:: [[XYZ restaurant]]) with (candidate:: [[John Doe]])</code><br>Ha ezt a kiváltót például <code>(</code>-ra állítod, akkor bárhol a mondatban beírva <code>(:::</code> aktiválja a javasolót (feltéve, hogy a generikus javasoló kiváltója az alapértelmezett <code>:::</code> - lásd fent).<br>További információ az inline mezőkről: [DataView Help](https://blacksmithgu.github.io/obsidian-dataview/data-annotation/)",BOLD_FIELDS_NAME:"Kijelölt mezők félkövéren",BOLD_FIELDS_DESC:"A kijelölt mezőt félkövér típusú szöveggel adja hozzá, azaz (**mező neve**:: ) eredményezve (<b>mező neve</b>:: )",DISPLAY_HEAD:"Megjelenítés",COMPACT_VIEW_NAME:"Sűrű nézet",COMPACT_VIEW_DESC:"A gráf megjelenítése sűrű nézetben",EXCLUDE_PATHLIST_NAME:"Kizárandó fájl elérési útvonalak",EXCLUDE_PATHLIST_DESC:"Adja meg a kizárandó fájlok elérési útvonalait vesszővel elválasztva.",RENDERALIAS_NAME:"Megjelenítési azonosító ha elérhető",RENDERALIAS_DESC:"Megjeleníti az oldal azonosítóját a fájlnév helyett, ha az az oldal előlapján van meghatározva.",NODETITLE_SCRIPT_NAME:"Node nevek megjelenítéséhez JavaScript kód",NODETITLE_SCRIPT_DESC:"JavaScript kód a node nevének megjelenítésére. Ha nem szükséges, hagyja ezt a mezőt üresen.<br>Függvény definíció: <code>customNodeLabel: (dvPage: Literal, defaultName:string) => string</code><br>A szkriptben hivatkozhat a dataview oldal objektumára a <code>dvPage</code> változóval; és az alapértelmezett oldalnévre (fájlnév vagy azonosító, ha elérhető) a <code>defaultName</code> változóval. Használhatod a következő kifejezést: <code>dvPage['mező 1']??defaultName</code> - ez az példa megjeleníti a 'mező 1' értékét, ha elérhető, különben az alapértelmezett névet<br>⚠ A kódodat éppen úgy futtatjuk le, ahogy van, tehát győződj meg róla, hogy megfelelő kivételkezelést adtál hozzá. Az <code>defaultName</code> és a dataview mezőnevek mellett szabadon használhatsz bármilyen JavaScript függvényt (például <code>defaultName.toLowerCase()</code>) és bármilyen értéket, ami a <code>dvPage</code> objektumon megjelenik, pl. <code>dvPage.file.path</code>, stb. <br> A dataview oldal objektumot felderítheted az Új oldal megnyitásával és a következő kóddal:<br><code>DataviewAPI.page('teljes fájlnév kiterjesztéssel')</code><br>Itt van egy példa kód, ami a cím mező értékét fogja megjeleníteni, ha elérhető, egyébként az alapértelmezett fájlnévet, és mögé fűzi az állapotot (ha elérhető): <br><code>dvPage.title??defaultName & (dvPage.state ? ' - ' & dvPage.state : '')</code>",SHOWINFERRED_NAME:"Az előállított kapcsolatok megjelenítése",SHOWINFERRED_DESC:"<b>Be:</b> Mind az expliciten meghatározott, mind az előállított kapcsolatokat megjeleníti. Az előre mutató linkek gyerekek, a visszamutatók szülők, ha két oldal egymásra hivatkozik, akkor barátokként lesznek kezelve. Az expliciten meghatározott kapcsolatok mindig előnyt élveznek.<br><b>Ki:</b> Csak az expliciten meghatározott kapcsolatokat jeleníti meg.",SHOWVIRTUAL_NAME:"Virtuális gyerek node-ok megjelenítése",SHOWVIRTUAL_DESC:"<b>Be:</b> Megjeleníti a feloldatlan linkeket.<br><b>Ki:</b> Nem jeleníti meg a feloldatlan linkeket.",SHOWATTACHMENTS_NAME:"Mellékletek beillesztése",SHOWATTACHMENTS_DESC:"<b>Be:</b> Minden típusú fájlt megjelenít a gráfban. <br><b>Ki:</b> Csak a markdown fájlokat jeleníti meg.",STYLE_HEAD:"Stílusok",STYLE_DESC:"A stílusokat sorrendben alkalmazzuk.<br><ol><li><b>Alap</b> node stílus</li><li><b>Előállított</b> node stílus (csak akkor alkalmazódik, ha a node előállított)</li><li><b>Virtuális</b> node stílus (csak akkor alkalmazódik, ha a node virtuális)</li> <li><b>Középponti</b> node stílus (csak akkor alkalmazódik, ha a node a középpontban van)</li><li><b>Testvérek</b> node stílus (csak akkor alkalmazódik, ha a node testvér)</li> <li><b>Melléklet</b> node stílus (csak akkor alkalmazódik, ha a node melléklet)</li><li><b>Opcionális</b> címkén alapuló stílus</li></ol>Az alap node stílus minden attribútumát meg kell adni. A többi stílusnak részleges definíciója lehet. Például hozzáadhatsz egy előtagot és felülírhatod az alapértelmezett node háttérszínét a címkén alapuló stílusban, a szöveg színét az előállított node stílusban és a szaggatott vonalas keretet a virtuális node stílusban.",CANVAS_BGCOLOR:"Vászon szín",SHOW_FULL_TAG_PATH_NAME:"Teljes címke név megjelenítése",SHOW_FULL_TAG_PATH_DESC:"<b>Be:</b> megjeleníti a teljes címkét, például #reading/books/sci-fi</br><b>Ki:</b> a címke aktuális részét jeleníti meg, például a fent említett címkéknél csak #reading, #books, #sci-fi lenne látható a gráfban a címke hierarchia mentén navigálva.",SHOW_COUNT_NAME:"Szomszédok számának megjelenítése",SHOW_COUNT_DESC:"Megmutatja a gyermekek, szülők, barátok számát a node kapuja mellett",ALLOW_AUTOZOOM_NAME:"Automatikus nagyítás",ALLOW_AUTOZOOM_DESC:"<b>Be:</b> Engedélyezi az automatikus nagyítást<br><b>Ki:</b> Letiltja az automatikus nagyítást",ALLOW_AUTOFOCUS_ON_SEARCH_NAME:"Automatikus fókusz a keresésnél",ALLOW_AUTOFOCUS_ON_SEARCH_DESC:"<b>Be:</b> Engedélyezi az automatikus fókuszt a keresésnél<br><b>Ki:</b> Letiltja az automatikus fókuszt",ALWAYS_ON_TOP_NAME:"Alapértelmezett 'mindig legfelül' viselkedés lebegtetett ablak esetén",ALWAYS_ON_TOP_DESC:"<b>Be:</b> Ha az ExcaliBrain-t lebegtetett ablakban nyitod meg, akkor az új ablak mindig 'mindig legfelül' módban nyílik meg.<br><b>Ki:</b> Az új ablak nem lesz 'mindig legfelül' módban.",EMBEDDED_FRAME_WIDTH_NAME:"Beágyazott keret szélessége",EMBEDDED_FRAME_HEIGHT_NAME:"Beágyazott keret magassága",TAGLIST_NAME:"Formázott címkék",TAGLIST_DESC:"Különleges formázási szabályokat adhatsz meg a node-okhoz címkék alapján. Ha az oldalon több címke van jelen, az első illeszkedő specifikációt használja. <br>A címkéknek a <mark>#</mark>-al kell kezdődniük és nem teljesek is lehetnek. Tehát például <code>#book</code> illeszkedni fog #books, #book/fiction stb.-re.<br>Add meg a címkéket vesszővel elválasztva, majd választhatsz a legördülő listából a formázás megváltoztatásához.",MAX_ITEMCOUNT_DESC:"Maximális node szám",MAX_ITEMCOUNT_NAME:"Maximális node-ok száma a elrendezés adott részén.Azaz a maximális szám a szülőknek, a gyermekeknek, a barátoknak és a testvéreknek a megjelenítéshez. Ha több elem van, akkor nem jelennek meg a rajzon.",NODESTYLE_INCLUDE_TOGGLE:"Be: Az alap node stílus felülbírálása ehhez az attribútumhoz; Ki: Az alap node stílus alkalmazása ehhez az attribútumhoz",NODESTYLE_PREFIX_NAME:"Előtag",NODESTYLE_PREFIX_DESC:"Előtag karakter vagy emojival a node címke elé",NODESTYLE_BGCOLOR:"Háttérszín",NODESTYLE_BG_FILLSTYLE:"Háttér kitöltési stílusa",NODESTYLE_TEXTCOLOR:"Szövegszín",NODESTYLE_BORDERCOLOR:"Keret szín",NODESTYLE_FONTSIZE:"Betűméret",NODESTYLE_FONTFAMILY:"Betűcsalád",NODESTYLE_MAXLABELLENGTH_NAME:"Max címke hossza",NODESTYLE_MAXLABELLENGTH_DESC:"A node címke maximum megjelenített karaktereinek száma. Hosszabb node-ok végén '...' jel lesz látható",NODESTYLE_ROUGHNESS:"Vonal élsőség",NODESTYLE_SHARPNESS:"Vonal élesség",NODESTYLE_STROKEWIDTH:"Vonal vastagsága",NODESTYLE_STROKESTYLE:"Vonal stílusa",NODESTYLE_RECTANGLEPADDING:"A node téglalapának kitöltése",NODESTYLE_GATE_RADIUS_NAME:"Kapu sugara",NODESTYLE_GATE_RADIUS_DESC:"A 3 kis kör sugara (alias: kapuk) a node-ok kapcsolódási pontjaként szolgál",NODESTYLE_GATE_OFFSET_NAME:"Kapu eltolása",NODESTYLE_GATE_OFFSET_DESC:"Az eltolás a szülők és gyermekek kapuinak bal és jobb oldalán",NODESTYLE_GATE_COLOR:"Kapu keret színe",NODESTYLE_GATE_BGCOLOR_NAME:"Kapu háttérszíne",NODESTYLE_GATE_BGCOLOR_DESC:"A kapu kitöltési színe, ha vannak gyermekek",NODESTYLE_GATE_FILLSTYLE:"Kapu háttér kitöltési stílusa",NODESTYLE_BASE:"Alap node stílus",NODESTYLE_CENTRAL:"Középponti node stílusa",NODESTYLE_INFERRED:"Előállított node stílusa",NODESTYLE_VIRTUAL:"Virtuális node stílusa",NODESTYLE_SIBLING:"Testvér node stílusa",NODESTYLE_ATTACHMENT:"Melléklet node stílusa",NODESTYLE_FOLDER:"Mappa node stílusa",NODESTYLE_TAG:"Címke node stílusa",LINKSTYLE_COLOR:"Szín",LINKSTYLE_WIDTH:"Vastagság",LINKSTYLE_STROKE:"Vonal stílusa",LINKSTYLE_ROUGHNESS:"Vonal élsőség",LINKSTYLE_ARROWSTART:"Nyíl feje kezdetén",LINKSTYLE_ARROWEND:"Nyíl feje végén",LINKSTYLE_SHOWLABEL:"Címke megjelenítése a link-en",LINKSTYLE_FONTSIZE:"Címke betűmérete",LINKSTYLE_FONTFAMILY:"Címke betűcsaládja",LINKSTYLE_BASE:"Alap link stílus",LINKSTYLE_INFERRED:"Előállított link stílusa",LINKSTYLE_FOLDER:"Mappa link stílusa",LINKSTYLE_TAG:"Címke link stílusa",DATAVIEW_NOT_FOUND:`A Dataview bővítmény nem található. Kérlek telepítsd vagy engedélyezd a Dataview-t, majd próbáld újra indítani a(z) ${L} alkalmazást.`,DATAVIEW_UPGRADE:`Kérlek frissítsd a Dataview-t 0.5.31 vagy újabb verzióra. Kérlek frissítsd a Dataview-t, majd próbáld újra indítani a(z) ${L} alkalmazást.`,EXCALIDRAW_NOT_FOUND:`Az Excalidraw bővítmény nem található. Kérlek telepítsd vagy engedélyezd az Excalidraw-t, majd próbáld újra indítani a(z) ${L} alkalmazást.`,EXCALIDRAW_MINAPP_VERSION:`Az ExcaliBrain az Excalidraw ${_} vagy újabb verzióját igényli. Kérlek frissítsd az Excalidraw-t, majd próbáld újra indítani a(z) ${L} alkalmazást.`,COMMAND_ADD_PARENT_FIELD:"Dataview mező hozzáadása ontológiaként SZÜLŐKÉNT",COMMAND_ADD_CHILD_FIELD:"Dataview mező hozzáadása ontológiaként GYERMEKEKKÉNT",COMMAND_ADD_LEFT_FRIEND_FIELD:"Dataview mező hozzáadása ontológiaként BAL-OLDALI BARÁTKÉNT",COMMAND_ADD_RIGHT_FRIEND_FIELD:"Dataview mező hozzáadása ontológiaként JOBB-OLDALI BARÁTKÉNT",COMMAND_ADD_PREVIOUS_FIELD:"Dataview mező hozzáadása ontológiaként ELŐZŐKÉNT",COMMAND_ADD_NEXT_FIELD:"Dataview mező hozzáadása ontológiaként KÖVETKEZŐKÉNT",COMMAND_START:"ExcaliBrain Normál",COMMAND_START_HOVER:"ExcaliBrain lebegő szerkesztő",COMMAND_START_POPOUT:"ExcaliBrain különálló ablak",COMMAND_STOP:"ExcaliBrain leállítása",HOVER_EDITOR_ERROR:"Sajnálom. Valami hiba történt. Valószínűleg a Hover Editor verziófrissítése okozta, amelyet még nem kezeltem megfelelően az ExcaliBrain-ben. Általában néhány napon belül megoldom ezt.",OPEN_DRAWING:"Mentés szerkesztéshez",SEARCH_IN_VAULT:"A csillagozott elemek megjelennek az üres keresésben.\nKeresés fájl, mappa vagy címke szerint a Vault-ban.\nKapcsold ki/ki a mappákat és címkéket a listában történő megjelenítéshez.",SHOW_HIDE_ATTACHMENTS:"Mellékletek megjelenítése/elrejtése",SHOW_HIDE_VIRTUAL:"Virtuális node-ok megjelenítése/elrejtése",SHOW_HIDE_INFERRED:"Előállított node-ok megjelenítése/elrejtése",SHOW_HIDE_ALIAS:"Dokumentum alias megjelenítése/elrejtése",SHOW_HIDE_SIBLINGS:"Testvér node-ok megjelenítése/elrejtése",SHOW_HIDE_EMBEDDEDCENTRAL:"Középponti node beágyazott keretként megjelenítése",SHOW_HIDE_FOLDER:"Mappa node-ok megjelenítése/elrejtése",SHOW_HIDE_TAG:"Címke node-ok megjelenítése/elrejtése",SHOW_HIDE_PAGES:"Oldal node-ok megjelenítése/elrejtése (definiált, előállított, virtuális és melléklet)",PIN_LEAF:"ExcaliBrain összekapcsolása az aktív legutóbbi elemmel"}}[n.moment.locale()];function z(e){return M||c({fn:z,where:"src/lang/helpers.ts",message:"Error: locale not found",data:n.moment.locale()}),M&&M[e]||R[e]}class H extends n.Modal{constructor(e,t,i){super(e),this.title=t,this.message=i}onOpen(){this.createForm()}onClose(){}createForm(){this.titleEl.setText(this.title),this.contentEl.createDiv({cls:"excalibrain-prompt-center",text:this.message}),this.contentEl.createDiv({cls:"excalibrain-prompt-center"},(e=>{e.style.textAlign="right",e.createEl("button",{text:"Ok"}).onclick=()=>{this.resolve(!0),this.close()},e.createEl("button",{text:"Cancel"}).onclick=()=>{this.resolve(!1),this.close()}}))}show(e){this.resolve=e,this.open()}}const P=/^(?:http(?:s)?:\/\/)?(?:www\.)?youtu(?:be\.com|\.be)\/(embed\/|watch\?v=|shorts\/|playlist\?list=|embed\/videoseries\?list=)?([a-zA-Z0-9_-]+)(?:\?t=|&t=|\?start=|&start=)?([a-zA-Z0-9_-]+)?[^\s]*$/,G=/^(?:http(?:s)?:\/\/)?(?:(?:w){3}.)?(?:player\.)?vimeo\.com\/(?:video\/)?([^?\s]+)(?:\?.*)?$/;class V{constructor(e){this.style={},this.center={x:0,y:0},this.isCentral=!1,this.isEmbedded=!1,this.embeddedElementIds=[],e.embeddedElementIds&&(this.embeddedElementIds=e.embeddedElementIds),this.isEmbedded=Boolean(e.isEmbeded),this.isCentral=e.isCentral,this.page=e.page,this.settings=e.page.plugin.settings,this.ea=e.ea,this.page.isFolder?this.style=Object.assign(Object.assign(Object.assign(Object.assign({},this.settings.baseNodeStyle),e.isCentral?this.settings.centralNodeStyle:{}),e.isSibling?this.settings.siblingNodeStyle:{}),this.settings.folderNodeStyle):this.page.isTag?this.style=Object.assign(Object.assign(Object.assign(Object.assign({},this.settings.baseNodeStyle),e.isCentral?this.settings.centralNodeStyle:{}),e.isSibling?this.settings.siblingNodeStyle:{}),this.settings.tagNodeStyle):this.style=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},this.settings.baseNodeStyle),e.isInferred?this.settings.inferredNodeStyle:{}),e.page.isURL?this.settings.urlNodeStyle:{}),e.page.isVirtual?this.settings.virtualNodeStyle:{}),e.isCentral?this.settings.centralNodeStyle:{}),e.isSibling?this.settings.siblingNodeStyle:{}),e.page.isAttachment?this.settings.attachmentNodeStyle:{}),h([this.page.primaryStyleTag,this.page.styleTags],this.settings)),{embedHeight:this.settings.centerEmbedHeight,embedWidth:this.settings.centerEmbedWidth}),this.friendGateOnLeft=e.friendGateOnLeft,this.title=this.page.getTitle()}get prefix(){var e;return null!==(e=this.style.prefix)&&void 0!==e?e:""}displayText(){var e;const t=(null!==(e=this.style.prefix)&&void 0!==e?e:"")+this.title,i=(new Intl.Segmenter).segment(t),n=Array.from(i,(({segment:e})=>e));return n.length>this.page.maxLabelLength?n.slice(0,this.page.maxLabelLength-3).join("")+"...":t}setCenter(e){this.center=e}async renderEmbedded(){const e=this.ea;let t={width:this.style.embedWidth,height:this.style.embedHeight};if(this.page.file&&f(this.page.file,e)||this.page.isURL){this.page.isURL&&(t=((e,t)=>{const i=(e=>{if(!e)return null;const t=e.match(P);if(null==t?void 0:t[2])return e.includes("shorts")?.5625:560/315;const i=e.match(G);return(null==i?void 0:i[1])?560/315:null})(e);return i?i>1?{width:t.width,height:t.width/i}:{width:t.height*i,height:t.height}:t})(this.page.url,t)),this.id=e.addEmbeddable(this.center.x-t.width/2,this.center.y-t.height/2,t.width,t.height,this.page.isURL?this.page.url:void 0,this.page.isURL?void 0:this.page.file);const i=e.getElement(this.id);return i.link=this.page.isURL?this.page.url:`[[${this.page.file.path}]]`,i.backgroundColor=this.style.backgroundColor,i.strokeColor=this.style.borderColor,i.strokeStyle=this.style.strokeStyle,this.embeddedElementIds.push(this.id),t}{this.id=await e.addImage(this.center.x-t.width/2,this.center.y-t.height/2,this.page.file,!1,!1);const i=e.getElement(this.id);i.link=`[[${this.page.file.path}]]`;let n=i.width,s=i.height;if(n>t.width||s>t.height){const e=n/s;n>t.width&&(n=t.width,s=n/e),s>t.height&&(s=t.height,n=s*e)}i.x=this.center.x-n/2,i.y=this.center.y-s/2,i.width=n,i.height=s;const r=e.addRect(this.center.x-n/2,this.center.y-s/2,n,s),a=e.getElement(r);return a.backgroundColor=this.style.backgroundColor,a.strokeColor=this.style.borderColor,a.strokeStyle=this.style.strokeStyle,a.fillStyle=this.style.fillStyle,delete e.elementsDict[i.id],e.elementsDict[i.id]=i,this.embeddedElementIds.push(r),this.embeddedElementIds.push(this.id),{width:n,height:s}}}renderText(){var e,t;const i=this.ea,n=this.displayText(),s=i.measureText(`${n}`);this.id=i.addText(this.center.x-s.width/2,this.center.y-s.height/2,n,{wrapAt:this.page.maxLabelLength+50,textAlign:"center",box:!0,boxPadding:this.style.padding});const r=i.getElement(this.id);return r.link=this.page.isURL?this.page.url:`[[${null!==(t=null===(e=this.page.file)||void 0===e?void 0:e.path)&&void 0!==t?t:this.page.path}]]`,r.backgroundColor=this.style.backgroundColor,r.strokeColor=this.style.borderColor,r.strokeStyle=this.style.strokeStyle,s}async render(){const e=this.ea,t=this.settings,i=2*this.style.gateRadius;e.style.fontSize=this.style.fontSize,e.style.fontFamily=this.style.fontFamily,e.style.fillStyle=this.style.fillStyle,e.style.roughness=this.style.roughness,e.style.strokeSharpness=this.style.strokeShaprness,e.style.strokeWidth=this.style.strokeWidth,e.style.strokeColor=this.style.textColor,e.style.backgroundColor="transparent";const n=this.isEmbedded?this.embeddedElementIds.length>0?{width:this.style.embedWidth,height:this.style.embedHeight}:await this.renderEmbedded():this.renderText();e.style.fillStyle=this.style.gateFillStyle,e.style.strokeColor=this.style.gateStrokeColor,e.style.strokeStyle="solid";const s=this.friendGateOnLeft?this.page.previousFriendCount():this.page.nextFriendCount(),r=this.friendGateOnLeft?this.page.nextFriendCount():this.page.previousFriendCount(),a=this.page.leftFriendCount()+s;e.style.backgroundColor=a>0?this.style.gateBackgroundColor:"transparent",this.friendGateId=e.addEllipse(this.friendGateOnLeft?this.center.x-i-this.style.padding-n.width/2:this.center.x+this.style.padding+n.width/2,this.center.y-this.style.gateRadius,i,i);const u=[];t.showNeighborCount&&a>0&&(e.style.fontSize=i,u.push(e.addText(this.friendGateOnLeft?a>9?this.center.x-2*i-this.style.padding-n.width/2:this.center.x-i-this.style.padding-n.width/2:this.center.x+this.style.padding+n.width/2,this.friendGateOnLeft?this.center.y-2*i:this.center.y-this.style.gateRadius+i,a.toString())));const o=this.page.rightFriendCount()+r;e.style.backgroundColor=o>0?this.style.gateBackgroundColor:"transparent",this.nextFriendGateId=e.addEllipse(this.friendGateOnLeft?this.center.x+this.style.padding+n.width/2:this.center.x-i-this.style.padding-n.width/2,this.center.y-this.style.gateRadius,i,i),t.showNeighborCount&&o>0&&(e.style.fontSize=i,u.push(e.addText(this.friendGateOnLeft?this.center.x+this.style.padding+n.width/2:o>9?this.center.x-2*i-this.style.padding-n.width/2:this.center.x-i-this.style.padding-n.width/2,this.friendGateOnLeft?this.center.y-this.style.gateRadius+i:this.center.y-2*i,o.toString()))),this.isCentral||(this.nextFriendGateId=this.friendGateId);const l=this.page.parentCount();e.style.backgroundColor=l>0?this.style.gateBackgroundColor:"transparent",this.parentGateId=e.addEllipse(this.center.x-this.style.gateRadius-this.style.gateOffset,this.center.y-i-this.style.padding-n.height/2,i,i),t.showNeighborCount&&l>0&&(e.style.fontSize=i,u.push(e.addText(this.center.x+i-this.style.gateOffset,this.center.y-i-this.style.padding-n.height/2,l.toString())));const D=this.page.childrenCount();e.style.backgroundColor=D>0?this.style.gateBackgroundColor:"transparent",this.childGateId=e.addEllipse(this.center.x-this.style.gateRadius+this.style.gateOffset,this.center.y+this.style.padding+n.height/2,i,i),t.showNeighborCount&&D>0&&(e.style.fontSize=i,u.push(e.addText(this.center.x+i+this.style.gateOffset,this.center.y+this.style.padding+n.height/2,D.toString()))),e.addToGroup([this.friendGateId,this.parentGateId,this.childGateId,...this.nextFriendGateId!==this.friendGateId?[this.nextFriendGateId]:[],...u,...this.isEmbedded?this.embeddedElementIds:[this.id,e.getElement(this.id).boundElements[0].id]])}}class Y{constructor(t,i,n,s,r,a,u,o){this.nodeA=t,this.nodeB=i,this.nodeBRole=n,this.hierarchyDefinition=r,this.ea=a,this.isInferred=!1;const l=null==r?void 0:r.split(",").map((e=>e.trim()));this.isInferred=s===e.INFERRED;let D={};l&&l.forEach((e=>{if(o.hierarchyLinkStylesExtended[e])D=Object.assign(Object.assign({},D),o.hierarchyLinkStylesExtended[e]);else switch(e){case"file-tree":D=Object.assign(Object.assign({},D),o.settings.folderLinkStyle);break;case"tag-tree":D=Object.assign(Object.assign({},D),o.settings.tagLinkStyle)}})),this.style=Object.assign(Object.assign(Object.assign({},u.baseLinkStyle),this.isInferred?u.inferredLinkStyle:{}),D)}render(e){const i=this.ea,n=this.style;let s,r;switch(i.style.strokeStyle=n.strokeStyle,i.style.roughness=n.roughness,i.style.strokeColor=n.strokeColor,i.style.strokeWidth=n.strokeWidth,i.style.opacity=e?10:100,this.nodeBRole){case t.CHILD:s=this.nodeA.childGateId,r=this.nodeB.parentGateId;break;case t.PARENT:s=this.nodeA.parentGateId,r=this.nodeB.childGateId;break;case t.RIGHT:s=this.nodeA.nextFriendGateId,r=this.nodeB.nextFriendGateId;break;default:s=this.nodeA.friendGateId,r=this.nodeB.friendGateId}const a=i.connectObjects(s,null,r,null,{startArrowHead:"none"===n.startArrowHead?null:n.startArrowHead,endArrowHead:"none"===n.endArrowHead?null:n.endArrowHead});n.showLabel&&this.hierarchyDefinition&&(i.style.fontSize=n.fontSize,i.style.fontFamily=n.fontFamily,i.style.strokeColor=n.textColor,i.addLabelToLine(a,this.hierarchyDefinition))}}const j={compactView:!1,compactingFactor:1.5,minLinkLength:18,excalibrainFilepath:"excalibrain.md",indexUpdateInterval:5e3,hierarchy:x,inferAllLinksAsFriends:!1,inverseInfer:!1,inverseArrowDirection:!0,renderAlias:!0,nodeTitleScript:"",backgroundColor:"#0c3e6aff",excludeFilepaths:[],autoOpenCentralDocument:!0,toggleEmbedTogglesAutoOpen:!0,showInferredNodes:!0,showAttachments:!0,showURLNodes:!0,showVirtualNodes:!0,showFolderNodes:!1,showTagNodes:!1,showPageNodes:!0,showNeighborCount:!0,showFullTagName:!1,maxItemCount:30,renderSiblings:!1,applyPowerFilter:!1,baseNodeStyle:B,centralNodeStyle:{fontSize:30,backgroundColor:"#B5B5B5",textColor:"#000000ff"},inferredNodeStyle:{backgroundColor:"#000005b3",textColor:"#95c7f3ff"},urlNodeStyle:{prefix:"🌐 "},virtualNodeStyle:{backgroundColor:"#ff000066",fillStyle:"hachure",textColor:"#ffffffff"},siblingNodeStyle:{fontSize:15},attachmentNodeStyle:{prefix:"📎 "},folderNodeStyle:{prefix:"📂 ",strokeShaprness:"sharp",borderColor:"#ffd700ff",textColor:"#ffd700ff"},tagNodeStyle:{prefix:"#",strokeShaprness:"sharp",borderColor:"#4682b4ff",textColor:"#4682b4ff"},tagNodeStyles:{},tagStyleList:[],primaryTagField:"Note type",primaryTagFieldLowerCase:"note-type",displayAllStylePrefixes:!0,baseLinkStyle:k,inferredLinkStyle:{strokeStyle:"dashed"},folderLinkStyle:{strokeColor:"#ffd700ff"},tagLinkStyle:{strokeColor:"#4682b4ff"},hierarchyLinkStyles:{},navigationHistory:[],allowOntologySuggester:!0,ontologySuggesterParentTrigger:"::p",ontologySuggesterChildTrigger:"::c",ontologySuggesterLeftFriendTrigger:"::l",ontologySuggesterRightFriendTrigger:"::r",ontologySuggesterPreviousTrigger:"::e",ontologySuggesterNextTrigger:"::n",ontologySuggesterTrigger:":::",ontologySuggesterMidSentenceTrigger:"(",boldFields:!1,allowAutozoom:!0,maxZoom:1,allowAutofocuOnSearch:!0,defaultAlwaysOnTop:!1,embedCentralNode:!1,centerEmbedWidth:550,centerEmbedHeight:700},W="excalibrain-hide-disabled",U="excalibrain-settings-disabled",K=e=>(255*e|256).toString(16).slice(1),q=e=>createFragment((t=>t.createDiv().innerHTML=e)),Z=e=>{const t=document.getElementById(e);if(!t)return;const i=t.parentNode;i&&i.removeChild(t)},$=(e,t)=>{const i=document.createElement("style");i.id=e,i.innerHTML=`.${t} {display: none;}`,document.body.appendChild(i)};class X extends n.PluginSettingTab{constructor(e,t){super(e,t),this.dirty=!1,this.updateTimer=!1,this.plugin=t}get hierarchyStyleList(){return I.concat(Array.from(this.plugin.settings.hierarchy.hidden)).concat(Array.from(this.plugin.settings.hierarchy.parents)).concat(Array.from(this.plugin.settings.hierarchy.children)).concat(Array.from(this.plugin.settings.hierarchy.leftFriends)).concat(Array.from(this.plugin.settings.hierarchy.rightFriends)).concat(Array.from(this.plugin.settings.hierarchy.previous)).concat(Array.from(this.plugin.settings.hierarchy.next))}async updateNodeDemoImg(){this.ea.reset(),this.ea.canvas.viewBackgroundColor=this.plugin.settings.backgroundColor,this.demoNode.style=Object.assign(Object.assign({},this.demoNodeStyle.getInheritedStyle()),this.demoNodeStyle.style),this.demoNode.render();const e=await this.ea.createSVG(null,!0,{withBackground:!0,withTheme:!1},null,"",40);e.removeAttribute("width"),e.removeAttribute("height"),this.demoNodeImg.setAttribute("src",g(e.outerHTML))}async updateLinkDemoImg(){this.ea.reset(),this.ea.canvas.viewBackgroundColor=this.plugin.settings.backgroundColor;const n=new A(null,"Start node",null,this.plugin),s=new A(null,"End node",null,this.plugin),r=this.plugin.settings.hierarchy;r.leftFriends.contains(this.demoLinkStyle.display)?(n.addLeftFriend(s,e.DEFINED,i.FROM),s.addLeftFriend(n,e.DEFINED,i.TO)):r.rightFriends.contains(this.demoLinkStyle.display)?(n.addRightFriend(s,e.DEFINED,i.FROM),s.addRightFriend(n,e.DEFINED,i.TO)):r.parents.contains(this.demoLinkStyle.display)?(n.addParent(s,e.DEFINED,i.FROM),s.addChild(n,e.DEFINED,i.TO)):(n.addChild(s,e.DEFINED,i.FROM),s.addParent(n,e.DEFINED,i.TO));const a=new V({ea:this.ea,page:n,isInferred:!1,isCentral:!0,isSibling:!1,friendGateOnLeft:!0});a.ea=this.ea,a.setCenter({x:0,y:0});const u=new V({ea:this.ea,page:s,isInferred:!1,isCentral:!1,isSibling:!1,friendGateOnLeft:!1});u.ea=this.ea;let o=t.CHILD;r.leftFriends.contains(this.demoLinkStyle.display)?(u.setCenter({x:-300,y:0}),o=t.LEFT):r.rightFriends.contains(this.demoLinkStyle.display)?(u.setCenter({x:300,y:0}),o=t.RIGHT):r.parents.contains(this.demoLinkStyle.display)?(u.setCenter({x:0,y:-150}),o=t.PARENT):u.setCenter({x:0,y:150});const l=new Y(a,u,o,e.DEFINED,"base",this.ea,this.plugin.settings,this.plugin);a.style=Object.assign(Object.assign({},this.plugin.settings.baseNodeStyle),this.plugin.settings.centralNodeStyle),a.render(),u.style=Object.assign(Object.assign({},this.demoNodeStyle.getInheritedStyle()),this.demoNodeStyle.style),u.render(),l.style=Object.assign(Object.assign({},this.demoLinkStyle.getInheritedStyle()),this.demoLinkStyle.style),l.render(!1);const D=await this.ea.createSVG(null,!0,{withBackground:!0,withTheme:!1},null,"",40);D.removeAttribute("width"),D.removeAttribute("height"),this.demoLinkImg.setAttribute("src",g(D.outerHTML))}async hide(){this.dirty&&(""===this.plugin.settings.ontologySuggesterParentTrigger&&(this.plugin.settings.ontologySuggesterParentTrigger="::p"),""===this.plugin.settings.ontologySuggesterChildTrigger&&(this.plugin.settings.ontologySuggesterChildTrigger="::c"),""===this.plugin.settings.ontologySuggesterLeftFriendTrigger&&(this.plugin.settings.ontologySuggesterLeftFriendTrigger="::l"),""===this.plugin.settings.ontologySuggesterRightFriendTrigger&&(this.plugin.settings.ontologySuggesterRightFriendTrigger="::r"),""===this.plugin.settings.ontologySuggesterPreviousTrigger&&(this.plugin.settings.ontologySuggesterPreviousTrigger="::e"),""===this.plugin.settings.ontologySuggesterNextTrigger&&(this.plugin.settings.ontologySuggesterNextTrigger="::n"),""===this.plugin.settings.ontologySuggesterTrigger&&(this.plugin.settings.ontologySuggesterTrigger=":::"),""===this.plugin.settings.ontologySuggesterMidSentenceTrigger&&(this.plugin.settings.ontologySuggesterMidSentenceTrigger="("),this.plugin.setHierarchyLinkStylesExtended(),this.plugin.settings.tagStyleList=Object.keys(this.plugin.settings.tagNodeStyles),this.plugin.loadCustomNodeLabelFunction(),this.plugin.saveSettings(),this.plugin.scene&&!this.plugin.scene.terminated&&(this.plugin.scene.setBaseLayoutParams(),this.updateTimer&&this.plugin.scene.setTimer(),this.plugin.scene.reRender()))}colorpicker(e,t,i,s,r,a,u,o){let l,D,d,h,c,g;const E=new n.Setting(e).setName(t);i&&E.setDesc(q(i));const p=e=>{e?E.settingEl.addClass(U):E.settingEl.removeClass(U),D.disabled=e,D.style.opacity=e?"0.3":"1",l.setDisabled(e),l.sliderEl.style.opacity=e?"0.3":"1",h.style.opacity=e?"0.3":"1",c.style.opacity=e?"0.3":"1",g.style.opacity=e?"0.3":"1"};u&&E.addToggle((e=>{d=e,e.toggleEl.addClass("excalibrain-settings-toggle"),e.setValue(void 0!==s()).setTooltip(z("NODESTYLE_INCLUDE_TOGGLE")).onChange((e=>{if(this.dirty=!0,!e)return p(!0),void a();r(D.value+K(l.getValue())),p(!1)}))})),E.settingEl.removeClass("mod-toggle"),h=createEl("span",{text:"color:",cls:"excalibrain-settings-colorlabel"}),E.controlEl.appendChild(h),D=createEl("input",{type:"color",cls:"excalibrain-settings-colorpicker"},(e=>{var t;e.value=(null!==(t=s())&&void 0!==t?t:o).substring(0,7),e.onchange=()=>{r(e.value+K(l.getValue())),this.dirty=!0}})),E.controlEl.appendChild(D),c=createEl("span",{text:"opacity:",cls:"excalibrain-settings-opacitylabel"}),E.controlEl.appendChild(c),E.addSlider((e=>{var t,i;l=e,e.setLimits(0,1,.1).setValue((i=null!==(t=s())&&void 0!==t?t:o,parseInt(i.substring(7,9),16)/255)).onChange((e=>{r(D.value+K(e)),g.innerText=` ${e.toString()}`,D.style.opacity=e.toString(),this.dirty=!0}))})),g=createDiv({text:`${l.getValue().toString()}`,cls:"excalibrain-settings-sliderlabel"}),E.controlEl.appendChild(g),D.style.opacity=l.getValue().toString(),p(u&&!d.getValue())}numberslider(e,t,i,s,r,a,u,o,l){let D,d,h;const c=new n.Setting(e).setName(t),g=e=>{e?c.settingEl.addClass(U):c.settingEl.removeClass(U),h.setDisabled(e),h.sliderEl.style.opacity=e?"0.3":"1",D.style.opacity=e?"0.3":"1"};return o&&c.addToggle((e=>{d=e,e.toggleEl.addClass("excalibrain-settings-toggle"),e.setValue(void 0!==r()).setTooltip(z("NODESTYLE_INCLUDE_TOGGLE")).onChange((e=>{if(this.dirty=!0,!e)return g(!0),void u();a(h.getValue()),g(!1)}))})),c.addSlider((e=>{var t;h=e,e.setLimits(s.min,s.max,s.step).setValue(null!==(t=r())&&void 0!==t?t:l).onChange((async e=>{D.innerText=` ${e.toString()}`,a(e),this.dirty=!0}))})),i&&c.setDesc(q(i)),c.settingEl.createDiv("",(e=>{D=e,e.style.minWidth="2.3em",e.style.textAlign="right",e.innerText=` ${h.getValue().toString()}`})),g(o&&!d.getValue()),c}toggle(e,t,i,s,r,a,u,o){let l,D;const d=new n.Setting(e).setName(t),h=e=>{e?d.settingEl.addClass(U):d.settingEl.removeClass(U),D.setDisabled(e),D.toggleEl.style.opacity=e?"0.3":"1"};u&&d.addToggle((e=>{l=e,e.toggleEl.addClass("excalibrain-settings-toggle"),e.setValue(void 0!==s()).setTooltip(z("NODESTYLE_INCLUDE_TOGGLE")).onChange((e=>{if(this.dirty=!0,!e)return h(!0),void a();r(D.getValue()),h(!1)}))})),d.addToggle((e=>{var t;D=e,e.setValue(null!==(t=s())&&void 0!==t?t:o).onChange((async e=>{r(e),this.dirty=!0}))})),i&&d.setDesc(q(i)),h(u&&!l.getValue())}dropdownpicker(e,t,i,s,r,a,u,o,l){let D,d;const h=new n.Setting(e).setName(t),c=e=>{e?h.settingEl.addClass(U):h.settingEl.removeClass(U),D.setDisabled(e),D.selectEl.style.opacity=e?"0.3":"1"};o&&h.addToggle((e=>{d=e,e.toggleEl.addClass("excalibrain-settings-toggle"),e.setValue(void 0!==r()).setTooltip(z("NODESTYLE_INCLUDE_TOGGLE")).onChange((e=>{if(this.dirty=!0,!e)return c(!0),void u();a(D.getValue()),c(!1)}))})),h.addDropdown((e=>{var t;D=e,e.addOptions(s).setValue(null!==(t=r())&&void 0!==t?t:l).onChange((e=>{a(e),this.dirty=!0}))})),i&&h.setDesc(q(i)),c(o&&!d.getValue())}nodeSettings(e,t,i=!0,s){let r,a;const u=new n.Setting(e).setName(z("NODESTYLE_PREFIX_NAME")).setDesc(q(z("NODESTYLE_PREFIX_DESC"))),o=e=>{e?u.settingEl.addClass(U):u.settingEl.removeClass(U),r.setDisabled(e),r.inputEl.style.opacity=e?"0.3":"1"};i&&u.addToggle((e=>{a=e,e.toggleEl.addClass("excalibrain-settings-toggle"),e.setValue(void 0!==t.prefix).setTooltip(z("NODESTYLE_INCLUDE_TOGGLE")).onChange((e=>{if(this.dirty=!0,!e)return o(!0),t.prefix=void 0,void this.updateNodeDemoImg();o(!1)}))})),u.addText((e=>{var i;r=e,e.setValue(null!==(i=t.prefix)&&void 0!==i?i:s.prefix).onChange((e=>{t.prefix=e,this.updateNodeDemoImg(),this.dirty=!0}))})),o(i&&!a.getValue()),this.colorpicker(e,z("NODESTYLE_BGCOLOR"),null,(()=>t.backgroundColor),(e=>{t.backgroundColor=e,this.updateNodeDemoImg()}),(()=>{delete t.backgroundColor,this.updateNodeDemoImg()}),i,s.backgroundColor),this.dropdownpicker(e,z("NODESTYLE_BG_FILLSTYLE"),null,{hachure:"Hachure","cross-hatch":"Cross-hatch",solid:"Solid"},(()=>{var e;return null===(e=t.fillStyle)||void 0===e?void 0:e.toString()}),(e=>{t.fillStyle=e,this.updateNodeDemoImg()}),(()=>{delete t.fillStyle,this.updateNodeDemoImg()}),i,s.fillStyle.toString()),this.colorpicker(e,z("NODESTYLE_TEXTCOLOR"),null,(()=>t.textColor),(e=>{t.textColor=e,this.updateNodeDemoImg()}),(()=>{delete t.textColor,this.updateNodeDemoImg()}),i,s.textColor),this.colorpicker(e,z("NODESTYLE_BORDERCOLOR"),null,(()=>t.borderColor),(e=>{t.borderColor=e,this.updateNodeDemoImg()}),(()=>{delete t.borderColor,this.updateNodeDemoImg()}),i,s.borderColor),this.numberslider(e,z("NODESTYLE_FONTSIZE"),null,{min:10,max:50,step:5},(()=>t.fontSize),(e=>{t.fontSize=e,this.updateNodeDemoImg()}),(()=>{delete t.fontSize,this.updateNodeDemoImg()}),i,s.fontSize),this.dropdownpicker(e,z("NODESTYLE_FONTFAMILY"),null,{1:"Hand-drawn",2:"Normal",3:"Code",4:"Fourth (custom) Font"},(()=>{var e;return null===(e=t.fontFamily)||void 0===e?void 0:e.toString()}),(e=>{t.fontFamily=parseInt(e),this.updateNodeDemoImg()}),(()=>{delete t.fontFamily,this.updateNodeDemoImg()}),i,s.fontFamily.toString()),this.numberslider(e,z("NODESTYLE_MAXLABELLENGTH_NAME"),z("NODESTYLE_MAXLABELLENGTH_DESC"),{min:15,max:100,step:5},(()=>t.maxLabelLength),(e=>{t.maxLabelLength=e,this.updateNodeDemoImg()}),(()=>{delete t.maxLabelLength,this.updateNodeDemoImg()}),i,s.maxLabelLength),this.dropdownpicker(e,z("NODESTYLE_ROUGHNESS"),null,{0:"Architect",1:"Artist",2:"Cartoonist"},(()=>{var e;return null===(e=t.roughness)||void 0===e?void 0:e.toString()}),(e=>{t.roughness=parseInt(e),this.updateNodeDemoImg()}),(()=>{delete t.roughness,this.updateNodeDemoImg()}),i,s.roughness.toString()),this.dropdownpicker(e,z("NODESTYLE_SHARPNESS"),null,{sharp:"Sharp",round:"Round"},(()=>t.strokeShaprness),(e=>{t.strokeShaprness=e,this.updateNodeDemoImg()}),(()=>{delete t.strokeShaprness,this.updateNodeDemoImg()}),i,s.strokeShaprness),this.numberslider(e,z("NODESTYLE_STROKEWIDTH"),null,{min:.5,max:6,step:.5},(()=>t.strokeWidth),(e=>{t.strokeWidth=e,this.updateNodeDemoImg()}),(()=>{delete t.strokeWidth,this.updateNodeDemoImg()}),i,s.strokeWidth),this.dropdownpicker(e,z("NODESTYLE_STROKESTYLE"),null,{solid:"Solid",dashed:"Dashed",dotted:"Dotted"},(()=>t.strokeStyle),(e=>{t.strokeStyle=e,this.updateNodeDemoImg()}),(()=>{delete t.strokeStyle,this.updateNodeDemoImg()}),i,s.strokeStyle),this.numberslider(e,z("NODESTYLE_RECTANGLEPADDING"),null,{min:5,max:50,step:5},(()=>t.padding),(e=>{t.padding=e,this.updateNodeDemoImg()}),(()=>{delete t.padding,this.updateNodeDemoImg()}),i,s.padding),this.numberslider(e,z("NODESTYLE_GATE_RADIUS_NAME"),z("NODESTYLE_GATE_RADIUS_DESC"),{min:3,max:10,step:1},(()=>t.gateRadius),(e=>{t.gateRadius=e,this.updateNodeDemoImg()}),(()=>{delete t.gateRadius,this.updateNodeDemoImg()}),i,s.gateRadius),this.numberslider(e,z("NODESTYLE_GATE_OFFSET_NAME"),z("NODESTYLE_GATE_OFFSET_DESC"),{min:0,max:25,step:1},(()=>t.gateOffset),(e=>{t.gateOffset=e,this.updateNodeDemoImg()}),(()=>{delete t.gateOffset,this.updateNodeDemoImg()}),i,s.gateOffset),this.colorpicker(e,z("NODESTYLE_GATE_COLOR"),null,(()=>t.gateStrokeColor),(e=>{t.gateStrokeColor=e,this.updateNodeDemoImg()}),(()=>{delete t.gateStrokeColor,this.updateNodeDemoImg()}),i,s.gateStrokeColor),this.colorpicker(e,z("NODESTYLE_GATE_BGCOLOR_NAME"),z("NODESTYLE_GATE_BGCOLOR_DESC"),(()=>t.gateBackgroundColor),(e=>{t.gateBackgroundColor=e,this.updateNodeDemoImg()}),(()=>{delete t.gateBackgroundColor,this.updateNodeDemoImg()}),i,s.gateBackgroundColor),this.dropdownpicker(e,z("NODESTYLE_GATE_FILLSTYLE"),null,{hachure:"Hachure","cross-hatch":"Cross-hatch",solid:"Solid"},(()=>{var e;return null===(e=t.gateFillStyle)||void 0===e?void 0:e.toString()}),(e=>{t.gateFillStyle=e,this.updateNodeDemoImg()}),(()=>{delete t.gateFillStyle,this.updateNodeDemoImg()}),i,s.gateFillStyle.toString())}linkSettings(e,t,i=!0,n){this.colorpicker(e,z("LINKSTYLE_COLOR"),null,(()=>t.strokeColor),(e=>{t.strokeColor=e,this.updateLinkDemoImg()}),(()=>{delete t.strokeColor,this.updateLinkDemoImg()}),i,n.strokeColor),this.numberslider(e,z("LINKSTYLE_WIDTH"),null,{min:.5,max:10,step:.5},(()=>t.strokeWidth),(e=>{t.strokeWidth=e,this.updateLinkDemoImg()}),(()=>{delete t.strokeWidth,this.updateLinkDemoImg()}),i,n.strokeWidth),this.dropdownpicker(e,z("LINKSTYLE_ROUGHNESS"),null,{0:"Architect",1:"Artist",2:"Cartoonist"},(()=>{var e;return null===(e=t.roughness)||void 0===e?void 0:e.toString()}),(e=>{t.roughness=parseInt(e),this.updateLinkDemoImg()}),(()=>{delete t.roughness,this.updateLinkDemoImg()}),i,n.roughness.toString()),this.dropdownpicker(e,z("LINKSTYLE_STROKE"),null,{solid:"Solid",dashed:"Dashed",dotted:"Dotted"},(()=>t.strokeStyle),(e=>{t.strokeStyle=e,this.updateLinkDemoImg()}),(()=>{delete t.strokeStyle,this.updateLinkDemoImg()}),i,n.strokeStyle),this.dropdownpicker(e,z("LINKSTYLE_ARROWSTART"),null,{none:"None",arrow:"Arrow",bar:"Bar",dot:"Dot",triangle:"Triangle"},(()=>t.startArrowHead),(e=>{t.startArrowHead=""===e?null:e,this.updateLinkDemoImg()}),(()=>{delete t.startArrowHead,this.updateLinkDemoImg()}),i,n.startArrowHead),this.dropdownpicker(e,z("LINKSTYLE_ARROWEND"),null,{none:"None",arrow:"Arrow",bar:"Bar",dot:"Dot",triangle:"Triangle"},(()=>t.endArrowHead),(e=>{t.endArrowHead=""===e?null:e,this.updateLinkDemoImg()}),(()=>{delete t.endArrowHead,this.updateLinkDemoImg()}),i,n.endArrowHead),this.toggle(e,z("LINKSTYLE_SHOWLABEL"),null,(()=>t.showLabel),(e=>{t.showLabel=e,this.updateLinkDemoImg()}),(()=>{delete t.showLabel,this.updateLinkDemoImg()}),i,n.showLabel),this.colorpicker(e,z("NODESTYLE_TEXTCOLOR"),null,(()=>t.textColor),(e=>{t.textColor=e,this.updateLinkDemoImg()}),(()=>{delete t.textColor,this.updateLinkDemoImg()}),i,n.textColor),this.numberslider(e,z("LINKSTYLE_FONTSIZE"),null,{min:6,max:30,step:3},(()=>t.fontSize),(e=>{t.fontSize=e,this.updateLinkDemoImg()}),(()=>{delete t.fontSize,this.updateLinkDemoImg()}),i,n.fontSize),this.dropdownpicker(e,z("LINKSTYLE_FONTFAMILY"),null,{1:"Hand-drawn",2:"Normal",3:"Code",4:"Fourth (custom) Font"},(()=>{var e;return null===(e=t.fontFamily)||void 0===e?void 0:e.toString()}),(e=>{t.fontFamily=parseInt(e),this.updateLinkDemoImg()}),(()=>{delete t.fontFamily,this.updateLinkDemoImg()}),i,n.fontFamily.toString())}getUnusedFieldNames(){const e=new Set;this.plugin.DVAPI.index.pages.forEach((t=>{const i=null==t?void 0:t.fields.keys();if(!i)return;let n;for(;!(n=i.next()).done;)n.value.contains(",")||n.value.startsWith("**")&&n.value.endsWith("**")||e.add(n.value)}));const t=new Map;e.forEach((e=>{t.set(e,e.toLowerCase().replaceAll(" ","-"))}));const i=new Set;return this.plugin.settings.hierarchy.hidden.forEach((e=>i.add(e.toLowerCase().replaceAll(" ","-")))),this.plugin.settings.hierarchy.parents.forEach((e=>i.add(e.toLowerCase().replaceAll(" ","-")))),this.plugin.settings.hierarchy.children.forEach((e=>i.add(e.toLowerCase().replaceAll(" ","-")))),this.plugin.settings.hierarchy.leftFriends.forEach((e=>i.add(e.toLowerCase().replaceAll(" ","-")))),this.plugin.settings.hierarchy.rightFriends.forEach((e=>i.add(e.toLowerCase().replaceAll(" ","-")))),this.plugin.settings.hierarchy.previous.forEach((e=>i.add(e.toLowerCase().replaceAll(" ","-")))),this.plugin.settings.hierarchy.next.forEach((e=>i.add(e.toLowerCase().replaceAll(" ","-")))),this.plugin.settings.hierarchy.exclusions.forEach((e=>i.add(e.toLowerCase().replaceAll(" ","-")))),Array.from(t.entries()).forEach((([e,n])=>i.has(e.toLowerCase().replaceAll(" ","-"))||i.has(n)?(t.delete(e),void t.delete(n)):void(e!==n&&t.delete(n)))),Array.from(t.keys()).sort(((e,t)=>e.toLowerCase()<t.toLowerCase()?-1:1)).join(", ")}async display(){await this.plugin.loadSettings(),this.ea=w();const t=new A(null,"This is a demo node that is 46 characters long",null,this.plugin),s=new A(null,"This is a child node",null,this.plugin);t.addChild(s,e.DEFINED,i.FROM),s.addParent(t,e.DEFINED,i.TO),this.demoNode=new V({ea:this.ea,page:t,isInferred:!1,isCentral:!1,isSibling:!1,friendGateOnLeft:!0}),this.demoNode.ea=this.ea,this.demoNode.setCenter({x:0,y:0});const{containerEl:r}=this;this.containerEl.empty();const a=r.createDiv("coffee");a.addClass("ex-coffee-div"),a.createEl("a",{href:"https://ko-fi.com/zsolt"}).createEl("img",{attr:{src:"https://cdn.ko-fi.com/cdn/kofi3.png?v=3"}}).height=45,new n.Setting(r).setName(z("EXCALIBRAIN_FILE_NAME")).setDesc(q(z("EXCALIBRAIN_FILE_DESC"))).addText((e=>e.setValue(this.plugin.settings.excalibrainFilepath).onChange((t=>{this.dirty=!0,t.endsWith(".md")||(t+=t.endsWith(".m")?"d":t.endsWith(".")?"md":".md"),this.app.vault.getAbstractFileByPath(t)?new H(this.app,"⚠ File Exists",`${t} already exists in your Vault. Is it ok to overwrite this file?`).show((i=>{i&&(this.plugin.settings.excalibrainFilepath=t,this.dirty=!0,e.inputEl.value=t)})):(this.plugin.settings.excalibrainFilepath=t,this.dirty=!0)})).inputEl.onblur=()=>{e.setValue(this.plugin.settings.excalibrainFilepath)})),this.numberslider(r,z("INDEX_REFRESH_FREQ_NAME"),z("INDEX_REFRESH_FREQ_DESC"),{min:5,max:120,step:5},(()=>this.plugin.settings.indexUpdateInterval/1e3),(e=>{this.plugin.settings.indexUpdateInterval=1e3*e,this.updateTimer=!0}),(()=>{}),!1,5e3),this.containerEl.createEl("h1",{cls:"excalibrain-settings-h1",text:z("HIERARCHY_HEAD")}),this.containerEl.createEl("p",{}).innerHTML=z("HIERARCHY_DESC");let u=()=>{};const o=new n.Setting(r).setName(z("PARENTS_NAME")).addTextArea((e=>{e.inputEl.style.height="90px",e.inputEl.style.width="100%",e.setValue(this.plugin.settings.hierarchy.parents.join(", ")).onChange((e=>{this.plugin.settings.hierarchy.parents=e.split(",").map((e=>e.trim())).sort(((e,t)=>e.toLowerCase()<t.toLowerCase()?-1:1)),this.plugin.hierarchyLowerCase.parents=[],this.plugin.settings.hierarchy.parents.forEach((e=>this.plugin.hierarchyLowerCase.parents.push(e.toLowerCase().replaceAll(" ","-")))),u(),this.dirty=!0}))}));o.nameEl.addClass("excalibrain-setting-nameEl"),o.descEl.addClass("excalibrain-setting-descEl"),o.controlEl.addClass("excalibrain-setting-controlEl");const l=new n.Setting(r).setName(z("CHILDREN_NAME")).addTextArea((e=>{e.inputEl.style.height="90px",e.inputEl.style.width="100%",e.setValue(this.plugin.settings.hierarchy.children.join(", ")).onChange((e=>{this.plugin.settings.hierarchy.children=e.split(",").map((e=>e.trim())).sort(((e,t)=>e.toLowerCase()<t.toLowerCase()?-1:1)),this.plugin.hierarchyLowerCase.children=[],this.plugin.settings.hierarchy.children.forEach((e=>this.plugin.hierarchyLowerCase.children.push(e.toLowerCase().replaceAll(" ","-")))),u(),this.dirty=!0}))}));l.nameEl.addClass("excalibrain-setting-nameEl"),l.descEl.addClass("excalibrain-setting-descEl"),l.controlEl.addClass("excalibrain-setting-controlEl");const D=new n.Setting(r).setName(z("LEFT_FRIENDS_NAME")).addTextArea((e=>{e.inputEl.style.height="90px",e.inputEl.style.width="100%",e.setValue(this.plugin.settings.hierarchy.leftFriends.join(", ")).onChange((e=>{this.plugin.settings.hierarchy.leftFriends=e.split(",").map((e=>e.trim())).sort(((e,t)=>e.toLowerCase()<t.toLowerCase()?-1:1)),this.plugin.hierarchyLowerCase.leftFriends=[],this.plugin.settings.hierarchy.leftFriends.forEach((e=>this.plugin.hierarchyLowerCase.leftFriends.push(e.toLowerCase().replaceAll(" ","-")))),u(),this.dirty=!0}))}));D.nameEl.addClass("excalibrain-setting-nameEl"),D.descEl.addClass("excalibrain-setting-descEl"),D.controlEl.addClass("excalibrain-setting-controlEl");const d=new n.Setting(r).setName(z("RIGHT_FRIENDS_NAME")).addTextArea((e=>{e.inputEl.style.height="90px",e.inputEl.style.width="100%",e.setValue(this.plugin.settings.hierarchy.rightFriends.join(", ")).onChange((e=>{this.plugin.settings.hierarchy.rightFriends=e.split(",").map((e=>e.trim())).sort(((e,t)=>e.toLowerCase()<t.toLowerCase()?-1:1)),this.plugin.hierarchyLowerCase.rightFriends=[],this.plugin.settings.hierarchy.rightFriends.forEach((e=>this.plugin.hierarchyLowerCase.rightFriends.push(e.toLowerCase().replaceAll(" ","-")))),u(),this.dirty=!0}))}));d.nameEl.addClass("excalibrain-setting-nameEl"),d.descEl.addClass("excalibrain-setting-descEl"),d.controlEl.addClass("excalibrain-setting-controlEl");const h=new n.Setting(r).setName(z("PREVIOUS_NAME")).addTextArea((e=>{e.inputEl.style.height="90px",e.inputEl.style.width="100%",e.setValue(this.plugin.settings.hierarchy.previous.join(", ")).onChange((e=>{this.plugin.settings.hierarchy.previous=e.split(",").map((e=>e.trim())).sort(((e,t)=>e.toLowerCase()<t.toLowerCase()?-1:1)),this.plugin.hierarchyLowerCase.previous=[],this.plugin.settings.hierarchy.previous.forEach((e=>this.plugin.hierarchyLowerCase.previous.push(e.toLowerCase().replaceAll(" ","-")))),u(),this.dirty=!0}))}));h.nameEl.addClass("excalibrain-setting-nameEl"),h.descEl.addClass("excalibrain-setting-descEl"),h.controlEl.addClass("excalibrain-setting-controlEl");const c=new n.Setting(r).setName(z("NEXT_NAME")).addTextArea((e=>{e.inputEl.style.height="90px",e.inputEl.style.width="100%",e.setValue(this.plugin.settings.hierarchy.next.join(", ")).onChange((e=>{this.plugin.settings.hierarchy.next=e.split(",").map((e=>e.trim())).sort(((e,t)=>e.toLowerCase()<t.toLowerCase()?-1:1)),this.plugin.hierarchyLowerCase.next=[],this.plugin.settings.hierarchy.next.forEach((e=>this.plugin.hierarchyLowerCase.next.push(e.toLowerCase().replaceAll(" ","-")))),u(),this.dirty=!0}))}));c.nameEl.addClass("excalibrain-setting-nameEl"),c.descEl.addClass("excalibrain-setting-descEl"),c.controlEl.addClass("excalibrain-setting-controlEl");const g=new n.Setting(r).setName(z("HIDDEN_NAME")).setDesc(z("HIDDEN_DESC")).addTextArea((e=>{e.inputEl.style.height="90px",e.inputEl.style.width="100%",e.setValue(this.plugin.settings.hierarchy.hidden.join(", ")).onChange((e=>{this.plugin.settings.hierarchy.hidden=e.split(",").map((e=>e.trim())).sort(((e,t)=>e.toLowerCase()<t.toLowerCase()?-1:1)),this.plugin.hierarchyLowerCase.hidden=[],this.plugin.settings.hierarchy.hidden.forEach((e=>this.plugin.hierarchyLowerCase.hidden.push(e.toLowerCase().replaceAll(" ","-")))),u(),this.dirty=!0}))}));g.nameEl.addClass("excalibrain-setting-nameEl"),g.descEl.addClass("excalibrain-setting-descEl"),g.controlEl.addClass("excalibrain-setting-controlEl");const E=new n.Setting(r).setName(z("EXCLUSIONS_NAME")).setDesc(z("EXCLUSIONS_DESC")).addTextArea((e=>{e.inputEl.style.height="90px",e.inputEl.style.width="100%",e.setValue(this.plugin.settings.hierarchy.exclusions.join(", ")).onChange((e=>{this.plugin.settings.hierarchy.exclusions=e.split(",").map((e=>e.trim())).sort(((e,t)=>e.toLowerCase()<t.toLowerCase()?-1:1)),u(),this.dirty=!0}))}));let p;E.nameEl.addClass("excalibrain-setting-nameEl"),E.descEl.addClass("excalibrain-setting-descEl"),E.controlEl.addClass("excalibrain-setting-controlEl");const f=new n.Setting(r).setName(z("UNASSIGNED_NAME")).setDesc(z("UNASSIGNED_DESC")).addTextArea((e=>{p=e,e.inputEl.style.height="90px",e.inputEl.style.width="100%",e.setValue(this.getUnusedFieldNames()),e.setDisabled(!0)}));let F,m,C,y,S,b;f.nameEl.addClass("excalibrain-setting-nameEl"),f.descEl.addClass("excalibrain-setting-descEl"),f.controlEl.addClass("excalibrain-setting-controlEl"),new n.Setting(r).setName(z("INFER_NAME")).setDesc(q(z("INFER_DESC"))).addToggle((e=>e.setValue(this.plugin.settings.inferAllLinksAsFriends).onChange((e=>{this.plugin.settings.inferAllLinksAsFriends=e,this.dirty=!0})))),new n.Setting(r).setName(z("REVERSE_NAME")).setDesc(q(z("REVERSE_DESC"))).addToggle((e=>e.setValue(this.plugin.settings.inverseInfer).onChange((e=>{this.plugin.settings.inverseInfer=e,this.dirty=!0})))),new n.Setting(r).setName(z("INVERSE_ARROW_DIRECTION_NAME")).setDesc(q(z("INVERSE_ARROW_DIRECTION_DESC"))).addToggle((e=>e.setValue(this.plugin.settings.inverseArrowDirection).onChange((e=>{this.plugin.settings.inverseArrowDirection=e,this.dirty=!0})))),new n.Setting(r).setName(z("ONTOLOGY_SUGGESTER_NAME")).setDesc(z("ONTOLOGY_SUGGESTER_DESC")).addToggle((e=>e.setValue(this.plugin.settings.allowOntologySuggester).onChange((e=>{this.plugin.settings.allowOntologySuggester=e,y.setDisabled(!e),F.setDisabled(!e),m.setDisabled(!e),C.setDisabled(!e),S.setDisabled(!e),b.setDisabled(!e),this.dirty=!0})))),y=new n.Setting(r).setName(z("ONTOLOGY_SUGGESTER_ALL_NAME")).setDisabled(!this.plugin.settings.allowOntologySuggester).addText((e=>e.setValue(this.plugin.settings.ontologySuggesterTrigger).onChange((e=>{this.plugin.settings.ontologySuggesterTrigger=e,this.dirty=!0})))),F=new n.Setting(r).setName(z("ONTOLOGY_SUGGESTER_PARENT_NAME")).setDisabled(!this.plugin.settings.allowOntologySuggester).addText((e=>e.setValue(this.plugin.settings.ontologySuggesterParentTrigger).onChange((e=>{this.plugin.settings.ontologySuggesterParentTrigger=e,this.dirty=!0})))),m=new n.Setting(r).setName(z("ONTOLOGY_SUGGESTER_CHILD_NAME")).setDisabled(!this.plugin.settings.allowOntologySuggester).addText((e=>e.setValue(this.plugin.settings.ontologySuggesterChildTrigger).onChange((e=>{this.plugin.settings.ontologySuggesterChildTrigger=e,this.dirty=!0})))),C=new n.Setting(r).setName(z("ONTOLOGY_SUGGESTER_LEFT_FRIEND_NAME")).setDisabled(!this.plugin.settings.allowOntologySuggester).addText((e=>e.setValue(this.plugin.settings.ontologySuggesterLeftFriendTrigger).onChange((e=>{this.plugin.settings.ontologySuggesterLeftFriendTrigger=e,this.dirty=!0})))),C=new n.Setting(r).setName(z("ONTOLOGY_SUGGESTER_RIGHT_FRIEND_NAME")).setDisabled(!this.plugin.settings.allowOntologySuggester).addText((e=>e.setValue(this.plugin.settings.ontologySuggesterRightFriendTrigger).onChange((e=>{this.plugin.settings.ontologySuggesterRightFriendTrigger=e,this.dirty=!0})))),C=new n.Setting(r).setName(q(z("ONTOLOGY_SUGGESTER_PREVIOUS_NAME"))).setDisabled(!this.plugin.settings.allowOntologySuggester).addText((e=>e.setValue(this.plugin.settings.ontologySuggesterPreviousTrigger).onChange((e=>{this.plugin.settings.ontologySuggesterPreviousTrigger=e,this.dirty=!0})))),C=new n.Setting(r).setName(z("ONTOLOGY_SUGGESTER_NEXT_NAME")).setDisabled(!this.plugin.settings.allowOntologySuggester).addText((e=>e.setValue(this.plugin.settings.ontologySuggesterNextTrigger).onChange((e=>{this.plugin.settings.ontologySuggesterNextTrigger=e,this.dirty=!0})))),S=new n.Setting(r).setName(z("MID_SENTENCE_SUGGESTER_TRIGGER_NAME")).setDesc(q(z("MID_SENTENCE_SUGGESTER_TRIGGER_DESC"))).addDropdown((e=>{e.addOption("(","(").addOption("[","[").setValue(this.plugin.settings.ontologySuggesterMidSentenceTrigger).onChange((e=>{this.plugin.settings.ontologySuggesterMidSentenceTrigger=e,this.dirty=!0}))})),b=new n.Setting(r).setName(z("BOLD_FIELDS_NAME")).setDesc(q(z("BOLD_FIELDS_DESC"))).addToggle((e=>e.setValue(this.plugin.settings.boldFields).onChange((e=>{this.plugin.settings.boldFields=e,this.dirty=!0})))),this.containerEl.createEl("h1",{cls:"excalibrain-settings-h1",text:z("BEHAVIOR_HEAD")}),new n.Setting(r).setName(z("TOGGLE_AUTOOPEN_WHEN_EMBED_TOGGLE_NAME")).setDesc(q(z("TOGGLE_AUTOOPEN_WHEN_EMBED_TOGGLE_DESC"))).addToggle((e=>e.setValue(this.plugin.settings.toggleEmbedTogglesAutoOpen).onChange((e=>{this.plugin.settings.toggleEmbedTogglesAutoOpen=e,this.dirty=!0}))));const T=new n.Setting(r).setName(z("EXCLUDE_PATHLIST_NAME")).setDesc(q(z("EXCLUDE_PATHLIST_DESC"))).addTextArea((e=>{e.inputEl.style.height="100px",e.inputEl.style.width="100%",e.setValue(this.plugin.settings.excludeFilepaths.join(", ")).onChange((e=>{const t=(e=e.replaceAll("\n"," ")).split(",").map((e=>e.trim()));this.plugin.settings.excludeFilepaths=t.filter((e=>""!==e)),this.dirty=!0}))}));T.descEl.style.width="90%",T.controlEl.style.width="90%";const v=new n.Setting(r).setName(z("NODETITLE_SCRIPT_NAME")).setDesc(q(z("NODETITLE_SCRIPT_DESC"))).addTextArea((e=>{e.inputEl.style.height="200px",e.inputEl.style.width="100%",e.setValue(this.plugin.settings.nodeTitleScript).onChange((e=>{this.plugin.settings.nodeTitleScript=e,this.dirty=!0}))}));let N,L;v.descEl.style.width="90%",v.controlEl.style.width="90%",this.containerEl.createEl("h1",{cls:"excalibrain-settings-h1",text:z("DISPLAY_HEAD")}),new n.Setting(r).setName(z("COMPACT_VIEW_NAME")).setDesc(q(z("COMPACT_VIEW_DESC"))).addToggle((e=>e.setValue(this.plugin.settings.compactView).onChange((e=>{this.plugin.settings.compactView=e,this.dirty=!0})))),this.numberslider(r,z("COMPACTING_FACTOR_NAME"),z("COMPACTING_FACTOR_DESC"),{min:1,max:2,step:.1},(()=>this.plugin.settings.compactingFactor),(e=>{this.plugin.settings.compactingFactor=e,this.dirty=!0}),(()=>{}),!1,1),this.numberslider(r,z("MINLINKLENGTH_NAME"),z("MINLINKLENGTH_DESC"),{min:0,max:50,step:1},(()=>this.plugin.settings.minLinkLength),(e=>{this.plugin.settings.minLinkLength=e,this.dirty=!0}),(()=>{}),!1,1),new n.Setting(r).setName(z("SHOW_FULL_TAG_PATH_NAME")).setDesc(q(z("SHOW_FULL_TAG_PATH_DESC"))).addToggle((e=>e.setValue(this.plugin.settings.showFullTagName).onChange((e=>{this.plugin.settings.showFullTagName=e,this.dirty=!0})))),this.numberslider(r,z("MAX_ITEMCOUNT_NAME"),z("MAX_ITEMCOUNT_DESC"),{min:5,max:150,step:5},(()=>this.plugin.settings.maxItemCount),(e=>this.plugin.settings.maxItemCount=e),(()=>{}),!1,30),new n.Setting(r).setName(z("SHOW_COUNT_NAME")).setDesc(q(z("SHOW_COUNT_DESC"))).addToggle((e=>e.setValue(this.plugin.settings.showNeighborCount).onChange((e=>{this.plugin.settings.showNeighborCount=e,this.dirty=!0})))),new n.Setting(r).setName(z("ALLOW_AUTOZOOM_NAME")).setDesc(q(z("ALLOW_AUTOZOOM_DESC"))).addToggle((e=>e.setValue(this.plugin.settings.allowAutozoom).onChange((e=>{this.plugin.settings.allowAutozoom=e,this.dirty=!0})))),this.numberslider(r,z("MAX_AUTOZOOM_NAME"),z("MAX_AUTOZOOM_DESC"),{min:10,max:1e3,step:10},(()=>100*this.plugin.settings.maxZoom),(e=>this.plugin.settings.maxZoom=e/100),(()=>{}),!1,100),new n.Setting(r).setName(z("ALLOW_AUTOFOCUS_ON_SEARCH_NAME")).setDesc(q(z("ALLOW_AUTOFOCUS_ON_SEARCH_DESC"))).addToggle((e=>e.setValue(this.plugin.settings.allowAutofocuOnSearch).onChange((e=>{this.plugin.settings.allowAutofocuOnSearch=e,this.dirty=!0})))),new n.Setting(r).setName(z("ALWAYS_ON_TOP_NAME")).setDesc(q(z("ALWAYS_ON_TOP_DESC"))).addToggle((e=>e.setValue(this.plugin.settings.defaultAlwaysOnTop).onChange((e=>{this.plugin.settings.defaultAlwaysOnTop=e,this.dirty=!0})))),this.numberslider(r,z("EMBEDDED_FRAME_WIDTH_NAME"),void 0,{min:400,max:1600,step:50},(()=>this.plugin.settings.centerEmbedWidth),(e=>this.plugin.settings.centerEmbedWidth=e),(()=>{}),!1,this.plugin.settings.centerEmbedWidth),this.numberslider(r,z("EMBEDDED_FRAME_HEIGHT_NAME"),void 0,{min:400,max:1600,step:50},(()=>this.plugin.settings.centerEmbedHeight),(e=>this.plugin.settings.centerEmbedHeight=e),(()=>{}),!1,this.plugin.settings.centerEmbedHeight),r.createEl("h1",{cls:"excalibrain-settings-h1",text:z("STYLE_HEAD")}),this.containerEl.createEl("p",{}).innerHTML=z("STYLE_DESC"),this.colorpicker(r,z("CANVAS_BGCOLOR"),null,(()=>this.plugin.settings.backgroundColor),(e=>{this.plugin.settings.backgroundColor=e,this.updateNodeDemoImg()}),(()=>{}),!1,this.plugin.settings.backgroundColor);const O=e=>{L.empty();const t=this.plugin.nodeStyles[e];this.nodeSettings(L,t.style,t.allowOverride,t.getInheritedStyle()),this.demoNodeStyle=t,this.updateNodeDemoImg()},_=new n.Setting(r).setName(z("TAGLIST_NAME")).setDesc(z("TAGLIST_DESC")).addTextArea((e=>{e.inputEl.style.height="200px",e.inputEl.style.width="100%",e.setValue(this.plugin.settings.tagStyleList.sort(((e,t)=>e.toLowerCase()<t.toLowerCase()?-1:1)).join(", ")).onChange((e=>{const t=this.plugin.settings.tagNodeStyles,i=this.plugin.nodeStyles,n=(e=e.replaceAll("\n"," ")).split(",").map((e=>e.trim())).sort(((e,t)=>e.toLocaleLowerCase()<t.toLocaleLowerCase()?-1:1));this.plugin.settings.tagStyleList=n,Object.keys(t).forEach((e=>{n.contains(e)||(delete t[e],delete i[e])})),n.forEach((e=>{Object.keys(t).contains(e)||(t[e]={},i[e]={style:t[e],allowOverride:!0,userStyle:!0,display:e,getInheritedStyle:()=>this.plugin.settings.baseNodeStyle})}));const s=N.getValue();for(let e=N.selectEl.options.length-1;e>=0;e--)N.selectEl.remove(e);Object.entries(i).forEach((e=>{N.addOption(e[0],e[1].display)})),i[s]?N.setValue(s):(N.setValue("base"),O("base")),this.dirty=!0}))}));new n.Setting(r).setName(z("NOTE_STYLE_TAG_NAME")).setDesc(z("NOTE_STYLE_TAG_DESC")).addText((e=>e.setValue(this.plugin.settings.primaryTagField).onChange((e=>{this.plugin.settings.primaryTagField=e,this.plugin.settings.primaryTagFieldLowerCase=e.toLocaleLowerCase().replaceAll(" ","-"),this.dirty=!0})))),new n.Setting(r).setName(z("ALL_STYLE_PREFIXES_NAME")).setDesc(z("ALL_STYLE_PREFIXES_DESC")).addToggle((e=>e.setValue(this.plugin.settings.displayAllStylePrefixes).onChange((e=>{this.plugin.settings.displayAllStylePrefixes=e,this.dirty=!0})))),_.descEl.style.width="90%",_.controlEl.style.width="90%";const k=r.createDiv({cls:"setting-item"}),B=k.createDiv({cls:"setting-item-info"});let x;N=new n.DropdownComponent(B),k.createDiv({text:"Show inherited",cls:"setting-item-name"}).style.marginRight="10px";let R=!1;const M=new n.ToggleComponent(k);M.setValue(!0).setTooltip("Show/Hide Inherited Properties").onChange((e=>{R?R=!1:(e?Z(W):$(W,U),R=!0,x.setValue(e))})),Object.entries(this.plugin.nodeStyles).forEach((e=>{N.addOption(e[0],e[1].display)})),this.demoNodeImg=r.createEl("img",{cls:"excalibrain-settings-demoimg"}),L=r.createDiv({cls:"excalibrain-setting-style-section"}),Z(W),N.setValue("base").onChange(O);const P=this.plugin.nodeStyles.base;let G,Y;this.nodeSettings(L,P.style,P.allowOverride,P.getInheritedStyle()),this.demoNodeStyle=P,this.updateNodeDemoImg();const j=e=>{Y.empty();const t=this.plugin.linkStyles[e];this.linkSettings(Y,t.style,t.allowOverride,t.getInheritedStyle()),this.demoLinkStyle=t,this.updateLinkDemoImg()},K=r.createDiv({cls:"setting-item"}),X=K.createDiv({cls:"setting-item-info"});G=new n.DropdownComponent(X),K.createDiv({text:"Show inherited",cls:"setting-item-name"}).style.marginRight="10px",x=new n.ToggleComponent(K),x.setValue(!0).setTooltip("Show/Hide Inherited Properties").onChange((e=>{R?R=!1:(e?Z(W):$(W,U),R=!0,M.setValue(e))})),Object.entries(this.plugin.linkStyles).forEach((e=>{G.addOption(e[0],e[1].display)})),this.demoLinkImg=r.createEl("img",{cls:"excalibrain-settings-demoimg"}),Y=r.createDiv({cls:"excalibrain-setting-nodestyle-section"}),G.setValue("base").onChange(j);const J=this.plugin.linkStyles.base;this.linkSettings(Y,J.style,J.allowOverride,J.getInheritedStyle()),this.demoLinkStyle=J,this.updateLinkDemoImg(),u=()=>{p.setValue(this.getUnusedFieldNames());const e=this.plugin.settings.hierarchyLinkStyles,t=this.plugin.linkStyles;Object.keys(t).forEach((i=>{I.contains(i)||this.hierarchyStyleList.contains(i)||(delete t[i],delete e[i])})),this.hierarchyStyleList.forEach((i=>{Object.keys(e).contains(i)||I.contains(i)||(e[i]={},t[i]={style:e[i],allowOverride:!0,userStyle:!0,display:i,getInheritedStyle:()=>this.plugin.settings.baseLinkStyle})}));const i=G.getValue();for(let e=G.selectEl.options.length-1;e>=0;e--)G.selectEl.remove(e);const n=this.plugin.settings.hierarchy,s=e=>I.includes(e)?"0"+e.toLowerCase():n.parents.includes(e)?"1"+e.toLowerCase():n.children.includes(e)?"2"+e.toLowerCase():"3"+e.toLowerCase();Object.entries(t).sort(((e,t)=>s(e[0])<s(t[0])?-1:1)).forEach((e=>{G.addOption(e[0],this.plugin.settings.hierarchy.parents.includes(e[1].display)?"Parent > "+e[1].display:this.plugin.settings.hierarchy.children.includes(e[1].display)?"Child > "+e[1].display:this.plugin.settings.hierarchy.leftFriends.includes(e[1].display)?"Left Friend > "+e[1].display:this.plugin.settings.hierarchy.rightFriends.includes(e[1].display)?"Right Friend > "+e[1].display:this.plugin.settings.hierarchy.previous.includes(e[1].display)?"Previous > "+e[1].display:this.plugin.settings.hierarchy.next.includes(e[1].display)?"Next > "+e[1].display:e[1].display)})),t[i]?G.setValue(i):(G.setValue("base"),j("base"))},u()}}var J={};Object.defineProperty(J,"__esModule",{value:!0});class Q extends Error{}class ee extends Q{constructor(e){super(`Invalid DateTime: ${e.toMessage()}`)}}class te extends Q{constructor(e){super(`Invalid Interval: ${e.toMessage()}`)}}class ie extends Q{constructor(e){super(`Invalid Duration: ${e.toMessage()}`)}}class ne extends Q{}class se extends Q{constructor(e){super(`Invalid unit ${e}`)}}class re extends Q{}class ae extends Q{constructor(){super("Zone is an abstract class")}}const ue="numeric",oe="short",le="long",De={year:ue,month:ue,day:ue},de={year:ue,month:oe,day:ue},he={year:ue,month:oe,day:ue,weekday:oe},ce={year:ue,month:le,day:ue},ge={year:ue,month:le,day:ue,weekday:le},Ee={hour:ue,minute:ue},pe={hour:ue,minute:ue,second:ue},fe={hour:ue,minute:ue,second:ue,timeZoneName:oe},Fe={hour:ue,minute:ue,second:ue,timeZoneName:le},me={hour:ue,minute:ue,hourCycle:"h23"},Ce={hour:ue,minute:ue,second:ue,hourCycle:"h23"},ye={hour:ue,minute:ue,second:ue,hourCycle:"h23",timeZoneName:oe},Se={hour:ue,minute:ue,second:ue,hourCycle:"h23",timeZoneName:le},be={year:ue,month:ue,day:ue,hour:ue,minute:ue},Ae={year:ue,month:ue,day:ue,hour:ue,minute:ue,second:ue},Te={year:ue,month:oe,day:ue,hour:ue,minute:ue},ve={year:ue,month:oe,day:ue,hour:ue,minute:ue,second:ue},Ne={year:ue,month:oe,day:ue,weekday:oe,hour:ue,minute:ue},we={year:ue,month:le,day:ue,hour:ue,minute:ue,timeZoneName:oe},Le={year:ue,month:le,day:ue,hour:ue,minute:ue,second:ue,timeZoneName:oe},Oe={year:ue,month:le,day:ue,weekday:le,hour:ue,minute:ue,timeZoneName:le},_e={year:ue,month:le,day:ue,weekday:le,hour:ue,minute:ue,second:ue,timeZoneName:le};class Ie{get type(){throw new ae}get name(){throw new ae}get ianaName(){return this.name}get isUniversal(){throw new ae}offsetName(e,t){throw new ae}formatOffset(e,t){throw new ae}offset(e){throw new ae}equals(e){throw new ae}get isValid(){throw new ae}}let ke=null;class Be extends Ie{static get instance(){return null===ke&&(ke=new Be),ke}get type(){return"system"}get name(){return(new Intl.DateTimeFormat).resolvedOptions().timeZone}get isUniversal(){return!1}offsetName(e,{format:t,locale:i}){return vt(e,t,i)}formatOffset(e,t){return Ot(this.offset(e),t)}offset(e){return-new Date(e).getTimezoneOffset()}equals(e){return"system"===e.type}get isValid(){return!0}}let xe={};const Re={year:0,month:1,day:2,era:3,hour:4,minute:5,second:6};let Me={};class ze extends Ie{static create(e){return Me[e]||(Me[e]=new ze(e)),Me[e]}static resetCache(){Me={},xe={}}static isValidSpecifier(e){return this.isValidZone(e)}static isValidZone(e){if(!e)return!1;try{return new Intl.DateTimeFormat("en-US",{timeZone:e}).format(),!0}catch(e){return!1}}constructor(e){super(),this.zoneName=e,this.valid=ze.isValidZone(e)}get type(){return"iana"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(e,{format:t,locale:i}){return vt(e,t,i,this.name)}formatOffset(e,t){return Ot(this.offset(e),t)}offset(e){const t=new Date(e);if(isNaN(t))return NaN;const i=(n=this.name,xe[n]||(xe[n]=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:n,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",era:"short"})),xe[n]);var n;let[s,r,a,u,o,l,D]=i.formatToParts?function(e,t){const i=e.formatToParts(t),n=[];for(let e=0;e<i.length;e++){const{type:t,value:s}=i[e],r=Re[t];"era"===t?n[r]=s:ot(r)||(n[r]=parseInt(s,10))}return n}(i,t):function(e,t){const i=e.format(t).replace(/\u200E/g,""),n=/(\d+)\/(\d+)\/(\d+) (AD|BC),? (\d+):(\d+):(\d+)/.exec(i),[,s,r,a,u,o,l,D]=n;return[a,s,r,u,o,l,D]}(i,t);"BC"===u&&(s=1-Math.abs(s));let d=+t;const h=d%1e3;return d-=h>=0?h:1e3+h,(bt({year:s,month:r,day:a,hour:24===o?0:o,minute:l,second:D,millisecond:0})-d)/6e4}equals(e){return"iana"===e.type&&e.name===this.name}get isValid(){return this.valid}}let He={},Pe={};function Ge(e,t={}){const i=JSON.stringify([e,t]);let n=Pe[i];return n||(n=new Intl.DateTimeFormat(e,t),Pe[i]=n),n}let Ve={},Ye={},je=null;function We(e,t,i,n){const s=e.listingMode();return"error"===s?null:"en"===s?i(t):n(t)}class Ue{constructor(e,t,i){this.padTo=i.padTo||0,this.floor=i.floor||!1;const{padTo:n,floor:s,...r}=i;if(!t||Object.keys(r).length>0){const t={useGrouping:!1,...i};i.padTo>0&&(t.minimumIntegerDigits=i.padTo),this.inf=function(e,t={}){const i=JSON.stringify([e,t]);let n=Ve[i];return n||(n=new Intl.NumberFormat(e,t),Ve[i]=n),n}(e,t)}}format(e){if(this.inf){const t=this.floor?Math.floor(e):e;return this.inf.format(t)}return Et(this.floor?Math.floor(e):mt(e,3),this.padTo)}}class Ke{constructor(e,t,i){let n;if(this.opts=i,this.originalZone=void 0,this.opts.timeZone)this.dt=e;else if("fixed"===e.zone.type){const t=e.offset/60*-1,i=t>=0?`Etc/GMT+${t}`:`Etc/GMT${t}`;0!==e.offset&&ze.create(i).valid?(n=i,this.dt=e):(n="UTC",this.dt=0===e.offset?e:e.setZone("UTC").plus({minutes:e.offset}),this.originalZone=e.zone)}else"system"===e.zone.type?this.dt=e:"iana"===e.zone.type?(this.dt=e,n=e.zone.name):(n="UTC",this.dt=e.setZone("UTC").plus({minutes:e.offset}),this.originalZone=e.zone);const s={...this.opts};s.timeZone=s.timeZone||n,this.dtf=Ge(t,s)}format(){return this.originalZone?this.formatToParts().map((({value:e})=>e)).join(""):this.dtf.format(this.dt.toJSDate())}formatToParts(){const e=this.dtf.formatToParts(this.dt.toJSDate());return this.originalZone?e.map((e=>{if("timeZoneName"===e.type){const t=this.originalZone.offsetName(this.dt.ts,{locale:this.dt.locale,format:this.opts.timeZoneName});return{...e,value:t}}return e})):e}resolvedOptions(){return this.dtf.resolvedOptions()}}class qe{constructor(e,t,i){this.opts={style:"long",...i},!t&&dt()&&(this.rtf=function(e,t={}){const{base:i,...n}=t,s=JSON.stringify([e,n]);let r=Ye[s];return r||(r=new Intl.RelativeTimeFormat(e,t),Ye[s]=r),r}(e,i))}format(e,t){return this.rtf?this.rtf.format(e,t):function(e,t,i="always",n=!1){const s={years:["year","yr."],quarters:["quarter","qtr."],months:["month","mo."],weeks:["week","wk."],days:["day","day","days"],hours:["hour","hr."],minutes:["minute","min."],seconds:["second","sec."]},r=-1===["hours","minutes","seconds"].indexOf(e);if("auto"===i&&r){const i="days"===e;switch(t){case 1:return i?"tomorrow":`next ${s[e][0]}`;case-1:return i?"yesterday":`last ${s[e][0]}`;case 0:return i?"today":`this ${s[e][0]}`}}const a=Object.is(t,-0)||t<0,u=Math.abs(t),o=1===u,l=s[e],D=n?o?l[1]:l[2]||l[1]:o?s[e][0]:e;return a?`${u} ${D} ago`:`in ${u} ${D}`}(t,e,this.opts.numeric,"long"!==this.opts.style)}formatToParts(e,t){return this.rtf?this.rtf.formatToParts(e,t):[]}}class Ze{static fromOpts(e){return Ze.create(e.locale,e.numberingSystem,e.outputCalendar,e.defaultToEN)}static create(e,t,i,n=!1){const s=e||ut.defaultLocale,r=s||(n?"en-US":je||(je=(new Intl.DateTimeFormat).resolvedOptions().locale,je)),a=t||ut.defaultNumberingSystem,u=i||ut.defaultOutputCalendar;return new Ze(r,a,u,s)}static resetCache(){je=null,Pe={},Ve={},Ye={}}static fromObject({locale:e,numberingSystem:t,outputCalendar:i}={}){return Ze.create(e,t,i)}constructor(e,t,i,n){const[s,r,a]=function(e){const t=e.indexOf("-x-");-1!==t&&(e=e.substring(0,t));const i=e.indexOf("-u-");if(-1===i)return[e];{let t,n;try{t=Ge(e).resolvedOptions(),n=e}catch(s){const r=e.substring(0,i);t=Ge(r).resolvedOptions(),n=r}const{numberingSystem:s,calendar:r}=t;return[n,s,r]}}(e);this.locale=s,this.numberingSystem=t||r||null,this.outputCalendar=i||a||null,this.intl=function(e,t,i){return i||t?(e.includes("-u-")||(e+="-u"),i&&(e+=`-ca-${i}`),t&&(e+=`-nu-${t}`),e):e}(this.locale,this.numberingSystem,this.outputCalendar),this.weekdaysCache={format:{},standalone:{}},this.monthsCache={format:{},standalone:{}},this.meridiemCache=null,this.eraCache={},this.specifiedLocale=n,this.fastNumbersCached=null}get fastNumbers(){var e;return null==this.fastNumbersCached&&(this.fastNumbersCached=(!(e=this).numberingSystem||"latn"===e.numberingSystem)&&("latn"===e.numberingSystem||!e.locale||e.locale.startsWith("en")||"latn"===new Intl.DateTimeFormat(e.intl).resolvedOptions().numberingSystem)),this.fastNumbersCached}listingMode(){const e=this.isEnglish(),t=!(null!==this.numberingSystem&&"latn"!==this.numberingSystem||null!==this.outputCalendar&&"gregory"!==this.outputCalendar);return e&&t?"en":"intl"}clone(e){return e&&0!==Object.getOwnPropertyNames(e).length?Ze.create(e.locale||this.specifiedLocale,e.numberingSystem||this.numberingSystem,e.outputCalendar||this.outputCalendar,e.defaultToEN||!1):this}redefaultToEN(e={}){return this.clone({...e,defaultToEN:!0})}redefaultToSystem(e={}){return this.clone({...e,defaultToEN:!1})}months(e,t=!1){return We(this,e,xt,(()=>{const i=t?{month:e,day:"numeric"}:{month:e},n=t?"format":"standalone";return this.monthsCache[n][e]||(this.monthsCache[n][e]=function(e){const t=[];for(let i=1;i<=12;i++){const n=Zn.utc(2009,i,1);t.push(e(n))}return t}((e=>this.extract(e,i,"month")))),this.monthsCache[n][e]}))}weekdays(e,t=!1){return We(this,e,Ht,(()=>{const i=t?{weekday:e,year:"numeric",month:"long",day:"numeric"}:{weekday:e},n=t?"format":"standalone";return this.weekdaysCache[n][e]||(this.weekdaysCache[n][e]=function(e){const t=[];for(let i=1;i<=7;i++){const n=Zn.utc(2016,11,13+i);t.push(e(n))}return t}((e=>this.extract(e,i,"weekday")))),this.weekdaysCache[n][e]}))}meridiems(){return We(this,void 0,(()=>Pt),(()=>{if(!this.meridiemCache){const e={hour:"numeric",hourCycle:"h12"};this.meridiemCache=[Zn.utc(2016,11,13,9),Zn.utc(2016,11,13,19)].map((t=>this.extract(t,e,"dayperiod")))}return this.meridiemCache}))}eras(e){return We(this,e,jt,(()=>{const t={era:e};return this.eraCache[e]||(this.eraCache[e]=[Zn.utc(-40,1,1),Zn.utc(2017,1,1)].map((e=>this.extract(e,t,"era")))),this.eraCache[e]}))}extract(e,t,i){const n=this.dtFormatter(e,t).formatToParts().find((e=>e.type.toLowerCase()===i));return n?n.value:null}numberFormatter(e={}){return new Ue(this.intl,e.forceSimple||this.fastNumbers,e)}dtFormatter(e,t={}){return new Ke(e,this.intl,t)}relFormatter(e={}){return new qe(this.intl,this.isEnglish(),e)}listFormatter(e={}){return function(e,t={}){const i=JSON.stringify([e,t]);let n=He[i];return n||(n=new Intl.ListFormat(e,t),He[i]=n),n}(this.intl,e)}isEnglish(){return"en"===this.locale||"en-us"===this.locale.toLowerCase()||new Intl.DateTimeFormat(this.intl).resolvedOptions().locale.startsWith("en-us")}equals(e){return this.locale===e.locale&&this.numberingSystem===e.numberingSystem&&this.outputCalendar===e.outputCalendar}}let $e=null;class Xe extends Ie{static get utcInstance(){return null===$e&&($e=new Xe(0)),$e}static instance(e){return 0===e?Xe.utcInstance:new Xe(e)}static parseSpecifier(e){if(e){const t=e.match(/^utc(?:([+-]\d{1,2})(?::(\d{2}))?)?$/i);if(t)return new Xe(Nt(t[1],t[2]))}return null}constructor(e){super(),this.fixed=e}get type(){return"fixed"}get name(){return 0===this.fixed?"UTC":`UTC${Ot(this.fixed,"narrow")}`}get ianaName(){return 0===this.fixed?"Etc/UTC":`Etc/GMT${Ot(-this.fixed,"narrow")}`}offsetName(){return this.name}formatOffset(e,t){return Ot(this.fixed,t)}get isUniversal(){return!0}offset(){return this.fixed}equals(e){return"fixed"===e.type&&e.fixed===this.fixed}get isValid(){return!0}}class Je extends Ie{constructor(e){super(),this.zoneName=e}get type(){return"invalid"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(){return null}formatOffset(){return""}offset(){return NaN}equals(){return!1}get isValid(){return!1}}function Qe(e,t){if(ot(e)||null===e)return t;if(e instanceof Ie)return e;if("string"==typeof e){const i=e.toLowerCase();return"default"===i?t:"local"===i||"system"===i?Be.instance:"utc"===i||"gmt"===i?Xe.utcInstance:Xe.parseSpecifier(i)||ze.create(e)}return lt(e)?Xe.instance(e):"object"==typeof e&&"offset"in e&&"function"==typeof e.offset?e:new Je(e)}let et,tt=()=>Date.now(),it="system",nt=null,st=null,rt=null,at=60;class ut{static get now(){return tt}static set now(e){tt=e}static set defaultZone(e){it=e}static get defaultZone(){return Qe(it,Be.instance)}static get defaultLocale(){return nt}static set defaultLocale(e){nt=e}static get defaultNumberingSystem(){return st}static set defaultNumberingSystem(e){st=e}static get defaultOutputCalendar(){return rt}static set defaultOutputCalendar(e){rt=e}static get twoDigitCutoffYear(){return at}static set twoDigitCutoffYear(e){at=e%100}static get throwOnInvalid(){return et}static set throwOnInvalid(e){et=e}static resetCaches(){Ze.resetCache(),ze.resetCache()}}function ot(e){return void 0===e}function lt(e){return"number"==typeof e}function Dt(e){return"number"==typeof e&&e%1==0}function dt(){try{return"undefined"!=typeof Intl&&!!Intl.RelativeTimeFormat}catch(e){return!1}}function ht(e,t,i){if(0!==e.length)return e.reduce(((e,n)=>{const s=[t(n),n];return e&&i(e[0],s[0])===e[0]?e:s}),null)[1]}function ct(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function gt(e,t,i){return Dt(e)&&e>=t&&e<=i}function Et(e,t=2){let i;return i=e<0?"-"+(""+-e).padStart(t,"0"):(""+e).padStart(t,"0"),i}function pt(e){return ot(e)||null===e||""===e?void 0:parseInt(e,10)}function ft(e){return ot(e)||null===e||""===e?void 0:parseFloat(e)}function Ft(e){if(!ot(e)&&null!==e&&""!==e){const t=1e3*parseFloat("0."+e);return Math.floor(t)}}function mt(e,t,i=!1){const n=10**t;return(i?Math.trunc:Math.round)(e*n)/n}function Ct(e){return e%4==0&&(e%100!=0||e%400==0)}function yt(e){return Ct(e)?366:365}function St(e,t){const i=(n=t-1)-12*Math.floor(n/12)+1;var n;return 2===i?Ct(e+(t-i)/12)?29:28:[31,null,31,30,31,30,31,31,30,31,30,31][i-1]}function bt(e){let t=Date.UTC(e.year,e.month-1,e.day,e.hour,e.minute,e.second,e.millisecond);return e.year<100&&e.year>=0&&(t=new Date(t),t.setUTCFullYear(e.year,e.month-1,e.day)),+t}function At(e){const t=(e+Math.floor(e/4)-Math.floor(e/100)+Math.floor(e/400))%7,i=e-1,n=(i+Math.floor(i/4)-Math.floor(i/100)+Math.floor(i/400))%7;return 4===t||3===n?53:52}function Tt(e){return e>99?e:e>ut.twoDigitCutoffYear?1900+e:2e3+e}function vt(e,t,i,n=null){const s=new Date(e),r={hourCycle:"h23",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"};n&&(r.timeZone=n);const a={timeZoneName:t,...r},u=new Intl.DateTimeFormat(i,a).formatToParts(s).find((e=>"timezonename"===e.type.toLowerCase()));return u?u.value:null}function Nt(e,t){let i=parseInt(e,10);Number.isNaN(i)&&(i=0);const n=parseInt(t,10)||0;return 60*i+(i<0||Object.is(i,-0)?-n:n)}function wt(e){const t=Number(e);if("boolean"==typeof e||""===e||Number.isNaN(t))throw new re(`Invalid unit value ${e}`);return t}function Lt(e,t){const i={};for(const n in e)if(ct(e,n)){const s=e[n];if(null==s)continue;i[t(n)]=wt(s)}return i}function Ot(e,t){const i=Math.trunc(Math.abs(e/60)),n=Math.trunc(Math.abs(e%60)),s=e>=0?"+":"-";switch(t){case"short":return`${s}${Et(i,2)}:${Et(n,2)}`;case"narrow":return`${s}${i}${n>0?`:${n}`:""}`;case"techie":return`${s}${Et(i,2)}${Et(n,2)}`;default:throw new RangeError(`Value format ${t} is out of range for property format`)}}function _t(e){return function(e,t){return["hour","minute","second","millisecond"].reduce(((t,i)=>(t[i]=e[i],t)),{})}(e)}const It=["January","February","March","April","May","June","July","August","September","October","November","December"],kt=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],Bt=["J","F","M","A","M","J","J","A","S","O","N","D"];function xt(e){switch(e){case"narrow":return[...Bt];case"short":return[...kt];case"long":return[...It];case"numeric":return["1","2","3","4","5","6","7","8","9","10","11","12"];case"2-digit":return["01","02","03","04","05","06","07","08","09","10","11","12"];default:return null}}const Rt=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],Mt=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],zt=["M","T","W","T","F","S","S"];function Ht(e){switch(e){case"narrow":return[...zt];case"short":return[...Mt];case"long":return[...Rt];case"numeric":return["1","2","3","4","5","6","7"];default:return null}}const Pt=["AM","PM"],Gt=["Before Christ","Anno Domini"],Vt=["BC","AD"],Yt=["B","A"];function jt(e){switch(e){case"narrow":return[...Yt];case"short":return[...Vt];case"long":return[...Gt];default:return null}}function Wt(e,t){let i="";for(const n of e)n.literal?i+=n.val:i+=t(n.val);return i}const Ut={D:De,DD:de,DDD:ce,DDDD:ge,t:Ee,tt:pe,ttt:fe,tttt:Fe,T:me,TT:Ce,TTT:ye,TTTT:Se,f:be,ff:Te,fff:we,ffff:Oe,F:Ae,FF:ve,FFF:Le,FFFF:_e};class Kt{static create(e,t={}){return new Kt(e,t)}static parseFormat(e){let t=null,i="",n=!1;const s=[];for(let r=0;r<e.length;r++){const a=e.charAt(r);"'"===a?(i.length>0&&s.push({literal:n||/^\s+$/.test(i),val:i}),t=null,i="",n=!n):n||a===t?i+=a:(i.length>0&&s.push({literal:/^\s+$/.test(i),val:i}),i=a,t=a)}return i.length>0&&s.push({literal:n||/^\s+$/.test(i),val:i}),s}static macroTokenToFormatOpts(e){return Ut[e]}constructor(e,t){this.opts=t,this.loc=e,this.systemLoc=null}formatWithSystemDefault(e,t){return null===this.systemLoc&&(this.systemLoc=this.loc.redefaultToSystem()),this.systemLoc.dtFormatter(e,{...this.opts,...t}).format()}dtFormatter(e,t={}){return this.loc.dtFormatter(e,{...this.opts,...t})}formatDateTime(e,t){return this.dtFormatter(e,t).format()}formatDateTimeParts(e,t){return this.dtFormatter(e,t).formatToParts()}formatInterval(e,t){return this.dtFormatter(e.start,t).dtf.formatRange(e.start.toJSDate(),e.end.toJSDate())}resolvedOptions(e,t){return this.dtFormatter(e,t).resolvedOptions()}num(e,t=0){if(this.opts.forceSimple)return Et(e,t);const i={...this.opts};return t>0&&(i.padTo=t),this.loc.numberFormatter(i).format(e)}formatDateTimeFromString(e,t){const i="en"===this.loc.listingMode(),n=this.loc.outputCalendar&&"gregory"!==this.loc.outputCalendar,s=(t,i)=>this.loc.extract(e,t,i),r=t=>e.isOffsetFixed&&0===e.offset&&t.allowZ?"Z":e.isValid?e.zone.formatOffset(e.ts,t.format):"",a=(t,n)=>i?function(e,t){return xt(t)[e.month-1]}(e,t):s(n?{month:t}:{month:t,day:"numeric"},"month"),u=(t,n)=>i?function(e,t){return Ht(t)[e.weekday-1]}(e,t):s(n?{weekday:t}:{weekday:t,month:"long",day:"numeric"},"weekday"),o=t=>{const i=Kt.macroTokenToFormatOpts(t);return i?this.formatWithSystemDefault(e,i):t},l=t=>i?function(e,t){return jt(t)[e.year<0?0:1]}(e,t):s({era:t},"era");return Wt(Kt.parseFormat(t),(t=>{switch(t){case"S":return this.num(e.millisecond);case"u":case"SSS":return this.num(e.millisecond,3);case"s":return this.num(e.second);case"ss":return this.num(e.second,2);case"uu":return this.num(Math.floor(e.millisecond/10),2);case"uuu":return this.num(Math.floor(e.millisecond/100));case"m":return this.num(e.minute);case"mm":return this.num(e.minute,2);case"h":return this.num(e.hour%12==0?12:e.hour%12);case"hh":return this.num(e.hour%12==0?12:e.hour%12,2);case"H":return this.num(e.hour);case"HH":return this.num(e.hour,2);case"Z":return r({format:"narrow",allowZ:this.opts.allowZ});case"ZZ":return r({format:"short",allowZ:this.opts.allowZ});case"ZZZ":return r({format:"techie",allowZ:this.opts.allowZ});case"ZZZZ":return e.zone.offsetName(e.ts,{format:"short",locale:this.loc.locale});case"ZZZZZ":return e.zone.offsetName(e.ts,{format:"long",locale:this.loc.locale});case"z":return e.zoneName;case"a":return i?function(e){return Pt[e.hour<12?0:1]}(e):s({hour:"numeric",hourCycle:"h12"},"dayperiod");case"d":return n?s({day:"numeric"},"day"):this.num(e.day);case"dd":return n?s({day:"2-digit"},"day"):this.num(e.day,2);case"c":case"E":return this.num(e.weekday);case"ccc":return u("short",!0);case"cccc":return u("long",!0);case"ccccc":return u("narrow",!0);case"EEE":return u("short",!1);case"EEEE":return u("long",!1);case"EEEEE":return u("narrow",!1);case"L":return n?s({month:"numeric",day:"numeric"},"month"):this.num(e.month);case"LL":return n?s({month:"2-digit",day:"numeric"},"month"):this.num(e.month,2);case"LLL":return a("short",!0);case"LLLL":return a("long",!0);case"LLLLL":return a("narrow",!0);case"M":return n?s({month:"numeric"},"month"):this.num(e.month);case"MM":return n?s({month:"2-digit"},"month"):this.num(e.month,2);case"MMM":return a("short",!1);case"MMMM":return a("long",!1);case"MMMMM":return a("narrow",!1);case"y":return n?s({year:"numeric"},"year"):this.num(e.year);case"yy":return n?s({year:"2-digit"},"year"):this.num(e.year.toString().slice(-2),2);case"yyyy":return n?s({year:"numeric"},"year"):this.num(e.year,4);case"yyyyyy":return n?s({year:"numeric"},"year"):this.num(e.year,6);case"G":return l("short");case"GG":return l("long");case"GGGGG":return l("narrow");case"kk":return this.num(e.weekYear.toString().slice(-2),2);case"kkkk":return this.num(e.weekYear,4);case"W":return this.num(e.weekNumber);case"WW":return this.num(e.weekNumber,2);case"o":return this.num(e.ordinal);case"ooo":return this.num(e.ordinal,3);case"q":return this.num(e.quarter);case"qq":return this.num(e.quarter,2);case"X":return this.num(Math.floor(e.ts/1e3));case"x":return this.num(e.ts);default:return o(t)}}))}formatDurationFromString(e,t){const i=e=>{switch(e[0]){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":return"hour";case"d":return"day";case"w":return"week";case"M":return"month";case"y":return"year";default:return null}},n=Kt.parseFormat(t),s=n.reduce(((e,{literal:t,val:i})=>t?e:e.concat(i)),[]);return Wt(n,(e=>t=>{const n=i(t);return n?this.num(e.get(n),t.length):t})(e.shiftTo(...s.map(i).filter((e=>e)))))}}class qt{constructor(e,t){this.reason=e,this.explanation=t}toMessage(){return this.explanation?`${this.reason}: ${this.explanation}`:this.reason}}const Zt=/[A-Za-z_+-]{1,256}(?::?\/[A-Za-z0-9_+-]{1,256}(?:\/[A-Za-z0-9_+-]{1,256})?)?/;function $t(...e){const t=e.reduce(((e,t)=>e+t.source),"");return RegExp(`^${t}$`)}function Xt(...e){return t=>e.reduce((([e,i,n],s)=>{const[r,a,u]=s(t,n);return[{...e,...r},a||i,u]}),[{},null,1]).slice(0,2)}function Jt(e,...t){if(null==e)return[null,null];for(const[i,n]of t){const t=i.exec(e);if(t)return n(t)}return[null,null]}function Qt(...e){return(t,i)=>{const n={};let s;for(s=0;s<e.length;s++)n[e[s]]=pt(t[i+s]);return[n,null,i+s]}}const ei=/(?:(Z)|([+-]\d\d)(?::?(\d\d))?)/,ti=/(\d\d)(?::?(\d\d)(?::?(\d\d)(?:[.,](\d{1,30}))?)?)?/,ii=RegExp(`${ti.source}(?:${ei.source}?(?:\\[(${Zt.source})\\])?)?`),ni=RegExp(`(?:T${ii.source})?`),si=Qt("weekYear","weekNumber","weekDay"),ri=Qt("year","ordinal"),ai=RegExp(`${ti.source} ?(?:${ei.source}|(${Zt.source}))?`),ui=RegExp(`(?: ${ai.source})?`);function oi(e,t,i){const n=e[t];return ot(n)?i:pt(n)}function li(e,t){return[{hours:oi(e,t,0),minutes:oi(e,t+1,0),seconds:oi(e,t+2,0),milliseconds:Ft(e[t+3])},null,t+4]}function Di(e,t){const i=!e[t]&&!e[t+1],n=Nt(e[t+1],e[t+2]);return[{},i?null:Xe.instance(n),t+3]}function di(e,t){return[{},e[t]?ze.create(e[t]):null,t+1]}const hi=RegExp(`^T?${ti.source}$`),ci=/^-?P(?:(?:(-?\d{1,20}(?:\.\d{1,20})?)Y)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20}(?:\.\d{1,20})?)W)?(?:(-?\d{1,20}(?:\.\d{1,20})?)D)?(?:T(?:(-?\d{1,20}(?:\.\d{1,20})?)H)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20})(?:[.,](-?\d{1,20}))?S)?)?)$/;function gi(e){const[t,i,n,s,r,a,u,o,l]=e,D="-"===t[0],d=o&&"-"===o[0],h=(e,t=!1)=>void 0!==e&&(t||e&&D)?-e:e;return[{years:h(ft(i)),months:h(ft(n)),weeks:h(ft(s)),days:h(ft(r)),hours:h(ft(a)),minutes:h(ft(u)),seconds:h(ft(o),"-0"===o),milliseconds:h(Ft(l),d)}]}const Ei={GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function pi(e,t,i,n,s,r,a){const u={year:2===t.length?Tt(pt(t)):pt(t),month:kt.indexOf(i)+1,day:pt(n),hour:pt(s),minute:pt(r)};return a&&(u.second=pt(a)),e&&(u.weekday=e.length>3?Rt.indexOf(e)+1:Mt.indexOf(e)+1),u}const fi=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\d\d)(\d\d)))$/;function Fi(e){const[,t,i,n,s,r,a,u,o,l,D,d]=e,h=pi(t,s,n,i,r,a,u);let c;return c=o?Ei[o]:l?0:Nt(D,d),[h,new Xe(c)]}const mi=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d\d):(\d\d):(\d\d) GMT$/,Ci=/^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d\d) (\d\d):(\d\d):(\d\d) GMT$/,yi=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \d|\d\d) (\d\d):(\d\d):(\d\d) (\d{4})$/;function Si(e){const[,t,i,n,s,r,a,u]=e;return[pi(t,s,n,i,r,a,u),Xe.utcInstance]}function bi(e){const[,t,i,n,s,r,a,u]=e;return[pi(t,u,i,n,s,r,a),Xe.utcInstance]}const Ai=$t(/([+-]\d{6}|\d{4})(?:-?(\d\d)(?:-?(\d\d))?)?/,ni),Ti=$t(/(\d{4})-?W(\d\d)(?:-?(\d))?/,ni),vi=$t(/(\d{4})-?(\d{3})/,ni),Ni=$t(ii),wi=Xt((function(e,t){return[{year:oi(e,t),month:oi(e,t+1,1),day:oi(e,t+2,1)},null,t+3]}),li,Di,di),Li=Xt(si,li,Di,di),Oi=Xt(ri,li,Di,di),_i=Xt(li,Di,di),Ii=Xt(li),ki=$t(/(\d{4})-(\d\d)-(\d\d)/,ui),Bi=$t(ai),xi=Xt(li,Di,di),Ri="Invalid Duration",Mi={weeks:{days:7,hours:168,minutes:10080,seconds:604800,milliseconds:6048e5},days:{hours:24,minutes:1440,seconds:86400,milliseconds:864e5},hours:{minutes:60,seconds:3600,milliseconds:36e5},minutes:{seconds:60,milliseconds:6e4},seconds:{milliseconds:1e3}},zi={years:{quarters:4,months:12,weeks:52,days:365,hours:8760,minutes:525600,seconds:31536e3,milliseconds:31536e6},quarters:{months:3,weeks:13,days:91,hours:2184,minutes:131040,seconds:7862400,milliseconds:78624e5},months:{weeks:4,days:30,hours:720,minutes:43200,seconds:2592e3,milliseconds:2592e6},...Mi},Hi={years:{quarters:4,months:12,weeks:52.1775,days:365.2425,hours:8765.82,minutes:525949.2,seconds:525949.2*60,milliseconds:525949.2*60*1e3},quarters:{months:3,weeks:13.044375,days:91.310625,hours:2191.455,minutes:131487.3,seconds:525949.2*60/4,milliseconds:7889237999.999999},months:{weeks:4.3481250000000005,days:30.436875,hours:730.485,minutes:43829.1,seconds:2629746,milliseconds:2629746e3},...Mi},Pi=["years","quarters","months","weeks","days","hours","minutes","seconds","milliseconds"],Gi=Pi.slice(0).reverse();function Vi(e,t,i=!1){const n={values:i?t.values:{...e.values,...t.values||{}},loc:e.loc.clone(t.loc),conversionAccuracy:t.conversionAccuracy||e.conversionAccuracy,matrix:t.matrix||e.matrix};return new Wi(n)}function Yi(e,t){let i=t.milliseconds??0;for(const n of Gi.slice(1))t[n]&&(i+=t[n]*e[n].milliseconds);return i}function ji(e,t){const i=Yi(e,t)<0?-1:1;Pi.reduceRight(((n,s)=>{if(ot(t[s]))return n;if(n){const r=t[n]*i,a=e[s][n],u=Math.floor(r/a);t[s]+=u*i,t[n]-=u*a*i}return s}),null),Pi.reduce(((i,n)=>{if(ot(t[n]))return i;if(i){const s=t[i]%1;t[i]-=s,t[n]+=s*e[i][n]}return n}),null)}class Wi{constructor(e){const t="longterm"===e.conversionAccuracy||!1;let i=t?Hi:zi;e.matrix&&(i=e.matrix),this.values=e.values,this.loc=e.loc||Ze.create(),this.conversionAccuracy=t?"longterm":"casual",this.invalid=e.invalid||null,this.matrix=i,this.isLuxonDuration=!0}static fromMillis(e,t){return Wi.fromObject({milliseconds:e},t)}static fromObject(e,t={}){if(null==e||"object"!=typeof e)throw new re("Duration.fromObject: argument expected to be an object, got "+(null===e?"null":typeof e));return new Wi({values:Lt(e,Wi.normalizeUnit),loc:Ze.fromObject(t),conversionAccuracy:t.conversionAccuracy,matrix:t.matrix})}static fromDurationLike(e){if(lt(e))return Wi.fromMillis(e);if(Wi.isDuration(e))return e;if("object"==typeof e)return Wi.fromObject(e);throw new re(`Unknown duration argument ${e} of type ${typeof e}`)}static fromISO(e,t){const[i]=function(e){return Jt(e,[ci,gi])}(e);return i?Wi.fromObject(i,t):Wi.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static fromISOTime(e,t){const[i]=function(e){return Jt(e,[hi,Ii])}(e);return i?Wi.fromObject(i,t):Wi.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static invalid(e,t=null){if(!e)throw new re("need to specify a reason the Duration is invalid");const i=e instanceof qt?e:new qt(e,t);if(ut.throwOnInvalid)throw new ie(i);return new Wi({invalid:i})}static normalizeUnit(e){const t={year:"years",years:"years",quarter:"quarters",quarters:"quarters",month:"months",months:"months",week:"weeks",weeks:"weeks",day:"days",days:"days",hour:"hours",hours:"hours",minute:"minutes",minutes:"minutes",second:"seconds",seconds:"seconds",millisecond:"milliseconds",milliseconds:"milliseconds"}[e?e.toLowerCase():e];if(!t)throw new se(e);return t}static isDuration(e){return e&&e.isLuxonDuration||!1}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}toFormat(e,t={}){const i={...t,floor:!1!==t.round&&!1!==t.floor};return this.isValid?Kt.create(this.loc,i).formatDurationFromString(this,e):Ri}toHuman(e={}){if(!this.isValid)return Ri;const t=Pi.map((t=>{const i=this.values[t];return ot(i)?null:this.loc.numberFormatter({style:"unit",unitDisplay:"long",...e,unit:t.slice(0,-1)}).format(i)})).filter((e=>e));return this.loc.listFormatter({type:"conjunction",style:e.listStyle||"narrow",...e}).format(t)}toObject(){return this.isValid?{...this.values}:{}}toISO(){if(!this.isValid)return null;let e="P";return 0!==this.years&&(e+=this.years+"Y"),0===this.months&&0===this.quarters||(e+=this.months+3*this.quarters+"M"),0!==this.weeks&&(e+=this.weeks+"W"),0!==this.days&&(e+=this.days+"D"),0===this.hours&&0===this.minutes&&0===this.seconds&&0===this.milliseconds||(e+="T"),0!==this.hours&&(e+=this.hours+"H"),0!==this.minutes&&(e+=this.minutes+"M"),0===this.seconds&&0===this.milliseconds||(e+=mt(this.seconds+this.milliseconds/1e3,3)+"S"),"P"===e&&(e+="T0S"),e}toISOTime(e={}){if(!this.isValid)return null;const t=this.toMillis();return t<0||t>=864e5?null:(e={suppressMilliseconds:!1,suppressSeconds:!1,includePrefix:!1,format:"extended",...e,includeOffset:!1},Zn.fromMillis(t,{zone:"UTC"}).toISOTime(e))}toJSON(){return this.toISO()}toString(){return this.toISO()}toMillis(){return this.isValid?Yi(this.matrix,this.values):NaN}valueOf(){return this.toMillis()}plus(e){if(!this.isValid)return this;const t=Wi.fromDurationLike(e),i={};for(const e of Pi)(ct(t.values,e)||ct(this.values,e))&&(i[e]=t.get(e)+this.get(e));return Vi(this,{values:i},!0)}minus(e){if(!this.isValid)return this;const t=Wi.fromDurationLike(e);return this.plus(t.negate())}mapUnits(e){if(!this.isValid)return this;const t={};for(const i of Object.keys(this.values))t[i]=wt(e(this.values[i],i));return Vi(this,{values:t},!0)}get(e){return this[Wi.normalizeUnit(e)]}set(e){return this.isValid?Vi(this,{values:{...this.values,...Lt(e,Wi.normalizeUnit)}}):this}reconfigure({locale:e,numberingSystem:t,conversionAccuracy:i,matrix:n}={}){return Vi(this,{loc:this.loc.clone({locale:e,numberingSystem:t}),matrix:n,conversionAccuracy:i})}as(e){return this.isValid?this.shiftTo(e).get(e):NaN}normalize(){if(!this.isValid)return this;const e=this.toObject();return ji(this.matrix,e),Vi(this,{values:e},!0)}rescale(){return this.isValid?Vi(this,{values:function(e){const t={};for(const[i,n]of Object.entries(e))0!==n&&(t[i]=n);return t}(this.normalize().shiftToAll().toObject())},!0):this}shiftTo(...e){if(!this.isValid)return this;if(0===e.length)return this;e=e.map((e=>Wi.normalizeUnit(e)));const t={},i={},n=this.toObject();let s;for(const r of Pi)if(e.indexOf(r)>=0){s=r;let e=0;for(const t in i)e+=this.matrix[t][r]*i[t],i[t]=0;lt(n[r])&&(e+=n[r]);const a=Math.trunc(e);t[r]=a,i[r]=(1e3*e-1e3*a)/1e3}else lt(n[r])&&(i[r]=n[r]);for(const e in i)0!==i[e]&&(t[s]+=e===s?i[e]:i[e]/this.matrix[s][e]);return ji(this.matrix,t),Vi(this,{values:t},!0)}shiftToAll(){return this.isValid?this.shiftTo("years","months","weeks","days","hours","minutes","seconds","milliseconds"):this}negate(){if(!this.isValid)return this;const e={};for(const t of Object.keys(this.values))e[t]=0===this.values[t]?0:-this.values[t];return Vi(this,{values:e},!0)}get years(){return this.isValid?this.values.years||0:NaN}get quarters(){return this.isValid?this.values.quarters||0:NaN}get months(){return this.isValid?this.values.months||0:NaN}get weeks(){return this.isValid?this.values.weeks||0:NaN}get days(){return this.isValid?this.values.days||0:NaN}get hours(){return this.isValid?this.values.hours||0:NaN}get minutes(){return this.isValid?this.values.minutes||0:NaN}get seconds(){return this.isValid?this.values.seconds||0:NaN}get milliseconds(){return this.isValid?this.values.milliseconds||0:NaN}get isValid(){return null===this.invalid}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}equals(e){if(!this.isValid||!e.isValid)return!1;if(!this.loc.equals(e.loc))return!1;for(const n of Pi)if(t=this.values[n],i=e.values[n],!(void 0===t||0===t?void 0===i||0===i:t===i))return!1;var t,i;return!0}}const Ui="Invalid Interval";class Ki{constructor(e){this.s=e.start,this.e=e.end,this.invalid=e.invalid||null,this.isLuxonInterval=!0}static invalid(e,t=null){if(!e)throw new re("need to specify a reason the Interval is invalid");const i=e instanceof qt?e:new qt(e,t);if(ut.throwOnInvalid)throw new te(i);return new Ki({invalid:i})}static fromDateTimes(e,t){const i=$n(e),n=$n(t),s=function(e,t){return e&&e.isValid?t&&t.isValid?t<e?Ki.invalid("end before start",`The end of an interval must be after its start, but you had start=${e.toISO()} and end=${t.toISO()}`):null:Ki.invalid("missing or invalid end"):Ki.invalid("missing or invalid start")}(i,n);return null==s?new Ki({start:i,end:n}):s}static after(e,t){const i=Wi.fromDurationLike(t),n=$n(e);return Ki.fromDateTimes(n,n.plus(i))}static before(e,t){const i=Wi.fromDurationLike(t),n=$n(e);return Ki.fromDateTimes(n.minus(i),n)}static fromISO(e,t){const[i,n]=(e||"").split("/",2);if(i&&n){let e,s,r,a;try{e=Zn.fromISO(i,t),s=e.isValid}catch(n){s=!1}try{r=Zn.fromISO(n,t),a=r.isValid}catch(n){a=!1}if(s&&a)return Ki.fromDateTimes(e,r);if(s){const i=Wi.fromISO(n,t);if(i.isValid)return Ki.after(e,i)}else if(a){const e=Wi.fromISO(i,t);if(e.isValid)return Ki.before(r,e)}}return Ki.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static isInterval(e){return e&&e.isLuxonInterval||!1}get start(){return this.isValid?this.s:null}get end(){return this.isValid?this.e:null}get isValid(){return null===this.invalidReason}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}length(e="milliseconds"){return this.isValid?this.toDuration(e).get(e):NaN}count(e="milliseconds"){if(!this.isValid)return NaN;const t=this.start.startOf(e),i=this.end.startOf(e);return Math.floor(i.diff(t,e).get(e))+(i.valueOf()!==this.end.valueOf())}hasSame(e){return!!this.isValid&&(this.isEmpty()||this.e.minus(1).hasSame(this.s,e))}isEmpty(){return this.s.valueOf()===this.e.valueOf()}isAfter(e){return!!this.isValid&&this.s>e}isBefore(e){return!!this.isValid&&this.e<=e}contains(e){return!!this.isValid&&this.s<=e&&this.e>e}set({start:e,end:t}={}){return this.isValid?Ki.fromDateTimes(e||this.s,t||this.e):this}splitAt(...e){if(!this.isValid)return[];const t=e.map($n).filter((e=>this.contains(e))).sort(),i=[];let{s:n}=this,s=0;for(;n<this.e;){const e=t[s]||this.e,r=+e>+this.e?this.e:e;i.push(Ki.fromDateTimes(n,r)),n=r,s+=1}return i}splitBy(e){const t=Wi.fromDurationLike(e);if(!this.isValid||!t.isValid||0===t.as("milliseconds"))return[];let i,{s:n}=this,s=1;const r=[];for(;n<this.e;){const e=this.start.plus(t.mapUnits((e=>e*s)));i=+e>+this.e?this.e:e,r.push(Ki.fromDateTimes(n,i)),n=i,s+=1}return r}divideEqually(e){return this.isValid?this.splitBy(this.length()/e).slice(0,e):[]}overlaps(e){return this.e>e.s&&this.s<e.e}abutsStart(e){return!!this.isValid&&+this.e==+e.s}abutsEnd(e){return!!this.isValid&&+e.e==+this.s}engulfs(e){return!!this.isValid&&this.s<=e.s&&this.e>=e.e}equals(e){return!(!this.isValid||!e.isValid)&&this.s.equals(e.s)&&this.e.equals(e.e)}intersection(e){if(!this.isValid)return this;const t=this.s>e.s?this.s:e.s,i=this.e<e.e?this.e:e.e;return t>=i?null:Ki.fromDateTimes(t,i)}union(e){if(!this.isValid)return this;const t=this.s<e.s?this.s:e.s,i=this.e>e.e?this.e:e.e;return Ki.fromDateTimes(t,i)}static merge(e){const[t,i]=e.sort(((e,t)=>e.s-t.s)).reduce((([e,t],i)=>t?t.overlaps(i)||t.abutsStart(i)?[e,t.union(i)]:[e.concat([t]),i]:[e,i]),[[],null]);return i&&t.push(i),t}static xor(e){let t=null,i=0;const n=[],s=e.map((e=>[{time:e.s,type:"s"},{time:e.e,type:"e"}])),r=Array.prototype.concat(...s).sort(((e,t)=>e.time-t.time));for(const e of r)i+="s"===e.type?1:-1,1===i?t=e.time:(t&&+t!=+e.time&&n.push(Ki.fromDateTimes(t,e.time)),t=null);return Ki.merge(n)}difference(...e){return Ki.xor([this].concat(e)).map((e=>this.intersection(e))).filter((e=>e&&!e.isEmpty()))}toString(){return this.isValid?`[${this.s.toISO()} – ${this.e.toISO()})`:Ui}toLocaleString(e=De,t={}){return this.isValid?Kt.create(this.s.loc.clone(t),e).formatInterval(this):Ui}toISO(e){return this.isValid?`${this.s.toISO(e)}/${this.e.toISO(e)}`:Ui}toISODate(){return this.isValid?`${this.s.toISODate()}/${this.e.toISODate()}`:Ui}toISOTime(e){return this.isValid?`${this.s.toISOTime(e)}/${this.e.toISOTime(e)}`:Ui}toFormat(e,{separator:t=" – "}={}){return this.isValid?`${this.s.toFormat(e)}${t}${this.e.toFormat(e)}`:Ui}toDuration(e,t){return this.isValid?this.e.diff(this.s,e,t):Wi.invalid(this.invalidReason)}mapEndpoints(e){return Ki.fromDateTimes(e(this.s),e(this.e))}}class qi{static hasDST(e=ut.defaultZone){const t=Zn.now().setZone(e).set({month:12});return!e.isUniversal&&t.offset!==t.set({month:6}).offset}static isValidIANAZone(e){return ze.isValidZone(e)}static normalizeZone(e){return Qe(e,ut.defaultZone)}static months(e="long",{locale:t=null,numberingSystem:i=null,locObj:n=null,outputCalendar:s="gregory"}={}){return(n||Ze.create(t,i,s)).months(e)}static monthsFormat(e="long",{locale:t=null,numberingSystem:i=null,locObj:n=null,outputCalendar:s="gregory"}={}){return(n||Ze.create(t,i,s)).months(e,!0)}static weekdays(e="long",{locale:t=null,numberingSystem:i=null,locObj:n=null}={}){return(n||Ze.create(t,i,null)).weekdays(e)}static weekdaysFormat(e="long",{locale:t=null,numberingSystem:i=null,locObj:n=null}={}){return(n||Ze.create(t,i,null)).weekdays(e,!0)}static meridiems({locale:e=null}={}){return Ze.create(e).meridiems()}static eras(e="short",{locale:t=null}={}){return Ze.create(t,null,"gregory").eras(e)}static features(){return{relative:dt()}}}function Zi(e,t){const i=e=>e.toUTC(0,{keepLocalTime:!0}).startOf("day").valueOf(),n=i(t)-i(e);return Math.floor(Wi.fromMillis(n).as("days"))}const $i={arab:"[٠-٩]",arabext:"[۰-۹]",bali:"[᭐-᭙]",beng:"[০-৯]",deva:"[०-९]",fullwide:"[０-９]",gujr:"[૦-૯]",hanidec:"[〇|一|二|三|四|五|六|七|八|九]",khmr:"[០-៩]",knda:"[೦-೯]",laoo:"[໐-໙]",limb:"[᥆-᥏]",mlym:"[൦-൯]",mong:"[᠐-᠙]",mymr:"[၀-၉]",orya:"[୦-୯]",tamldec:"[௦-௯]",telu:"[౦-౯]",thai:"[๐-๙]",tibt:"[༠-༩]",latn:"\\d"},Xi={arab:[1632,1641],arabext:[1776,1785],bali:[6992,7001],beng:[2534,2543],deva:[2406,2415],fullwide:[65296,65303],gujr:[2790,2799],khmr:[6112,6121],knda:[3302,3311],laoo:[3792,3801],limb:[6470,6479],mlym:[3430,3439],mong:[6160,6169],mymr:[4160,4169],orya:[2918,2927],tamldec:[3046,3055],telu:[3174,3183],thai:[3664,3673],tibt:[3872,3881]},Ji=$i.hanidec.replace(/[\[|\]]/g,"").split("");function Qi({numberingSystem:e},t=""){return new RegExp(`${$i[e||"latn"]}${t}`)}function en(e,t=(e=>e)){return{regex:e,deser:([e])=>t(function(e){let t=parseInt(e,10);if(isNaN(t)){t="";for(let i=0;i<e.length;i++){const n=e.charCodeAt(i);if(-1!==e[i].search($i.hanidec))t+=Ji.indexOf(e[i]);else for(const e in Xi){const[i,s]=Xi[e];n>=i&&n<=s&&(t+=n-i)}}return parseInt(t,10)}return t}(e))}}const tn=`[ ${String.fromCharCode(160)}]`,nn=new RegExp(tn,"g");function sn(e){return e.replace(/\./g,"\\.?").replace(nn,tn)}function rn(e){return e.replace(/\./g,"").replace(nn," ").toLowerCase()}function an(e,t){return null===e?null:{regex:RegExp(e.map(sn).join("|")),deser:([i])=>e.findIndex((e=>rn(i)===rn(e)))+t}}function un(e,t){return{regex:e,deser:([,e,t])=>Nt(e,t),groups:t}}function on(e){return{regex:e,deser:([e])=>e}}const ln={year:{"2-digit":"yy",numeric:"yyyyy"},month:{numeric:"M","2-digit":"MM",short:"MMM",long:"MMMM"},day:{numeric:"d","2-digit":"dd"},weekday:{short:"EEE",long:"EEEE"},dayperiod:"a",dayPeriod:"a",hour12:{numeric:"h","2-digit":"hh"},hour24:{numeric:"H","2-digit":"HH"},minute:{numeric:"m","2-digit":"mm"},second:{numeric:"s","2-digit":"ss"},timeZoneName:{long:"ZZZZZ",short:"ZZZ"}};let Dn=null;function dn(e,t){return Array.prototype.concat(...e.map((e=>function(e,t){if(e.literal)return e;const i=cn(Kt.macroTokenToFormatOpts(e.val),t);return null==i||i.includes(void 0)?e:i}(e,t))))}function hn(e,t,i){const n=dn(Kt.parseFormat(i),e),s=n.map((t=>function(e,t){const i=Qi(t),n=Qi(t,"{2}"),s=Qi(t,"{3}"),r=Qi(t,"{4}"),a=Qi(t,"{6}"),u=Qi(t,"{1,2}"),o=Qi(t,"{1,3}"),l=Qi(t,"{1,6}"),D=Qi(t,"{1,9}"),d=Qi(t,"{2,4}"),h=Qi(t,"{4,6}"),c=e=>{return{regex:RegExp((t=e.val,t.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&"))),deser:([e])=>e,literal:!0};var t},g=(g=>{if(e.literal)return c(g);switch(g.val){case"G":return an(t.eras("short"),0);case"GG":return an(t.eras("long"),0);case"y":return en(l);case"yy":case"kk":return en(d,Tt);case"yyyy":case"kkkk":return en(r);case"yyyyy":return en(h);case"yyyyyy":return en(a);case"M":case"L":case"d":case"H":case"h":case"m":case"q":case"s":case"W":return en(u);case"MM":case"LL":case"dd":case"HH":case"hh":case"mm":case"qq":case"ss":case"WW":return en(n);case"MMM":return an(t.months("short",!0),1);case"MMMM":return an(t.months("long",!0),1);case"LLL":return an(t.months("short",!1),1);case"LLLL":return an(t.months("long",!1),1);case"o":case"S":return en(o);case"ooo":case"SSS":return en(s);case"u":return on(D);case"uu":return on(u);case"uuu":case"E":case"c":return en(i);case"a":return an(t.meridiems(),0);case"EEE":return an(t.weekdays("short",!1),1);case"EEEE":return an(t.weekdays("long",!1),1);case"ccc":return an(t.weekdays("short",!0),1);case"cccc":return an(t.weekdays("long",!0),1);case"Z":case"ZZ":return un(new RegExp(`([+-]${u.source})(?::(${n.source}))?`),2);case"ZZZ":return un(new RegExp(`([+-]${u.source})(${n.source})?`),2);case"z":return on(/[a-z_+-/]{1,256}?/i);case" ":return on(/[^\S\n\r]/);default:return c(g)}})(e)||{invalidReason:"missing Intl.DateTimeFormat.formatToParts support"};return g.token=e,g}(t,e))),r=s.find((e=>e.invalidReason));if(r)return{input:t,tokens:n,invalidReason:r.invalidReason};{const[e,i]=function(e){return[`^${e.map((e=>e.regex)).reduce(((e,t)=>`${e}(${t.source})`),"")}$`,e]}(s),r=RegExp(e,"i"),[a,u]=function(e,t,i){const n=e.match(t);if(n){const e={};let t=1;for(const s in i)if(ct(i,s)){const r=i[s],a=r.groups?r.groups+1:1;!r.literal&&r.token&&(e[r.token.val[0]]=r.deser(n.slice(t,t+a))),t+=a}return[n,e]}return[n,{}]}(t,r,i),[o,l,D]=u?function(e){let t,i=null;return ot(e.z)||(i=ze.create(e.z)),ot(e.Z)||(i||(i=new Xe(e.Z)),t=e.Z),ot(e.q)||(e.M=3*(e.q-1)+1),ot(e.h)||(e.h<12&&1===e.a?e.h+=12:12===e.h&&0===e.a&&(e.h=0)),0===e.G&&e.y&&(e.y=-e.y),ot(e.u)||(e.S=Ft(e.u)),[Object.keys(e).reduce(((t,i)=>{const n=(e=>{switch(e){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":case"H":return"hour";case"d":return"day";case"o":return"ordinal";case"L":case"M":return"month";case"y":return"year";case"E":case"c":return"weekday";case"W":return"weekNumber";case"k":return"weekYear";case"q":return"quarter";default:return null}})(i);return n&&(t[n]=e[i]),t}),{}),i,t]}(u):[null,null,void 0];if(ct(u,"a")&&ct(u,"H"))throw new ne("Can't include meridiem when specifying 24-hour format");return{input:t,tokens:n,regex:r,rawMatches:a,matches:u,result:o,zone:l,specificOffset:D}}}function cn(e,t){if(!e)return null;const i=Kt.create(t,e).dtFormatter((Dn||(Dn=Zn.fromMillis(1555555555555)),Dn)),n=i.formatToParts(),s=i.resolvedOptions();return n.map((t=>function(e,t,i){const{type:n,value:s}=e;if("literal"===n){const e=/^\s+$/.test(s);return{literal:!e,val:e?" ":s}}const r=t[n];let a=n;"hour"===n&&(a=null!=t.hour12?t.hour12?"hour12":"hour24":null!=t.hourCycle?"h11"===t.hourCycle||"h12"===t.hourCycle?"hour12":"hour24":i.hour12?"hour12":"hour24");let u=ln[a];if("object"==typeof u&&(u=u[r]),u)return{literal:!1,val:u}}(t,e,s)))}const gn=[0,31,59,90,120,151,181,212,243,273,304,334],En=[0,31,60,91,121,152,182,213,244,274,305,335];function pn(e,t){return new qt("unit out of range",`you specified ${t} (of type ${typeof t}) as a ${e}, which is invalid`)}function fn(e,t,i){const n=new Date(Date.UTC(e,t-1,i));e<100&&e>=0&&n.setUTCFullYear(n.getUTCFullYear()-1900);const s=n.getUTCDay();return 0===s?7:s}function Fn(e,t,i){return i+(Ct(e)?En:gn)[t-1]}function mn(e,t){const i=Ct(e)?En:gn,n=i.findIndex((e=>e<t));return{month:n+1,day:t-i[n]}}function Cn(e){const{year:t,month:i,day:n}=e,s=Fn(t,i,n),r=fn(t,i,n);let a,u=Math.floor((s-r+10)/7);return u<1?(a=t-1,u=At(a)):u>At(t)?(a=t+1,u=1):a=t,{weekYear:a,weekNumber:u,weekday:r,..._t(e)}}function yn(e){const{weekYear:t,weekNumber:i,weekday:n}=e,s=fn(t,1,4),r=yt(t);let a,u=7*i+n-s-3;u<1?(a=t-1,u+=yt(a)):u>r?(a=t+1,u-=yt(t)):a=t;const{month:o,day:l}=mn(a,u);return{year:a,month:o,day:l,..._t(e)}}function Sn(e){const{year:t,month:i,day:n}=e;return{year:t,ordinal:Fn(t,i,n),..._t(e)}}function bn(e){const{year:t,ordinal:i}=e,{month:n,day:s}=mn(t,i);return{year:t,month:n,day:s,..._t(e)}}function An(e){const t=Dt(e.year),i=gt(e.month,1,12),n=gt(e.day,1,St(e.year,e.month));return t?i?!n&&pn("day",e.day):pn("month",e.month):pn("year",e.year)}function Tn(e){const{hour:t,minute:i,second:n,millisecond:s}=e,r=gt(t,0,23)||24===t&&0===i&&0===n&&0===s,a=gt(i,0,59),u=gt(n,0,59),o=gt(s,0,999);return r?a?u?!o&&pn("millisecond",s):pn("second",n):pn("minute",i):pn("hour",t)}const vn="Invalid DateTime",Nn=864e13;function wn(e){return new qt("unsupported zone",`the zone "${e.name}" is not supported`)}function Ln(e){return null===e.weekData&&(e.weekData=Cn(e.c)),e.weekData}function On(e,t){const i={ts:e.ts,zone:e.zone,c:e.c,o:e.o,loc:e.loc,invalid:e.invalid};return new Zn({...i,...t,old:i})}function _n(e,t,i){let n=e-60*t*1e3;const s=i.offset(n);if(t===s)return[n,t];n-=60*(s-t)*1e3;const r=i.offset(n);return s===r?[n,s]:[e-60*Math.min(s,r)*1e3,Math.max(s,r)]}function In(e,t){const i=new Date(e+=60*t*1e3);return{year:i.getUTCFullYear(),month:i.getUTCMonth()+1,day:i.getUTCDate(),hour:i.getUTCHours(),minute:i.getUTCMinutes(),second:i.getUTCSeconds(),millisecond:i.getUTCMilliseconds()}}function kn(e,t,i){return _n(bt(e),t,i)}function Bn(e,t){const i=e.o,n=e.c.year+Math.trunc(t.years),s=e.c.month+Math.trunc(t.months)+3*Math.trunc(t.quarters),r={...e.c,year:n,month:s,day:Math.min(e.c.day,St(n,s))+Math.trunc(t.days)+7*Math.trunc(t.weeks)},a=Wi.fromObject({years:t.years-Math.trunc(t.years),quarters:t.quarters-Math.trunc(t.quarters),months:t.months-Math.trunc(t.months),weeks:t.weeks-Math.trunc(t.weeks),days:t.days-Math.trunc(t.days),hours:t.hours,minutes:t.minutes,seconds:t.seconds,milliseconds:t.milliseconds}).as("milliseconds"),u=bt(r);let[o,l]=_n(u,i,e.zone);return 0!==a&&(o+=a,l=e.zone.offset(o)),{ts:o,o:l}}function xn(e,t,i,n,s,r){const{setZone:a,zone:u}=i;if(e&&0!==Object.keys(e).length||t){const n=t||u,s=Zn.fromObject(e,{...i,zone:n,specificOffset:r});return a?s:s.setZone(u)}return Zn.invalid(new qt("unparsable",`the input "${s}" can't be parsed as ${n}`))}function Rn(e,t,i=!0){return e.isValid?Kt.create(Ze.create("en-US"),{allowZ:i,forceSimple:!0}).formatDateTimeFromString(e,t):null}function Mn(e,t){const i=e.c.year>9999||e.c.year<0;let n="";return i&&e.c.year>=0&&(n+="+"),n+=Et(e.c.year,i?6:4),t?(n+="-",n+=Et(e.c.month),n+="-",n+=Et(e.c.day)):(n+=Et(e.c.month),n+=Et(e.c.day)),n}function zn(e,t,i,n,s,r){let a=Et(e.c.hour);return t?(a+=":",a+=Et(e.c.minute),0===e.c.millisecond&&0===e.c.second&&i||(a+=":")):a+=Et(e.c.minute),0===e.c.millisecond&&0===e.c.second&&i||(a+=Et(e.c.second),0===e.c.millisecond&&n||(a+=".",a+=Et(e.c.millisecond,3))),s&&(e.isOffsetFixed&&0===e.offset&&!r?a+="Z":e.o<0?(a+="-",a+=Et(Math.trunc(-e.o/60)),a+=":",a+=Et(Math.trunc(-e.o%60))):(a+="+",a+=Et(Math.trunc(e.o/60)),a+=":",a+=Et(Math.trunc(e.o%60)))),r&&(a+="["+e.zone.ianaName+"]"),a}const Hn={month:1,day:1,hour:0,minute:0,second:0,millisecond:0},Pn={weekNumber:1,weekday:1,hour:0,minute:0,second:0,millisecond:0},Gn={ordinal:1,hour:0,minute:0,second:0,millisecond:0},Vn=["year","month","day","hour","minute","second","millisecond"],Yn=["weekYear","weekNumber","weekday","hour","minute","second","millisecond"],jn=["year","ordinal","hour","minute","second","millisecond"];function Wn(e){const t={year:"year",years:"year",month:"month",months:"month",day:"day",days:"day",hour:"hour",hours:"hour",minute:"minute",minutes:"minute",quarter:"quarter",quarters:"quarter",second:"second",seconds:"second",millisecond:"millisecond",milliseconds:"millisecond",weekday:"weekday",weekdays:"weekday",weeknumber:"weekNumber",weeksnumber:"weekNumber",weeknumbers:"weekNumber",weekyear:"weekYear",weekyears:"weekYear",ordinal:"ordinal"}[e.toLowerCase()];if(!t)throw new se(e);return t}function Un(e,t){const i=Qe(t.zone,ut.defaultZone),n=Ze.fromObject(t),s=ut.now();let r,a;if(ot(e.year))r=s;else{for(const t of Vn)ot(e[t])&&(e[t]=Hn[t]);const t=An(e)||Tn(e);if(t)return Zn.invalid(t);const n=i.offset(s);[r,a]=kn(e,n,i)}return new Zn({ts:r,zone:i,loc:n,o:a})}function Kn(e,t,i){const n=!!ot(i.round)||i.round,s=(e,s)=>(e=mt(e,n||i.calendary?0:2,!0),t.loc.clone(i).relFormatter(i).format(e,s)),r=n=>i.calendary?t.hasSame(e,n)?0:t.startOf(n).diff(e.startOf(n),n).get(n):t.diff(e,n).get(n);if(i.unit)return s(r(i.unit),i.unit);for(const e of i.units){const t=r(e);if(Math.abs(t)>=1)return s(t,e)}return s(e>t?-0:0,i.units[i.units.length-1])}function qn(e){let t,i={};return e.length>0&&"object"==typeof e[e.length-1]?(i=e[e.length-1],t=Array.from(e).slice(0,e.length-1)):t=Array.from(e),[i,t]}class Zn{constructor(e){const t=e.zone||ut.defaultZone;let i=e.invalid||(Number.isNaN(e.ts)?new qt("invalid input"):null)||(t.isValid?null:wn(t));this.ts=ot(e.ts)?ut.now():e.ts;let n=null,s=null;if(!i)if(e.old&&e.old.ts===this.ts&&e.old.zone.equals(t))[n,s]=[e.old.c,e.old.o];else{const e=t.offset(this.ts);n=In(this.ts,e),i=Number.isNaN(n.year)?new qt("invalid input"):null,n=i?null:n,s=i?null:e}this._zone=t,this.loc=e.loc||Ze.create(),this.invalid=i,this.weekData=null,this.c=n,this.o=s,this.isLuxonDateTime=!0}static now(){return new Zn({})}static local(){const[e,t]=qn(arguments),[i,n,s,r,a,u,o]=t;return Un({year:i,month:n,day:s,hour:r,minute:a,second:u,millisecond:o},e)}static utc(){const[e,t]=qn(arguments),[i,n,s,r,a,u,o]=t;return e.zone=Xe.utcInstance,Un({year:i,month:n,day:s,hour:r,minute:a,second:u,millisecond:o},e)}static fromJSDate(e,t={}){const i=(n=e,"[object Date]"===Object.prototype.toString.call(n)?e.valueOf():NaN);var n;if(Number.isNaN(i))return Zn.invalid("invalid input");const s=Qe(t.zone,ut.defaultZone);return s.isValid?new Zn({ts:i,zone:s,loc:Ze.fromObject(t)}):Zn.invalid(wn(s))}static fromMillis(e,t={}){if(lt(e))return e<-Nn||e>Nn?Zn.invalid("Timestamp out of range"):new Zn({ts:e,zone:Qe(t.zone,ut.defaultZone),loc:Ze.fromObject(t)});throw new re(`fromMillis requires a numerical input, but received a ${typeof e} with value ${e}`)}static fromSeconds(e,t={}){if(lt(e))return new Zn({ts:1e3*e,zone:Qe(t.zone,ut.defaultZone),loc:Ze.fromObject(t)});throw new re("fromSeconds requires a numerical input")}static fromObject(e,t={}){e=e||{};const i=Qe(t.zone,ut.defaultZone);if(!i.isValid)return Zn.invalid(wn(i));const n=ut.now(),s=ot(t.specificOffset)?i.offset(n):t.specificOffset,r=Lt(e,Wn),a=!ot(r.ordinal),u=!ot(r.year),o=!ot(r.month)||!ot(r.day),l=u||o,D=r.weekYear||r.weekNumber,d=Ze.fromObject(t);if((l||a)&&D)throw new ne("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(o&&a)throw new ne("Can't mix ordinal dates with month/day");const h=D||r.weekday&&!l;let c,g,E=In(n,s);h?(c=Yn,g=Pn,E=Cn(E)):a?(c=jn,g=Gn,E=Sn(E)):(c=Vn,g=Hn);let p=!1;for(const e of c)ot(r[e])?r[e]=p?g[e]:E[e]:p=!0;const f=h?function(e){const t=Dt(e.weekYear),i=gt(e.weekNumber,1,At(e.weekYear)),n=gt(e.weekday,1,7);return t?i?!n&&pn("weekday",e.weekday):pn("week",e.week):pn("weekYear",e.weekYear)}(r):a?function(e){const t=Dt(e.year),i=gt(e.ordinal,1,yt(e.year));return t?!i&&pn("ordinal",e.ordinal):pn("year",e.year)}(r):An(r),F=f||Tn(r);if(F)return Zn.invalid(F);const m=h?yn(r):a?bn(r):r,[C,y]=kn(m,s,i),S=new Zn({ts:C,zone:i,o:y,loc:d});return r.weekday&&l&&e.weekday!==S.weekday?Zn.invalid("mismatched weekday",`you can't specify both a weekday of ${r.weekday} and a date of ${S.toISO()}`):S}static fromISO(e,t={}){const[i,n]=function(e){return Jt(e,[Ai,wi],[Ti,Li],[vi,Oi],[Ni,_i])}(e);return xn(i,n,t,"ISO 8601",e)}static fromRFC2822(e,t={}){const[i,n]=function(e){return Jt(function(e){return e.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").trim()}(e),[fi,Fi])}(e);return xn(i,n,t,"RFC 2822",e)}static fromHTTP(e,t={}){const[i,n]=function(e){return Jt(e,[mi,Si],[Ci,Si],[yi,bi])}(e);return xn(i,n,t,"HTTP",t)}static fromFormat(e,t,i={}){if(ot(e)||ot(t))throw new re("fromFormat requires an input string and a format");const{locale:n=null,numberingSystem:s=null}=i,r=Ze.fromOpts({locale:n,numberingSystem:s,defaultToEN:!0}),[a,u,o,l]=function(e,t,i){const{result:n,zone:s,specificOffset:r,invalidReason:a}=hn(e,t,i);return[n,s,r,a]}(r,e,t);return l?Zn.invalid(l):xn(a,u,i,`format ${t}`,e,o)}static fromString(e,t,i={}){return Zn.fromFormat(e,t,i)}static fromSQL(e,t={}){const[i,n]=function(e){return Jt(e,[ki,wi],[Bi,xi])}(e);return xn(i,n,t,"SQL",e)}static invalid(e,t=null){if(!e)throw new re("need to specify a reason the DateTime is invalid");const i=e instanceof qt?e:new qt(e,t);if(ut.throwOnInvalid)throw new ee(i);return new Zn({invalid:i})}static isDateTime(e){return e&&e.isLuxonDateTime||!1}static parseFormatForOpts(e,t={}){const i=cn(e,Ze.fromObject(t));return i?i.map((e=>e?e.val:null)).join(""):null}static expandFormat(e,t={}){return dn(Kt.parseFormat(e),Ze.fromObject(t)).map((e=>e.val)).join("")}get(e){return this[e]}get isValid(){return null===this.invalid}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}get outputCalendar(){return this.isValid?this.loc.outputCalendar:null}get zone(){return this._zone}get zoneName(){return this.isValid?this.zone.name:null}get year(){return this.isValid?this.c.year:NaN}get quarter(){return this.isValid?Math.ceil(this.c.month/3):NaN}get month(){return this.isValid?this.c.month:NaN}get day(){return this.isValid?this.c.day:NaN}get hour(){return this.isValid?this.c.hour:NaN}get minute(){return this.isValid?this.c.minute:NaN}get second(){return this.isValid?this.c.second:NaN}get millisecond(){return this.isValid?this.c.millisecond:NaN}get weekYear(){return this.isValid?Ln(this).weekYear:NaN}get weekNumber(){return this.isValid?Ln(this).weekNumber:NaN}get weekday(){return this.isValid?Ln(this).weekday:NaN}get ordinal(){return this.isValid?Sn(this.c).ordinal:NaN}get monthShort(){return this.isValid?qi.months("short",{locObj:this.loc})[this.month-1]:null}get monthLong(){return this.isValid?qi.months("long",{locObj:this.loc})[this.month-1]:null}get weekdayShort(){return this.isValid?qi.weekdays("short",{locObj:this.loc})[this.weekday-1]:null}get weekdayLong(){return this.isValid?qi.weekdays("long",{locObj:this.loc})[this.weekday-1]:null}get offset(){return this.isValid?+this.o:NaN}get offsetNameShort(){return this.isValid?this.zone.offsetName(this.ts,{format:"short",locale:this.locale}):null}get offsetNameLong(){return this.isValid?this.zone.offsetName(this.ts,{format:"long",locale:this.locale}):null}get isOffsetFixed(){return this.isValid?this.zone.isUniversal:null}get isInDST(){return!this.isOffsetFixed&&(this.offset>this.set({month:1,day:1}).offset||this.offset>this.set({month:5}).offset)}getPossibleOffsets(){if(!this.isValid||this.isOffsetFixed)return[this];const e=864e5,t=6e4,i=bt(this.c),n=this.zone.offset(i-e),s=this.zone.offset(i+e),r=this.zone.offset(i-n*t),a=this.zone.offset(i-s*t);if(r===a)return[this];const u=i-r*t,o=i-a*t,l=In(u,r),D=In(o,a);return l.hour===D.hour&&l.minute===D.minute&&l.second===D.second&&l.millisecond===D.millisecond?[On(this,{ts:u}),On(this,{ts:o})]:[this]}get isInLeapYear(){return Ct(this.year)}get daysInMonth(){return St(this.year,this.month)}get daysInYear(){return this.isValid?yt(this.year):NaN}get weeksInWeekYear(){return this.isValid?At(this.weekYear):NaN}resolvedLocaleOptions(e={}){const{locale:t,numberingSystem:i,calendar:n}=Kt.create(this.loc.clone(e),e).resolvedOptions(this);return{locale:t,numberingSystem:i,outputCalendar:n}}toUTC(e=0,t={}){return this.setZone(Xe.instance(e),t)}toLocal(){return this.setZone(ut.defaultZone)}setZone(e,{keepLocalTime:t=!1,keepCalendarTime:i=!1}={}){if((e=Qe(e,ut.defaultZone)).equals(this.zone))return this;if(e.isValid){let n=this.ts;if(t||i){const t=e.offset(this.ts),i=this.toObject();[n]=kn(i,t,e)}return On(this,{ts:n,zone:e})}return Zn.invalid(wn(e))}reconfigure({locale:e,numberingSystem:t,outputCalendar:i}={}){return On(this,{loc:this.loc.clone({locale:e,numberingSystem:t,outputCalendar:i})})}setLocale(e){return this.reconfigure({locale:e})}set(e){if(!this.isValid)return this;const t=Lt(e,Wn),i=!ot(t.weekYear)||!ot(t.weekNumber)||!ot(t.weekday),n=!ot(t.ordinal),s=!ot(t.year),r=!ot(t.month)||!ot(t.day),a=s||r,u=t.weekYear||t.weekNumber;if((a||n)&&u)throw new ne("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(r&&n)throw new ne("Can't mix ordinal dates with month/day");let o;i?o=yn({...Cn(this.c),...t}):ot(t.ordinal)?(o={...this.toObject(),...t},ot(t.day)&&(o.day=Math.min(St(o.year,o.month),o.day))):o=bn({...Sn(this.c),...t});const[l,D]=kn(o,this.o,this.zone);return On(this,{ts:l,o:D})}plus(e){return this.isValid?On(this,Bn(this,Wi.fromDurationLike(e))):this}minus(e){return this.isValid?On(this,Bn(this,Wi.fromDurationLike(e).negate())):this}startOf(e){if(!this.isValid)return this;const t={},i=Wi.normalizeUnit(e);switch(i){case"years":t.month=1;case"quarters":case"months":t.day=1;case"weeks":case"days":t.hour=0;case"hours":t.minute=0;case"minutes":t.second=0;case"seconds":t.millisecond=0}if("weeks"===i&&(t.weekday=1),"quarters"===i){const e=Math.ceil(this.month/3);t.month=3*(e-1)+1}return this.set(t)}endOf(e){return this.isValid?this.plus({[e]:1}).startOf(e).minus(1):this}toFormat(e,t={}){return this.isValid?Kt.create(this.loc.redefaultToEN(t)).formatDateTimeFromString(this,e):vn}toLocaleString(e=De,t={}){return this.isValid?Kt.create(this.loc.clone(t),e).formatDateTime(this):vn}toLocaleParts(e={}){return this.isValid?Kt.create(this.loc.clone(e),e).formatDateTimeParts(this):[]}toISO({format:e="extended",suppressSeconds:t=!1,suppressMilliseconds:i=!1,includeOffset:n=!0,extendedZone:s=!1}={}){if(!this.isValid)return null;const r="extended"===e;let a=Mn(this,r);return a+="T",a+=zn(this,r,t,i,n,s),a}toISODate({format:e="extended"}={}){return this.isValid?Mn(this,"extended"===e):null}toISOWeekDate(){return Rn(this,"kkkk-'W'WW-c")}toISOTime({suppressMilliseconds:e=!1,suppressSeconds:t=!1,includeOffset:i=!0,includePrefix:n=!1,extendedZone:s=!1,format:r="extended"}={}){return this.isValid?(n?"T":"")+zn(this,"extended"===r,t,e,i,s):null}toRFC2822(){return Rn(this,"EEE, dd LLL yyyy HH:mm:ss ZZZ",!1)}toHTTP(){return Rn(this.toUTC(),"EEE, dd LLL yyyy HH:mm:ss 'GMT'")}toSQLDate(){return this.isValid?Mn(this,!0):null}toSQLTime({includeOffset:e=!0,includeZone:t=!1,includeOffsetSpace:i=!0}={}){let n="HH:mm:ss.SSS";return(t||e)&&(i&&(n+=" "),t?n+="z":e&&(n+="ZZ")),Rn(this,n,!0)}toSQL(e={}){return this.isValid?`${this.toSQLDate()} ${this.toSQLTime(e)}`:null}toString(){return this.isValid?this.toISO():vn}valueOf(){return this.toMillis()}toMillis(){return this.isValid?this.ts:NaN}toSeconds(){return this.isValid?this.ts/1e3:NaN}toUnixInteger(){return this.isValid?Math.floor(this.ts/1e3):NaN}toJSON(){return this.toISO()}toBSON(){return this.toJSDate()}toObject(e={}){if(!this.isValid)return{};const t={...this.c};return e.includeConfig&&(t.outputCalendar=this.outputCalendar,t.numberingSystem=this.loc.numberingSystem,t.locale=this.loc.locale),t}toJSDate(){return new Date(this.isValid?this.ts:NaN)}diff(e,t="milliseconds",i={}){if(!this.isValid||!e.isValid)return Wi.invalid("created by diffing an invalid DateTime");const n={locale:this.locale,numberingSystem:this.numberingSystem,...i},s=(u=t,Array.isArray(u)?u:[u]).map(Wi.normalizeUnit),r=e.valueOf()>this.valueOf(),a=function(e,t,i,n){let[s,r,a,u]=function(e,t,i){const n=[["years",(e,t)=>t.year-e.year],["quarters",(e,t)=>t.quarter-e.quarter+4*(t.year-e.year)],["months",(e,t)=>t.month-e.month+12*(t.year-e.year)],["weeks",(e,t)=>{const i=Zi(e,t);return(i-i%7)/7}],["days",Zi]],s={},r=e;let a,u;for(const[o,l]of n)i.indexOf(o)>=0&&(a=o,s[o]=l(e,t),u=r.plus(s),u>t?(s[o]--,(e=r.plus(s))>t&&(u=e,s[o]--,e=r.plus(s))):e=u);return[e,s,u,a]}(e,t,i);const o=t-s,l=i.filter((e=>["hours","minutes","seconds","milliseconds"].indexOf(e)>=0));0===l.length&&(a<t&&(a=s.plus({[u]:1})),a!==s&&(r[u]=(r[u]||0)+o/(a-s)));const D=Wi.fromObject(r,n);return l.length>0?Wi.fromMillis(o,n).shiftTo(...l).plus(D):D}(r?this:e,r?e:this,s,n);var u;return r?a.negate():a}diffNow(e="milliseconds",t={}){return this.diff(Zn.now(),e,t)}until(e){return this.isValid?Ki.fromDateTimes(this,e):this}hasSame(e,t){if(!this.isValid)return!1;const i=e.valueOf(),n=this.setZone(e.zone,{keepLocalTime:!0});return n.startOf(t)<=i&&i<=n.endOf(t)}equals(e){return this.isValid&&e.isValid&&this.valueOf()===e.valueOf()&&this.zone.equals(e.zone)&&this.loc.equals(e.loc)}toRelative(e={}){if(!this.isValid)return null;const t=e.base||Zn.fromObject({},{zone:this.zone}),i=e.padding?this<t?-e.padding:e.padding:0;let n=["years","months","days","hours","minutes","seconds"],s=e.unit;return Array.isArray(e.unit)&&(n=e.unit,s=void 0),Kn(t,this.plus(i),{...e,numeric:"always",units:n,unit:s})}toRelativeCalendar(e={}){return this.isValid?Kn(e.base||Zn.fromObject({},{zone:this.zone}),this,{...e,numeric:"auto",units:["years","months","days"],calendary:!0}):null}static min(...e){if(!e.every(Zn.isDateTime))throw new re("min requires all arguments be DateTimes");return ht(e,(e=>e.valueOf()),Math.min)}static max(...e){if(!e.every(Zn.isDateTime))throw new re("max requires all arguments be DateTimes");return ht(e,(e=>e.valueOf()),Math.max)}static fromFormatExplain(e,t,i={}){const{locale:n=null,numberingSystem:s=null}=i;return hn(Ze.fromOpts({locale:n,numberingSystem:s,defaultToEN:!0}),e,t)}static fromStringExplain(e,t,i={}){return Zn.fromFormatExplain(e,t,i)}static get DATE_SHORT(){return De}static get DATE_MED(){return de}static get DATE_MED_WITH_WEEKDAY(){return he}static get DATE_FULL(){return ce}static get DATE_HUGE(){return ge}static get TIME_SIMPLE(){return Ee}static get TIME_WITH_SECONDS(){return pe}static get TIME_WITH_SHORT_OFFSET(){return fe}static get TIME_WITH_LONG_OFFSET(){return Fe}static get TIME_24_SIMPLE(){return me}static get TIME_24_WITH_SECONDS(){return Ce}static get TIME_24_WITH_SHORT_OFFSET(){return ye}static get TIME_24_WITH_LONG_OFFSET(){return Se}static get DATETIME_SHORT(){return be}static get DATETIME_SHORT_WITH_SECONDS(){return Ae}static get DATETIME_MED(){return Te}static get DATETIME_MED_WITH_SECONDS(){return ve}static get DATETIME_MED_WITH_WEEKDAY(){return Ne}static get DATETIME_FULL(){return we}static get DATETIME_FULL_WITH_SECONDS(){return Le}static get DATETIME_HUGE(){return Oe}static get DATETIME_HUGE_WITH_SECONDS(){return _e}}function $n(e){if(Zn.isDateTime(e))return e;if(e&&e.valueOf&&lt(e.valueOf()))return Zn.fromJSDate(e);if(e&&"object"==typeof e)return Zn.fromObject(e);throw new re(`Unknown datetime argument: ${e}, of type ${typeof e}`)}const Xn={renderNullAs:"\\-",taskCompletionTracking:!1,taskCompletionUseEmojiShorthand:!1,taskCompletionText:"completion",taskCompletionDateFormat:"yyyy-MM-dd",recursiveSubTaskCompletion:!1,warnOnEmptyResult:!0,refreshEnabled:!0,refreshInterval:2500,defaultDateFormat:"MMMM dd, yyyy",defaultDateTimeFormat:"h:mm a - MMMM dd, yyyy",maxRecursiveRenderDepth:4,tableIdColumnName:"File",tableGroupColumnName:"Group",showResultCount:!0};class Jn{value;successful;constructor(e){this.value=e,this.successful=!0}map(e){return new Jn(e(this.value))}flatMap(e){return e(this.value)}mapErr(e){return this}bimap(e,t){return this.map(e)}orElse(e){return this.value}cast(){return this}orElseThrow(e){return this.value}}class Qn{error;successful;constructor(e){this.error=e,this.successful=!1}map(e){return this}flatMap(e){return this}mapErr(e){return new Qn(e(this.error))}bimap(e,t){return this.mapErr(t)}orElse(e){return e}cast(){return this}orElseThrow(e){throw e?new Error(e(this.error)):new Error(""+this.error)}}var es;!function(e){function t(e){return new Jn(e)}function i(e){return new Qn(e)}function n(e,t,n){return e.successful?t.successful?n(e.value,t.value):i(t.error):i(e.error)}e.success=t,e.failure=i,e.flatMap2=n,e.map2=function(e,i,s){return n(e,i,((e,i)=>t(s(e,i))))}}(es||(es={})),"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0!==T||"undefined"!=typeof self&&self;var ts={exports:{}};"undefined"!=typeof self&&self,ts.exports=function(e){var t={};function i(n){if(t[n])return t[n].exports;var s=t[n]={i:n,l:!1,exports:{}};return e[n].call(s.exports,s,s.exports,i),s.l=!0,s.exports}return i.m=e,i.c=t,i.d=function(e,t,n){i.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:n})},i.r=function(e){Object.defineProperty(e,"__esModule",{value:!0})},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i(i.s=0)}([function(e,t,i){function n(e){if(!(this instanceof n))return new n(e);this._=e}var s=n.prototype;function r(e,t){for(var i=0;i<e;i++)t(i)}function a(e,t,i){return function(e,t){r(t.length,(function(i){e(t[i],i,t)}))}((function(i,n,s){t=e(t,i,n,s)}),i),t}function u(e,t){return a((function(t,i,n,s){return t.concat([e(i,n,s)])}),[],t)}function o(e,t){var i={v:0,buf:t};return r(e,(function(){var e;i={v:i.v<<1|(e=i.buf,e[0]>>7),buf:function(e){var t=a((function(e,t,i,n){return e.concat(i===n.length-1?Buffer.from([t,0]).readUInt16BE(0):n.readUInt16BE(i))}),[],e);return Buffer.from(u((function(e){return(e<<1&65535)>>8}),t))}(i.buf)}})),i}function l(){return"undefined"!=typeof Buffer}function D(){if(!l())throw new Error("Buffer global does not exist; please use webpack if you need to parse Buffers in the browser.")}function d(e){D();var t=a((function(e,t){return e+t}),0,e);if(t%8!=0)throw new Error("The bits ["+e.join(", ")+"] add up to "+t+" which is not an even number of bytes; the total should be divisible by 8");var i,s=t/8,r=(i=function(e){return e>48},a((function(e,t){return e||(i(t)?t:e)}),null,e));if(r)throw new Error(r+" bit range requested exceeds 48 bit (6 byte) Number max.");return new n((function(t,i){var n=s+i;return n>t.length?S(i,s.toString()+" bytes"):y(n,a((function(e,t){var i=o(t,e.buf);return{coll:e.coll.concat(i.v),buf:i.buf}}),{coll:[],buf:t.slice(i,n)},e).coll)}))}function h(e,t){return new n((function(i,n){return D(),n+t>i.length?S(n,t+" bytes for "+e):y(n+t,i.slice(n,n+t))}))}function c(e,t){if("number"!=typeof(i=t)||Math.floor(i)!==i||t<0||t>6)throw new Error(e+" requires integer length in range [0, 6].");var i}function g(e){return c("uintBE",e),h("uintBE("+e+")",e).map((function(t){return t.readUIntBE(0,e)}))}function E(e){return c("uintLE",e),h("uintLE("+e+")",e).map((function(t){return t.readUIntLE(0,e)}))}function p(e){return c("intBE",e),h("intBE("+e+")",e).map((function(t){return t.readIntBE(0,e)}))}function f(e){return c("intLE",e),h("intLE("+e+")",e).map((function(t){return t.readIntLE(0,e)}))}function F(e){return e instanceof n}function m(e){return"[object Array]"==={}.toString.call(e)}function C(e){return l()&&Buffer.isBuffer(e)}function y(e,t){return{status:!0,index:e,value:t,furthest:-1,expected:[]}}function S(e,t){return m(t)||(t=[t]),{status:!1,index:-1,value:null,furthest:e,expected:t}}function b(e,t){if(!t)return e;if(e.furthest>t.furthest)return e;var i=e.furthest===t.furthest?function(e,t){if(function(){if(void 0!==n._supportsSet)return n._supportsSet;var e="undefined"!=typeof Set;return n._supportsSet=e,e}()&&Array.from){for(var i=new Set(e),s=0;s<t.length;s++)i.add(t[s]);var r=Array.from(i);return r.sort(),r}for(var a={},u=0;u<e.length;u++)a[e[u]]=!0;for(var o=0;o<t.length;o++)a[t[o]]=!0;var l=[];for(var D in a)({}).hasOwnProperty.call(a,D)&&l.push(D);return l.sort(),l}(e.expected,t.expected):t.expected;return{status:e.status,index:e.index,value:e.value,furthest:t.furthest,expected:i}}var A={};function T(e,t){if(C(e))return{offset:t,line:-1,column:-1};e in A||(A[e]={});for(var i=A[e],n=0,s=0,r=0,a=t;a>=0;){if(a in i){n=i[a].line,0===r&&(r=i[a].lineStart);break}("\n"===e.charAt(a)||"\r"===e.charAt(a)&&"\n"!==e.charAt(a+1))&&(s++,0===r&&(r=a+1)),a--}var u=n+s,o=t-r;return i[t]={line:u,lineStart:r},{offset:t,line:u+1,column:o+1}}function v(e){if(!F(e))throw new Error("not a parser: "+e)}function N(e,t){return"string"==typeof e?e.charAt(t):e[t]}function w(e){if("number"!=typeof e)throw new Error("not a number: "+e)}function L(e){if("function"!=typeof e)throw new Error("not a function: "+e)}function O(e){if("string"!=typeof e)throw new Error("not a string: "+e)}var _=2,I=3,k=8,B=5*k,x=4*k,R="  ";function M(e,t){return new Array(t+1).join(e)}function z(e,t,i){var n=t-e.length;return n<=0?e:M(i,n)+e}function H(e,t,i,n){return{from:e-t>0?e-t:0,to:e+i>n?n:e+i}}function P(e,t){var i,n,s,r,o,l=t.index,D=l.offset,d=1;if(D===e.length)return"Got the end of the input";if(C(e)){var h=D-D%k,c=D-h,g=H(h,B,x+k,e.length),E=u((function(e){return u((function(e){return z(e.toString(16),2,"0")}),e)}),function(e,t){var i=e.length,n=[],s=0;if(i<=t)return[e.slice()];for(var r=0;r<i;r++)n[s]||n.push([]),n[s].push(e[r]),(r+1)%t==0&&s++;return n}(e.slice(g.from,g.to).toJSON().data,k));r=function(e){return 0===e.from&&1===e.to?{from:e.from,to:e.to}:{from:e.from/k,to:Math.floor(e.to/k)}}(g),n=h/k,i=3*c,c>=4&&(i+=1),d=2,s=u((function(e){return e.length<=4?e.join(" "):e.slice(0,4).join(" ")+"  "+e.slice(4).join(" ")}),E),(o=(8*(r.to>0?r.to-1:r.to)).toString(16).length)<2&&(o=2)}else{var p=e.split(/\r\n|[\n\r\u2028\u2029]/);i=l.column-1,n=l.line-1,r=H(n,_,I,p.length),s=p.slice(r.from,r.to),o=r.to.toString().length}var f=n-r.from;return C(e)&&(o=(8*(r.to>0?r.to-1:r.to)).toString(16).length)<2&&(o=2),a((function(t,n,s){var a,u=s===f,l=u?"> ":R;return a=C(e)?z((8*(r.from+s)).toString(16),o,"0"):z((r.from+s+1).toString(),o," "),[].concat(t,[l+a+" | "+n],u?[R+M(" ",o)+" | "+z("",i," ")+M("^",d)]:[])}),[],s).join("\n")}function G(e,t){return["\n","-- PARSING FAILED "+M("-",50),"\n\n",P(e,t),"\n\n",(i=t.expected,1===i.length?"Expected:\n\n"+i[0]:"Expected one of the following: \n\n"+i.join(", ")),"\n"].join("");var i}function V(e){return void 0!==e.flags?e.flags:[e.global?"g":"",e.ignoreCase?"i":"",e.multiline?"m":"",e.unicode?"u":"",e.sticky?"y":""].join("")}function Y(){for(var e=[].slice.call(arguments),t=e.length,i=0;i<t;i+=1)v(e[i]);return n((function(i,n){for(var s,r=new Array(t),a=0;a<t;a+=1){if(!(s=b(e[a]._(i,n),s)).status)return s;r[a]=s.value,n=s.index}return b(y(n,r),s)}))}function j(){var e=[].slice.call(arguments);if(0===e.length)throw new Error("seqMap needs at least one argument");var t=e.pop();return L(t),Y.apply(null,e).map((function(e){return t.apply(null,e)}))}function W(){var e=[].slice.call(arguments),t=e.length;if(0===t)return X("zero alternates");for(var i=0;i<t;i+=1)v(e[i]);return n((function(t,i){for(var n,s=0;s<e.length;s+=1)if((n=b(e[s]._(t,i),n)).status)return n;return n}))}function U(e,t){return K(e,t).or($([]))}function K(e,t){return v(e),v(t),j(e,t.then(e).many(),(function(e,t){return[e].concat(t)}))}function q(e){O(e);var t="'"+e+"'";return n((function(i,n){var s=n+e.length,r=i.slice(n,s);return r===e?y(s,r):S(n,t)}))}function Z(e,t){!function(e){if(!(e instanceof RegExp))throw new Error("not a regexp: "+e);for(var t=V(e),i=0;i<t.length;i++){var n=t.charAt(i);if("i"!==n&&"m"!==n&&"u"!==n&&"s"!==n)throw new Error('unsupported regexp flag "'+n+'": '+e)}}(e),arguments.length>=2?w(t):t=0;var i=function(e){return RegExp("^(?:"+e.source+")",V(e))}(e),s=""+e;return n((function(e,n){var r=i.exec(e.slice(n));if(r){if(0<=t&&t<=r.length){var a=r[0],u=r[t];return y(n+a.length,u)}return S(n,"valid match group (0 to "+r.length+") in "+s)}return S(n,s)}))}function $(e){return n((function(t,i){return y(i,e)}))}function X(e){return n((function(t,i){return S(i,e)}))}function J(e){if(F(e))return n((function(t,i){var n=e._(t,i);return n.index=i,n.value="",n}));if("string"==typeof e)return J(q(e));if(e instanceof RegExp)return J(Z(e));throw new Error("not a string, regexp, or parser: "+e)}function Q(e){return v(e),n((function(t,i){var n=e._(t,i),s=t.slice(i,n.index);return n.status?S(i,'not "'+s+'"'):y(i,null)}))}function ee(e){return L(e),n((function(t,i){var n=N(t,i);return i<t.length&&e(n)?y(i+1,n):S(i,"a character/byte matching "+e)}))}function te(e,t){arguments.length<2&&(t=e,e=void 0);var i=n((function(e,n){return i._=t()._,i._(e,n)}));return e?i.desc(e):i}function ie(){return X("fantasy-land/empty")}s.parse=function(e){if("string"!=typeof e&&!C(e))throw new Error(".parse must be called with a string or Buffer as its argument");var t,i=this.skip(ae)._(e,0);return t=i.status?{status:!0,value:i.value}:{status:!1,index:T(e,i.furthest),expected:i.expected},delete A[e],t},s.tryParse=function(e){var t=this.parse(e);if(t.status)return t.value;var i=G(e,t),n=new Error(i);throw n.type="ParsimmonError",n.result=t,n},s.assert=function(e,t){return this.chain((function(i){return e(i)?$(i):X(t)}))},s.or=function(e){return W(this,e)},s.trim=function(e){return this.wrap(e,e)},s.wrap=function(e,t){return j(e,this,t,(function(e,t){return t}))},s.thru=function(e){return e(this)},s.then=function(e){return v(e),Y(this,e).map((function(e){return e[1]}))},s.many=function(){var e=this;return n((function(t,i){for(var n=[],s=void 0;;){if(!(s=b(e._(t,i),s)).status)return b(y(i,n),s);if(i===s.index)throw new Error("infinite loop detected in .many() parser --- calling .many() on a parser which can accept zero characters is usually the cause");i=s.index,n.push(s.value)}}))},s.tieWith=function(e){return O(e),this.map((function(t){if(function(e){if(!m(e))throw new Error("not an array: "+e)}(t),t.length){O(t[0]);for(var i=t[0],n=1;n<t.length;n++)O(t[n]),i+=e+t[n];return i}return""}))},s.tie=function(){return this.tieWith("")},s.times=function(e,t){var i=this;return arguments.length<2&&(t=e),w(e),w(t),n((function(n,s){for(var r=[],a=void 0,u=void 0,o=0;o<e;o+=1){if(u=b(a=i._(n,s),u),!a.status)return u;s=a.index,r.push(a.value)}for(;o<t&&(u=b(a=i._(n,s),u),a.status);o+=1)s=a.index,r.push(a.value);return b(y(s,r),u)}))},s.result=function(e){return this.map((function(){return e}))},s.atMost=function(e){return this.times(0,e)},s.atLeast=function(e){return j(this.times(e),this.many(),(function(e,t){return e.concat(t)}))},s.map=function(e){L(e);var t=this;return n((function(i,n){var s=t._(i,n);return s.status?b(y(s.index,e(s.value)),s):s}))},s.contramap=function(e){L(e);var t=this;return n((function(i,n){var s=t.parse(e(i.slice(n)));return s.status?y(n+i.length,s.value):s}))},s.promap=function(e,t){return L(e),L(t),this.contramap(e).map(t)},s.skip=function(e){return Y(this,e).map((function(e){return e[0]}))},s.mark=function(){return j(ne,this,ne,(function(e,t,i){return{start:e,value:t,end:i}}))},s.node=function(e){return j(ne,this,ne,(function(t,i,n){return{name:e,value:i,start:t,end:n}}))},s.sepBy=function(e){return U(this,e)},s.sepBy1=function(e){return K(this,e)},s.lookahead=function(e){return this.skip(J(e))},s.notFollowedBy=function(e){return this.skip(Q(e))},s.desc=function(e){m(e)||(e=[e]);var t=this;return n((function(i,n){var s=t._(i,n);return s.status||(s.expected=e),s}))},s.fallback=function(e){return this.or($(e))},s.ap=function(e){return j(e,this,(function(e,t){return e(t)}))},s.chain=function(e){var t=this;return n((function(i,n){var s=t._(i,n);return s.status?b(e(s.value)._(i,s.index),s):s}))},s.concat=s.or,s.empty=ie,s.of=$,s["fantasy-land/ap"]=s.ap,s["fantasy-land/chain"]=s.chain,s["fantasy-land/concat"]=s.concat,s["fantasy-land/empty"]=s.empty,s["fantasy-land/of"]=s.of,s["fantasy-land/map"]=s.map;var ne=n((function(e,t){return y(t,T(e,t))})),se=n((function(e,t){return t>=e.length?S(t,"any character/byte"):y(t+1,N(e,t))})),re=n((function(e,t){return y(e.length,e.slice(t))})),ae=n((function(e,t){return t<e.length?S(t,"EOF"):y(t,null)})),ue=Z(/[0-9]/).desc("a digit"),oe=Z(/[0-9]*/).desc("optional digits"),le=Z(/[a-z]/i).desc("a letter"),De=Z(/[a-z]*/i).desc("optional letters"),de=Z(/\s*/).desc("optional whitespace"),he=Z(/\s+/).desc("whitespace"),ce=q("\r"),ge=q("\n"),Ee=q("\r\n"),pe=W(Ee,ge,ce).desc("newline"),fe=W(pe,ae);n.all=re,n.alt=W,n.any=se,n.cr=ce,n.createLanguage=function(e){var t={};for(var i in e)({}).hasOwnProperty.call(e,i)&&function(i){t[i]=te((function(){return e[i](t)}))}(i);return t},n.crlf=Ee,n.custom=function(e){return n(e(y,S))},n.digit=ue,n.digits=oe,n.empty=ie,n.end=fe,n.eof=ae,n.fail=X,n.formatError=G,n.index=ne,n.isParser=F,n.lazy=te,n.letter=le,n.letters=De,n.lf=ge,n.lookahead=J,n.makeFailure=S,n.makeSuccess=y,n.newline=pe,n.noneOf=function(e){return ee((function(t){return e.indexOf(t)<0})).desc("none of '"+e+"'")},n.notFollowedBy=Q,n.of=$,n.oneOf=function(e){for(var t=e.split(""),i=0;i<t.length;i++)t[i]="'"+t[i]+"'";return ee((function(t){return e.indexOf(t)>=0})).desc(t)},n.optWhitespace=de,n.Parser=n,n.range=function(e,t){return ee((function(i){return e<=i&&i<=t})).desc(e+"-"+t)},n.regex=Z,n.regexp=Z,n.sepBy=U,n.sepBy1=K,n.seq=Y,n.seqMap=j,n.seqObj=function(){for(var e,t={},i=0,s=(e=arguments,Array.prototype.slice.call(e)),r=s.length,a=0;a<r;a+=1){var u=s[a];if(!F(u)){if(m(u)&&2===u.length&&"string"==typeof u[0]&&F(u[1])){var o=u[0];if(Object.prototype.hasOwnProperty.call(t,o))throw new Error("seqObj: duplicate key "+o);t[o]=!0,i++;continue}throw new Error("seqObj arguments must be parsers or [string, parser] array pairs.")}}if(0===i)throw new Error("seqObj expects at least one named parser, found zero");return n((function(e,t){for(var i,n={},a=0;a<r;a+=1){var u,o;if(m(s[a])?(u=s[a][0],o=s[a][1]):(u=null,o=s[a]),!(i=b(o._(e,t),i)).status)return i;u&&(n[u]=i.value),t=i.index}return b(y(t,n),i)}))},n.string=q,n.succeed=$,n.takeWhile=function(e){return L(e),n((function(t,i){for(var n=i;n<t.length&&e(N(t,n));)n++;return y(n,t.slice(i,n))}))},n.test=ee,n.whitespace=he,n["fantasy-land/empty"]=ie,n["fantasy-land/of"]=$,n.Binary={bitSeq:d,bitSeqObj:function(e){D();var t={},i=0,n=u((function(e){if(m(e)){var n=e;if(2!==n.length)throw new Error("["+n.join(", ")+"] should be length 2, got length "+n.length);if(O(n[0]),w(n[1]),Object.prototype.hasOwnProperty.call(t,n[0]))throw new Error("duplicate key in bitSeqObj: "+n[0]);return t[n[0]]=!0,i++,n}return w(e),[null,e]}),e);if(i<1)throw new Error("bitSeqObj expects at least one named pair, got ["+e.join(", ")+"]");var s=u((function(e){return e[0]}),n);return d(u((function(e){return e[1]}),n)).map((function(e){return a((function(e,t){return null!==t[0]&&(e[t[0]]=t[1]),e}),{},u((function(t,i){return[t,e[i]]}),s))}))},byte:function(e){if(D(),w(e),e>255)throw new Error("Value specified to byte constructor ("+e+"=0x"+e.toString(16)+") is larger in value than a single byte.");var t=(e>15?"0x":"0x0")+e.toString(16);return n((function(i,n){var s=N(i,n);return s===e?y(n+1,s):S(n,t)}))},buffer:function(e){return h("buffer",e).map((function(e){return Buffer.from(e)}))},encodedString:function(e,t){return h("string",t).map((function(t){return t.toString(e)}))},uintBE:g,uint8BE:g(1),uint16BE:g(2),uint32BE:g(4),uintLE:E,uint8LE:E(1),uint16LE:E(2),uint32LE:E(4),intBE:p,int8BE:p(1),int16BE:p(2),int32BE:p(4),intLE:f,int8LE:f(1),int16LE:f(2),int32LE:f(4),floatBE:h("floatBE",4).map((function(e){return e.readFloatBE(0)})),floatLE:h("floatLE",4).map((function(e){return e.readFloatLE(0)})),doubleBE:h("doubleBE",8).map((function(e){return e.readDoubleBE(0)})),doubleLE:h("doubleLE",8).map((function(e){return e.readDoubleLE(0)}))},e.exports=n}]);var is=ts.exports;function ns(e){return null==e?e:e.shiftToAll().normalize()}function ss(e){return e.includes("/")&&(e=e.substring(e.lastIndexOf("/")+1)),e.endsWith(".md")&&(e=e.substring(0,e.length-3)),e}is.alt(is.regex(new RegExp(/[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26D3\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26F9(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC3\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC08\uDC26](?:\u200D\u2B1B)?|[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE88\uDE90-\uDEBD\uDEBF-\uDEC2\uDECE-\uDEDB\uDEE0-\uDEE8]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g,"")),is.regex(/[0-9\p{Letter}_-]+/u).map((e=>e.toLocaleLowerCase())),is.whitespace.map((e=>"-")),is.any.map((e=>""))).many().map((e=>e.join("")));const rs=is.alt(is.regex(new RegExp(/[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26D3\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26F9(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC3\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC08\uDC26](?:\u200D\u2B1B)?|[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE88\uDE90-\uDEBD\uDEBF-\uDEC2\uDECE-\uDEDB\uDEE0-\uDEE8]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g,"")),is.regex(/[0-9\p{Letter}_-]+/u),is.whitespace.map((e=>" ")),is.any.map((e=>" "))).many().map((e=>e.join("").split(/\s+/).join(" ").trim()));function as(e){return rs.tryParse(e)}var us,os,ls,Ds,ds;!function(e){function t(e){return a(e)?{type:"null",value:e}:n(e)?{type:"number",value:e}:i(e)?{type:"string",value:e}:o(e)?{type:"boolean",value:e}:r(e)?{type:"duration",value:e}:s(e)?{type:"date",value:e}:D(e)?{type:"widget",value:e}:u(e)?{type:"array",value:e}:l(e)?{type:"link",value:e}:c(e)?{type:"function",value:e}:d(e)?{type:"html",value:e}:h(e)?{type:"object",value:e}:void 0}function i(e){return"string"==typeof e}function n(e){return"number"==typeof e}function s(e){return e instanceof Zn}function r(e){return e instanceof Wi}function a(e){return null==e}function u(e){return Array.isArray(e)}function o(e){return"boolean"==typeof e}function l(e){return e instanceof hs}function D(e){return e instanceof cs}function d(e){return"undefined"!=typeof HTMLElement&&e instanceof HTMLElement}function h(e){return!("object"!=typeof e||d(e)||D(e)||u(e)||r(e)||s(e)||l(e)||void 0===e||a(e))}function c(e){return"function"==typeof e}e.toString=function e(i,n=Xn,s=!1){let r=t(i);if(!r)return n.renderNullAs;switch(r.type){case"null":return n.renderNullAs;case"string":return r.value;case"number":case"boolean":return""+r.value;case"html":return r.value.outerHTML;case"widget":case"link":return r.value.markdown();case"function":return"<function>";case"array":let t="";return s&&(t+="["),t+=r.value.map((t=>e(t,n,!0))).join(", "),s&&(t+="]"),t;case"object":return"{ "+Object.entries(r.value).map((t=>t[0]+": "+e(t[1],n,!0))).join(", ")+" }";case"date":return 0==r.value.second&&0==r.value.hour&&0==r.value.minute?r.value.toFormat(n.defaultDateFormat):r.value.toFormat(n.defaultDateTimeFormat);case"duration":return a=ns(a=r.value),(a=Wi.fromObject(Object.fromEntries(Object.entries(a.toObject()).filter((([,e])=>0!=e))))).toHuman()}var a},e.wrapValue=t,e.mapLeaves=function e(t,i){if(h(t)){let n={};for(let[s,r]of Object.entries(t))n[s]=e(r,i);return n}if(u(t)){let n=[];for(let s of t)n.push(e(s,i));return n}return i(t)},e.compareValue=function e(i,n,s){if(void 0===i&&(i=null),void 0===n&&(n=null),null===i&&null===n)return 0;if(null===i)return-1;if(null===n)return 1;let r=t(i),a=t(n);if(void 0===r&&void 0===a)return 0;if(void 0===r)return-1;if(void 0===a)return 1;if(r.type!=a.type)return r.type.localeCompare(a.type);if(r.value===a.value)return 0;switch(r.type){case"string":return r.value.localeCompare(a.value);case"number":return r.value<a.value?-1:r.value==a.value?0:1;case"null":case"widget":case"html":case"function":return 0;case"boolean":return r.value==a.value?0:r.value?1:-1;case"link":let t=r.value,i=a.value,n=s??(e=>e),u=n(t.path).localeCompare(n(i.path));if(0!=u)return u;let o=t.type.localeCompare(i.type);return 0!=o?o:t.subpath&&!i.subpath?1:!t.subpath&&i.subpath?-1:t.subpath||i.subpath?(t.subpath??"").localeCompare(i.subpath??""):0;case"date":case"duration":return r.value<a.value?-1:r.value.equals(a.value)?0:1;case"array":let l=r.value,D=a.value;for(let t=0;t<Math.min(l.length,D.length);t++){let i=e(l[t],D[t]);if(0!=i)return i}return l.length-D.length;case"object":let d=r.value,h=a.value,c=Array.from(Object.keys(d)),g=Array.from(Object.keys(h));c.sort(),g.sort();let E=e(c,g);if(0!=E)return E;for(let t of c){let i=e(d[t],h[t]);if(0!=i)return i}return 0}},e.typeOf=function(e){return t(e)?.type},e.isTruthy=function(e){let i=t(e);if(!i)return!1;switch(i.type){case"number":return 0!=i.value;case"string":case"array":return i.value.length>0;case"boolean":return i.value;case"link":return!!i.value.path;case"date":return 0!=i.value.toMillis();case"duration":return 0!=i.value.as("seconds");case"object":return Object.keys(i.value).length>0;case"null":return!1;case"html":case"widget":case"function":return!0}},e.deepCopy=function t(i){if(null==i)return i;if(e.isArray(i))return[].concat(i.map((e=>t(e))));if(e.isObject(i)){let e={};for(let[n,s]of Object.entries(i))e[n]=t(s);return e}return i},e.isString=i,e.isNumber=n,e.isDate=s,e.isDuration=r,e.isNull=a,e.isArray=u,e.isBoolean=o,e.isLink=l,e.isWidget=D,e.isHtml=d,e.isObject=h,e.isFunction=c}(us||(us={})),function(e){function t(e){return us.isObject(e)&&2==Object.keys(e).length&&"key"in e&&"rows"in e}function i(e){for(let i of e)if(!t(i))return!1;return!0}e.isElementGroup=t,e.isGrouping=i,e.count=function e(t){if(i(t)){let i=0;for(let n of t)i+=e(n.rows);return i}return t.length}}(os||(os={}));class hs{path;display;subpath;embed;type;static file(e,t=!1,i){return new hs({path:e,embed:t,display:i,subpath:void 0,type:"file"})}static infer(e,t=!1,i){if(e.includes("#^")){let n=e.split("#^");return hs.block(n[0],n[1],t,i)}if(e.includes("#")){let n=e.split("#");return hs.header(n[0],n[1],t,i)}return hs.file(e,t,i)}static header(e,t,i,n){return new hs({path:e,embed:i,display:n,subpath:as(t),type:"header"})}static block(e,t,i,n){return new hs({path:e,embed:i,display:n,subpath:t,type:"block"})}static fromObject(e){return new hs(e)}constructor(e){Object.assign(this,e)}equals(e){return null!=e&&null!=e&&this.path==e.path&&this.type==e.type&&this.subpath==e.subpath}toString(){return this.markdown()}toObject(){return{path:this.path,type:this.type,subpath:this.subpath,display:this.display,embed:this.embed}}withPath(e){return new hs(Object.assign({},this,{path:e}))}withDisplay(e){return new hs(Object.assign({},this,{display:e}))}withHeader(e){return hs.header(this.path,e,this.embed,this.display)}toFile(){return hs.file(this.path,this.embed,this.display)}toEmbed(){if(this.embed)return this;{let e=new hs(this);return e.embed=!0,e}}fromEmbed(){if(this.embed){let e=new hs(this);return e.embed=!1,e}return this}markdown(){let e=(this.embed?"!":"")+"[["+this.obsidianLink();return this.display?e+="|"+this.display:(e+="|"+ss(this.path),"header"!=this.type&&"block"!=this.type||(e+=" > "+this.subpath)),e+="]]",e}obsidianLink(){const e=this.path.replaceAll("|","\\|");return"header"==this.type?e+"#"+this.subpath?.replaceAll("|","\\|"):"block"==this.type?e+"#^"+this.subpath?.replaceAll("|","\\|"):e}fileName(){return ss(this.path).replace(".md","")}}class cs{$widget;constructor(e){this.$widget=e}}class gs extends cs{key;value;constructor(e,t){super("dataview:list-pair"),this.key=e,this.value=t}markdown(){return`${us.toString(this.key)}: ${us.toString(this.value)}`}}class Es extends cs{url;display;constructor(e,t){super("dataview:external-link"),this.url=e,this.display=t}markdown(){return`[${this.display??this.url}](${this.url})`}}!function(e){function t(e){return"dataview:list-pair"===e.$widget}function i(e){return"dataview:external-link"===e.$widget}e.listPair=function(e,t){return new gs(e,t)},e.externalLink=function(e,t){return new Es(e,t)},e.isListPair=t,e.isExternalLink=i,e.isBuiltin=function(e){return t(e)||i(e)}}(ls||(ls={})),function(e){e.variable=function(e){return{type:"variable",name:e}},e.literal=function(e){return{type:"literal",value:e}},e.binaryOp=function(e,t,i){return{type:"binaryop",left:e,op:t,right:i}},e.index=function(e,t){return{type:"index",object:e,index:t}},e.indexVariable=function(t){let i=t.split("."),n=e.variable(i[0]);for(let t=1;t<i.length;t++)n=e.index(n,e.literal(i[t]));return n},e.lambda=function(e,t){return{type:"lambda",arguments:e,value:t}},e.func=function(e,t){return{type:"function",func:e,arguments:t}},e.list=function(e){return{type:"list",values:e}},e.object=function(e){return{type:"object",values:e}},e.negate=function(e){return{type:"negated",child:e}},e.isCompareOp=function(e){return"<="==e||"<"==e||">"==e||">="==e||"!="==e||"="==e},e.NULL=e.literal(null)}(Ds||(Ds={})),function(e){e.tag=function(e){return{type:"tag",tag:e}},e.csv=function(e){return{type:"csv",path:e}},e.folder=function(e){return{type:"folder",folder:e}},e.link=function(e,t){return{type:"link",file:e,direction:t?"incoming":"outgoing"}},e.binaryOp=function(e,t,i){return{type:"binaryop",left:e,op:t,right:i}},e.and=function(e,t){return{type:"binaryop",left:e,op:"&",right:t}},e.or=function(e,t){return{type:"binaryop",left:e,op:"|",right:t}},e.negate=function(e){return{type:"negate",child:e}},e.empty=function(){return{type:"empty"}}}(ds||(ds={}));const ps=new RegExp(/[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26D3\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26F9(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC3\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC08\uDC26](?:\u200D\u2B1B)?|[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE88\uDE90-\uDEBD\uDEBF-\uDEC2\uDECE-\uDEDB\uDEE0-\uDEE8]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g,""),fs={year:Wi.fromObject({years:1}),years:Wi.fromObject({years:1}),yr:Wi.fromObject({years:1}),yrs:Wi.fromObject({years:1}),month:Wi.fromObject({months:1}),months:Wi.fromObject({months:1}),mo:Wi.fromObject({months:1}),mos:Wi.fromObject({months:1}),week:Wi.fromObject({weeks:1}),weeks:Wi.fromObject({weeks:1}),wk:Wi.fromObject({weeks:1}),wks:Wi.fromObject({weeks:1}),w:Wi.fromObject({weeks:1}),day:Wi.fromObject({days:1}),days:Wi.fromObject({days:1}),d:Wi.fromObject({days:1}),hour:Wi.fromObject({hours:1}),hours:Wi.fromObject({hours:1}),hr:Wi.fromObject({hours:1}),hrs:Wi.fromObject({hours:1}),h:Wi.fromObject({hours:1}),minute:Wi.fromObject({minutes:1}),minutes:Wi.fromObject({minutes:1}),min:Wi.fromObject({minutes:1}),mins:Wi.fromObject({minutes:1}),m:Wi.fromObject({minutes:1}),second:Wi.fromObject({seconds:1}),seconds:Wi.fromObject({seconds:1}),sec:Wi.fromObject({seconds:1}),secs:Wi.fromObject({seconds:1}),s:Wi.fromObject({seconds:1})},Fs={now:()=>Zn.local(),today:()=>Zn.local().startOf("day"),yesterday:()=>Zn.local().startOf("day").minus(Wi.fromObject({days:1})),tomorrow:()=>Zn.local().startOf("day").plus(Wi.fromObject({days:1})),sow:()=>Zn.local().startOf("week"),"start-of-week":()=>Zn.local().startOf("week"),eow:()=>Zn.local().endOf("week"),"end-of-week":()=>Zn.local().endOf("week"),soy:()=>Zn.local().startOf("year"),"start-of-year":()=>Zn.local().startOf("year"),eoy:()=>Zn.local().endOf("year"),"end-of-year":()=>Zn.local().endOf("year"),som:()=>Zn.local().startOf("month"),"start-of-month":()=>Zn.local().startOf("month"),eom:()=>Zn.local().endOf("month"),"end-of-month":()=>Zn.local().endOf("month")},ms=["FROM","WHERE","LIMIT","GROUP","FLATTEN"];function Cs(e,t,i){return is.seqMap(e,is.seq(is.optWhitespace,t,is.optWhitespace,e).many(),((e,t)=>{if(0==t.length)return e;let n=i(e,t[0][1],t[0][3]);for(let e=1;e<t.length;e++)n=i(n,t[e][1],t[e][3]);return n}))}const ys=is.createLanguage({number:e=>is.regexp(/-?[0-9]+(\.[0-9]+)?/).map((e=>Number.parseFloat(e))).desc("number"),string:e=>is.string('"').then(is.alt(e.escapeCharacter,is.noneOf('"\\')).atLeast(0).map((e=>e.join("")))).skip(is.string('"')).desc("string"),escapeCharacter:e=>is.string("\\").then(is.any).map((e=>'"'===e?'"':"\\"===e?"\\":"\\"+e)),bool:e=>is.regexp(/true|false|True|False/).map((e=>"true"==e.toLowerCase())).desc("boolean ('true' or 'false')"),tag:e=>is.seqMap(is.string("#"),is.alt(is.regexp(/[^\u2000-\u206F\u2E00-\u2E7F'!"#$%&()*+,.:;<=>?@^`{|}~\[\]\\\s]/).desc("text")).many(),((e,t)=>e+t.join(""))).desc("tag ('#hello/stuff')"),identifier:e=>is.seqMap(is.alt(is.regexp(/\p{Letter}/u),is.regexp(ps).desc("text")),is.alt(is.regexp(/[0-9\p{Letter}_-]/u),is.regexp(ps).desc("text")).many(),((e,t)=>e+t.join(""))).desc("variable identifier"),link:e=>is.regexp(/\[\[([^\[\]]*?)\]\]/u,1).map((e=>function(e){let[t,i]=function(e){let t=-1;for(;(t=e.indexOf("|",t+1))>=0;)if(!(t>0&&"\\"==e[t-1]))return[e.substring(0,t).replace(/\\\|/g,"|"),e.substring(t+1)];return[e.replace(/\\\|/g,"|"),void 0]}(e);return hs.infer(t,!1,i)}(e))).desc("file link"),embedLink:e=>is.seqMap(is.string("!").atMost(1),e.link,((e,t)=>(e.length>0&&(t.embed=!0),t))).desc("file link"),binaryPlusMinus:e=>is.regexp(/\+|-/).map((e=>e)).desc("'+' or '-'"),binaryMulDiv:e=>is.regexp(/\*|\/|%/).map((e=>e)).desc("'*' or '/' or '%'"),binaryCompareOp:e=>is.regexp(/>=|<=|!=|>|<|=/).map((e=>e)).desc("'>=' or '<=' or '!=' or '=' or '>' or '<'"),binaryBooleanOp:e=>is.regexp(/and|or|&|\|/i).map((e=>"and"==e.toLowerCase()?"&":"or"==e.toLowerCase()?"|":e)).desc("'and' or 'or'"),rootDate:e=>is.seqMap(is.regexp(/\d{4}/),is.string("-"),is.regexp(/\d{2}/),((e,t,i)=>Zn.fromObject({year:Number.parseInt(e),month:Number.parseInt(i)}))).desc("date in format YYYY-MM[-DDTHH-MM-SS.MS]"),dateShorthand:e=>is.alt(...Object.keys(Fs).sort(((e,t)=>t.length-e.length)).map(is.string)),date:e=>function(e,...t){return is.custom(((i,n)=>(i,n)=>{let s=e._(i,n);if(!s.status)return s;for(let e of t){let t=e(s.value)._(i,s.index);if(!t.status)return s;s=t}return s}))}(e.rootDate,(e=>is.seqMap(is.string("-"),is.regexp(/\d{2}/),((t,i)=>e.set({day:Number.parseInt(i)})))),(e=>is.seqMap(is.string("T"),is.regexp(/\d{2}/),((t,i)=>e.set({hour:Number.parseInt(i)})))),(e=>is.seqMap(is.string(":"),is.regexp(/\d{2}/),((t,i)=>e.set({minute:Number.parseInt(i)})))),(e=>is.seqMap(is.string(":"),is.regexp(/\d{2}/),((t,i)=>e.set({second:Number.parseInt(i)})))),(e=>is.alt(is.seqMap(is.string("."),is.regexp(/\d{3}/),((t,i)=>e.set({millisecond:Number.parseInt(i)}))),is.succeed(e))),(e=>is.alt(is.seqMap(is.string("+").or(is.string("-")),is.regexp(/\d{1,2}(:\d{2})?/),((t,i)=>e.setZone("UTC"+t+i,{keepLocalTime:!0}))),is.seqMap(is.string("Z"),(()=>e.setZone("utc",{keepLocalTime:!0}))),is.seqMap(is.string("["),is.regexp(/[0-9A-Za-z+-\/]+/u),is.string("]"),((t,i,n)=>e.setZone(i,{keepLocalTime:!0})))))).assert((e=>e.isValid),"valid date").desc("date in format YYYY-MM[-DDTHH-MM-SS.MS]"),datePlus:e=>is.alt(e.dateShorthand.map((e=>Fs[e]())),e.date).desc("date in format YYYY-MM[-DDTHH-MM-SS.MS] or in shorthand"),durationType:e=>is.alt(...Object.keys(fs).sort(((e,t)=>t.length-e.length)).map(is.string)),duration:e=>is.seqMap(e.number,is.optWhitespace,e.durationType,((e,t,i)=>fs[i].mapUnits((t=>t*e)))).sepBy1(is.string(",").trim(is.optWhitespace).or(is.optWhitespace)).map((e=>e.reduce(((e,t)=>e.plus(t))))).desc("duration like 4hr2min"),rawNull:e=>is.string("null"),tagSource:e=>e.tag.map((e=>ds.tag(e))),csvSource:e=>is.seqMap(is.string("csv(").skip(is.optWhitespace),e.string,is.string(")"),((e,t,i)=>ds.csv(t))),linkIncomingSource:e=>e.link.map((e=>ds.link(e.path,!0))),linkOutgoingSource:e=>is.seqMap(is.string("outgoing(").skip(is.optWhitespace),e.link,is.string(")"),((e,t,i)=>ds.link(t.path,!1))),folderSource:e=>e.string.map((e=>ds.folder(e))),parensSource:e=>is.seqMap(is.string("("),is.optWhitespace,e.source,is.optWhitespace,is.string(")"),((e,t,i,n,s)=>i)),negateSource:e=>is.seqMap(is.alt(is.string("-"),is.string("!")),e.atomSource,((e,t)=>ds.negate(t))),atomSource:e=>is.alt(e.parensSource,e.negateSource,e.linkOutgoingSource,e.linkIncomingSource,e.folderSource,e.tagSource,e.csvSource),binaryOpSource:e=>Cs(e.atomSource,e.binaryBooleanOp.map((e=>e)),ds.binaryOp),source:e=>e.binaryOpSource,variableField:e=>e.identifier.chain((e=>ms.includes(e.toUpperCase())?is.fail("Variable fields cannot be a keyword ("+ms.join(" or ")+")"):is.succeed(Ds.variable(e)))).desc("variable"),numberField:e=>e.number.map((e=>Ds.literal(e))).desc("number"),stringField:e=>e.string.map((e=>Ds.literal(e))).desc("string"),boolField:e=>e.bool.map((e=>Ds.literal(e))).desc("boolean"),dateField:e=>is.seqMap(is.string("date("),is.optWhitespace,e.datePlus,is.optWhitespace,is.string(")"),((e,t,i,n,s)=>Ds.literal(i))).desc("date"),durationField:e=>is.seqMap(is.string("dur("),is.optWhitespace,e.duration,is.optWhitespace,is.string(")"),((e,t,i,n,s)=>Ds.literal(i))).desc("duration"),nullField:e=>e.rawNull.map((e=>Ds.NULL)),linkField:e=>e.link.map((e=>Ds.literal(e))),listField:e=>e.field.sepBy(is.string(",").trim(is.optWhitespace)).wrap(is.string("[").skip(is.optWhitespace),is.optWhitespace.then(is.string("]"))).map((e=>Ds.list(e))).desc("list ('[1, 2, 3]')"),objectField:e=>is.seqMap(e.identifier.or(e.string),is.string(":").trim(is.optWhitespace),e.field,((e,t,i)=>({name:e,value:i}))).sepBy(is.string(",").trim(is.optWhitespace)).wrap(is.string("{").skip(is.optWhitespace),is.optWhitespace.then(is.string("}"))).map((e=>{let t={};for(let i of e)t[i.name]=i.value;return Ds.object(t)})).desc("object ('{ a: 1, b: 2 }')"),atomInlineField:e=>is.alt(e.date,e.duration.map((e=>ns(e))),e.string,e.tag,e.embedLink,e.bool,e.number,e.rawNull),inlineFieldList:e=>e.atomInlineField.sepBy(is.string(",").trim(is.optWhitespace).lookahead(e.atomInlineField)),inlineField:e=>is.alt(is.seqMap(e.atomInlineField,is.string(",").trim(is.optWhitespace),e.inlineFieldList,((e,t,i)=>[e].concat(i))),e.atomInlineField),atomField:e=>is.alt(e.embedLink.map((e=>Ds.literal(e))),e.negatedField,e.linkField,e.listField,e.objectField,e.lambdaField,e.parensField,e.boolField,e.numberField,e.stringField,e.dateField,e.durationField,e.nullField,e.variableField),indexField:e=>is.seqMap(e.atomField,is.alt(e.dotPostfix,e.indexPostfix,e.functionPostfix).many(),((e,t)=>{let i=e;for(let e of t)switch(e.type){case"dot":i=Ds.index(i,Ds.literal(e.field));break;case"index":i=Ds.index(i,e.field);break;case"function":i=Ds.func(i,e.fields)}return i})),negatedField:e=>is.seqMap(is.string("!"),e.indexField,((e,t)=>Ds.negate(t))).desc("negated field"),parensField:e=>is.seqMap(is.string("("),is.optWhitespace,e.field,is.optWhitespace,is.string(")"),((e,t,i,n,s)=>i)),lambdaField:e=>is.seqMap(e.identifier.sepBy(is.string(",").trim(is.optWhitespace)).wrap(is.string("(").trim(is.optWhitespace),is.string(")").trim(is.optWhitespace)),is.string("=>").trim(is.optWhitespace),e.field,((e,t,i)=>({type:"lambda",arguments:e,value:i}))),dotPostfix:e=>is.seqMap(is.string("."),e.identifier,((e,t)=>({type:"dot",field:t}))),indexPostfix:e=>is.seqMap(is.string("["),is.optWhitespace,e.field,is.optWhitespace,is.string("]"),((e,t,i,n,s)=>({type:"index",field:i}))),functionPostfix:e=>is.seqMap(is.string("("),is.optWhitespace,e.field.sepBy(is.string(",").trim(is.optWhitespace)),is.optWhitespace,is.string(")"),((e,t,i,n,s)=>({type:"function",fields:i}))),binaryMulDivField:e=>Cs(e.indexField,e.binaryMulDiv,Ds.binaryOp),binaryPlusMinusField:e=>Cs(e.binaryMulDivField,e.binaryPlusMinus,Ds.binaryOp),binaryCompareField:e=>Cs(e.binaryPlusMinusField,e.binaryCompareOp,Ds.binaryOp),binaryBooleanField:e=>Cs(e.binaryCompareField,e.binaryBooleanOp,Ds.binaryOp),binaryOpField:e=>e.binaryBooleanField,field:e=>e.binaryOpField});var Ss;function bs(e,t){return is.eof.map(e).or(is.whitespace.then(t))}!function(e){e.named=function(e,t){return{name:e,field:t}},e.sortBy=function(e,t){return{field:e,direction:t}}}(Ss||(Ss={}));const As=is.createLanguage({queryType:e=>is.alt(is.regexp(/TABLE|LIST|TASK|CALENDAR/i)).map((e=>e.toLowerCase())).desc("query type ('TABLE', 'LIST', 'TASK', or 'CALENDAR')"),explicitNamedField:e=>is.seqMap(ys.field.skip(is.whitespace),is.regexp(/AS/i).skip(is.whitespace),ys.identifier.or(ys.string),((e,t,i)=>Ss.named(i,e))),comment:()=>is.Parser(((e,t)=>{let i=e.substring(t);if(!i.startsWith("//"))return is.makeFailure(t,"Not a comment");i=i.split("\n")[0];let n=i.substring(2).trim();return is.makeSuccess(t+i.length,n)})),namedField:e=>{return is.alt(e.explicitNamedField,(t=ys.field,is.custom(((e,i)=>(e,i)=>{let n=t._(e,i);return n.status?Object.assign({},n,{value:[n.value,e.substring(i,n.index)]}):n}))).map((([e,t])=>Ss.named(function(e){return e.split(/[\r\n]+/).map((e=>e.trim())).join("")}(t),e))));var t},sortField:e=>is.seqMap(ys.field.skip(is.optWhitespace),is.regexp(/ASCENDING|DESCENDING|ASC|DESC/i).atMost(1),((e,t)=>{let i=0==t.length?"ascending":t[0].toLowerCase();return"desc"==i&&(i="descending"),"asc"==i&&(i="ascending"),{field:e,direction:i}})),headerClause:e=>e.queryType.chain((t=>{switch(t){case"table":return bs((()=>({type:t,fields:[],showId:!0})),is.seqMap(is.regexp(/WITHOUT\s+ID/i).skip(is.optWhitespace).atMost(1),is.sepBy(e.namedField,is.string(",").trim(is.optWhitespace)),((e,i)=>({type:t,fields:i,showId:0==e.length}))));case"list":return bs((()=>({type:t,format:void 0,showId:!0})),is.seqMap(is.regexp(/WITHOUT\s+ID/i).skip(is.optWhitespace).atMost(1),ys.field.atMost(1),((e,i)=>({type:t,format:1==i.length?i[0]:void 0,showId:0==e.length}))));case"task":return is.succeed({type:t});case"calendar":return is.whitespace.then(is.seqMap(e.namedField,(e=>({type:t,showId:!0,field:e}))));default:return is.fail(`Unrecognized query type '${t}'`)}})).desc("TABLE or LIST or TASK or CALENDAR"),fromClause:e=>is.seqMap(is.regexp(/FROM/i),is.whitespace,ys.source,((e,t,i)=>i)),whereClause:e=>is.seqMap(is.regexp(/WHERE/i),is.whitespace,ys.field,((e,t,i)=>({type:"where",clause:i}))).desc("WHERE <expression>"),sortByClause:e=>is.seqMap(is.regexp(/SORT/i),is.whitespace,e.sortField.sepBy1(is.string(",").trim(is.optWhitespace)),((e,t,i)=>({type:"sort",fields:i}))).desc("SORT field [ASC/DESC]"),limitClause:e=>is.seqMap(is.regexp(/LIMIT/i),is.whitespace,ys.field,((e,t,i)=>({type:"limit",amount:i}))).desc("LIMIT <value>"),flattenClause:e=>is.seqMap(is.regexp(/FLATTEN/i).skip(is.whitespace),e.namedField,((e,t)=>({type:"flatten",field:t}))).desc("FLATTEN <value> [AS <name>]"),groupByClause:e=>is.seqMap(is.regexp(/GROUP BY/i).skip(is.whitespace),e.namedField,((e,t)=>({type:"group",field:t}))).desc("GROUP BY <value> [AS <name>]"),clause:e=>is.alt(e.fromClause,e.whereClause,e.sortByClause,e.limitClause,e.groupByClause,e.flattenClause),query:e=>is.seqMap(e.headerClause.trim(Ts),e.fromClause.trim(Ts).atMost(1),e.clause.trim(Ts).many(),((e,t,i)=>({header:e,source:0==t.length?ds.folder(""):t[0],operations:i,settings:Xn})))}),Ts=is.alt(is.whitespace,As.comment).many().map((e=>e.join("")));J.DATE_SHORTHANDS=Fs,J.DURATION_TYPES=fs,J.EXPRESSION=ys,J.KEYWORDS=ms,J.QUERY_LANGUAGE=As;var vs=J.getAPI=e=>e?e.plugins.plugins.dataview?.api:window.DataviewAPI;J.isPluginEnabled=e=>e.plugins.enabledPlugins.has("dataview"),J.parseField=function(e){try{return es.success(ys.field.tryParse(e))}catch(e){return es.failure(""+e)}};const Ns=`---\n\nexcalidraw-plugin: parsed\nexcalidraw-default-mode: view\nexcalidraw-export-dark: false\nexcalidraw-export-transparent: false\nexcalidraw-linkbutton-opacity: 0.3\nexcalidraw-onload-script: "app.plugins.plugins[${String.fromCharCode(96)}excalibrain${String.fromCharCode(96)}].start(ea.targetView.leaf);"\n\ntags: [excalidraw]\n\n---\n\n# Text Elements\nOpen a document in another pane and click it to get started.\n\nFor the best experience enable 'Open in adjacent pane'\nin Excalidraw settings under 'Links and Transclusion'. ^4mylk7KK\n\n%%\n# Drawing\n${String.fromCharCode(96,96,96)}json\n{\n\t"type": "excalidraw",\n\t"version": 2,\n\t"source": "https://excalidraw.com",\n\t"elements": [\n\t\t{\n\t\t\t"type": "text",\n\t\t\t"version": 1,\n\t\t\t"versionNonce": 423577018,\n\t\t\t"isDeleted": false,\n\t\t\t"id": "4mylk7KK",\n\t\t\t"fillStyle": "hachure",\n\t\t\t"strokeWidth": 1,\n\t\t\t"strokeStyle": "solid",\n\t\t\t"roughness": 1,\n\t\t\t"opacity": 100,\n\t\t\t"angle": 0,\n\t\t\t"x": 0,\n\t\t\t"y": 0,\n\t\t\t"strokeColor": "white",\n\t\t\t"backgroundColor": "transparent",\n\t\t\t"width": 703,\n\t\t\t"height": 96,\n\t\t\t"seed": 4429,\n\t\t\t"groupIds": [],\n\t\t\t"strokeSharpness": "sharp",\n\t\t\t"boundElements": [],\n\t\t\t"updated": 1650784785611,\n\t\t\t"link": null,\n\t\t\t"locked": false,\n\t\t\t"fontSize": 20,\n\t\t\t"fontFamily": 3,\n\t\t\t"text": "Open a document in another pane and click it to get started.\\n\\nFor the best experience enable 'Open in adjacent pane'\\nin Excalidraw settings under 'Links and Transclusion'.",\n\t\t\t"rawText": "Open a document in another pane and click it to get started.\\n\\nFor the best experience enable 'Open in adjacent pane'\\nin Excalidraw settings under 'Links and Transclusion'.",\n\t\t\t"baseline": 91,\n\t\t\t"textAlign": "center",\n\t\t\t"verticalAlign": "top",\n\t\t\t"containerId": null,\n\t\t\t"originalText": "Open a document in another pane and click it to get started.\\n\\nFor the best experience enable 'Open in adjacent pane'\\nin Excalidraw settings under 'Links and Transclusion'."\n\t\t}\n\t],\n\t"appState": {\n\t\t"theme": "dark",\n\t\t"viewBackgroundColor": "hsl(208, 80%, 23%)",\n\t\t"currentItemStrokeColor": "#000000",\n\t\t"currentItemBackgroundColor": "transparent",\n\t\t"currentItemFillStyle": "hachure",\n\t\t"currentItemStrokeWidth": 2,\n\t\t"currentItemStrokeStyle": "solid",\n\t\t"currentItemRoughness": 1,\n\t\t"currentItemOpacity": 100,\n\t\t"currentItemFontFamily": 1,\n\t\t"currentItemFontSize": 16,\n\t\t"currentItemTextAlign": "left",\n\t\t"currentItemStrokeSharpness": "sharp",\n\t\t"currentItemStartArrowhead": null,\n\t\t"currentItemEndArrowhead": "arrow",\n\t\t"currentItemLinearStrokeSharpness": "round",\n\t\t"gridSize": null,\n\t\t"colorPalette": {}\n\t},\n\t"files": {}\n}\n${String.fromCharCode(96,96,96)}\n%%\n`;class ws{constructor(e){this.nodes=[],this.renderedNodes=[],this.spec=e}layout(e=this.spec.columns){const t=this.nodes.sort(((e,t)=>e.title.toLowerCase()<t.title.toLowerCase()?-1:1)),i=t.length;if(0===i)return;const n=Math.ceil(i/e);this.renderedNodes=Array(n).fill(null).map(((s,r)=>{return r+1<n||i%e==0?Array(e).fill(null).map(((i,n)=>t[r*e+n])):(a=i%e,e%2?(t=>{const i=[];let n=0;for(n=e/2;n>t[0];n--)i.push(null);for(n=0;n<t[0];n++)i.push(n+1);for(n=0;n<e/2;n++)i.push(n<t[1]?t[0]+n+1:null);return i})(a%2?[(a+1)/2,(a-1)/2]:[a/2,a/2]):(e=>{const t=[];let i=1,n=!0;return e.map((e=>Math.floor(e))).forEach((e=>{for(let s=0;s<e;s++)t.push(n?null:i++);n=!n})),t})(a%2?[(e-a)/2,a,(e-a)/2]:[(e-a)/2,a/2,1,a/2,(e-a)/2])).map((i=>i?t[r*e+i-1]:null));var a}))}async render(){this.layout();const e=this.renderedNodes.length*this.spec.rowHeight,t=null===this.spec.top&&null===this.spec.bottom?this.spec.origoY-e/2:null!==this.spec.top?this.spec.origoY-e/2<this.spec.top?this.spec.top:this.spec.origoY-e/2:this.spec.origoY+e/2>this.spec.bottom?this.spec.bottom-e:this.spec.origoY-e/2,i=this.spec.origoX-(1===this.spec.columns?0:(this.spec.columns-1)/2*this.spec.columnWidth),n=t;for(const[e,t]of this.renderedNodes.entries())for(const[s,r]of t.entries())r&&(r.setCenter({x:i+s*this.spec.columnWidth,y:n+e*this.spec.rowHeight}),await r.render())}}class Os{constructor(e){this.plugin=e,this.links=new Map,this.reverseLinks=new Set}addLink(e,n,s,r,a,u,o,l){const D=e.page.path+"|:?:|"+n.page.path;if(this.links.has(D)||this.reverseLinks.has(D))return;const d=n.page.path+"|:?:|"+e.page.path,h=new Y(u===(l.inverseArrowDirection?i.TO:i.FROM)?n:e,u===(l.inverseArrowDirection?i.TO:i.FROM)?e:n,u===(l.inverseArrowDirection?i.TO:i.FROM)?s===t.LEFT||s===t.RIGHT?s===t.LEFT?t.LEFT:t.RIGHT:s===t.CHILD?t.PARENT:t.CHILD:s,r,a,o,l,this.plugin);this.links.set(D,h),this.reverseLinks.add(d)}render(e){this.links.forEach((t=>t.render(e.some((e=>{var i;return null===(i=t.hierarchyDefinition)||void 0===i?void 0:i.includes(e)}))||t.isInferred&&e.includes("inferred-link"))))}}class _s{constructor({plugin:e,getVal:t,setVal:i,isEnabled:n,wrapper:s,options:r,updateIndex:a,shouldRerenderOnToggle:u}){var o;void 0===u&&(u=!0),this.getVal=t,this.isEnabled=n,this.button=s.createEl("button",{cls:"excalibrain-button"});const l=e=>{var t,i;return"string"==typeof r.icon?r.icon:e?null===(t=r.icon)||void 0===t?void 0:t.on:null===(i=r.icon)||void 0===i?void 0:i.off};r.icon?this.button.innerHTML=l(t()):this.button.createSpan({text:null!==(o=r.display)&&void 0!==o?o:""}),this.button.ariaLabel=r.tooltip,this.updateButton(),this.button.onclick=()=>{var n;i(!t())&&e.saveSettings(),this.updateButton(),r.icon&&(this.button.innerHTML=l(t())),u&&(null===(n=e.scene)||void 0===n||n.reRender(a))}}updateButton(){this.setColor(),this.setEnabled()}setColor(){if(this.getVal())return this.button.removeClass("off"),void this.button.addClass("on");this.button.removeClass("on"),this.button.addClass("off")}setEnabled(){this.isEnabled&&(this.isEnabled()?this.button.removeClass("disabled"):this.button.addClass("disabled"))}}var Is="top",ks="bottom",Bs="right",xs="left",Rs="auto",Ms=[Is,ks,Bs,xs],zs="start",Hs="end",Gs="viewport",Vs="popper",js=Ms.reduce((function(e,t){return e.concat([t+"-"+zs,t+"-"+Hs])}),[]),Ws=[].concat(Ms,[Rs]).reduce((function(e,t){return e.concat([t,t+"-"+zs,t+"-"+Hs])}),[]),Us=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function Ks(e){return e?(e.nodeName||"").toLowerCase():null}function qs(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Zs(e){return e instanceof qs(e).Element||e instanceof Element}function $s(e){return e instanceof qs(e).HTMLElement||e instanceof HTMLElement}function Xs(e){return"undefined"!=typeof ShadowRoot&&(e instanceof qs(e).ShadowRoot||e instanceof ShadowRoot)}var Js={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach((function(e){var i=t.styles[e]||{},n=t.attributes[e]||{},s=t.elements[e];$s(s)&&Ks(s)&&(Object.assign(s.style,i),Object.keys(n).forEach((function(e){var t=n[e];!1===t?s.removeAttribute(e):s.setAttribute(e,!0===t?"":t)})))}))},effect:function(e){var t=e.state,i={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,i.popper),t.styles=i,t.elements.arrow&&Object.assign(t.elements.arrow.style,i.arrow),function(){Object.keys(t.elements).forEach((function(e){var n=t.elements[e],s=t.attributes[e]||{},r=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:i[e]).reduce((function(e,t){return e[t]="",e}),{});$s(n)&&Ks(n)&&(Object.assign(n.style,r),Object.keys(s).forEach((function(e){n.removeAttribute(e)})))}))}},requires:["computeStyles"]};function Qs(e){return e.split("-")[0]}var er=Math.max,tr=Math.min,ir=Math.round;function nr(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map((function(e){return e.brand+"/"+e.version})).join(" "):navigator.userAgent}function sr(){return!/^((?!chrome|android).)*safari/i.test(nr())}function rr(e,t,i){void 0===t&&(t=!1),void 0===i&&(i=!1);var n=e.getBoundingClientRect(),s=1,r=1;t&&$s(e)&&(s=e.offsetWidth>0&&ir(n.width)/e.offsetWidth||1,r=e.offsetHeight>0&&ir(n.height)/e.offsetHeight||1);var a=(Zs(e)?qs(e):window).visualViewport,u=!sr()&&i,o=(n.left+(u&&a?a.offsetLeft:0))/s,l=(n.top+(u&&a?a.offsetTop:0))/r,D=n.width/s,d=n.height/r;return{width:D,height:d,top:l,right:o+D,bottom:l+d,left:o,x:o,y:l}}function ar(e){var t=rr(e),i=e.offsetWidth,n=e.offsetHeight;return Math.abs(t.width-i)<=1&&(i=t.width),Math.abs(t.height-n)<=1&&(n=t.height),{x:e.offsetLeft,y:e.offsetTop,width:i,height:n}}function ur(e,t){var i=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(i&&Xs(i)){var n=t;do{if(n&&e.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function or(e){return qs(e).getComputedStyle(e)}function lr(e){return["table","td","th"].indexOf(Ks(e))>=0}function Dr(e){return((Zs(e)?e.ownerDocument:e.document)||window.document).documentElement}function dr(e){return"html"===Ks(e)?e:e.assignedSlot||e.parentNode||(Xs(e)?e.host:null)||Dr(e)}function hr(e){return $s(e)&&"fixed"!==or(e).position?e.offsetParent:null}function cr(e){for(var t=qs(e),i=hr(e);i&&lr(i)&&"static"===or(i).position;)i=hr(i);return i&&("html"===Ks(i)||"body"===Ks(i)&&"static"===or(i).position)?t:i||function(e){var t=/firefox/i.test(nr());if(/Trident/i.test(nr())&&$s(e)&&"fixed"===or(e).position)return null;var i=dr(e);for(Xs(i)&&(i=i.host);$s(i)&&["html","body"].indexOf(Ks(i))<0;){var n=or(i);if("none"!==n.transform||"none"!==n.perspective||"paint"===n.contain||-1!==["transform","perspective"].indexOf(n.willChange)||t&&"filter"===n.willChange||t&&n.filter&&"none"!==n.filter)return i;i=i.parentNode}return null}(e)||t}function gr(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Er(e,t,i){return er(e,tr(t,i))}function pr(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function fr(e,t){return t.reduce((function(t,i){return t[i]=e,t}),{})}var Fr={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,i=e.state,n=e.name,s=e.options,r=i.elements.arrow,a=i.modifiersData.popperOffsets,u=Qs(i.placement),o=gr(u),l=[xs,Bs].indexOf(u)>=0?"height":"width";if(r&&a){var D=function(e,t){return pr("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:fr(e,Ms))}(s.padding,i),d=ar(r),h="y"===o?Is:xs,c="y"===o?ks:Bs,g=i.rects.reference[l]+i.rects.reference[o]-a[o]-i.rects.popper[l],E=a[o]-i.rects.reference[o],p=cr(r),f=p?"y"===o?p.clientHeight||0:p.clientWidth||0:0,F=g/2-E/2,m=D[h],C=f-d[l]-D[c],y=f/2-d[l]/2+F,S=Er(m,y,C),b=o;i.modifiersData[n]=((t={})[b]=S,t.centerOffset=S-y,t)}},effect:function(e){var t=e.state,i=e.options.element,n=void 0===i?"[data-popper-arrow]":i;null!=n&&("string"!=typeof n||(n=t.elements.popper.querySelector(n)))&&ur(t.elements.popper,n)&&(t.elements.arrow=n)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function mr(e){return e.split("-")[1]}var Cr={top:"auto",right:"auto",bottom:"auto",left:"auto"};function yr(e){var t,i=e.popper,n=e.popperRect,s=e.placement,r=e.variation,a=e.offsets,u=e.position,o=e.gpuAcceleration,l=e.adaptive,D=e.roundOffsets,d=e.isFixed,h=a.x,c=void 0===h?0:h,g=a.y,E=void 0===g?0:g,p="function"==typeof D?D({x:c,y:E}):{x:c,y:E};c=p.x,E=p.y;var f=a.hasOwnProperty("x"),F=a.hasOwnProperty("y"),m=xs,C=Is,y=window;if(l){var S=cr(i),b="clientHeight",A="clientWidth";S===qs(i)&&"static"!==or(S=Dr(i)).position&&"absolute"===u&&(b="scrollHeight",A="scrollWidth"),(s===Is||(s===xs||s===Bs)&&r===Hs)&&(C=ks,E-=(d&&S===y&&y.visualViewport?y.visualViewport.height:S[b])-n.height,E*=o?1:-1),s!==xs&&(s!==Is&&s!==ks||r!==Hs)||(m=Bs,c-=(d&&S===y&&y.visualViewport?y.visualViewport.width:S[A])-n.width,c*=o?1:-1)}var T,v=Object.assign({position:u},l&&Cr),N=!0===D?function(e,t){var i=e.x,n=e.y,s=t.devicePixelRatio||1;return{x:ir(i*s)/s||0,y:ir(n*s)/s||0}}({x:c,y:E},qs(i)):{x:c,y:E};return c=N.x,E=N.y,o?Object.assign({},v,((T={})[C]=F?"0":"",T[m]=f?"0":"",T.transform=(y.devicePixelRatio||1)<=1?"translate("+c+"px, "+E+"px)":"translate3d("+c+"px, "+E+"px, 0)",T)):Object.assign({},v,((t={})[C]=F?E+"px":"",t[m]=f?c+"px":"",t.transform="",t))}var Sr={passive:!0},br={left:"right",right:"left",bottom:"top",top:"bottom"};function Ar(e){return e.replace(/left|right|bottom|top/g,(function(e){return br[e]}))}var Tr={start:"end",end:"start"};function vr(e){return e.replace(/start|end/g,(function(e){return Tr[e]}))}function Nr(e){var t=qs(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function wr(e){return rr(Dr(e)).left+Nr(e).scrollLeft}function Lr(e){var t=or(e),i=t.overflow,n=t.overflowX,s=t.overflowY;return/auto|scroll|overlay|hidden/.test(i+s+n)}function Or(e){return["html","body","#document"].indexOf(Ks(e))>=0?e.ownerDocument.body:$s(e)&&Lr(e)?e:Or(dr(e))}function _r(e,t){var i;void 0===t&&(t=[]);var n=Or(e),s=n===(null==(i=e.ownerDocument)?void 0:i.body),r=qs(n),a=s?[r].concat(r.visualViewport||[],Lr(n)?n:[]):n,u=t.concat(a);return s?u:u.concat(_r(dr(a)))}function Ir(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function kr(e,t,i){return t===Gs?Ir(function(e,t){var i=qs(e),n=Dr(e),s=i.visualViewport,r=n.clientWidth,a=n.clientHeight,u=0,o=0;if(s){r=s.width,a=s.height;var l=sr();(l||!l&&"fixed"===t)&&(u=s.offsetLeft,o=s.offsetTop)}return{width:r,height:a,x:u+wr(e),y:o}}(e,i)):Zs(t)?function(e,t){var i=rr(e,!1,"fixed"===t);return i.top=i.top+e.clientTop,i.left=i.left+e.clientLeft,i.bottom=i.top+e.clientHeight,i.right=i.left+e.clientWidth,i.width=e.clientWidth,i.height=e.clientHeight,i.x=i.left,i.y=i.top,i}(t,i):Ir(function(e){var t,i=Dr(e),n=Nr(e),s=null==(t=e.ownerDocument)?void 0:t.body,r=er(i.scrollWidth,i.clientWidth,s?s.scrollWidth:0,s?s.clientWidth:0),a=er(i.scrollHeight,i.clientHeight,s?s.scrollHeight:0,s?s.clientHeight:0),u=-n.scrollLeft+wr(e),o=-n.scrollTop;return"rtl"===or(s||i).direction&&(u+=er(i.clientWidth,s?s.clientWidth:0)-r),{width:r,height:a,x:u,y:o}}(Dr(e)))}function Br(e){var t,i=e.reference,n=e.element,s=e.placement,r=s?Qs(s):null,a=s?mr(s):null,u=i.x+i.width/2-n.width/2,o=i.y+i.height/2-n.height/2;switch(r){case Is:t={x:u,y:i.y-n.height};break;case ks:t={x:u,y:i.y+i.height};break;case Bs:t={x:i.x+i.width,y:o};break;case xs:t={x:i.x-n.width,y:o};break;default:t={x:i.x,y:i.y}}var l=r?gr(r):null;if(null!=l){var D="y"===l?"height":"width";switch(a){case zs:t[l]=t[l]-(i[D]/2-n[D]/2);break;case Hs:t[l]=t[l]+(i[D]/2-n[D]/2)}}return t}function xr(e,t){void 0===t&&(t={});var i=t,n=i.placement,s=void 0===n?e.placement:n,r=i.strategy,a=void 0===r?e.strategy:r,u=i.boundary,o=void 0===u?"clippingParents":u,l=i.rootBoundary,D=void 0===l?Gs:l,d=i.elementContext,h=void 0===d?Vs:d,c=i.altBoundary,g=void 0!==c&&c,E=i.padding,p=void 0===E?0:E,f=pr("number"!=typeof p?p:fr(p,Ms)),F=h===Vs?"reference":Vs,m=e.rects.popper,C=e.elements[g?F:h],y=function(e,t,i,n){var s="clippingParents"===t?function(e){var t=_r(dr(e)),i=["absolute","fixed"].indexOf(or(e).position)>=0&&$s(e)?cr(e):e;return Zs(i)?t.filter((function(e){return Zs(e)&&ur(e,i)&&"body"!==Ks(e)})):[]}(e):[].concat(t),r=[].concat(s,[i]),a=r[0],u=r.reduce((function(t,i){var s=kr(e,i,n);return t.top=er(s.top,t.top),t.right=tr(s.right,t.right),t.bottom=tr(s.bottom,t.bottom),t.left=er(s.left,t.left),t}),kr(e,a,n));return u.width=u.right-u.left,u.height=u.bottom-u.top,u.x=u.left,u.y=u.top,u}(Zs(C)?C:C.contextElement||Dr(e.elements.popper),o,D,a),S=rr(e.elements.reference),b=Br({reference:S,element:m,strategy:"absolute",placement:s}),A=Ir(Object.assign({},m,b)),T=h===Vs?A:S,v={top:y.top-T.top+f.top,bottom:T.bottom-y.bottom+f.bottom,left:y.left-T.left+f.left,right:T.right-y.right+f.right},N=e.modifiersData.offset;if(h===Vs&&N){var w=N[s];Object.keys(v).forEach((function(e){var t=[Bs,ks].indexOf(e)>=0?1:-1,i=[Is,ks].indexOf(e)>=0?"y":"x";v[e]+=w[i]*t}))}return v}var Mr={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,i=e.options,n=e.name;if(!t.modifiersData[n]._skip){for(var s=i.mainAxis,r=void 0===s||s,a=i.altAxis,u=void 0===a||a,o=i.fallbackPlacements,l=i.padding,D=i.boundary,d=i.rootBoundary,h=i.altBoundary,c=i.flipVariations,g=void 0===c||c,E=i.allowedAutoPlacements,p=t.options.placement,f=Qs(p),F=o||(f!==p&&g?function(e){if(Qs(e)===Rs)return[];var t=Ar(e);return[vr(e),t,vr(t)]}(p):[Ar(p)]),m=[p].concat(F).reduce((function(e,i){return e.concat(Qs(i)===Rs?function(e,t){void 0===t&&(t={});var i=t,n=i.placement,s=i.boundary,r=i.rootBoundary,a=i.padding,u=i.flipVariations,o=i.allowedAutoPlacements,l=void 0===o?Ws:o,D=mr(n),d=D?u?js:js.filter((function(e){return mr(e)===D})):Ms,h=d.filter((function(e){return l.indexOf(e)>=0}));0===h.length&&(h=d);var c=h.reduce((function(t,i){return t[i]=xr(e,{placement:i,boundary:s,rootBoundary:r,padding:a})[Qs(i)],t}),{});return Object.keys(c).sort((function(e,t){return c[e]-c[t]}))}(t,{placement:i,boundary:D,rootBoundary:d,padding:l,flipVariations:g,allowedAutoPlacements:E}):i)}),[]),C=t.rects.reference,y=t.rects.popper,S=new Map,b=!0,A=m[0],T=0;T<m.length;T++){var v=m[T],N=Qs(v),w=mr(v)===zs,L=[Is,ks].indexOf(N)>=0,O=L?"width":"height",_=xr(t,{placement:v,boundary:D,rootBoundary:d,altBoundary:h,padding:l}),I=L?w?Bs:xs:w?ks:Is;C[O]>y[O]&&(I=Ar(I));var k=Ar(I),B=[];if(r&&B.push(_[N]<=0),u&&B.push(_[I]<=0,_[k]<=0),B.every((function(e){return e}))){A=v,b=!1;break}S.set(v,B)}if(b)for(var x=function(e){var t=m.find((function(t){var i=S.get(t);if(i)return i.slice(0,e).every((function(e){return e}))}));if(t)return A=t,"break"},R=g?3:1;R>0&&"break"!==x(R);R--);t.placement!==A&&(t.modifiersData[n]._skip=!0,t.placement=A,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function zr(e,t,i){return void 0===i&&(i={x:0,y:0}),{top:e.top-t.height-i.y,right:e.right-t.width+i.x,bottom:e.bottom-t.height+i.y,left:e.left-t.width-i.x}}function Hr(e){return[Is,Bs,ks,xs].some((function(t){return e[t]>=0}))}function Pr(e,t,i){void 0===i&&(i=!1);var n,s,r=$s(t),a=$s(t)&&function(e){var t=e.getBoundingClientRect(),i=ir(t.width)/e.offsetWidth||1,n=ir(t.height)/e.offsetHeight||1;return 1!==i||1!==n}(t),u=Dr(t),o=rr(e,a,i),l={scrollLeft:0,scrollTop:0},D={x:0,y:0};return(r||!r&&!i)&&(("body"!==Ks(t)||Lr(u))&&(l=(n=t)!==qs(n)&&$s(n)?{scrollLeft:(s=n).scrollLeft,scrollTop:s.scrollTop}:Nr(n)),$s(t)?((D=rr(t,!0)).x+=t.clientLeft,D.y+=t.clientTop):u&&(D.x=wr(u))),{x:o.left+l.scrollLeft-D.x,y:o.top+l.scrollTop-D.y,width:o.width,height:o.height}}function Gr(e){var t=new Map,i=new Set,n=[];function s(e){i.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){if(!i.has(e)){var n=t.get(e);n&&s(n)}})),n.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){i.has(e.name)||s(e)})),n}var Vr={placement:"bottom",modifiers:[],strategy:"absolute"};function Yr(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}var Wr,Ur=function(e){void 0===e&&(e={});var t=e,i=t.defaultModifiers,n=void 0===i?[]:i,s=t.defaultOptions,r=void 0===s?Vr:s;return function(e,t,i){void 0===i&&(i=r);var s,a,u={placement:"bottom",orderedModifiers:[],options:Object.assign({},Vr,r),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},o=[],l=!1,D={state:u,setOptions:function(i){var s="function"==typeof i?i(u.options):i;d(),u.options=Object.assign({},r,u.options,s),u.scrollParents={reference:Zs(e)?_r(e):e.contextElement?_r(e.contextElement):[],popper:_r(t)};var a,l,h=function(e){var t=Gr(e);return Us.reduce((function(e,i){return e.concat(t.filter((function(e){return e.phase===i})))}),[])}((a=[].concat(n,u.options.modifiers),l=a.reduce((function(e,t){var i=e[t.name];return e[t.name]=i?Object.assign({},i,t,{options:Object.assign({},i.options,t.options),data:Object.assign({},i.data,t.data)}):t,e}),{}),Object.keys(l).map((function(e){return l[e]}))));return u.orderedModifiers=h.filter((function(e){return e.enabled})),u.orderedModifiers.forEach((function(e){var t=e.name,i=e.options,n=void 0===i?{}:i,s=e.effect;if("function"==typeof s){var r=s({state:u,name:t,instance:D,options:n});o.push(r||function(){})}})),D.update()},forceUpdate:function(){if(!l){var e=u.elements,t=e.reference,i=e.popper;if(Yr(t,i)){u.rects={reference:Pr(t,cr(i),"fixed"===u.options.strategy),popper:ar(i)},u.reset=!1,u.placement=u.options.placement,u.orderedModifiers.forEach((function(e){return u.modifiersData[e.name]=Object.assign({},e.data)}));for(var n=0;n<u.orderedModifiers.length;n++)if(!0!==u.reset){var s=u.orderedModifiers[n],r=s.fn,a=s.options,o=void 0===a?{}:a,d=s.name;"function"==typeof r&&(u=r({state:u,options:o,name:d,instance:D})||u)}else u.reset=!1,n=-1}}},update:(s=function(){return new Promise((function(e){D.forceUpdate(),e(u)}))},function(){return a||(a=new Promise((function(e){Promise.resolve().then((function(){a=void 0,e(s())}))}))),a}),destroy:function(){d(),l=!0}};if(!Yr(e,t))return D;function d(){o.forEach((function(e){return e()})),o=[]}return D.setOptions(i).then((function(e){!l&&i.onFirstUpdate&&i.onFirstUpdate(e)})),D}}({defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,i=e.instance,n=e.options,s=n.scroll,r=void 0===s||s,a=n.resize,u=void 0===a||a,o=qs(t.elements.popper),l=[].concat(t.scrollParents.reference,t.scrollParents.popper);return r&&l.forEach((function(e){e.addEventListener("scroll",i.update,Sr)})),u&&o.addEventListener("resize",i.update,Sr),function(){r&&l.forEach((function(e){e.removeEventListener("scroll",i.update,Sr)})),u&&o.removeEventListener("resize",i.update,Sr)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,i=e.name;t.modifiersData[i]=Br({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,i=e.options,n=i.gpuAcceleration,s=void 0===n||n,r=i.adaptive,a=void 0===r||r,u=i.roundOffsets,o=void 0===u||u,l={placement:Qs(t.placement),variation:mr(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:s,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,yr(Object.assign({},l,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:a,roundOffsets:o})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,yr(Object.assign({},l,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:o})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},Js,{name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,i=e.options,n=e.name,s=i.offset,r=void 0===s?[0,0]:s,a=Ws.reduce((function(e,i){return e[i]=function(e,t,i){var n=Qs(e),s=[xs,Is].indexOf(n)>=0?-1:1,r="function"==typeof i?i(Object.assign({},t,{placement:e})):i,a=r[0],u=r[1];return a=a||0,u=(u||0)*s,[xs,Bs].indexOf(n)>=0?{x:u,y:a}:{x:a,y:u}}(i,t.rects,r),e}),{}),u=a[t.placement],o=u.x,l=u.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=o,t.modifiersData.popperOffsets.y+=l),t.modifiersData[n]=a}},Mr,{name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,i=e.options,n=e.name,s=i.mainAxis,r=void 0===s||s,a=i.altAxis,u=void 0!==a&&a,o=i.boundary,l=i.rootBoundary,D=i.altBoundary,d=i.padding,h=i.tether,c=void 0===h||h,g=i.tetherOffset,E=void 0===g?0:g,p=xr(t,{boundary:o,rootBoundary:l,padding:d,altBoundary:D}),f=Qs(t.placement),F=mr(t.placement),m=!F,C=gr(f),y="x"===C?"y":"x",S=t.modifiersData.popperOffsets,b=t.rects.reference,A=t.rects.popper,T="function"==typeof E?E(Object.assign({},t.rects,{placement:t.placement})):E,v="number"==typeof T?{mainAxis:T,altAxis:T}:Object.assign({mainAxis:0,altAxis:0},T),N=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,w={x:0,y:0};if(S){if(r){var L,O="y"===C?Is:xs,_="y"===C?ks:Bs,I="y"===C?"height":"width",k=S[C],B=k+p[O],x=k-p[_],R=c?-A[I]/2:0,M=F===zs?b[I]:A[I],z=F===zs?-A[I]:-b[I],H=t.elements.arrow,P=c&&H?ar(H):{width:0,height:0},G=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},V=G[O],Y=G[_],j=Er(0,b[I],P[I]),W=m?b[I]/2-R-j-V-v.mainAxis:M-j-V-v.mainAxis,U=m?-b[I]/2+R+j+Y+v.mainAxis:z+j+Y+v.mainAxis,K=t.elements.arrow&&cr(t.elements.arrow),q=K?"y"===C?K.clientTop||0:K.clientLeft||0:0,Z=null!=(L=null==N?void 0:N[C])?L:0,$=k+U-Z,X=Er(c?tr(B,k+W-Z-q):B,k,c?er(x,$):x);S[C]=X,w[C]=X-k}if(u){var J,Q="x"===C?Is:xs,ee="x"===C?ks:Bs,te=S[y],ie="y"===y?"height":"width",ne=te+p[Q],se=te-p[ee],re=-1!==[Is,xs].indexOf(f),ae=null!=(J=null==N?void 0:N[y])?J:0,ue=re?ne:te-b[ie]-A[ie]-ae+v.altAxis,oe=re?te+b[ie]+A[ie]-ae-v.altAxis:se,le=c&&re?function(e,t,i){var n=Er(e,t,i);return n>i?i:n}(ue,te,oe):Er(c?ue:ne,te,c?oe:se);S[y]=le,w[y]=le-te}t.modifiersData[n]=w}},requiresIfExists:["offset"]},Fr,{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,i=e.name,n=t.rects.reference,s=t.rects.popper,r=t.modifiersData.preventOverflow,a=xr(t,{elementContext:"reference"}),u=xr(t,{altBoundary:!0}),o=zr(a,n),l=zr(u,s,r),D=Hr(o),d=Hr(l);t.modifiersData[i]={referenceClippingOffsets:o,popperEscapeOffsets:l,isReferenceHidden:D,hasPopperEscaped:d},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":D,"data-popper-escaped":d})}}]});class Kr{constructor(e,t,i,n=30){this.owner=e,this.containerEl=t,this.limit=n,this.owner=e,this.containerEl=t,t.on("click",".suggestion-item",this.onSuggestionClick.bind(this)),t.on("mousemove",".suggestion-item",this.onSuggestionMouseover.bind(this)),i.register([],"ArrowUp",(e=>{if(!e.isComposing)return this.setSelectedItem(this.selectedItem-1,!0),!1})),i.register([],"ArrowDown",(e=>{if(!e.isComposing)return this.setSelectedItem(this.selectedItem+1,!0),!1})),i.register([],"Enter",(e=>{if(!e.isComposing)return this.useSelectedItem(e),!1}))}onSuggestionClick(e,t){e.preventDefault();const i=this.suggestions.indexOf(t);this.setSelectedItem(i,!1),this.useSelectedItem(e)}onSuggestionMouseover(e,t){const i=this.suggestions.indexOf(t);this.setSelectedItem(i,!1)}setSuggestions(e){this.containerEl.empty();const t=[];e.slice(0,this.limit).forEach((e=>{const i=this.containerEl.createDiv("suggestion-item");this.owner.renderSuggestion(e,i),t.push(i)})),this.values=e,this.suggestions=t,this.setSelectedItem(0,!1)}useSelectedItem(e){const t=this.values[this.selectedItem];t&&this.owner.selectSuggestion(t,e)}setSelectedItem(e,t){const i=(e%(n=this.suggestions.length)+n)%n;var n;const s=this.suggestions[this.selectedItem],r=this.suggestions[i];null==s||s.removeClass("is-selected"),null==r||r.addClass("is-selected"),this.selectedItem=i,t&&r.scrollIntoView(!1)}}class qr{constructor(e,t,i){this.app=e,this.inputEl=t,this.containerEl=i,this.scope=new n.Scope,this.suggestEl=i.createDiv("suggestion-container"),this.suggestEl.style.left="-1000px";const s=this.suggestEl.createDiv("suggestion");this.suggest=new Kr(this,s,this.scope),this.scope.register([],"Escape",this.close.bind(this)),this.inputEl.addEventListener("input",this.onInputChanged.bind(this)),this.inputEl.addEventListener("focus",this.onInputChanged.bind(this)),this.inputEl.addEventListener("blur",this.close.bind(this)),this.suggestEl.on("mousedown",".suggestion-container",(e=>{e.preventDefault()}))}onInputChanged(){const e=this.inputEl.value,t=this.getSuggestions(e);t&&t.length>0?(this.suggest.setSuggestions(t),this.open(this.containerEl,this.inputEl)):this.close()}open(e,t){this.app.keymap.pushScope(this.scope),e.appendChild(this.suggestEl),this.popper=Ur(t,this.suggestEl,{placement:"bottom-start",modifiers:[{name:"sameWidth",enabled:!0,fn:({state:e,instance:t})=>{const i=`${e.rects.reference.width}px`;e.styles.popper.width!==i&&(e.styles.popper.width=i,t.update())},phase:"beforeWrite",requires:["computeStyles"]}]})}close(){this.app.keymap.popScope(this.scope),this.suggest.setSuggestions([]),this.popper&&this.popper.destroy(),this.suggestEl.detach()}}!function(e){e[e.TemplateFiles=0]="TemplateFiles",e[e.ScriptFiles=1]="ScriptFiles"}(Wr||(Wr={}));class Zr extends qr{constructor(e,t,i,n){super(e,t,n),this.plugin=i,this.inputStr=""}getSuggestions(e){var t,i,s;if(this.inputStr=e.trim(),""===e)return this.plugin.starred;const r=e.toLowerCase(),a=null===(t=this.plugin.pages)||void 0===t?void 0:t.getPages().filter((e=>(!e.file||(this.plugin.settings.showAttachments||"md"===e.file.extension)&&!this.plugin.settings.excludeFilepaths.some((t=>e.path.startsWith(t))))&&(e.file||(this.plugin.settings.showFolderNodes||!e.path.startsWith("folder:"))&&(this.plugin.settings.showTagNodes||!e.path.startsWith("tag:")))&&e.name.toLowerCase().contains(r)));if(a.length>30)return a;const u=a.concat(null===(i=this.plugin.pages)||void 0===i?void 0:i.getPages().filter((e=>!a.contains(e)&&(!e.file||(this.plugin.settings.showAttachments||"md"===e.file.extension)&&!this.plugin.settings.excludeFilepaths.some((t=>e.path.startsWith(t))))&&(e.file||(this.plugin.settings.showFolderNodes||!e.path.startsWith("folder:"))&&(this.plugin.settings.showTagNodes||!e.path.startsWith("tag:")))&&e.path.toLowerCase().contains(r))));if(u.length>30)return u;const o=n.prepareFuzzySearch(e);return u.concat(null===(s=this.plugin.pages)||void 0===s?void 0:s.getPages().filter((e=>!e.isVirtual&&(!e.file||(this.plugin.settings.showAttachments||"md"===e.file.extension)&&!this.plugin.settings.excludeFilepaths.some((t=>e.path.startsWith(t))))&&(e.file||(this.plugin.settings.showFolderNodes||!e.path.startsWith("folder:"))&&(this.plugin.settings.showTagNodes||!e.path.startsWith("tag:")))&&!u.contains(e)&&o(e.path))).sort(((e,t)=>o(t.path).score-o(e.path).score)))}renderSuggestion(e,t){var i,n;const s=""===this.inputStr?null:new RegExp(`(${this.inputStr})`,"gi");t.ariaLabel=e.path;const r=(e.isFolder||e.isTag?e.path.replace(/^folder:/,null!==(i=this.plugin.settings.folderNodeStyle.prefix)&&void 0!==i?i:"📂").replace(/^tag:/,null!==(n=this.plugin.settings.tagNodeStyle.prefix)&&void 0!==n?n:"🏷️"):s?e.name.match(s)?e.name:e.path:e.name).split("/"),a=r.pop(),u=r.join("/")+(r.length>0?"/":""),[o,l]=this.highlightSequence(u,a);t.innerHTML=`<span style="font-size: 0.8em; opacity: 0.8;">${o}</span>${l}`}highlightSequence(e,t){let i=-1;const n=(e,t)=>{let n=0,s="";return t.split(" ").forEach(((t,r)=>{const a=e.toLowerCase().indexOf(t.toLowerCase(),n);-1!==a&&(s+=e.substring(n,a)+`<b>${t}</b>`,n=a+t.length,i=r)})),s+=e.substring(n),s};let s=this.inputStr;const r=n(e,s);s=s.split(" ").slice(i+1).join(" ");const a=n(t,s);return s=s.split(" ").slice(i+1).join(" "),[r,a]}selectSuggestion(e){this.inputEl.value=e.path,this.inputEl.trigger("input"),this.close()}}class $r{constructor(e,t,i,n){this.label="",this.selected=!1,this.value=e,this.label=t,this.multiple=i,this.onChange=n,this.createOptionElement(),this.createListeners()}select(e=!0){this.selected=!0,this.setAttribute(),e&&this.onChange(this)}deselect(e=!0){this.selected=!1,this.setAttribute(),e&&this.onChange(this)}createOptionElement(){const e=document.createElement("div");e.classList.add("option"),this.singleSelectTextSpanRef=document.createElement("span"),this.singleSelectTextSpanRef.classList.add("option-text"),this.singleSelectTextSpanRef.innerText=this.label,e.appendChild(this.singleSelectTextSpanRef),e.appendChild(this.createCheckbox()),this.optionRef=e}createCheckbox(){const e=document.createElement("label");e.classList.add("checkbox-wrapper");const t=document.createElement("span");t.classList.add("checkbox-text"),t.innerText=this.label;const i=document.createElement("input");i.setAttribute("type","checkbox"),this.checkboxRef=i;const n=document.createElement("span");return n.classList.add("checkbox-checkmark"),e.appendChild(t),e.appendChild(i),e.appendChild(n),e}setAttribute(){this.multiple?this.selected?this.checkboxRef.setAttribute("checked","checked"):this.checkboxRef.removeAttribute("checked"):this.selected?this.singleSelectTextSpanRef.classList.add("selected"):this.singleSelectTextSpanRef.classList.remove("selected")}createListeners(){this.multiple?this.checkboxRef.addEventListener("change",(()=>{this.selected=this.checkboxRef.checked,this.onChange(this)})):this.singleSelectTextSpanRef.addEventListener("click",(()=>{this.selected=!0,this.onChange(this)}))}}class Xr{constructor(e){if(this.options=[],this.dropdownOpened=!1,this.destroyed=!1,this.rendered=!1,this.documentClickDropdownToggle=e=>{this.selectWrapperRef.contains(e.target)||this.handleDropdownToggle(!1,this.dropdownOpened)},this.config=e,this.assignConfig(),this.setOrigin(),!this.origin)throw"You have to pass origin element!";this.init()}init(){this.destroyed=!1,this.dropdownOpened=!1,this.createSelect(),this.createListeners()}destroy(){this.destroyed=!0,this.rendered=!1,this.hide(),document.removeEventListener("click",this.documentClickDropdownToggle),this.selectWrapperRef.cloneNode(!0),this.origin=null,this.selectHeaderRef=null,this.selectWrapperRef=null,this.selectedValueRef=null,this.optionsWrapperRef=null,this.options=[]}reset(){this.options.forEach((e=>e.deselect(!1))),this.updateSelection()}hide(){this.origin.innerHTML="",this.rendered=!1}render(){if(this.destroyed)throw"But you destroyed me... :(";if(this.origin.innerText.trim())throw"Hey! I am rendered already!";this.origin.appendChild(this.selectWrapperRef),this.rendered=!0,this.selectHeaderRef&&this.origin.prepend(this.selectHeaderRef)}assignConfig(){var e,t;this.origin=this.config.origin,this.configOptions=this.config.options,this.multiple=null===(e=this.config.multiple)||void 0===e||e,this.singularNominativeLabel=this.config.singularNominativeLabel,this.pluralNominativeLabel=this.config.pluralNominativeLabel,this.pluralGenitiveLabel=this.config.pluralGenitiveLabel,this.placeholder=null!==(t=this.config.placeholder)&&void 0!==t?t:"",this.headerLabel=this.config.headerLabel,this.selected=this.config.selected,this.onDropdownOpen=this.config.onDropdownOpen,this.onDropdownClose=this.config.onDropdownClose,this.onSelectionChange=this.config.onSelectionChange}setOrigin(){this.origin&&this.origin.classList.add("multiselect-container")}createHeader(){this.headerLabel&&(this.selectHeaderRef=document.createElement("div"),this.selectHeaderRef.classList.add("multiselect-header"),this.selectHeaderRef.innerText=this.headerLabel)}createSelect(){this.selectWrapperRef=document.createElement("div"),this.selectWrapperRef.classList.add("multiselect-wrapper"),this.multiple||this.selectWrapperRef.classList.add("single-select"),this.selectedValueRef=document.createElement("div"),this.selectedValueRef.classList.add("selected-value"),this.selectWrapperRef.appendChild(this.selectedValueRef),this.optionsWrapperRef=document.createElement("div"),this.optionsWrapperRef.classList.add("options-wrapper"),this.configOptions.forEach((e=>{var t;const i=new $r(e.value,e.label,this.multiple,this.onSelectChange.bind(this));(null===(t=this.selected)||void 0===t?void 0:t.includes(e.value))&&i.select(!1),this.options.push(i),this.optionsWrapperRef.appendChild(i.optionRef)})),this.updateSelection(),this.selectWrapperRef.appendChild(this.optionsWrapperRef),this.createHeader(),this.render()}createListeners(){this.selectWrapperRef.addEventListener("click",(e=>{this.selectWrapperRef.contains(e.target)&&!this.optionsWrapperRef.contains(e.target)&&this.handleDropdownToggle(!this.dropdownOpened)})),document.addEventListener("click",this.documentClickDropdownToggle)}handleDropdownToggle(e,t=!0){this.dropdownOpened=e,this.dropdownOpened?(this.selectWrapperRef.classList.add("opened"),this.onDropdownOpen&&t&&this.onDropdownOpen()):(this.selectWrapperRef.classList.remove("opened"),this.onDropdownClose&&t&&this.onDropdownClose(this.selected))}onSelectChange(e){this.multiple||(this.options.forEach((e=>e.deselect(!1))),e.select(!1)),this.updateSelection(),this.onSelectionChange&&this.onSelectionChange(this.selected),this.multiple||this.handleDropdownToggle(!1)}updateSelection(){this.selected=this.options.filter((e=>!!e.selected)).map((e=>e.value));const e=this.options.filter((e=>!!e.selected)).map((e=>e.label));let t=this.placeholder;1===e.length?t=e[0]:e.length>1&&(t=`${e.length} ${this.transformPluralLabel(e.length)}`),this.selectedValueRef.innerText=t}transformPluralLabel(e){var t,i,n;return 1===e?null!==(t=this.singularNominativeLabel)&&void 0!==t?t:"items":e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?null!==(i=this.pluralNominativeLabel)&&void 0!==i?i:"items":null!==(n=this.pluralGenitiveLabel)&&void 0!==n?n:"items"}}class Jr{constructor(e,t){this.plugin=e,this.selectedLinks=new Set,this.selectedTags=new Set,this.isOpen=!1,this.filterDiv=t.createDiv({attr:{id:"filter"}})}render(){if(this.isOpen)return;if(this.filterDiv.empty(),!this.plugin.scene)return;const e=Array.from(this.selectedTags);this.selectedLinks.forEach((t=>e.push("link::"+t)));const t=[],i=new Set(this.selectedLinks.keys());this.plugin.scene.links.links.forEach((e=>{var t;null===(t=e.hierarchyDefinition)||void 0===t||t.split(",").map((e=>e.trim())).forEach((e=>i.add(e))),e.hierarchyDefinition||(e.isInferred?i.add("inferred-link"):i.add("normal-link"))})),i.forEach((e=>t.push({label:e,value:"link::"+e})));const n=new Set(this.selectedTags.keys());this.plugin.scene.nodesMap.forEach((e=>{e.page.primaryStyleTag&&n.add(e.page.primaryStyleTag)})),n.forEach((e=>t.push({label:e,value:e})));const s=new Xr({origin:this.filterDiv,placeholder:"filter links and tags",options:t.sort(((e,t)=>e.label>t.label?1:-1)),selected:e,onDropdownOpen:()=>{this.isOpen=!0,this.selectedItems=s.selected},onSelectionChange:e=>{var t;this.selectedLinks.clear(),this.selectedTags.clear(),e.forEach((e=>{e.startsWith("link::")?this.selectedLinks.add(e.substring(6)):this.selectedTags.add(e)})),null===(t=this.plugin.scene)||void 0===t||t.reRender(!1)},onDropdownClose:e=>{var t;this.isOpen=!1,e!==this.selectedItems&&(null===(t=this.plugin.scene)||void 0===t||t.reRender(!1))}})}}const Qr=e=>e.createDiv({cls:"excalibrain-toolspanel-divider"});class ea{constructor(e,t){this.contentEl=e,this.plugin=t,this.buttons=[],e.addClass("excalibrain-contentEl"),this.wrapperDiv=this.contentEl.createDiv({cls:"excalibrain-toolspanel-wrapper"});const i=this.wrapperDiv.createDiv({cls:"excalibrain-dropdown-wrapper"}),s=i.createEl("input",{type:"text",cls:"excalibrain-searchinput"});s.ariaLabel=z("SEARCH_IN_VAULT"),s.oninput=()=>{var e;const t=this.plugin.pages.get(s.value);t&&(null===(e=this.plugin.scene)||void 0===e||e.renderGraphForPath(t.path))},s.onblur=()=>{s.value=""},new Zr(this.plugin.app,s,this.plugin,e),this.searchElement=s,this.linkTagFilter=new Jr(t,i),this.linkTagFilter.render();const r=this.wrapperDiv.createDiv({cls:"excalibrain-buttons"});this.buttons.push(new _s({plugin:this.plugin,getVal:()=>!1,setVal:e=>{const t=this.plugin.EA.getExcalidrawAPI().getSceneElements(),i=this.plugin.EA.getExcalidrawAPI().getAppState(),n=this.plugin.EA;return n.reset(),n.canvas.viewBackgroundColor=i.viewBackgroundColor,n.canvas.theme="light",t.forEach((e=>n.elementsDict[e.id]=e)),n.create({filename:`ExcaliBrain Snapshot - ${p(this.plugin.scene.centralPagePath).basename}`,onNewPane:!0}),!1},wrapper:r,options:{display:"✏",icon:n.getIcon("lucide-pencil").outerHTML,tooltip:z("OPEN_DRAWING")},updateIndex:!0})),Qr(r);const a=new _s({plugin:this.plugin,getVal:()=>!1,setVal:e=>(this.plugin.scene.renderGraphForPath(this.plugin.navigationHistory.getPrevious()),this.rerender(),!1),isEnabled:()=>this.plugin.navigationHistory.hasPrevious(),wrapper:r,options:{display:"<",icon:n.getIcon("lucide-arrow-big-left").outerHTML,tooltip:z("NAVIGATE_BACK")},updateIndex:!1,shouldRerenderOnToggle:!1});this.buttons.push(a);const u=new _s({plugin:this.plugin,getVal:()=>!1,setVal:e=>(this.plugin.scene.renderGraphForPath(this.plugin.navigationHistory.getNext()),this.rerender(),!1),isEnabled:()=>this.plugin.navigationHistory.hasNext(),wrapper:r,options:{display:">",icon:n.getIcon("lucide-arrow-big-right").outerHTML,tooltip:z("NAVIGATE_FORWARD")},updateIndex:!1,shouldRerenderOnToggle:!1});this.buttons.push(u),this.plugin.navigationHistory.setNavigateButtons([a,u]),this.buttons.push(new _s({plugin:this.plugin,getVal:()=>!1,setVal:e=>!1,wrapper:r,options:{display:"🔄",icon:n.getIcon("lucide-refresh-cw").outerHTML,tooltip:z("REFRESH_VIEW")},updateIndex:!0}));const o=new _s({plugin:this.plugin,getVal:()=>this.plugin.scene.pinLeaf,setVal:e=>{if(this.plugin.scene.pinLeaf=e,e){const e=[];this.plugin.app.workspace.iterateAllLeaves((t=>{var i,s;("empty"===(null===(i=t.view)||void 0===i?void 0:i.getViewType())||t.view instanceof n.EditableFileView&&t!==(null===(s=this.plugin.scene)||void 0===s?void 0:s.leaf))&&e.push(t)})),e.sort(((e,t)=>e.activeTime-t.activeTime>0?-1:1)),e.length>0&&(this.plugin.scene.centralLeaf=e[0])}return!0},isEnabled:()=>!!this.plugin.settings.autoOpenCentralDocument&&(this.plugin.scene&&!this.plugin.scene.isCentralLeafStillThere()&&(this.plugin.scene.pinLeaf=!1),!0),wrapper:r,options:{display:"📌",icon:{on:n.getIcon("lucide-pin").outerHTML,off:n.getIcon("lucide-pin-off").outerHTML},tooltip:z("PIN_LEAF")},updateIndex:!1});this.buttons.push(o),this.buttons.push(new _s({plugin:this.plugin,getVal:()=>this.plugin.settings.autoOpenCentralDocument,setVal:e=>(this.plugin.settings.autoOpenCentralDocument=e,o.updateButton(),!0),wrapper:r,options:{display:"🔌",icon:{on:'<svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 256 256"><path d="M237.66,18.34a8,8,0,0,0-11.32,0l-52.4,52.41-5.37-5.38a32.05,32.05,0,0,0-45.26,0L100,88.69l-6.34-6.35A8,8,0,0,0,82.34,93.66L88.69,100,65.37,123.31a32,32,0,0,0,0,45.26l5.38,5.37-52.41,52.4a8,8,0,0,0,11.32,11.32l52.4-52.41,5.37,5.38a32,32,0,0,0,45.26,0L156,167.31l6.34,6.35a8,8,0,0,0,11.32-11.32L167.31,156l23.32-23.31a32,32,0,0,0,0-45.26l-5.38-5.37,52.41-52.4A8,8,0,0,0,237.66,18.34Zm-116.29,161a16,16,0,0,1-22.62,0L76.69,157.25a16,16,0,0,1,0-22.62L100,111.31,144.69,156Zm57.94-57.94L156,144.69,111.31,100l23.32-23.31a16,16,0,0,1,22.62,0l22.06,22A16,16,0,0,1,179.31,121.37ZM88.41,34.53a8,8,0,0,1,15.18-5.06l8,24a8,8,0,0,1-15.18,5.06Zm-64,58.94a8,8,0,0,1,10.12-5.06l24,8a8,8,0,0,1-5.06,15.18l-24-8A8,8,0,0,1,24.41,93.47Zm207.18,69.06a8,8,0,0,1-10.12,5.06l-24-8a8,8,0,0,1,5.06-15.18l24,8A8,8,0,0,1,231.59,162.53Zm-64,58.94a8,8,0,0,1-15.18,5.06l-8-24a8,8,0,0,1,15.18-5.06Z"></path></svg>',off:'<svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 256 256"><path d="M149.66,138.34a8,8,0,0,0-11.32,0L120,156.69,99.31,136l18.35-18.34a8,8,0,0,0-11.32-11.32L88,124.69,69.66,106.34a8,8,0,0,0-11.32,11.32L64.69,124,41.37,147.31a32,32,0,0,0,0,45.26l5.38,5.37-28.41,28.4a8,8,0,0,0,11.32,11.32l28.4-28.41,5.37,5.38a32,32,0,0,0,45.26,0L132,191.31l6.34,6.35a8,8,0,0,0,11.32-11.32L131.31,168l18.35-18.34A8,8,0,0,0,149.66,138.34Zm-52.29,65a16,16,0,0,1-22.62,0L52.69,181.25a16,16,0,0,1,0-22.62L76,135.31,120.69,180Zm140.29-185a8,8,0,0,0-11.32,0l-28.4,28.41-5.37-5.38a32.05,32.05,0,0,0-45.26,0L124,64.69l-6.34-6.35a8,8,0,0,0-11.32,11.32l80,80a8,8,0,0,0,11.32-11.32L191.31,132l23.32-23.31a32,32,0,0,0,0-45.26l-5.38-5.37,28.41-28.4A8,8,0,0,0,237.66,18.34Zm-34.35,79L180,120.69,135.31,76l23.32-23.31a16,16,0,0,1,22.62,0l22.06,22A16,16,0,0,1,203.31,97.37Z"></path></svg>'},tooltip:z("AUTO_OPEN_DOCUMENT")},updateIndex:!1})),Qr(r),this.buttons.push(new _s({plugin:this.plugin,getVal:()=>this.plugin.settings.showAttachments,setVal:e=>(this.plugin.settings.showAttachments=e,!0),wrapper:r,options:{display:"📎",icon:n.getIcon("lucide-paperclip").outerHTML,tooltip:z("SHOW_HIDE_ATTACHMENTS")},updateIndex:!0})),this.buttons.push(new _s({plugin:this.plugin,getVal:()=>this.plugin.settings.showVirtualNodes,setVal:e=>(this.plugin.settings.showVirtualNodes=e,!0),wrapper:r,options:{display:"∅",icon:n.getIcon("lucide-minus-circle").outerHTML,tooltip:z("SHOW_HIDE_VIRTUAL")},updateIndex:!1})),this.buttons.push(new _s({plugin:this.plugin,getVal:()=>this.plugin.settings.showInferredNodes,setVal:e=>(this.plugin.settings.showInferredNodes=e,!0),wrapper:r,options:{display:"🤔",icon:n.getIcon("lucide-git-pull-request-draft").outerHTML,tooltip:z("SHOW_HIDE_INFERRED")},updateIndex:!0})),this.buttons.push(new _s({plugin:this.plugin,getVal:()=>this.plugin.settings.showPageNodes,setVal:e=>(this.plugin.settings.showPageNodes=e,!0),wrapper:r,options:{display:"📄",icon:n.getIcon("lucide-file-text").outerHTML,tooltip:z("SHOW_HIDE_PAGES")},updateIndex:!0})),this.buttons.push(new _s({plugin:this.plugin,getVal:()=>this.plugin.settings.renderAlias,setVal:e=>(this.plugin.settings.renderAlias=e,!0),wrapper:r,options:{display:"🧥",icon:n.getIcon("lucide-venetian-mask").outerHTML,tooltip:z("SHOW_HIDE_ALIAS")},updateIndex:!1})),this.buttons.push(new _s({plugin:this.plugin,getVal:()=>this.plugin.settings.showFolderNodes,setVal:e=>(this.plugin.settings.showFolderNodes=e,!0),wrapper:r,options:{display:"📂",icon:n.getIcon("lucide-folder").outerHTML,tooltip:z("SHOW_HIDE_FOLDER")},updateIndex:!0})),this.buttons.push(new _s({plugin:this.plugin,getVal:()=>this.plugin.settings.showTagNodes,setVal:e=>(this.plugin.settings.showTagNodes=e,!0),wrapper:r,options:{display:"#",icon:n.getIcon("lucide-tag").outerHTML,tooltip:z("SHOW_HIDE_TAG")},updateIndex:!1})),this.buttons.push(new _s({plugin:this.plugin,getVal:()=>this.plugin.settings.showURLNodes,setVal:e=>(this.plugin.settings.showURLNodes=e,!0),wrapper:r,options:{display:"🌐",icon:n.getIcon("lucide-globe").outerHTML,tooltip:z("SHOW_HIDE_URLS")},updateIndex:!1})),this.buttons.push(new _s({plugin:this.plugin,getVal:()=>this.plugin.settings.renderSiblings,setVal:e=>(this.plugin.settings.renderSiblings=e,!0),wrapper:r,options:{display:"👨‍👩‍👧‍👦",icon:n.getIcon("lucide-grip").outerHTML,tooltip:z("SHOW_HIDE_SIBLINGS")},updateIndex:!1})),this.buttons.push(new _s({plugin:this.plugin,getVal:()=>this.plugin.settings.embedCentralNode,setVal:e=>(this.plugin.settings.embedCentralNode=e,this.plugin.settings.toggleEmbedTogglesAutoOpen&&(this.plugin.settings.autoOpenCentralDocument=!e),!0),wrapper:r,options:{display:"⏹️",icon:n.getIcon("lucide-code").outerHTML,tooltip:z("SHOW_HIDE_EMBEDDEDCENTRAL")},updateIndex:!1})),this.contentEl.appendChild(this.wrapperDiv)}rerender(){this.buttons.forEach((e=>{e instanceof _s&&e.updateButton()})),this.linkTagFilter.render()}terminate(){var e;if(this.contentEl&&this.contentEl.removeClass("excalibrain-contentEl"),this.wrapperDiv){try{null===(e=this.contentEl)||void 0===e||e.removeChild(this.wrapperDiv)}catch(e){}this.wrapperDiv=null}}}class ta{constructor(e,t){this.contentEl=e,this.plugin=t,this.wrapperDiv=this.contentEl.createDiv({cls:"excalibrain-history-wrapper"}),this.rerender(),this.contentEl.appendChild(this.wrapperDiv)}rerender(){this.wrapperDiv.empty();const e=this.wrapperDiv.createDiv({cls:"excalibrain-history-container"}),t=this.plugin.navigationHistory.get();for(let i=t.length-1;i>=0;i--){i!==t.length-1&&e.createDiv({text:"•",cls:"excalibrain-history-divider"});let n="",s="";const r=this.plugin.pages.get(t[i]);if(!r)return;const a=r.path.startsWith("folder:")?this.plugin.settings.folderNodeStyle:r.path.startsWith("tag:")?this.plugin.settings.tagNodeStyle:Object.assign(Object.assign({},this.plugin.settings.baseNodeStyle),h(D(r.dvPage,this.plugin.settings),this.plugin.settings));r.file?(n=a.prefix+r.getTitle(),s=r.path):(n=a.prefix+r.name,r.path,s=r.path),e.createDiv({text:n,cls:"excalibrain-history-item"},(e=>{e.onclick=()=>{var e;return null===(e=this.plugin.scene)||void 0===e?void 0:e.renderGraphForPath(s)}}))}}terminate(){var e;if(this.wrapperDiv){try{null===(e=this.contentEl)||void 0===e||e.removeChild(this.wrapperDiv)}catch(e){}this.wrapperDiv=null}}}class ia{constructor(e,t,i){this.minLinkLength=100,this.disregardLeafChange=!1,this.nodesMap=new Map,this.layouts=[],this.blockUpdateTimer=!1,this.vaultFileChanged=!1,this.pinLeaf=!1,this.focusSearchAfterInitiation=!0,this.zoomToFitOnNextBrainLeafActivate=!1,this.ea=e.EA,this.plugin=e,this.app=e.app,this.leaf=null!=i?i:app.workspace.getLeaf(t),this.terminated=!1,this.links=new Os(e)}set centralLeaf(e){this._centralLeaf=e}get centralLeaf(){return this.plugin.settings.autoOpenCentralDocument&&this._centralLeaf&&this.app.workspace.getLeafById(this._centralLeaf.id)?this._centralLeaf:null}async initialize(e){var t;this.focusSearchAfterInitiation=e,await this.plugin.loadSettings(),(null===(t=this.leaf)||void 0===t?void 0:t.view)&&(this.toolsPanel=new ea(this.leaf.view.contentEl.querySelector(".excalidraw"),this.plugin),this.initializeScene())}isActive(){var e;return!this.terminated&&app.workspace.getLeafById(null===(e=this.leaf)||void 0===e?void 0:e.id)}async reRender(e=!0){var t,i;if(!this.isActive())return;if(!this.centralPagePath)return;e&&(this.vaultFileChanged=!1,await this.plugin.createIndex()),E(this.ea);const n=this.plugin.pages.get(this.centralPagePath);(null==n?void 0:n.file)&&!(n.isFolder||n.isTag||n.isVirtual)&&this.plugin.settings.autoOpenCentralDocument&&(this.centralLeaf?(null===(i=null===(t=this.centralLeaf.view)||void 0===t?void 0:t.file)||void 0===i?void 0:i.path)!==n.file.path&&this.centralLeaf.openFile(n.file,{active:!1}):this.ea.openFileInNewOrAdjacentLeaf(n.file)),await this.render(this.plugin.settings.embedCentralNode)}getCentralPage(){let e=this.plugin.pages.get(this.centralPagePath);return!e&&this.centralPageFile&&(this.centralPagePath=this.centralPageFile.path,e=this.plugin.pages.get(this.centralPageFile.path)),e}longestTitle(e,t=20){const i=[0];for(let n=0;n<e.length&&n<=t;n++){const t=e[n];i.push(t.page.getTitle().length)}return Math.max(...i)}async renderGraphForPath(e,t=!0){var i;if(!this.isActive())return;this.blockUpdateTimer=!0;const n=this.plugin.settings,s=this.plugin.pages.get(e);if(!s)return void(this.blockUpdateTimer=!1);const r=!(s.isFolder||s.isTag||s.isVirtual||s.isURL);if(r&&!s.file)return void(this.blockUpdateTimer=!1);if(!(null===(i=this.ea.targetView)||void 0===i?void 0:i.file)||this.ea.targetView.file.path!==n.excalibrainFilepath)return void this.unloadScene();if(r&&s.file.path===this.ea.targetView.file.path)return void(this.blockUpdateTimer=!1);E(this.plugin.EA);const a=this.getCentralPage(),u=a&&(r&&a.file===s.file||s.isURL&&a.isURL&&a.url===s.url);if(!u||!s.isURL&&s.file.stat.mtime!==a.mtime){if(r&&t&&n.autoOpenCentralDocument){const e=this.centralLeaf;e&&this.app.workspace.getLeafById(e.id)?e.openFile(s.file,{active:!1}):this.centralLeaf=this.ea.openFileInNewOrAdjacentLeaf(s.file,{active:!1}),this.plugin.navigationHistory.addToHistory(s.file.path)}else this.plugin.navigationHistory.addToHistory(s.path);s.isFolder&&!n.showFolderNodes&&(n.showFolderNodes=!0,this.toolsPanel.rerender()),s.isURL&&!n.showURLNodes&&(n.showURLNodes=!0,this.toolsPanel.rerender()),s.isTag&&!n.showTagNodes&&(n.showTagNodes=!0,this.toolsPanel.rerender()),this.centralPagePath=e,this.centralPageFile=s.file,await this.render(u)}else this.blockUpdateTimer=!1}static async openExcalidrawLeaf(e,t,i){var s,r;let a=0,u=app.vault.getAbstractFileByPath(t.excalibrainFilepath);if(!u||u instanceof n.TFile){if(!u)for(u=await app.vault.create(t.excalibrainFilepath,Ns);u instanceof n.TFile&&!e.isExcalidrawFile(u)&&a++<10;)await sleep(50);if(a=0,u&&u instanceof n.TFile&&!e.isExcalidrawFile(u))new H(app,"⚠ File Exists",`${u.path} already exists in your Vault. Is it ok to overwrite this file? If not, change ExcaliBrain file path in plugin settings.`).show((async s=>{if(s){for(await app.vault.modify(u,Ns);u instanceof n.TFile&&!e.isExcalidrawFile(u)&&a++<10;)await sleep(50);ia.openExcalidrawLeaf(e,t,i)}else new n.Notice("Could not start ExcaliBrain. Please change the ExcaliBrain file path in plugin settings.")}));else{if(i||"empty"!==(i=app.workspace.getLeaf(!1)).getViewState().type&&(i=e.getLeaf(i,"new-pane")),t.defaultAlwaysOnTop&&i&&e.DEVICE.isDesktop){const e=null===(s=i.view)||void 0===s?void 0:s.ownerWindow;e&&e!==window&&!(null===(r=e.electronWindow)||void 0===r?void 0:r.isMaximized())&&e.electronWindow.setAlwaysOnTop(!0)}await i.openFile(u)}}else new n.Notice(`Please check settings. ExcaliBrain path (${t.excalibrainFilepath}) points to a folder, not a file`)}async initializeScene(){this.disregardLeafChange=!1;const e=this.ea,t=this.plugin.settings;let i=0;for(e.clear(),e.setView(this.leaf.view),e.copyViewElementsToEAforEditing(e.getViewElements()),e.getElements().forEach((e=>e.isDeleted=!0));!e.targetView.excalidrawAPI&&i++<10;)await sleep(50);if(!e.targetView.excalidrawAPI)return void new n.Notice("Error initializing Excalidraw view");const s=e.getExcalidrawAPI();this.ea.registerThisAsViewEA(),this.ea.targetView.semaphores.saving=!0,s.setMobileModeAllowed(!1),this.setBaseLayoutParams(),(()=>{s.updateScene({appState:{viewModeEnabled:!0,activeTool:{lastActiveToolBeforeEraser:null,locked:!1,type:"selection"},theme:"light",viewBackgroundColor:this.plugin.settings.backgroundColor}})})(),e.style.strokeColor=t.baseNodeStyle.textColor,e.addText(0,0,"🚀 To get started\nselect a document using the search in the top left or\nopen a document in another pane.\n\n✨ For the best experience enable 'Open in adjacent pane'\nin Excalidraw settings under 'Links and Transclusion'.\n\n⚠ ExcaliBrain may need to wait for DataView to initialize its index.\nThis can take up to a few minutes after starting Obsidian.",{textAlign:"center"}),e.addElementsToView(!1,!1).then((()=>{e.targetView.clearDirty()})),(async()=>{this.plugin.settings.allowAutozoom&&setTimeout((()=>s.zoomToFit(null,this.plugin.settings.maxZoom,.15)),100),e.targetView.linksAlwaysOpenInANewPane=!0,e.targetView.allowFrameButtonsInViewMode=!0,await this.addEventHandler(),this.historyPanel=new ta(this.leaf.view.contentEl.querySelector(".excalidraw"),this.plugin),new n.Notice("ExcaliBrain On")})()}setBaseLayoutParams(){const e=this.ea,t=this.plugin.settings,i=Object.assign(Object.assign({},t.baseNodeStyle),t.centralNodeStyle);e.style.fontFamily=t.baseLinkStyle.fontFamily,e.style.fontSize=t.baseLinkStyle.fontSize,this.minLinkLength=e.measureText("m".repeat(t.minLinkLength)).width,e.style.fontFamily=i.fontFamily,e.style.fontSize=i.fontSize,this.textSize=e.measureText("m".repeat(i.maxLabelLength)),this.nodeWidth=this.textSize.width+2*i.padding,this.nodeHeight=2*(this.textSize.height+2*i.padding)}addNodes(t){t.neighbours.forEach((i=>{if(i.page.path===this.ea.targetView.file.path)return;i.page.maxLabelLength=t.layout.spec.maxLabelLength;const n=new V({ea:this.ea,page:i.page,isInferred:i.relationType===e.INFERRED,isCentral:t.isCentral,isSibling:t.isSibling,friendGateOnLeft:t.friendGateOnLeft});this.nodesMap.set(i.page.path,n),t.layout.nodes.push(n)}))}getNeighbors(e){const t=this.plugin.settings,i=e.getParents().filter((i=>!(i.page.path===e.path||t.excludeFilepaths.some((e=>i.page.path.startsWith(e)))||i.page.primaryStyleTag&&this.toolsPanel.linkTagFilter.selectedTags.has(i.page.primaryStyleTag)))).slice(0,t.maxItemCount),n=i.map((e=>e.page.path)),s=e.getChildren().filter((i=>!(i.page.path===e.path||t.excludeFilepaths.some((e=>i.page.path.startsWith(e)))||i.page.primaryStyleTag&&this.toolsPanel.linkTagFilter.selectedTags.has(i.page.primaryStyleTag)))).slice(0,t.maxItemCount),r=e.getLeftFriends().concat(e.getPreviousFriends()).filter((i=>!(i.page.path===e.path||t.excludeFilepaths.some((e=>i.page.path.startsWith(e)))||i.page.primaryStyleTag&&this.toolsPanel.linkTagFilter.selectedTags.has(i.page.primaryStyleTag)))).slice(0,t.maxItemCount),a=e.getRightFriends().concat(e.getNextFriends()).filter((i=>!(i.page.path===e.path||t.excludeFilepaths.some((e=>i.page.path.startsWith(e)))||i.page.primaryStyleTag&&this.toolsPanel.linkTagFilter.selectedTags.has(i.page.primaryStyleTag)))).slice(0,t.maxItemCount),u=e.getSiblings().filter((n=>!(i.some((e=>e.page.path===n.page.path))||s.some((e=>e.page.path===n.page.path))||r.some((e=>e.page.path===n.page.path))||a.some((e=>e.page.path===n.page.path))||t.excludeFilepaths.some((e=>n.page.path.startsWith(e))))&&n.page.path!==e.path)),o=u.filter((e=>e.page.getParents().map((e=>e.page.path)).some((e=>n.includes(e)))&&(!e.page.primaryStyleTag||!this.toolsPanel.linkTagFilter.selectedTags.has(e.page.primaryStyleTag)))).slice(0,t.maxItemCount);return{parents:i,children:s,leftFriends:r,rightFriends:a,siblings:o}}calculateAreas({parents:e,parentCols:t,parentWidth:i,children:n,childrenCols:s,childWidth:r,leftFriends:a,leftFriendCols:u,leftFriendWidth:o,rightFriends:l,rightFriendCols:D,rightFriendWidth:d,siblings:h,siblingsCols:c,siblingsNodeWidth:g,siblingsNodeHeight:E}){return{leftFriendsArea:{width:a.length>0?u*o:0,height:a.length>0?Math.ceil(a.length/u)*this.nodeHeight:0},rightFriendsArea:{width:l.length>0?D*d:0,height:l.length>0?Math.ceil(l.length/D)*this.nodeHeight:0},parentsArea:{width:e.length>0?t*i:0,height:e.length>0?Math.ceil(e.length/t)*this.nodeHeight:0},childrenArea:{width:n.length>0?s*r:0,height:n.length>0?Math.ceil(n.length/s)*this.nodeHeight:0},siblingsArea:{width:h.length>0?g*c:0,height:h.length>0?Math.ceil(h.length/c)*E:0}}}calculateLayoutParams({centralPage:e,parents:t,children:i,leftFriends:n,rightFriends:s,siblings:r,isCenterEmbedded:a,centerEmbedHeight:u,centerEmbedWidth:o,style:l,rootNode:D}){var d;const h=this.plugin.settings,c=this.ea,g=h.baseNodeStyle,E=h.compactView,p=1.166*h.compactingFactor,f=.833*h.compactingFactor,F=this.minLinkLength*f*2,m=n.length>=10,C=s.length>=10,y=this.ea.measureText("mi3L".repeat(1)),S=.25*y.width,b=y.height;this.nodeWidth=g.maxLabelLength*S+2*g.padding,this.nodeHeight=p*(b+2*g.padding);const A=6*g.padding,T=Math.max(D.prefix.length,2),v=c.targetView.containerEl,N=1/((v.innerHeight-150)/v.innerWidth),w=Math.min(N,1),L=Math.round(l.maxLabelLength*w),O=Math.max(7,L),_=r.length>=20?3:r.length>=10?2:1,I=E?i.length<=12?[1,1,2,3,3,3,3,2,2,3,3,2,2][i.length]:3:i.length<=12?[1,1,2,3,3,3,3,4,4,5,5,4,4][i.length]:5,k=E?t.length<2?1:2:t.length<5?[1,1,2,3,2][t.length]:3,B=a?Math.ceil(n.length*this.nodeHeight/u):m?2:1,x=a?Math.ceil(s.length*this.nodeHeight/u):C?2:1,R=e.getTitle(),M=c.measureText(R.repeat(1)),z=[...(new Intl.Segmenter).segment(R)].length,H=Math.min(z+T,l.maxLabelLength),P=M.width+2*l.padding,G=a?u+2*this.nodeHeight:4*this.nodeHeight,V=Math.min(this.longestTitle(t)+T,O),Y=f*(V*S+A),j=Math.min(this.longestTitle(i,20)+T,O),W=f*(j*S+A),U=Math.min(this.longestTitle(n)+T,O),K=f*(U*S+A),q=Math.min(this.longestTitle(s)+T,O),Z=f*(q*S+A),$=h.siblingNodeStyle,X=null!==(d=$.padding)&&void 0!==d?d:h.baseNodeStyle.padding,J=Math.min(this.longestTitle(r,20)+T,O);c.style.fontFamily=$.fontFamily,c.style.fontSize=$.fontSize;const Q=c.measureText("m".repeat(J+3)),ee=f*(Q.width+3*X),te=p*(Q.height+2*X),{parentsArea:ie,childrenArea:ne,leftFriendsArea:se,rightFriendsArea:re,siblingsArea:ae}=this.calculateAreas({parents:t,parentCols:k,parentWidth:Y,children:i,childrenCols:I,childWidth:W,leftFriends:n,leftFriendCols:B,leftFriendWidth:K,rightFriends:s,rightFriendCols:x,rightFriendWidth:Z,siblings:r,siblingsCols:_,siblingsNodeWidth:ee,siblingsNodeHeight:te}),ue=.5*(ie.height+Math.max(se.height,re.height,G))+A;return{rootNodeDimensions:M,rootWidth:P,rootNodeLength:H,childrenOrigoY:.5*(ne.height+Math.max(se.height,re.height,G))+A,childWidth:W,childLength:j,childrenCols:I,parentsOrigoY:ue,parentWidth:Y,parentLabelLength:V,parentCols:k,leftFriendOrigoX:Math.max((a?o+8.4:P+F)+se.width,ne.width-se.width,ie.width-se.width)/2+A,leftFriendWidth:K,leftFriendLength:U,leftFriendCols:B,rightFriendOrigoX:Math.max((a?o+8.4:P+F)+re.width,ne.width-re.width,ie.width-re.width)/2+A,rightFriendWidth:Z,rightFriendLength:q,rightFriendCols:x,siblingsOrigoX:(Math.max(ie.width,a?o:P)+ae.width)/2+3*X*(1+_),siblingsOrigoY:Math.max(ue,(ae.height+re.height)/2)+this.nodeHeight,siblingsNodeWidth:ee,siblingsNodeHeight:te,siblingsLabelLength:J,siblingsCols:_}}async render(e=!1){var i;if(this.historyPanel&&this.historyPanel.rerender(),!this.centralPagePath)return;const n=this.plugin.settings;let s=this.plugin.pages.get(this.centralPagePath);if(!s){if(this.centralPagePath=this.plugin.lowercasePathMap.get(this.centralPagePath.toLowerCase()),s=this.plugin.pages.get(this.centralPagePath),!s)return;this.centralPageFile=s.file}const r=this.ea;e=e&&Boolean(this.rootNode)&&n.embedCentralNode&&(s.file&&f(s.file,r)||s.isURL),this.zoomToFitOnNextBrainLeafActivate=!r.targetView.containerEl.isShown(),r.clear(),r.copyViewElementsToEAforEditing(r.getViewElements()),r.getElements().filter((t=>!e||!this.rootNode.embeddedElementIds.includes(t.id))).forEach((e=>e.isDeleted=!0)),r.style.verticalAlign="middle";const{parents:a,children:u,leftFriends:o,rightFriends:l,siblings:D}=this.getNeighbors(s);this.nodesMap=new Map,this.links=new Os(this.plugin),this.layouts=[];const d=n.embedCentralNode&&!s.isVirtual&&!s.isFolder&&!s.isTag,h=n.centerEmbedWidth,c=n.centerEmbedHeight,g=Object.assign(Object.assign({},n.baseNodeStyle),n.centralNodeStyle),E=n.baseNodeStyle;r.style.fontFamily=E.fontFamily,r.style.fontSize=E.fontSize,this.rootNode=new V({ea:r,page:s,isInferred:!1,isCentral:!0,isSibling:!1,friendGateOnLeft:!0,isEmbeded:d,embeddedElementIds:e?null===(i=this.rootNode)||void 0===i?void 0:i.embeddedElementIds:void 0});const{rootNodeDimensions:p,rootWidth:F,rootNodeLength:m,childrenOrigoY:C,childWidth:y,childLength:S,childrenCols:b,parentsOrigoY:A,parentWidth:T,parentLabelLength:v,parentCols:N,leftFriendOrigoX:w,leftFriendWidth:L,leftFriendLength:O,leftFriendCols:_,rightFriendOrigoX:I,rightFriendWidth:k,rightFriendLength:B,rightFriendCols:x,siblingsOrigoX:R,siblingsOrigoY:M,siblingsNodeWidth:z,siblingsNodeHeight:H,siblingsLabelLength:P,siblingsCols:G}=this.calculateLayoutParams({centralPage:s,parents:a,children:u,leftFriends:o,rightFriends:l,siblings:D,isCenterEmbedded:d,centerEmbedHeight:c,centerEmbedWidth:h,style:g,rootNode:this.rootNode}),Y=new ws({origoX:0,origoY:d?c/2:0,top:null,bottom:null,columns:1,columnWidth:d?h:F,rowHeight:d?c:p.height,maxLabelLength:m});this.layouts.push(Y);const j=new ws({origoX:0,origoY:C,top:0,bottom:null,columns:b,columnWidth:y,rowHeight:this.nodeHeight,maxLabelLength:S});this.layouts.push(j);const W=new ws({origoX:-w,origoY:0,top:null,bottom:null,columns:_,columnWidth:L,rowHeight:this.nodeHeight,maxLabelLength:O});this.layouts.push(W);const U=new ws({origoX:I,origoY:0,top:null,bottom:null,columns:x,columnWidth:k,rowHeight:this.nodeHeight,maxLabelLength:B});this.layouts.push(U);const K=new ws({origoX:0,origoY:-A,top:null,bottom:-2*this.nodeHeight,columns:N,columnWidth:T,rowHeight:this.nodeHeight,maxLabelLength:v});this.layouts.push(K);const q=new ws({origoX:R,origoY:-M,top:null,bottom:null,columns:G,columnWidth:z,rowHeight:H,maxLabelLength:P});this.layouts.push(q),s.maxLabelLength=m,this.nodesMap.set(s.path,this.rootNode),Y.nodes.push(this.rootNode),this.addNodes({neighbours:a,layout:K,isCentral:!1,isSibling:!1,friendGateOnLeft:!0}),this.addNodes({neighbours:u,layout:j,isCentral:!1,isSibling:!1,friendGateOnLeft:!0}),this.addNodes({neighbours:o,layout:W,isCentral:!1,isSibling:!1,friendGateOnLeft:!1}),this.addNodes({neighbours:l,layout:U,isCentral:!1,isSibling:!1,friendGateOnLeft:!0}),n.renderSiblings&&this.addNodes({neighbours:D,layout:q,isCentral:!1,isSibling:!0,friendGateOnLeft:!0});const Z=(e,t,i)=>{t.forEach((t=>{const s=this.nodesMap.get(t.page.path);s&&this.links.addLink(e,s,i,t.relationType,t.typeDefinition,t.linkDirection,r,n)}))};Array.from(this.nodesMap.values()).forEach((e=>{Z(e,e.page.getChildren(),t.CHILD),Z(e,e.page.getParents(),t.PARENT),Z(e,e.page.getLeftFriends(),t.LEFT),Z(e,e.page.getPreviousFriends(),t.LEFT),Z(e,e.page.getRightFriends(),t.RIGHT),Z(e,e.page.getNextFriends(),t.RIGHT)})),r.style.opacity=100,await Promise.all(this.layouts.map((async e=>await e.render())));const $=r.getElements();this.links.render(Array.from(this.toolsPanel.linkTagFilter.selectedLinks));const X=r.getElements().filter((e=>!$.includes(e))).concat($).reduce(((e,t)=>(e[t.id]=t,e)),{});r.elementsDict=X;const J=r.getExcalidrawAPI();r.addElementsToView(!1,!1).then((()=>{J.updateScene({appState:{viewBackgroundColor:n.backgroundColor}}),r.targetView.clearDirty()})),n.allowAutozoom&&!e&&setTimeout((()=>J.zoomToFit(r.getViewElements(),n.maxZoom,.15)),100),this.toolsPanel.rerender(),this.focusSearchAfterInitiation&&n.allowAutofocuOnSearch&&(this.toolsPanel.searchElement.focus(),this.focusSearchAfterInitiation=!1),this.blockUpdateTimer=!1}isCentralLeafStillThere(){var e,t,i;const n=this.plugin.settings;return!(null===app.workspace.getLeafById(null===(e=this.centralLeaf)||void 0===e?void 0:e.id))&&(null===(i=null===(t=this.centralLeaf.view)||void 0===t?void 0:t.file)||void 0===i?void 0:i.path)!==n.excalibrainFilepath}async brainEventHandler(e,t=!1){var i;const s=this.plugin.settings;if(!(null===(i=this.ea.targetView)||void 0===i?void 0:i.file)||this.ea.targetView.file.path!==s.excalibrainFilepath)return void this.unloadScene();if(this.disregardLeafChange)return;if(!t&&!s.autoOpenCentralDocument)return;if(this.blockUpdateTimer=!0,await sleep(100),this.pinLeaf&&!this.isCentralLeafStillThere()&&(this.pinLeaf=!1,this.toolsPanel.rerender()),this.pinLeaf&&e!==this.centralLeaf)return;if(!((null==e?void 0:e.view)&&e.view instanceof n.FileView&&e.view.file))return void(this.blockUpdateTimer=!1);const r=e.view.file;if(r.path===this.ea.targetView.file.path)return this.vaultFileChanged&&(this.zoomToFitOnNextBrainLeafActivate=!1,await this.reRender(!0)),this.zoomToFitOnNextBrainLeafActivate&&(this.zoomToFitOnNextBrainLeafActivate=!1,s.allowAutozoom&&this.ea.getExcalidrawAPI().zoomToFit(null,s.maxZoom,.15)),void(this.blockUpdateTimer=!1);const a=this.getCentralPage();a&&a.path===r.path&&r.stat.mtime===a.mtime?this.blockUpdateTimer=!1:(this.plugin.pages.get(r.path)||await this.plugin.createIndex(),this.plugin.navigationHistory.addToHistory(r.path),this.centralPagePath=r.path,this.centralPageFile=r,this.centralLeaf=e,this.render())}async addEventHandler(){const e=()=>{this.vaultFileChanged=!0},t=e=>this.brainEventHandler(e);this.app.workspace.on("active-leaf-change",t),this.removeEH=()=>app.workspace.off("active-leaf-change",t),this.setTimer(),this.app.vault.on("rename",e),this.removeOnRename=()=>app.vault.off("rename",e),this.app.vault.on("modify",e),this.removeOnModify=()=>app.vault.off("modify",e),this.app.vault.on("create",e),this.removeOnCreate=()=>app.vault.off("create",e),this.app.vault.on("delete",e),this.removeOnDelete=()=>app.vault.off("delete",e);const i=[];app.workspace.iterateAllLeaves((e=>{e.view instanceof n.FileView&&e.view.file&&e.view.file.path!==this.ea.targetView.file.path&&i.push(e)})),await this.plugin.createIndex();let s=i[0];if(i.length>0){const e=app.workspace.getLastOpenFiles()[0];if(e&&""!==e){const t=i.filter((t=>{var i,n;return(null===(n=null===(i=t.view)||void 0===i?void 0:i.file)||void 0===n?void 0:n.path)===e}));t.length>0&&(s=t[0])}E(this.plugin.EA),this.brainEventHandler(s,!0)}else if(this.plugin.navigationHistory.length>0){const e=this.plugin.navigationHistory.last;setTimeout((()=>this.renderGraphForPath(e,!0)),100)}}setTimer(){this.removeTimer&&(this.removeTimer(),this.removeTimer=void 0);const e=setInterval((async()=>{!this.blockUpdateTimer&&this.vaultFileChanged&&(this.vaultFileChanged=!1,await this.plugin.createIndex(),this.centralPagePath&&(this.getCentralPage()||this.centralLeaf&&this.centralLeaf.view&&this.centralLeaf.view.file&&(this.centralPageFile=this.centralLeaf.view.file,this.centralPagePath=this.centralPageFile.path)),this.render(!0))}),this.plugin.settings.indexUpdateInterval);this.removeTimer=()=>clearInterval(e)}unloadScene(e=!0,t=!1){var i,s;if(this.removeEH&&(this.removeEH(),this.removeEH=void 0),this.removeTimer&&(this.removeTimer(),this.removeTimer=void 0),this.removeOnRename&&(this.removeOnRename(),this.removeOnRename=void 0),this.removeOnModify&&(this.removeOnModify(),this.removeOnModify=void 0),this.removeOnCreate&&(this.removeOnCreate(),this.removeOnCreate=void 0),this.removeOnDelete&&(this.removeOnDelete(),this.removeOnDelete=void 0),this.ea.targetView&&isBoolean(this.ea.targetView.linksAlwaysOpenInANewPane)&&(this.ea.targetView.linksAlwaysOpenInANewPane=!1),this.ea.targetView&&isBoolean(this.ea.targetView.allowFrameButtonsInViewMode)&&(this.ea.targetView.allowFrameButtonsInViewMode=!1),this.ea.targetView&&this.ea.targetView.excalidrawAPI)try{this.ea.targetView.semaphores.saving=!1,this.ea.targetView.excalidrawAPI.setMobileModeAllowed(!0),this.ea.targetView.excalidrawAPI.updateScene({appState:{viewModeEnabled:!1}})}catch(e){}if(this.ea.targetView&&this.ea.targetView._loaded)try{this.ea.deregisterThisAsViewEA()}catch(e){}e&&setTimeout((async()=>{await this.plugin.loadSettings(),this.plugin.settings.navigationHistory=[...this.plugin.navigationHistory.get()],await this.plugin.saveSettings()}),400),null===(i=this.toolsPanel)||void 0===i||i.terminate(),this.toolsPanel=void 0,null===(s=this.historyPanel)||void 0===s||s.terminate(),this.historyPanel=void 0,this.ea.targetView=void 0,this.leaf=void 0,this.centralLeaf=void 0,this.centralPagePath=void 0,this.centralPageFile=void 0,this.terminated=!0,this.app.plugins.plugins["obsidian-excalidraw-plugin"]||(this.plugin.EA=null),t||new n.Notice("Brain Graph Off");const r=this.app.workspace.getMostRecentLeaf();r&&this.app.workspace.setActiveLeaf(r,{focus:!0})}}class na extends n.EditorSuggest{constructor(e){super(e.app),this.getKeys=()=>{const e=this.plugin.settings.hierarchy,t=this.suggestType;return"all"===t?[...e.hidden,...e.parents,...e.children,...e.leftFriends,...e.rightFriends,...e.previous,...e.next,this.plugin.settings.primaryTagField].sort(((e,t)=>e.toLowerCase()>t.toLowerCase()?1:-1)):"parent"===t?e.parents:"child"===t?e.children:"rightFriend"===t?e.rightFriends:"leftFriend"===t?e.leftFriends:"previous"===t?e.previous:e.next},this.getTrigger=()=>{const e=this.suggestType,t=this.plugin.settings;return"all"===e?t.ontologySuggesterTrigger:"parent"===e?t.ontologySuggesterParentTrigger:"child"===e?t.ontologySuggesterChildTrigger:"rightFriend"===e?t.ontologySuggesterRightFriendTrigger:"leftFriend"===e?t.ontologySuggesterLeftFriendTrigger:"previous"===e?t.ontologySuggesterPreviousTrigger:t.ontologySuggesterNextTrigger},this.getSuggestions=e=>{const t=e.query.toLowerCase();return this.getKeys().filter((e=>e.toLowerCase().includes(t)))},this.plugin=e}onTrigger(e,t,i){const n=this.plugin.settings;if(n.allowOntologySuggester){const i=t.getLine(e.line).substring(0,e.ch),s=new RegExp(`(^${n.ontologySuggesterTrigger}|\\${n.ontologySuggesterMidSentenceTrigger+n.ontologySuggesterTrigger}|^${n.ontologySuggesterParentTrigger}|\\${n.ontologySuggesterMidSentenceTrigger+n.ontologySuggesterParentTrigger}|^${n.ontologySuggesterChildTrigger}|\\${n.ontologySuggesterMidSentenceTrigger+n.ontologySuggesterChildTrigger}|^${n.ontologySuggesterLeftFriendTrigger}|\\${n.ontologySuggesterMidSentenceTrigger+n.ontologySuggesterLeftFriendTrigger}|^${n.ontologySuggesterRightFriendTrigger}|\\${n.ontologySuggesterMidSentenceTrigger+n.ontologySuggesterRightFriendTrigger}|^${n.ontologySuggesterPreviousTrigger}|\\${n.ontologySuggesterMidSentenceTrigger+n.ontologySuggesterPreviousTrigger}|^${n.ontologySuggesterNextTrigger}|\\${n.ontologySuggesterMidSentenceTrigger+n.ontologySuggesterNextTrigger})([^\\s\\${n.ontologySuggesterTrigger}]*)`,"g");let r,a,u;const o=i.matchAll(s);for(;!(u=o.next()).done;)a=u.value[1],r=u.value[2];if(void 0!==r){switch(a){case n.ontologySuggesterTrigger:case n.ontologySuggesterMidSentenceTrigger+n.ontologySuggesterTrigger:this.suggestType="all";break;case n.ontologySuggesterParentTrigger:case n.ontologySuggesterMidSentenceTrigger+n.ontologySuggesterParentTrigger:this.suggestType="parent";break;case n.ontologySuggesterChildTrigger:case n.ontologySuggesterMidSentenceTrigger+n.ontologySuggesterChildTrigger:this.suggestType="child";break;case n.ontologySuggesterRightFriendTrigger:case n.ontologySuggesterMidSentenceTrigger+n.ontologySuggesterRightFriendTrigger:this.suggestType="rightFriend";break;case n.ontologySuggesterPreviousTrigger:case n.ontologySuggesterMidSentenceTrigger+n.ontologySuggesterPreviousTrigger:this.suggestType="previous";break;case n.ontologySuggesterNextTrigger:case n.ontologySuggesterMidSentenceTrigger+n.ontologySuggesterNextTrigger:this.suggestType="next";break;case n.ontologySuggesterLeftFriendTrigger:case n.ontologySuggesterMidSentenceTrigger+n.ontologySuggesterLeftFriendTrigger:this.suggestType="leftFriend";break;default:this.suggestType="all"}return this.latestTriggerInfo={end:e,start:{ch:e.ch-r.length-this.getTrigger().length,line:e.line},query:r},this.latestTriggerInfo}}return null}renderSuggestion(e,t){t.createEl("b",{text:e})}selectSuggestion(e){const{context:t}=this;if(t){const i=this.plugin.settings.boldFields,n=(i?"**":"")+e+(i?"**":"")+":: ";if(t.editor.replaceRange(n,this.latestTriggerInfo.start,this.latestTriggerInfo.end),this.latestTriggerInfo.start.ch===this.latestTriggerInfo.end.ch){const e=this.latestTriggerInfo.end;e.ch+=n.length,t.editor.setCursor(e)}}}}var sa;!function(e){e.Hidden="hidden",e.Parent="parent",e.Child="child",e.LeftFriend="leftFriend",e.RightFriend="rightFriend",e.Previous="previous",e.Next="next"}(sa||(sa={}));class ra extends n.Modal{constructor(e,t){super(e),this.plugin=t,this.ontology=null}getCurrentOntology(){const{settings:e}=this.plugin,t=this.fieldName;return e.hierarchy.hidden.includes(t)?sa.Hidden:e.hierarchy.parents.includes(t)?sa.Parent:e.hierarchy.children.includes(t)?sa.Child:e.hierarchy.leftFriends.includes(t)?sa.LeftFriend:e.hierarchy.rightFriends.includes(t)?sa.RightFriend:e.hierarchy.previous.includes(t)?sa.Previous:e.hierarchy.next.includes(t)?sa.Next:null}async setOntology(e){if(this.ontology===e)return;const{settings:t}=this.plugin,i=this.plugin;switch(this.ontology){case sa.Hidden:t.hierarchy.hidden=t.hierarchy.hidden.filter((e=>e!==this.fieldName)),i.hierarchyLowerCase.hidden=[],t.hierarchy.hidden.forEach((e=>i.hierarchyLowerCase.hidden.push(e.toLowerCase().replaceAll(" ","-"))));break;case sa.Parent:t.hierarchy.parents=t.hierarchy.parents.filter((e=>e!==this.fieldName)),i.hierarchyLowerCase.parents=[],t.hierarchy.parents.forEach((e=>i.hierarchyLowerCase.parents.push(e.toLowerCase().replaceAll(" ","-"))));break;case sa.Child:t.hierarchy.children=t.hierarchy.children.filter((e=>e!==this.fieldName)),i.hierarchyLowerCase.children=[],t.hierarchy.children.forEach((e=>i.hierarchyLowerCase.children.push(e.toLowerCase().replaceAll(" ","-"))));break;case sa.LeftFriend:t.hierarchy.leftFriends=t.hierarchy.leftFriends.filter((e=>e!==this.fieldName)),i.hierarchyLowerCase.leftFriends=[],t.hierarchy.leftFriends.forEach((e=>i.hierarchyLowerCase.leftFriends.push(e.toLowerCase().replaceAll(" ","-"))));break;case sa.RightFriend:t.hierarchy.rightFriends=t.hierarchy.rightFriends.filter((e=>e!==this.fieldName)),i.hierarchyLowerCase.rightFriends=[],t.hierarchy.rightFriends.forEach((e=>i.hierarchyLowerCase.rightFriends.push(e.toLowerCase().replaceAll(" ","-"))));break;case sa.Previous:t.hierarchy.previous=t.hierarchy.previous.filter((e=>e!==this.fieldName)),i.hierarchyLowerCase.previous=[],t.hierarchy.previous.forEach((e=>i.hierarchyLowerCase.previous.push(e.toLowerCase().replaceAll(" ","-"))));break;case sa.Next:t.hierarchy.next=t.hierarchy.next.filter((e=>e!==this.fieldName)),i.hierarchyLowerCase.next=[],t.hierarchy.next.forEach((e=>i.hierarchyLowerCase.next.push(e.toLowerCase().replaceAll(" ","-"))))}switch(e){case sa.Hidden:t.hierarchy.hidden.push(this.fieldName),t.hierarchy.hidden=t.hierarchy.hidden.sort(((e,t)=>e.toLowerCase()<t.toLowerCase()?-1:1)),i.hierarchyLowerCase.hidden=[],t.hierarchy.hidden.forEach((e=>i.hierarchyLowerCase.hidden.push(e.toLowerCase().replaceAll(" ","-"))));break;case sa.Parent:t.hierarchy.parents.push(this.fieldName),t.hierarchy.parents=t.hierarchy.parents.sort(((e,t)=>e.toLowerCase()<t.toLowerCase()?-1:1)),i.hierarchyLowerCase.parents=[],t.hierarchy.parents.forEach((e=>i.hierarchyLowerCase.parents.push(e.toLowerCase().replaceAll(" ","-"))));break;case sa.Child:t.hierarchy.children.push(this.fieldName),t.hierarchy.children=t.hierarchy.children.sort(((e,t)=>e.toLowerCase()<t.toLowerCase()?-1:1)),i.hierarchyLowerCase.children=[],t.hierarchy.children.forEach((e=>i.hierarchyLowerCase.children.push(e.toLowerCase().replaceAll(" ","-"))));break;case sa.LeftFriend:t.hierarchy.leftFriends.push(this.fieldName),t.hierarchy.leftFriends=t.hierarchy.leftFriends.sort(((e,t)=>e.toLowerCase()<t.toLowerCase()?-1:1)),i.hierarchyLowerCase.leftFriends=[],t.hierarchy.leftFriends.forEach((e=>i.hierarchyLowerCase.leftFriends.push(e.toLowerCase().replaceAll(" ","-"))));break;case sa.RightFriend:t.hierarchy.rightFriends.push(this.fieldName),t.hierarchy.rightFriends=t.hierarchy.rightFriends.sort(((e,t)=>e.toLowerCase()<t.toLowerCase()?-1:1)),i.hierarchyLowerCase.rightFriends=[],t.hierarchy.rightFriends.forEach((e=>i.hierarchyLowerCase.rightFriends.push(e.toLowerCase().replaceAll(" ","-"))));break;case sa.Previous:t.hierarchy.previous.push(this.fieldName),t.hierarchy.previous=t.hierarchy.previous.sort(((e,t)=>e.toLowerCase()<t.toLowerCase()?-1:1)),i.hierarchyLowerCase.previous=[],t.hierarchy.previous.forEach((e=>i.hierarchyLowerCase.previous.push(e.toLowerCase().replaceAll(" ","-"))));break;case sa.Next:t.hierarchy.next.push(this.fieldName),t.hierarchy.next=t.hierarchy.next.sort(((e,t)=>e.toLowerCase()<t.toLowerCase()?-1:1)),i.hierarchyLowerCase.next=[],t.hierarchy.next.forEach((e=>i.hierarchyLowerCase.next.push(e.toLowerCase().replaceAll(" ","-"))))}await this.plugin.saveSettings(),i.scene&&!i.scene.terminated&&(i.scene.vaultFileChanged=!0),new n.Notice(`Added ${this.fieldName} as ${e}`),this.fieldName=null,this.close()}async show(e){await this.plugin.loadSettings(),this.fieldName=e,this.ontology=this.getCurrentOntology(),this.open()}async addFieldToOntology(e,t){await this.plugin.loadSettings(),this.fieldName=t,this.ontology=this.getCurrentOntology(),await this.setOntology(e),this.fieldName=null}open(){if(!this.fieldName)return;const{contentEl:e,titleEl:t}=this;t.setText(this.fieldName),e.createEl("p",{text:z("ADD_TO_ONTOLOGY_MODAL_DESC")});const i=new n.Setting(e).addButton((e=>{e.buttonEl.style.flex="1 0 calc(33.33% - var(--size-4-2))",e.setButtonText(z("HIDDEN_NAME")),this.ontology===sa.Hidden&&e.setCta(),e.onClick((()=>this.setOntology(sa.Hidden)))})).addButton((e=>{e.buttonEl.style.flex="1 0 calc(33.33% - var(--size-4-2))",e.setButtonText(z("PARENTS_NAME")),this.ontology===sa.Parent&&e.setCta(),e.onClick((()=>this.setOntology(sa.Parent)))})).addButton((e=>{e.buttonEl.style.flex="1 0 calc(33.33% - var(--size-4-2))",e.setButtonText(z("CHILDREN_NAME")),this.ontology===sa.Child&&e.setCta(),e.onClick((()=>this.setOntology(sa.Child)))})).addButton((e=>{e.buttonEl.style.flex="1 0 calc(33.33% - var(--size-4-2))",e.setButtonText(z("LEFT_FRIENDS_NAME")),this.ontology===sa.LeftFriend&&e.setCta(),e.onClick((()=>this.setOntology(sa.LeftFriend)))})).addButton((e=>{e.buttonEl.style.flex="1 0 calc(33.33% - var(--size-4-2))",e.setButtonText(z("RIGHT_FRIENDS_NAME")),this.ontology===sa.RightFriend&&e.setCta(),e.onClick((()=>this.setOntology(sa.RightFriend)))})).addButton((e=>{e.buttonEl.style.flex="1 0 calc(33.33% - var(--size-4-2))",e.setButtonText(z("PREVIOUS_NAME")),this.ontology===sa.Previous&&e.setCta(),e.onClick((()=>this.setOntology(sa.Previous)))})).addButton((e=>{e.buttonEl.style.flex="1 0 calc(33.33% - var(--size-4-2))",e.setButtonText(z("NEXT_NAME")),this.ontology===sa.Next&&e.setCta(),e.onClick((()=>this.setOntology(sa.Next)))}));i.controlEl.style.flexWrap="wrap",i.controlEl.style.justifyContent="space-between",super.open()}onClose(){const{contentEl:e}=this;e.empty()}}class aa{constructor(e){this.history=[],this.currentPosition=-1,this.history=e,this.currentPosition=e.length-1}setNavigateButtons(e){this.navigateButtons=e}addToHistory(e){if(this.navigateButtons&&this.navigateButtons.forEach((e=>e.updateButton())),this.history[this.currentPosition]===e)return;const t=this.history.indexOf(e);t>-1&&this.history.splice(t,1),this.history.push(e),this.history.length>50&&this.history.shift(),this.currentPosition=this.history.length-1}get length(){return this.history.length}get last(){return this.history[this.currentPosition]}get(){return this.history}getPrevious(){return this.currentPosition>0?(this.currentPosition--,this.history[this.currentPosition]):null}getNext(){return this.currentPosition<this.history.length-1?(this.currentPosition++,this.history[this.currentPosition]):null}hasNext(){return this.currentPosition<this.history.length-1}hasPrevious(){return this.currentPosition>0}}const ua="YYYY-MM-DD";function oa(){var e,t,i,n;try{const{internalPlugins:s,plugins:r}=window.app;if(function(e){var t,i;const n=window.app.plugins.getPlugin("periodic-notes");return n&&(null===(i=null===(t=n.settings)||void 0===t?void 0:t.daily)||void 0===i?void 0:i.enabled)}()){const{format:i,folder:n,template:s}=(null===(t=null===(e=r.getPlugin("periodic-notes"))||void 0===e?void 0:e.settings)||void 0===t?void 0:t.daily)||{};return{format:i||ua,folder:(null==n?void 0:n.trim())||"",template:(null==s?void 0:s.trim())||""}}const{folder:a,format:u,template:o}=(null===(n=null===(i=s.getPluginById("daily-notes"))||void 0===i?void 0:i.instance)||void 0===n?void 0:n.options)||{};return{format:u||ua,folder:(null==a?void 0:a.trim())||"",template:(null==o?void 0:o.trim())||""}}catch(e){console.info("No custom daily note settings found!",e)}}class la extends n.Plugin{constructor(e,t){super(e,t),this.hierarchyLowerCase={hidden:[],parents:[],children:[],leftFriends:[],rightFriends:[],previous:[],next:[]},this.scene=null,this.pluginLoaded=!1,this.starred=[],this.focusSearchAfterInitiation=!1,this.starred=[new A(null,"Initializing index, please wait",null,this,!1,!1,"Initializing index, please wait")],this.addToOntologyModal=new ra(e,this)}async onload(){await this.loadSettings(),this.dailyNoteSettings=oa(),this.navigationHistory=new aa(this.settings.navigationHistory),this.addSettingTab(new X(this.app,this)),this.registerEditorSuggest(new na(this)),this.registerEvents(),this.urlParser=new r(this),this.app.workspace.onLayoutReady((()=>{this.urlParser.init(),this.DVAPI=vs(),this.DVAPI?(this.EA=w(),this.EA?this.EA.verifyMinimumPluginVersion(_)?(this.registerCommands(),this.registerExcalidrawAutomateHooks(),this.pluginLoaded=!0):new H(this.app,"⚠ ExcaliBrain Disabled: Please upgrade Excalidraw and try again",z("EXCALIDRAW_MINAPP_VERSION")).show((async e=>{new n.Notice("Disabling ExcaliBrain Plugin",8e3),c({fn:this.onload,where:"main.ts/onload()",message:"ExcaliBrain requires a new version of Excalidraw"}),this.app.plugins.disablePlugin(O)})):new H(this.app,"⚠ ExcaliBrain Disabled: Excalidraw Plugin not found",z("EXCALIDRAW_NOT_FOUND")).show((async e=>{new n.Notice("Disabling ExcaliBrain Plugin",8e3),c({fn:this.onload,where:"main.ts/onload()",message:"Excalidraw not found"}),this.app.plugins.disablePlugin(O)}))):new H(this.app,"⚠ ExcaliBrain Disabled: DataView Plugin not found",z("DATAVIEW_NOT_FOUND")).show((async e=>{new n.Notice("Disabling ExcaliBrain Plugin",8e3),c({fn:this.onload,where:"main.ts/onload()",message:"Dataview not found"}),this.app.plugins.disablePlugin(O)}))}))}registerEvents(){this.registerEvent(this.app.workspace.on("editor-menu",this.handleEditorMenu,this))}getFieldName(e){let t=e.getLine(e.getCursor().line).substring(0,e.getCursor().ch);const i=/(?:^|[(\[])(?:==|\*\*|~~|\*|_|__)?([^\:\]\()]*?)(?:==|\*\*|~~|\*|_|__)?::/g;let n,s=null;for(;null!==(n=i.exec(t));)s=n;if(!s)for(t=e.getLine(e.getCursor().line);null!==(n=i.exec(t));)s=n;return null!==s?s[1]:null}handleEditorMenu(e,t,i){const n=this.getFieldName(t);n&&e.addItem((e=>{e.setTitle(`Add "${n}" to ExcaliBrain Ontology`).setIcon("plus").onClick((()=>{this.addToOntologyModal.show(n)}))}))}async createIndex(){this.pages=new F(this),this.lowercasePathMap=new Map;let t=0;for(;this.app.metadataCache.inProgressTaskCount>0||this.DVAPI.index.importer.reloadQueue.length>0;)t++%100==10&&new n.Notice("ExcaliBrain is waiting for Dataview to update its index",1e3),await sleep(100);for(t=0;!this.urlParser.initalized;)t++%100==10&&new n.Notice("ExcaliBrain is waiting for URLParser to finish indexing",1e3),await sleep(100);this.urlParser.hosts.forEach((e=>{this.pages.add(e,new A(this.pages,e,null,this,!1,!1,e,e))}));const s=(t,r)=>{t.children.forEach((t=>{if(t instanceof n.TFolder){const n=new A(this.pages,"folder:"+t.path,null,this,!0,!1,t.name);return this.pages.add("folder:"+t.path,n),n.addParent(r,e.DEFINED,i.TO,"file-tree"),r.addChild(n,e.DEFINED,i.FROM,"file-tree"),void s(t,n)}{this.lowercasePathMap.set(t.path.toLowerCase(),t.path);const n=new A(this.pages,t.path,t,this);this.pages.add(t.path,n),n.addParent(r,e.DEFINED,i.TO,"file-tree"),r.addChild(n,e.DEFINED,i.FROM,"file-tree")}}))},r=app.vault.getRoot(),a=new A(this.pages,"folder:/",null,this,!0,!1,"/");this.pages.add("folder:/",a),s(r,a);const u=Object.keys(app.metadataCache.getTags()).map((e=>e.substring(1).split("/")));u.forEach((t=>{const n=[];t.forEach(((t,s,r)=>{const a=r.slice(0,s+1).join("/"),u="tag:"+a;let o=this.pages.get(u);if(o)n.push(o);else if(o=new A(this.pages,u,null,this,!1,!0,this.settings.showFullTagName?a:t),this.pages.add(u,o),n.push(o),s>0){const t=n[s-1];o.addParent(t,e.DEFINED,i.FROM,"tag-tree"),t.addChild(o,e.DEFINED,i.TO,"tag-tree")}}))})),this.pages.addUnresolvedLinks(),this.pages.addResolvedLinks(),this.pages.addPageURLs();const o=this;setTimeout((async()=>{const e=this.app.internalPlugins.getPluginById("bookmarks");if(!e){const e=this.app.internalPlugins.getPluginById("starred");if(!e)return;return void(o.starred=(await e.loadData()).items.filter((e=>"file"===e.type)).map((e=>e.path)).filter((e=>e!==o.settings.excalibrainFilepath&&o.pages.has(e))).map((e=>o.pages.get(e))))}e._loaded||await e.loadData();const t=e=>{if(!e)return;let i=e.filter((e=>"file"===e.type)).map((e=>e.path)).filter((e=>e!==o.settings.excalibrainFilepath&&o.pages.has(e))).map((e=>o.pages.get(e)));return i=i.concat(e.filter((e=>"folder"===e.type)).map((e=>e.path)).filter((e=>e!==o.settings.excalibrainFilepath&&o.pages.has(`folder:${e}`))).map((e=>o.pages.get(`folder:${e}`)))),e.filter((e=>"group"===e.type)).forEach((e=>i=i.concat(t(e.items)))),i};o.starred=t(e.instance.items)}))}excalidrawAvailable(){var e,t;if(this.app.plugins.plugins["obsidian-excalidraw-plugin"]===this.EA.plugin)return!0;const i=w(null===(t=null===(e=this.scene)||void 0===e?void 0:e.leaf)||void 0===t?void 0:t.view);return i?(this.EA=i,this.registerExcalidrawAutomateHooks(),!0):(this.EA=null,this.scene&&this.scene.unloadScene(),new n.Notice("ExcaliBrain: Please start Excalidraw and try again.",4e3),!1)}revealBrainLeaf(){var e;if(!this.scene||this.scene.terminated)return;app.workspace.revealLeaf(this.scene.leaf);const t=app.plugins.getPlugin("obsidian-hover-editor");if(t){const e=t.activePopovers.filter((e=>e.leaves()[0]===this.scene.leaf))[0];e&&0===this.scene.leaf.view.containerEl.offsetHeight&&e.titleEl.querySelector("a.popover-action.mod-minimize").click()}const i=null===(e=this.scene.toolsPanel)||void 0===e?void 0:e.searchElement;null==i||i.focus()}addFieldToOntology(e,t){this.addToOntologyModal.addFieldToOntology(t,e)}registerCommands(){const e=(e,t)=>{var i;const s=null===(i=app.workspace.activeLeaf)||void 0===i?void 0:i.view;let r;if(!s)return!1;if("excalidraw"===s.getViewType()){const e=this.EA.getActiveEmbeddableViewOrEditor(s);if(!e)return!1;"view"in e&&"editor"in e.view?r=e.view.editor:"editor"in e&&(r=e.editor)}if(s instanceof n.MarkdownView&&"source"===s.getMode()&&(r=s.editor),!r)return!1;const a=this.getFieldName(r);return!(!a||!e&&("select"===t?(this.addToOntologyModal.show(a),0):(this.addFieldToOntology(a,t),0)))};this.addCommand({id:"excalibrain-addHiddenField",name:z("COMMAND_ADD_HIDDEN_FIELD"),checkCallback:t=>e(t,sa.Hidden)}),this.addCommand({id:"excalibrain-addParentField",name:z("COMMAND_ADD_PARENT_FIELD"),checkCallback:t=>e(t,sa.Parent)}),this.addCommand({id:"excalibrain-addChildField",name:z("COMMAND_ADD_CHILD_FIELD"),checkCallback:t=>e(t,sa.Child)}),this.addCommand({id:"excalibrain-addLeftFriendField",name:z("COMMAND_ADD_LEFT_FRIEND_FIELD"),checkCallback:t=>e(t,sa.LeftFriend)}),this.addCommand({id:"excalibrain-addRightFriendField",name:z("COMMAND_ADD_RIGHT_FRIEND_FIELD"),checkCallback:t=>e(t,sa.RightFriend)}),this.addCommand({id:"excalibrain-addPreviousField",name:z("COMMAND_ADD_PREVIOUS_FIELD"),checkCallback:t=>e(t,sa.Previous)}),this.addCommand({id:"excalibrain-addNextField",name:z("COMMAND_ADD_NEXT_FIELD"),checkCallback:t=>e(t,sa.Next)}),this.addCommand({id:"excalibrain-selectOntology",name:z("COMMAND_ADD_ONTOLOGY_MODAL"),checkCallback:t=>e(t,"select")}),this.addCommand({id:"excalibrain-start",name:z("COMMAND_START"),checkCallback:e=>{var t;if(e)return this.excalidrawAvailable();if(!this.excalidrawAvailable())return;if(this.scene&&!this.scene.terminated){if(this.app.workspace.getLeafById(null===(t=this.scene.leaf)||void 0===t?void 0:t.id))return void this.revealBrainLeaf();this.scene.unloadScene(!1,!0)}const i=this.getBrainLeaf();if(i)return this.scene=new ia(this,!0,i),this.scene.initialize(!0),void this.revealBrainLeaf();this.focusSearchAfterInitiation=!0,ia.openExcalidrawLeaf(window.ExcalidrawAutomate,this.settings,i)}}),this.addCommand({id:"excalibrain-start-popout",name:z("COMMAND_START_POPOUT"),checkCallback:e=>{var t;if(e)return!this.EA.DEVICE.isMobile&&this.excalidrawAvailable();if(!this.excalidrawAvailable()||this.EA.DEVICE.isMobile)return;if(this.scene&&!this.scene.terminated){if(this.app.workspace.getLeafById(null===(t=this.scene.leaf)||void 0===t?void 0:t.id))return void this.revealBrainLeaf();this.scene.unloadScene(!1,!0)}const i=this.getBrainLeaf();if(i)return this.scene=new ia(this,!0,i),this.scene.initialize(!0),void this.revealBrainLeaf();this.focusSearchAfterInitiation=!0,ia.openExcalidrawLeaf(window.ExcalidrawAutomate,this.settings,app.workspace.openPopoutLeaf())}}),this.addCommand({id:"excalibrain-open-hover",name:z("COMMAND_START_HOVER"),checkCallback:e=>{var t;const i=this.app.plugins.getPlugin("obsidian-hover-editor");if(e)return i&&this.excalidrawAvailable();if(this.excalidrawAvailable()&&i){if(this.scene&&!this.scene.terminated){if(this.app.workspace.getLeafById(null===(t=this.scene.leaf)||void 0===t?void 0:t.id))return void this.revealBrainLeaf();this.scene.unloadScene(!1,!0)}try{const e=this.getBrainLeaf();if(e){const t=i.activePopovers.filter((t=>t.leaves()[0]===e))[0];if(t)return app.workspace.revealLeaf(e),0===e.view.containerEl.offsetHeight&&t.titleEl.querySelector("a.popover-action.mod-maximize").click(),this.scene=new ia(this,!0,e),void this.scene.initialize(!0)}const t=i.spawnPopover(void 0,(()=>{if(this.app.workspace.setActiveLeaf(t,!1,!0),!i.activePopovers.filter((e=>e.leaves()[0]===t))[0])return new n.Notice(z("HOVER_EDITOR_ERROR"),6e3),!1;setTimeout((()=>app.commands.executeCommandById("obsidian-hover-editor:snap-active-popover-to-viewport"))),this.focusSearchAfterInitiation=!0,ia.openExcalidrawLeaf(window.ExcalidrawAutomate,this.settings,t)}))}catch(e){new n.Notice(z("HOVER_EDITOR_ERROR"),6e3)}}}})}getBrainLeaf(){let e;return this.app.workspace.iterateAllLeaves((t=>{t.view&&this.EA.isExcalidrawView(t.view)&&t.view instanceof n.TextFileView&&t.view.file.path===this.settings.excalibrainFilepath&&(e=t)})),e}registerExcalidrawAutomateHooks(){this.EA.onViewModeChangeHook=e=>{var t;this.EA.targetView&&(null===(t=this.EA.targetView.file)||void 0===t?void 0:t.path)===this.settings.excalibrainFilepath&&(e||this.stop())},this.EA.onLinkHoverHook=(e,t)=>{var i;return!(this.scene&&this.EA.targetView&&(null===(i=this.EA.targetView.file)||void 0===i?void 0:i.path)===this.settings.excalibrainFilepath&&this.EA.targetView.excalidrawAPI&&this.EA.targetView.excalidrawAPI.getAppState().viewModeEnabled&&(this.scene.disregardLeafChange=!0,this.disregardLeafChangeTimer&&clearTimeout(this.disregardLeafChangeTimer),this.disregardLeafChangeTimer=setTimeout((()=>{this.disregardLeafChangeTimer=null,this.scene&&(this.scene.disregardLeafChange=!1)}),1e3),0))},this.EA.onLinkClickHook=(e,t,i)=>{var s,r,a,u,o,l;const D=null!==(r=null===(s=t.match(/\[\[([^\]]*)/))||void 0===s?void 0:s[1])&&void 0!==r?r:null===(a=t.match(/(http.*)/))||void 0===a?void 0:a[1];if(!D)return!0;const d=this.pages.get(D),h=this.EA;if(!d||!this.scene||!h)return!0;if(E(h),d.isVirtual)return i.shiftKey?(async()=>{var e,t,i;const n=null!==(i=null!==(t=null!==(e=d.getParents()[0])&&void 0!==e?e:d.getLeftFriends()[0])&&void 0!==t?t:d.getRightFriends()[0])&&void 0!==i?i:d.getChildren()[0],s=await h.newFilePrompt(d.path,!1,void 0,null==n?void 0:n.page.file);s&&(d.file=s,await this.scene.renderGraphForPath(D),await this.scene.reRender(!0))})():null===(u=this.scene)||void 0===u||u.renderGraphForPath(D),!1;if(!this.settings.autoOpenCentralDocument)return this.scene.centralPagePath===d.path?!!d.isURL||(this.scene.isCentralLeafStillThere()?(this.scene.centralLeaf.openFile(d.file,{active:!0}),!1):(h.targetView.linksAlwaysOpenInANewPane=!1,setTimeout((()=>h.targetView.linksAlwaysOpenInANewPane=!0),300),!0)):(this.scene.renderGraphForPath(D),!1);const c=this.scene.centralLeaf;if(!d.isFolder&&!d.isTag&&!d.isURL){if((null===(l=null===(o=null==c?void 0:c.view)||void 0===o?void 0:o.file)||void 0===l?void 0:l.path)===D)return this.scene.renderGraphForPath(D),!1;if(this.scene.isCentralLeafStillThere()){const e=app.vault.getAbstractFileByPath(D.split("#")[0]);if(e&&e instanceof n.TFile)return c.openFile(e,{active:!1}),this.scene.renderGraphForPath(D,!1),!1}return this.scene.renderGraphForPath(D,!0),!0}return!(this.scene.centralPagePath!==d.path||!d.isURL)||(this.scene.renderGraphForPath(D),!1)},this.EA.onViewUnloadHook=e=>{this.scene&&this.scene.leaf===e.leaf&&this.stop()}}onunload(){this.scene&&(this.scene.unloadScene(),this.scene=null)}setHierarchyLinkStylesExtended(){this.hierarchyLinkStylesExtended={},Object.entries(this.settings.hierarchyLinkStyles).forEach((e=>{const t=e[0].toLowerCase().replaceAll(" ","-");this.hierarchyLinkStylesExtended[e[0]]=e[1],e[0]!==t&&(this.hierarchyLinkStylesExtended[t]=e[1])}))}loadCustomNodeLabelFunction(){if(this.settings.nodeTitleScript)try{this.customNodeLabel=new Function("dvPage","defaultName","return "+this.settings.nodeTitleScript)}catch(e){c({fn:this.loadCustomNodeLabelFunction,message:"error processing custom node label script",where:"loadCustomNodeLabelFunction()",data:this.settings.nodeTitleScript,error:e}),new n.Notice("Could not load custom node label function. See Developer console for details"),this.customNodeLabel=null}else this.customNodeLabel=null}async loadSettings(){var e;this.settings=Object.assign({},j,await this.loadData()),this.settings.hierarchy.exclusions||(this.settings.hierarchy.exclusions=x.exclusions),this.loadCustomNodeLabelFunction(),this.settings.baseLinkStyle=Object.assign(Object.assign({},k),this.settings.baseLinkStyle),this.settings.baseNodeStyle=Object.assign(Object.assign({},B),this.settings.baseNodeStyle),this.hierarchyLowerCase.hidden=[],this.settings.hierarchy.hidden||(this.settings.hierarchy.hidden=[""]),this.settings.hierarchy.hidden=this.settings.hierarchy.hidden.sort(((e,t)=>e.toLowerCase()<t.toLowerCase()?-1:1)),this.settings.hierarchy.hidden.forEach((e=>this.hierarchyLowerCase.hidden.push(e.toLowerCase().replaceAll(" ","-"))));let t=[...this.hierarchyLowerCase.hidden];this.hierarchyLowerCase.parents=[],this.settings.hierarchy.parents=this.settings.hierarchy.parents.sort(((e,t)=>e.toLowerCase()<t.toLowerCase()?-1:1)),this.settings.hierarchy.parents.forEach((e=>this.hierarchyLowerCase.parents.push(e.toLowerCase().replaceAll(" ","-")))),t=[...t,...this.hierarchyLowerCase.parents],this.hierarchyLowerCase.children=[],this.settings.hierarchy.children=this.settings.hierarchy.children.filter((e=>!t.includes(e.toLowerCase().replaceAll(" ","-")))).sort(((e,t)=>e.toLowerCase()<t.toLowerCase()?-1:1)),this.settings.hierarchy.children.forEach((e=>this.hierarchyLowerCase.children.push(e.toLowerCase().replaceAll(" ","-")))),t=[...t,...this.hierarchyLowerCase.children],this.hierarchyLowerCase.leftFriends=[],this.settings.hierarchy.leftFriends||(this.settings.hierarchy.leftFriends=null!==(e=this.settings.hierarchy.friends)&&void 0!==e?e:x.leftFriends),this.settings.hierarchy.leftFriends=this.settings.hierarchy.leftFriends.filter((e=>!t.includes(e.toLowerCase().replaceAll(" ","-")))).sort(((e,t)=>e.toLowerCase()<t.toLowerCase()?-1:1)),this.settings.hierarchy.leftFriends.forEach((e=>this.hierarchyLowerCase.leftFriends.push(e.toLowerCase().replaceAll(" ","-")))),t=[...t,...this.hierarchyLowerCase.leftFriends],this.hierarchyLowerCase.rightFriends=[],this.settings.hierarchy.rightFriends||(this.settings.hierarchy.rightFriends=x.rightFriends),this.settings.hierarchy.rightFriends=this.settings.hierarchy.rightFriends.filter((e=>!t.includes(e.toLowerCase().replaceAll(" ","-")))).sort(((e,t)=>e.toLowerCase()<t.toLowerCase()?-1:1)),this.settings.hierarchy.rightFriends.forEach((e=>this.hierarchyLowerCase.rightFriends.push(e.toLowerCase().replaceAll(" ","-")))),t=[...t,...this.hierarchyLowerCase.rightFriends],this.hierarchyLowerCase.previous=[],this.settings.hierarchy.previous||(this.settings.hierarchy.previous=x.previous),this.settings.hierarchy.previous=this.settings.hierarchy.previous.filter((e=>!t.includes(e.toLowerCase().replaceAll(" ","-")))).sort(((e,t)=>e.toLowerCase()<t.toLowerCase()?-1:1)),this.settings.hierarchy.previous.forEach((e=>this.hierarchyLowerCase.previous.push(e.toLowerCase().replaceAll(" ","-")))),t=[...t,...this.hierarchyLowerCase.previous],this.hierarchyLowerCase.next=[],this.settings.hierarchy.next||(this.settings.hierarchy.next=x.next),this.settings.hierarchy.next=this.settings.hierarchy.next.filter((e=>!t.includes(e.toLowerCase().replaceAll(" ","-")))).sort(((e,t)=>e.toLowerCase()<t.toLowerCase()?-1:1)),this.settings.hierarchy.next.forEach((e=>this.hierarchyLowerCase.next.push(e.toLowerCase().replaceAll(" ","-")))),t=[...t,...this.hierarchyLowerCase.next],this.settings.hierarchy.exclusions=this.settings.hierarchy.exclusions.filter((e=>!t.includes(e.toLowerCase().replaceAll(" ","-")))).sort(((e,t)=>e.toLowerCase()<t.toLowerCase()?-1:1)),this.setHierarchyLinkStylesExtended(),this.linkStyles={},this.linkStyles.base={style:this.settings.baseLinkStyle,allowOverride:!1,userStyle:!1,display:z("LINKSTYLE_BASE"),getInheritedStyle:()=>this.settings.baseLinkStyle},this.linkStyles.inferred={style:this.settings.inferredLinkStyle,allowOverride:!0,userStyle:!1,display:z("LINKSTYLE_INFERRED"),getInheritedStyle:()=>this.settings.baseLinkStyle},this.linkStyles["file-tree"]={style:this.settings.folderLinkStyle,allowOverride:!0,userStyle:!1,display:z("LINKSTYLE_FOLDER"),getInheritedStyle:()=>this.settings.baseLinkStyle},this.linkStyles["tag-tree"]={style:this.settings.tagLinkStyle,allowOverride:!0,userStyle:!1,display:z("LINKSTYLE_TAG"),getInheritedStyle:()=>this.settings.baseLinkStyle},Object.entries(this.settings.hierarchyLinkStyles).forEach((e=>{I.contains(e[0])||(this.linkStyles[e[0]]={style:e[1],allowOverride:!0,userStyle:!0,display:e[0],getInheritedStyle:()=>this.settings.baseLinkStyle})})),this.nodeStyles={},this.nodeStyles.base={style:this.settings.baseNodeStyle,allowOverride:!1,userStyle:!1,display:z("NODESTYLE_BASE"),getInheritedStyle:()=>this.settings.baseNodeStyle},this.nodeStyles.inferred={style:this.settings.inferredNodeStyle,allowOverride:!0,userStyle:!1,display:z("NODESTYLE_INFERRED"),getInheritedStyle:()=>this.settings.baseNodeStyle},this.nodeStyles.url={style:this.settings.urlNodeStyle,allowOverride:!0,userStyle:!1,display:z("NODESTYLE_URL"),getInheritedStyle:()=>this.settings.baseNodeStyle},this.nodeStyles.virtual={style:this.settings.virtualNodeStyle,allowOverride:!0,userStyle:!1,display:z("NODESTYLE_VIRTUAL"),getInheritedStyle:()=>this.settings.baseNodeStyle},this.nodeStyles.central={style:this.settings.centralNodeStyle,allowOverride:!0,userStyle:!1,display:z("NODESTYLE_CENTRAL"),getInheritedStyle:()=>this.settings.baseNodeStyle},this.nodeStyles.sibling={style:this.settings.siblingNodeStyle,allowOverride:!0,userStyle:!1,display:z("NODESTYLE_SIBLING"),getInheritedStyle:()=>this.settings.baseNodeStyle},this.nodeStyles.attachment={style:this.settings.attachmentNodeStyle,allowOverride:!0,userStyle:!1,display:z("NODESTYLE_ATTACHMENT"),getInheritedStyle:()=>this.settings.baseNodeStyle},this.nodeStyles.folder={style:this.settings.folderNodeStyle,allowOverride:!0,userStyle:!1,display:z("NODESTYLE_FOLDER"),getInheritedStyle:()=>this.settings.baseNodeStyle},this.nodeStyles.tag={style:this.settings.tagNodeStyle,allowOverride:!0,userStyle:!1,display:z("NODESTYLE_TAG"),getInheritedStyle:()=>this.settings.baseNodeStyle},Object.entries(this.settings.tagNodeStyles).sort(((e,t)=>e[0].toLowerCase()<t[0].toLowerCase()?-1:1)).forEach((e=>{this.nodeStyles[e[0]]={style:e[1],allowOverride:!0,userStyle:!0,display:e[0],getInheritedStyle:()=>this.settings.baseNodeStyle}}))}async saveSettings(){await this.saveData(this.settings)}stop(){this.scene&&!this.scene.terminated&&(this.scene.unloadScene(),this.scene=null)}async start(e){if(this.dailyNoteSettings=oa(),!e.view)return;if(!(e.view instanceof n.TextFileView))return void new n.Notice("Wrong view type. Cannot start ExcaliBrain.");if(e.view.file.path!==this.settings.excalibrainFilepath)return void new n.Notice(`The brain file is not the one configured in settings!\nThe file in settings is ${this.settings.excalibrainFilepath}.\nThis file is ${e.view.file.path}.\nPlease start ExcaliBrain using the Command Palette action.`,5e3);let t=0;for(;!this.pluginLoaded&&t++<100;)await sleep(50);if(!this.pluginLoaded)return new n.Notice("ExcaliBrain plugin did not load - aborting start()"),void c({where:"ExcaliBrain.start()",fn:this.start,message:"ExcaliBrain did not load. Aborting after 5000ms of trying"});this.excalidrawAvailable()&&(this.stop(),e?(this.scene=new ia(this,!0,e),this.scene.initialize(this.focusSearchAfterInitiation),this.focusSearchAfterInitiation=!1):await ia.openExcalidrawLeaf(window.ExcalidrawAutomate,this.settings,this.getBrainLeaf()))}}module.exports=la;
/* nosourcemap */