---
up: "[[TG projects]]"
---
## Tasks
- [ ] test some query parameters to see 
## ParamType
```json
// vertex with vertexType
{
    "paramName": "ver",
    "paramType": {
        "type": "VERTEX",
        "vertexType": "Card"
    }
}

// simple type
{
    "paramName": "print_results",
    "paramType": {
        "type": "BOOL"
    },
    "paramDefaultValue": "false"
}
```

## Notes
- activeFiles
	- open a file: make a copy of file, push to activeFiles
	- activeFile.content, activeFile.sourceCode

- `QueryMetaLogic.loadFromGSQL`
	- handle parameters
		- filter `paramName !== 'query' && !paramName.endsWith('.type')`
		- convert INT64 and UINT64 to INT, UINT64 to UINT
		- convert to `VERTEX` type if has `is_id`
			- get `vertexType` : generic or specific
		- handle `LIST` type
		- handle default value
- Run in interpret mode
	- replace with `interpret query` header
	- `getParamsForInterpretedQuery`
	- `runQueryWithParams`
		- `buildParamsForInterpretedMode`
			- handle simple type
			- vertex type
			- List type
- query info
```json
{
    "query": {
        "Transaction_Fraud": {
            "occupation": {
                "GET/POST": {
                    "action": "query",
                    "alternative_endpoint": "/query/occupation",
                    "createDataList": {},
                    "deleteDataList": {},
                    "enabled": false,
                    "executeGranted": false,
                    "executorList": [],
                    "function": "queryDispatcher",
                    "graphUpdate": true,
                    "graph_name": "Transaction_Fraud",
                    "libudf": "libudf-Transaction_Fraud-1",
                    "needCurrentRoles": false,
                    "needReadRole": false,
                    "parameters": {
                        "query": {
                            "default": "occupation",
                            "type": "STRING"
                        }
                    },
                    "payload": [
                        {
                            "rule": "AS_JSON"
                        },
                        {
                            "rule": "AS_QUERY_STRING"
                        }
                    ],
                    "readDataList": {
                        "Card": [
                            "card_number",
                            "occupation"
                        ],
                        "Card_Send_Transaction": [
                            "from",
                            "to"
                        ],
                        "Payment_Transaction": [
                            "id"
                        ]
                    },
                    "summary": "This is query entrance",
                    "tagAccess": false,
                    "target": "GPE",
                    "updateDataList": {
                        "Payment_Transaction": [
                            "transaction_time",
                            "amount",
                            "is_fraud",
                            "unix_time",
                            "shortest_path_length",
                            "max_txn_amt_interval",
                            "max_txn_cnt_interval",
                            "cnt_repeated_card",
                            "com_mer_txn_cnt",
                            "com_mer_txn_total_amt",
                            "com_mer_txn_avg_amt",
                            "com_mer_txn_max_amt",
                            "com_mer_txn_min_amt",
                            "com_cd_txn_cnt",
                            "com_cd_txn_total_amt",
                            "com_cd_txn_avg_amt",
                            "com_cd_txn_max_amt",
                            "com_cd_txn_min_amt",
                            "indegree",
                            "outdegree",
                            "mer_cat",
                            "mer_cat_cnt",
                            "mer_cat_total_amt",
                            "mer_cat_avg_amt",
                            "mer_cat_max_amt",
                            "mer_cat_min_amt",
                            "mer_pagerank",
                            "cd_pagerank",
                            "mer_com_size",
                            "cd_com_size",
                            "age",
                            "city_pop",
                            "occupation",
                            "gender"
                        ]
                    }
                }
            }
        }
    }
}
```

