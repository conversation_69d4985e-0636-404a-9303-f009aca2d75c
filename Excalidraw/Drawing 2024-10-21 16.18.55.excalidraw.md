---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'


# Excalidraw Data
## Text Elements
query params ^pAk2xSlN

json payload
 ^INpATi9s

url query string ^hQNY9MIE

%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebTiAdho6IIR9BA4oZm4AbXAwUDAiiBJuCDgAQQBrHkwAZXwAOWSiyFhEMqgsKBbizG5nHgS4gFZ+YpgBngBOADZxyAoSdW4A

RhntAA4R6c3pgGYE3c2eTfWFqQRCZWluHjH8yGtlYO4ABgvmKFI2KoQAYTY+DYpDKAGJVghIZDepBNLhsFVlD8hBxiIDgaCJN9rMw4LhAplYRAAGaEfD4OqwV4SQQeYlfH5/ADqy0kd0+31+CCpMBp6DppQuKJuHHC2TQqwubHx2DUk0lbw+jwgyOEcAAksQJagcgBdC4k8jpLXcDhCCkXQhorBlXBvYkotFi5g682WlVhBDENb7VYnEZ7A4XRgs

dhcNA8KUq0OsTiNThibibN7TEY8WZzaOtErMAAiqS6PrQJIIYQummEaIAosF0pk3Rb8BchHBiLgi2thtMACy7BIjHupnsXIgcKpmpujtiI73cUv4csqrqYHoSACOQiYMFQ+ON2WFlAAKt0ypvt7uCbh9AeVSTOFA6oQjOJeMqc/fMgAxa/khWobNihXKAKiIZQI3QYISR6EMmCgcwCFA64IOgGViT0TJcGtJhTTQd1mxVEFrmtAgT1XM8t1IHc92

vW8c1wIQoDYAAlcJn1fb4t1HbCAAkrhuNcAPiEZ8gAX3GQpilKCR9GIAAZZgAH02AAaWcRSyQAK2IfZplIAApABZfR9i/Yl2lfCACR+KgLn6NBnHWfZtCVXS3lme4Ln/QYzm0Dy9nczyVSWYgVjQOZtASWYRg8h4c0kfjbjQN5tDTBI3j9IL6I4F5X3fYpGW5DEQXBaEoSQCsESRJ10SBErsXIDg8QJDIYLvclKWpSzBR9TkmQQVlQvZSM+u5Xl+

QgHrHWEUVxTWaVZXlNYlQuNVWy1HV9UNY0EFw1B8KtG17PQXBVmm1FiBdHVJKA+BXx4R5xM9BA50lA4PISfYlWmWCw04bhZn2X64w4BMOCTcKUx2N4U0AyBCHzQtXtQBclxzSsLtrNJWsbD0c1bdtO0lbs+02AdNi+uGIDHCc8KnFVgVnYsUbLBALjgNhrSyXJHjAPJWiKfLWjeXmDV5/mBYiqKYqygXnBStKMvWESBb1R4xZzfBQigQF9H0NQiw

ABU5ok6bxgqolIKAACFrUcHLJ3NyAMmIW20WtZRHYInMvgJEDSBshLcGZg6VRdioA7YCgg+ZqzI6oMSJJVaT0A1RpKiPQhpjo26OmxU87IGWZUp7EZMriiYpmmaYXL2TYe0OCvFjZNYo20HsjmmN4e1WBIexTHtMwuBLriS1ARiFqycv5SfCr+YqsXQCFyphSrETWtEF86RrmsJNqPw68buqBIVPS5FkW5Gs/+qPsopuFGbJCu+bCMW2BlsntbNW

1XINeKI1ry7RDvTHMdtbQSFwDwc6zo5pm29gVF6zNe6HH2DwfYKYEjA3DNwXSWD4yJlfPsPsPZTgwypgjAswQiYs0XGzFUGMax1hxl7FsbYOzI17jMUmmxZipmmFTGmLCGYzj+MzVGdCczATKJpQQHBLx8jYMHAAOlwQ8FAyKCQgDIzg8jgTKNUXeB8T4Xx3Enp+KAP59b4H/FTYCSFwJlCgvvYooZ4LuHsShJicB0IPiwmKUge1Q45iIv4Uip4J

DaLkfiBR+jiQMSYqxVgJi0CcQkcUMcCA+Kj0EqsYSid8g3RKLHSQABFRoABNaYhkNTVnMndToBcVTHUGMXEYqw0zly8lMVYuTeGrBlk3CAIUwoAQnqldpPCYa6Xro3YeiVBKyyeNPPKo1551UXhAZeZViTwnXjVLeDVcR7lasSMkFJb60hPr1a+3JBojL4Dcv4FyBRXPOrNV0L9glvxsStFUX8Nq/22oAwJICpJHTtPsaBl1YH7VBQIRBXY0FRR6

W0vBEF0xorBhDACswKYJGGGQq0iMqHI3ERWKsxAsb1m5nA1hhMOEk2OD3HYAjrS01hU7amIjSWswuFIiQIh8CoHPFRVAjIPaOmPOE9AgrhWUR3OKh2hojHsVMcq78v5rGfKAt0DxjiEDQWJK4hC+A9XYjQhcDCURsIBOAZykJJF8AaLKLKkVCrvgSouPElibFkmoFSdxMUWSBJrDyUUUS4ANZWTgHAKk7DuCSWgAldIZR2ykAnOMBghAEAUGtlVD

etVMTghJCW0tvQIDYBEHvDUXR9BUn6gcpeZVV75ArVW1qNa0h5r2RSxt0Ad7HKJJmytAcO21q/IfLqd9XnDvbZkTtdbz4DUvrwWdo7521vrWNKdlz6RrurbW5ij9n6Sn3WOtIAB5b5H8z0brSF+B8li/zarbeuqAC6H2ZGMfdfKr6D1pA0WayCBrnF/vPYuv2EdA4hDtfAsDd79DVjRFBqOMc7Tx3LcwbAPwKQAA1uAjCipmrDOH8DlO4P3B5xQj

BsAMAmmMBAtxrHycUEd/79BHouie9ANVy3IhIN+oRxR+OFvqqgQp1sgSxzBP8aYsnZNfjMhcViygLQEnBNWPMmnNOKYgCxyAbHWpbr+Fe+CnBcZwbgIEMwwhmAAHFSACdVbS1tAD0isRtI5h2aBCkZFwJoYIzMA0qmwEQbxKTSBcRVBwQB3BgvBMYhkuLkWED6YgHYTSCBsBZDqDFuAhk2DEAQEh/zgX5ys3AE9SAZzwgJojaJIAA===
```
%%