## Features
- 对象模拟
	- [Tinyspy](https://github.com/tinylibs/tinyspy)
	- function
	```ts
	const spy = vi.spyOn(messages, 'getLatest')
	expect(spy).toHaveBeenCalledTimes(1)
	spy.mockImplementationOnce(() => 'access-restricted')
	```
	- module
		- 第三方库、内部模块
	- virtual module

- 快照测试
	- [Jest 快照测试](https://jestjs.io/zh-Hans/docs/snapshot-testing)
- 浏览器dom和api
	- [happy-dom](https://github.com/capricorn86/happy-dom)
	- [全局变量](https://cn.vitest.dev/guide/mocking.html#%E5%85%A8%E5%B1%80-globals)