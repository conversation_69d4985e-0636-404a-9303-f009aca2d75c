---
title: "What are LLMs? - Hugging Face Agents Course"
source: "https://huggingface.co/learn/agents-course/unit1/messages-and-special-tokens"
author:
published:
created: 2025-03-27
description: "We’re on a journey to advance and democratize artificial intelligence through open source and open science."
tags:
  - "clippings"
---
既然我们已经理解了LLMs的工作原理，接下来让我们看看它们是如何通过聊天模板来组织生成内容的。

答：没错！但这实际上是用户界面的抽象。在输入LLM之前，对话中的所有消息都会被串联成一个单一的提示。模型并不会“记住”对话内容：它每次都会完整地读取整个对话。

![Behind models](https://huggingface.co/datasets/agents-course/course-images/resolve/main/en/unit1/assistant.jpg)

这就是聊天模板的用武之地。它们充当对话消息（用户和助手轮次）与你所选LLM特定格式要求之间的桥梁。换言之，聊天模板构建了用户与代理之间的通信结构，确保每个模型——尽管有其独特的特殊标记——都能接收到正确格式化的提示。

## Messages: The Underlying System of LLMs

### System Messages

系统消息（亦称系统提示）定义了模型应如何表现。它们作为持久指令，引导每一次后续互动。

### Conversations: User and Assistant Messages

conversation = \[
    {"role": "user", "content": "I need help with my order"},
    {"role": "assistant", "content": "I'd be happy to help. Could you provide your order number?"},
    {"role": "user", "content": "It's ORDER-123"},
\]

例如，SmolLM2 聊天模板会将之前的对话格式化为如下提示：

<|im\_start|\>system
You are a helpful AI assistant named SmolLM, trained by Hugging Face<|im\_end|\>
<|im\_start|\>user
I need help with my order<|im\_end|\>
<|im\_start|\>assistant
I'd be happy to help. Could you provide your order number?<|im\_end|\>
<|im\_start|\>user
It's ORDER\-123<|im\_end|\>
<|im\_start|\>assistant

### Understanding Chat Templates

由于每个指令模型使用不同的对话格式和特殊标记，因此实现了聊天模板以确保我们按照每个模型期望的方式正确格式化提示。

以下是 `SmolLM2-135M-Instruct` 聊天模板的简化版本：

{% for message in messages %}
{% if loop.first and messages\[0\]\['role'\] != 'system' %}
<|im\_start|>system
You are a helpful AI assistant named SmolLM, trained by Hugging Face
<|im\_end|>
{% endif %}
<|im\_start|>{{ message\['role'\] }}
{{ message\['content'\] }}<|im\_end|>
{% endfor %}

### Messages to prompt

确保您的LLM正确接收格式化的对话的最简单方法是使用模型分词器的 `chat_template` 。

将之前的对话转换为提示时，我们加载分词器并调用 `apply_chat_template` :

from transformers import AutoTokenizer

tokenizer = AutoTokenizer.from\_pretrained("HuggingFaceTB/SmolLM2-1.7B-Instruct")
rendered\_prompt = tokenizer.apply\_chat\_template(messages, tokenize=False, add\_generation\_prompt=True)

此函数返回的 `rendered_prompt` 现在可以作为您所选模型的输入使用了！