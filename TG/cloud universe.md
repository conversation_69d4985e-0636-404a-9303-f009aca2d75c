## Connect to staging db
```bash
# step1: teleport to staging dev

# step2: enter the container
sudo docker exec -it 54a56e0fdf63 bash

# step3: connect to db
# password: tgcloud-dev-what-a-beautiful-day
# dev db url: terraform-20200112090740541600000001.cvgla8nd7lox.us-west-1.rds.amazonaws.com
psql -h solution-db-staging.cvgla8nd7lox.us-west-1.rds.amazonaws.com -U tgclouddev -d solution

psql -h terraform-20200112090740541600000001.cvgla8nd7lox.us-west-1.rds.amazonaws.com -U tgclouddev -d solution

```
## Access k8s pod
* access k8s pod(test)
```shell
kubectl config set-cluster my-cluster --server=https://8701E84AC76A61A1FAD3E07561199AE4.gr7.us-east-1.eks.amazonaws.com --insecure-skip-tls-verify=true
kubectl config set-credentials my-cluster-user --token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
kubectl config set-context my-cluster-context --cluster=my-cluster --user=my-cluster-user
kubectl config use-context my-cluster-context
kubectl exec -it f471fbd4-54b5-41e6-955f-57527616add2-0 -n 5e8f4f28-551b-4208-8a52-d071ba953eb8 bash
```
* access k8s pod(staging)
```shell
kubectl config set-cluster staging --server=[https://********************************.gr7.us-east-1.eks.amazonaws.com](https://fd03cf2797c1db47a4768bf613921d20.gr7.us-east-1.eks.amazonaws.com/) --insecure-skip-tls-verify=true kubectl config set-credentials chengrun.peng --token=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************** kubectl config set-context staging --cluster=staging --user=chengrun.peng kubectl config use-context stagin

```
## deploy controller locally
	```shell
	pid=`pgrep controller`
	echo $pid
	sudo kill -9 $pid
	cd ~/cloud-universe
	git pull
	make release/controller
	./release/controller >> ../logs/server/log &
	```
* access db container
  ```shell
	sudo docker exec -it deploy-db /bin/bash
	```

## PSQL
* psql
```shell
PGPASSWORD=tgcloud-dev-what-a-beautiful-day psql -U tgclouddev -h localhost solution

PGPASSWORD=tgcloud-dev-what-a-beautiful-day psql -U tgclouddev -h terraform-20200112090740541600000001.cvgla8nd7lox.us-west-1.rds.amazonaws.com solution

psql -h localhost -p 5432 -U tgclouddev -d solution
```
* local psql connect to remote psql server
```shell
ssh -CNL 5432:localhost:5432 ubuntu@************* -i qebot.pem 
```
* mac: stop posgresql server
```shell
sudo -u postgres -i

postgres$ pg_ctl -D /Library/PostgreSQL/16/data stop
```
## Populate onboarding_tasks
```sql
DELETE FROM public.onboarding_tasks
INSERT INTO public.onboarding_tasks (id, created_at, updated_at, task_name, description, credits, index) VALUES ('f6de5408-a49b-4a6b-a024-3ff00879f3dd', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP,'Sign up for TigerGraph Cloud', 'Create an account with TigerGraph Cloud to get started with your free trial.', 50, 0);
INSERT INTO public.onboarding_tasks (id, created_at, updated_at, task_name, description, credits, index) VALUES ('41216f7a-5d18-4399-9298-f8abf30462e4', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP,'Create a workspace', 'Set up your workspace and database to start building your graph analytics solutions.', 5, 10);
INSERT INTO public.onboarding_tasks (id, created_at, updated_at, task_name, description, credits, index) VALUES ('dbf4310e-5983-4783-8614-01f77e93ae49', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP,'Load data', 'Load data into your TigerGraph database to start running queries and building applications.', 5, 20);
INSERT INTO public.onboarding_tasks (id, created_at, updated_at, task_name, description, credits, index) VALUES ('9190ed89-cf74-4b3f-a56a-fb94ddcbe8c7', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP,'Invite a team member', 'Collaborate with your team by inviting a team member to your workspace.', 5, 30);
INSERT INTO public.onboarding_tasks (id, created_at, updated_at, task_name, description, credits, index) VALUES ('8c95eaa2-9268-420f-acb4-c5a0e69c6a94', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP,'Add payment method', 'Add a payment method to your account to continue using TigerGraph Cloud after your free trial ends.', 5, 40);
INSERT INTO public.onboarding_tasks (id, created_at, updated_at, task_name, description, credits, index) VALUES ('b210445f-f22d-4e05-99b8-ed9e71ef8727', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP,'Give feedback', 'Help us improve TigerGraph Cloud by providing feedback on your experience.', 5, 50);
```
## Config git
1. upload ~/.ssh/tg
2. modify .ssh/config in the instance
   ```txt
	 Host github.com
	HostName github.com
	IdentityFile ~/.ssh/id_rsa_github
	 ```
3. set remote url of origin
   ```txt
	 git remote set-<NAME_EMAIL>:tigergraph/cloud-universe.git
	 ```
4. config username and email
  ```txt
	git config --global user.name "FIRST_NAME LAST_NAME"
	git config --global user.email "<EMAIL>"
	```