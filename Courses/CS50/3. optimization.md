---
id: 2552jm8lon4o38yxyycf7uf
title: optimization
desc: ''
updated: 1708613047058
created: 1708518871525
---
## Children
- [[Project.crossword]]
## Source
- https://cs50.harvard.edu/ai/2024/notes/3/
  
## Optimization
-

## Local Search
- 跟search problem的不同点
- state-space landscape
- few important terms
  - Objective Function
  - Cost Function
  - Current State
  - Neighbor State
- the way local search algorithms wren yi
- 医院问题
  - 先随机选择位置，neighbor state:任一医院移动一格

## Hill Climbing
- a hill climbing algorithm can get stuck in
  - when neighbors not better
- Hill Climbing Variants

## Simulated Annealing
- starts with a high temperature, being more likely to
- e^(ΔE/T)
- Traveling Salesman Problem
  
## Linear Programming
- a family of problems that optimize a linear equation (an equation of the form ).
- given constraints: 

## Constraint Satisfaction
- Constraints satisfaction problems have the following properties:
- 数独
- 选课问题：n名学生从m节课中选3个，如何安排考试？
  - node表示课，有edge连结的边不能选择同一个日期
- Node Consistency
  - variable所有选择都满足unary constraint
- Arc Consistency
  - X arc-consistent with respect to Y：对于X中的任意值，Y都有possible value  s
- Revise(csp, X, Y): make x arc-consistent with y
- ??AC-3(csp): making the whole problem arc-consistent 
  - use Revise function
-  A constraint satisfaction problem can be seen as a search problem:
  
## Backtracking Search
- Backtrack(assignment, csp):
- ??Inference
  - enforcing arc consistency
    - after every new assignment of the backtracking search
- make the algorithm more efficient.
  - heuristics
    - Minimum Remaining Values
    - The Degree heuristic
      - Constraining Values heuristic: select the value that will constrain the least other variables