---
id: p2u0vd0c598lgig0fk6owqw
title: Knowledge
desc: ''
updated: 1706951681906
created: 1706442224426
---
## Children
- [[Project.minesweeper]]
## Source
- https://cs50.harvard.edu/ai/2024/notes/1/

## What I have learned?
- 命题逻辑是对现实现象的一种描述，通过操作符形成更复杂的事实
- symbol是命题逻辑中的最小单元
- 我们知道一些事实knowledge base(kb)的真假，想知道另外事实P是否为true
  - 考虑所有的model，如果每个使得kb成立的model中p都为true，则p为true
  - 从kb进行推导，直到无法获得新的结论，是否能推出P
- 所有的sentence都能转换成Conjunctive Normal Form，通过resolution，可以对其进行简化

## Notes
### Knowledge-Based Agents
- agents that reason by operating on internal representations of knowledge.

### Propositional Logic
- Propositional Symbols: Propositional symbols are most often letters (P, Q, R) that are used to represent a proposition.
- Logical Connectives
  - Not (¬)
  - And (∧)
  - Or (∨)
  - Implies (→)
    ![Alt text](image-84.png)
  - Iff (↔)
    ![Alt text](image-85.png)
- Model: The model is an assignment of a truth value to every proposition. 
  - In fact, the number of possible models is 2 to the power of the number of propositions. 
- Knowledge Base (KB)
  - The knowledge base is a set of sentences known by a knowledge-based agent. This is knowledge that the AI is provided about the world in the form of propositional logic sentences that can be used to make additional inferences about the world.
- Entailment (⊨): If α ⊨ β (α entails β), then in any world where α is true, β is true, too.
  - Entailment is different from implication. Implication is a logical connective between two propositions. Entailment, on the other hand, is a relation that means that if all the information in α is true, then all the information in β is true.

### Inference
- Inference is the process of deriving new sentences from old ones.

### Model Checking
- To determine if KB ⊨ α (in other words, answering the question: “can we conclude that α is true based on our knowledge base”)
Enumerate all possible models.
If in every model where KB is true, α is true as well, then KB entails α (KB ⊨ α).

### Knowledge Engineering
- Knowledge engineering is the process of figuring out how to represent propositions and logic in AI.

### Inference Rules
- Model Checking is not an efficient algorithm because it has to consider every possible model before giving the answer (a reminder: a query R is true if under all the models (truth assignments) where the KB is true, R is true as well). Inference rules allow us to generate new information based on existing knowledge without considering every possible model.
- Modus Ponens: If P is true, and P → Q is true, then Q is true.
- many more...
- Knowledge and Search Problems
  - Inference can be viewed as a search problem with the following properties:
  ```txt
  Initial state: starting knowledge base
  Actions: inference rules
  Transition model: new knowledge base after inference
  Goal test: checking whether the statement that we are trying to prove is in the KB
  Path cost function: the number of steps in the proof
  This shows just how versatile search algorithms are, allowing us to derive new information based on existing knowledge using inference rules.
  ```

### Resolution
- Resolution is a powerful inference rule that states that if one of two atomic propositions in an Or proposition is false, the other has to be true.
  ![Alt text](image-86.png)
  ![Alt text](image-87.png)

### Conjunctive Normal Form (CNF)
- A Clause is a disjunction of literals (a propositional symbol or a negation of a propositional symbol, such as P, ¬P). A disjunction consists of propositions that are connected with an Or logical connective (P ∨ Q ∨ R). 
- Clauses allow us to convert any logical statement into a Conjunctive Normal Form (CNF), which is a conjunction of clauses, for example: (A ∨ B ∨ C) ∧ (D ∨ ¬E) ∧ (F ∨ G).
- Resolving a literal and its negation, i.e. ¬P and P, gives the empty clause (). The empty clause is always false, and this makes sense because it is impossible that both P and ¬P are true. 
- This fact is used by the **resolution algorithm.**
```txt
To determine if KB ⊨ α:
Convert (KB ∧ ¬α) to Conjunctive Normal Form.
Keep checking to see if we can use resolution to produce a new clause.
If we ever produce the empty clause (equivalent to False), congratulations! We have arrived at a contradiction, thus proving that KB ⊨ α.
However, if contradiction is not achieved and no more clauses can be inferred, there is no entailment.
```

### First Order Logic
- First order logic is another type of logic that allows us to express more complex ideas more succinctly than propositional logic. 
- For example, the sentence ∀x. Person(x) → (∃y. House(y) ∧ BelongsTo(x, y)) expresses the idea that if x is a person, then there is at least one house, y, to which this person belongs. In other words, this sentence means that every person belongs to a house.