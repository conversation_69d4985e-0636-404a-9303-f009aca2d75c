/* Dashboard navigator plugin for Obsidian
MIT License (c) <PERSON> (@drbap)
*/
body {
	--dn-background-color: var(--background-secondary);
	--dn-foreground-color: var(--text-normal);
	--dn-border-color: var(--background-modifier-border);
	--dn-border-color-hover: var(--background-modifier-border-hover);
	--dn-file-property-color: #828282;
	--dn-font-size: 16px;

	--dn-notes-color: #bf48ff;
	--dn-images-color: #007fff;
	--dn-canvas-color: #ff7f28;
	--dn-videos-color: #d34848;
	--dn-audios-color: #bfbf00;
	--dn-pdfs-color: #00a300;
	--dn-other-color: #828282;

}

.theme-dark {
	--dn-search-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='18' height='18' viewBox='0 0 24 24' fill='none' stroke='%23f1f1f1' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'/%3E%3Cpath d='m21 21-4.3-4.3'/%3E%3C/svg%3E");
	--dn-search-background-color: var(--background-primary);
	/* File icons */
	--dn-notes-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23cfcfcf' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z'/%3E%3Cpath d='M14 2v4a2 2 0 0 0 2 2h4'/%3E%3C/svg%3E");
	--dn-images-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23cfcfcf' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect width='18' height='18' x='3' y='3' rx='2' ry='2'/%3E%3Ccircle cx='9' cy='9' r='2'/%3E%3Cpath d='m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21'/%3E%3C/svg%3E");
	--dn-canvas-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23cfcfcf' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect width='7' height='9' x='3' y='3' rx='1'/%3E%3Crect width='7' height='5' x='14' y='3' rx='1'/%3E%3Crect width='7' height='9' x='14' y='12' rx='1'/%3E%3Crect width='7' height='5' x='3' y='16' rx='1'/%3E%3C/svg%3E");
	--dn-audios-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23cfcfcf' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M9 18V5l12-2v13'/%3E%3Ccircle cx='6' cy='18' r='3'/%3E%3Ccircle cx='18' cy='16' r='3'/%3E%3C/svg%3E");
	--dn-videos-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23cfcfcf' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect width='18' height='18' x='3' y='3' rx='2'/%3E%3Cpath d='M7 3v18'/%3E%3Cpath d='M3 7.5h4'/%3E%3Cpath d='M3 12h18'/%3E%3Cpath d='M3 16.5h4'/%3E%3Cpath d='M17 3v18'/%3E%3Cpath d='M17 7.5h4'/%3E%3Cpath d='M17 16.5h4'/%3E%3C/svg%3E");
	--dn-pdfs-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23cfcfcf' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z'/%3E%3Cpath d='M14 2v4a2 2 0 0 0 2 2h4'/%3E%3Cpath d='M10 9H8'/%3E%3Cpath d='M16 13H8'/%3E%3Cpath d='M16 17H8'/%3E%3C/svg%3E");
	--dn-other-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23cfcfcf' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 17h.01'/%3E%3Cpath d='M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7z'/%3E%3Cpath d='M9.1 9a3 3 0 0 1 5.82 1c0 2-3 3-3 3'/%3E%3C/svg%3E");
	--dn-even-background-color: #00000048;
}

.theme-light {
	--dn-search-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='18' height='18' viewBox='0 0 24 24' fill='none' stroke='%23000000' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'/%3E%3Cpath d='m21 21-4.3-4.3'/%3E%3C/svg%3E");
	--dn-search-background-color: #fff;
	/* File icons */
	--dn-notes-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23282828' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z'/%3E%3Cpath d='M14 2v4a2 2 0 0 0 2 2h4'/%3E%3C/svg%3E");
	--dn-images-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23282828' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect width='18' height='18' x='3' y='3' rx='2' ry='2'/%3E%3Ccircle cx='9' cy='9' r='2'/%3E%3Cpath d='m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21'/%3E%3C/svg%3E");
	--dn-canvas-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23282828' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect width='7' height='9' x='3' y='3' rx='1'/%3E%3Crect width='7' height='5' x='14' y='3' rx='1'/%3E%3Crect width='7' height='9' x='14' y='12' rx='1'/%3E%3Crect width='7' height='5' x='3' y='16' rx='1'/%3E%3C/svg%3E");
	--dn-audios-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23282828' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M9 18V5l12-2v13'/%3E%3Ccircle cx='6' cy='18' r='3'/%3E%3Ccircle cx='18' cy='16' r='3'/%3E%3C/svg%3E");
	--dn-videos-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23282828' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect width='18' height='18' x='3' y='3' rx='2'/%3E%3Cpath d='M7 3v18'/%3E%3Cpath d='M3 7.5h4'/%3E%3Cpath d='M3 12h18'/%3E%3Cpath d='M3 16.5h4'/%3E%3Cpath d='M17 3v18'/%3E%3Cpath d='M17 7.5h4'/%3E%3Cpath d='M17 16.5h4'/%3E%3C/svg%3E");
	--dn-pdfs-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23282828' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z'/%3E%3Cpath d='M14 2v4a2 2 0 0 0 2 2h4'/%3E%3Cpath d='M10 9H8'/%3E%3Cpath d='M16 13H8'/%3E%3Cpath d='M16 17H8'/%3E%3C/svg%3E");
	--dn-other-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23282828' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 17h.01'/%3E%3Cpath d='M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7z'/%3E%3Cpath d='M9.1 9a3 3 0 0 1 5.82 1c0 2-3 3-3 3'/%3E%3C/svg%3E");
	--dn-even-background-color: #cfcfcf48;
}

.modal-container.mod-dim .modal:has(.dn-container) {
	width: 90%;
	max-width: 1920px;
	height: 90%;
	max-height: 1080px;
	background-color: var(--dn-background-color);
	color: var(--dn-foreground-color);
}

.dn-container {
	width: 100%;
	max-width: 100%;
	height: 100%;
	min-height: 100%;
	padding: 1em;
}

.dn-top-nav {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	margin-left: 0;
	margin-right: 0;
	margin-top: var(--size-4-1);
	margin-bottom: var(--size-4-1);
	flex-wrap: wrap;
	align-content: center;
}

.dn-top-nav button {
	margin: var(--size-4-2) 0;
	margin-right: var(--size-4-2);
	cursor: pointer;
}

.dn-flex {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	flex-direction: row;
	flex-wrap: wrap;
	gap: 1.8em;
}

.dn-flex>div {
	width: 30%;
	border-radius: 0;
}

.dn-flex>div:not(#dn-vault-stats) {
	line-height: 1.8em;
	word-wrap: break-word;
}

.dn-subtitles {
	width: 100%;
	margin-top: var(--size-4-1);
	font-size: 1em;
	font-weight: var(--font-semibold);
	color: var(--text-normal);
	border-bottom: 1px solid var(--dn-border-color);
}

#dashboard-canvas {
	padding: var(--size-4-2);
}

.dn-stats-files-folders {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	width: 50%;
	margin-left: 30%;
}

.dn-stats-folders,
.dn-stats-files {
	text-align: left;
	width: 100%;
	color: var(--text-normal);
	margin-bottom: var(--size-4-2);
	font-size: 1em;
	color: var(--text-normal);
}

.dn-stats-files::before {
	content: "■";
	color: var(--text-muted);
	margin-right: var(--size-4-1);
}

.dn-stats-folders::before {
	content: "■";
	color: var(--text-faint);
	margin-right: var(--size-4-1);
}


.dn-container a {
	text-decoration: none;
	color: var(--link-color);
	font-size: var(--dn-font-size);
}

.dn-container a:hover {
	text-decoration: none;
	color: var(--link-color-hover);
}

#dn-vault-stats {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	justify-content: space-between;
	gap: var(--size-4-2);
}

.dn-btn-stats {
	position: relative;
	margin-bottom: 8px;
	background-color: var(--background-primary);
	width: 48%;
	height: 64px;
	text-align: center;
	border: 1px solid var(--dn-border-color);
	border-radius: var(--radius-s);
	font-size: 1em;
	font-weight: var(--font-semibold);
	color: var(--text-muted);
}

.dn-btn-stats:hover {
	border: 1px solid var(--text-accent);
	cursor: pointer;
}

.dn-btn-stats .dn-btn-stats-label {
	position: absolute;
	top: 4px;
	right: 6px;
	display: block;
	font-size: .7em;
	font-weight: normal;
	color: var(--text-normal);
}

.dn-btn-stats-icon {
	position: absolute;
	top: calc(50% - 14px);
	left: 8px;
	font-weight: var(--font-semibold);
	color: var(--text-normal);
	width: 24px;
	height: 24px;
	opacity: .5;
}

.dn-btn-stats .dn-btn-stats-number {
	position: absolute;
	top: calc(50% - .6em);
	left: 4px;
	font-size: 1.2em;
	font-weight: var(--font-semibold);
	color: var(--text-accent);
	display: block;
	width: 100%;
	text-align: right;
	padding-right: 12px;
}

/* Btns Icons */
#dn-btn-notes .dn-btn-stats-icon {
	content: var(--dn-notes-icon);
}

#dn-btn-canvas .dn-btn-stats-icon {
	content: var(--dn-canvas-icon);
}

#dn-btn-images .dn-btn-stats-icon {
	content: var(--dn-images-icon);
}

#dn-btn-audios .dn-btn-stats-icon {
	content: var(--dn-audios-icon);
}

#dn-btn-videos .dn-btn-stats-icon {
	content: var(--dn-videos-icon);
}

#dn-btn-pdf .dn-btn-stats-icon {
	content: var(--dn-pdfs-icon);
}

#dn-btn-other .dn-btn-stats-icon {
	content: var(--dn-other-icon);
}

.dn-display-none {
	display: none;
}

a.dn-ext,
a.dn-folder-path {
	color: var(--text-muted);
}

a.dn-folder-path:hover {
	background-color: var(--tag-background);
	color: var(--link-color);
}

div.dn-div-table {
	width: 100%;
}

table#dn-table {
	table-layout: fixed;
	width: 100%;
	border-collapse: collapse;
	background-color: var(--background-primary);
	border: 1px solid var(--dn-border-color);
	font-size: 1em;
}

table#dn-table th,
table#dn-table td {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	padding: var(--size-4-1) var(--size-4-2);
	min-width: 50px;
	font-size: var(--dn-font-size);
}

table#dn-table th {
	position: sticky;
	top: 80px;
	z-index: 901;
	text-align: left;
	font-weight: var(--font-semibold);
	color: var(--text-normal);
	cursor: pointer;
	background-color: var(--background-primary);
}

table#dn-table th:not(th:nth-child(6), th:nth-child(7)):hover {
	background-color: var(--background-secondary);
}

table#dn-table th.sort-active {
	color: var(--text-normal);
	background-color: var(--background-secondary);
}

table#dn-table th.sort-active.sort-asc::before {
	content: " ↑";
	font-weight: var(--font-bold);
	color: var(--interactive-accent);

}

table#dn-table th.sort-active.sort-desc::before {
	content: " ↓";
	font-weight: var(--font-bold);
	color: var(--interactive-accent);
}

table#dn-table th:nth-child(1),
table#dn-table th:nth-child(2),
table#dn-table th:nth-child(3),
table#dn-table th:nth-child(4),
table#dn-table th:nth-child(5),
table#dn-table th:nth-child(6) {
	resize: horizontal;
}

table#dn-table th:nth-child(2),
table#dn-table td:nth-child(2) {
	width: 82px;
	min-width: 82px;
}

table#dn-table td:nth-child(4) {
	text-align: right;
	padding-right: 1em;
}

table#dn-table th:nth-child(6),
table#dn-table th:nth-child(7) {
	cursor: default;
}

table#dn-table tr:hover td {
	background-color: var(--nav-item-background-hover) !important;
}

.dn-search-input-container {
	position: sticky;
	top: 0;
	left: 0;
	z-index: 9900;
	width: 50%;
	height: var(--input-height);
}

.dn-search-input-container .search-input-clear-button {
	top: calc(var(--input-height) / 4);
	z-index: 9901;
}

#dn-input-filter {
	background-image: var(--dn-search-icon);
	background-color: var(--dn-search-background-color);
	background-position: 8px 6px;
	background-repeat: no-repeat;
	width: 100%;
	height: var(--input-height);
	font-size: var(--font-size);
	padding: 8px 18px 6px 32px;
	border: 1px solid var(--dn-border-color);
	margin-top: var(--size-4-1);
	border-radius: var(--input-radius);
}

#dn-vault-graph {
	filter: saturate(0.7);
	text-align: center;
}

.dn-pagination {
	position: sticky;
	display: flex;
	top: 40px;
	padding: var(--size-4-1) var(--size-4-2);
	text-align: right;
	font-size: .9em;
	background-color: var(--background-secondary);
	z-index: 900;
	justify-content: space-between;
	border: 1px solid var(--dn-border-color);
	opacity: .9;
}

.dn-btn-prev:disabled,
.dn-btn-next:disabled {
	opacity: .3;
}

.dn-btn-prev,
.dn-btn-next {
	cursor: pointer;
	margin-left: var(--size-4-1);
}

/* File icons */

a.dn-f-note::before,
a.dn-f-canvas::before,
a.dn-f-image::before,
a.dn-f-audio::before,
a.dn-f-video::before,
a.dn-f-pdf::before,
a.dn-f-other::before {
	width: 1em;
	height: 1em;
	vertical-align: top;
	display: inline-block;
	margin-top: 2px;
	margin-right: 4px;
}

a.dn-f-note::before {
	content: var(--dn-notes-icon);
}

a.dn-f-canvas::before {
	content: var(--dn-canvas-icon);
}

a.dn-f-image::before {
	content: var(--dn-images-icon);
}

a.dn-f-audio::before {
	content: var(--dn-audios-icon);
}

a.dn-f-video::before {
	content: var(--dn-videos-icon);
}

a.dn-f-pdf::before {
	content: var(--dn-pdfs-icon);
}

a.dn-f-other::before {
	content: var(--dn-other-icon);
}

/* Colored Files */
/* Dashboard buttons */
.dn-colored-files #dn-btn-notes .dn-btn-stats-number {
	color: var(--dn-notes-color);
}

.dn-colored-files #dn-btn-canvas .dn-btn-stats-number {
	color: var(--dn-canvas-color);
}

.dn-colored-files #dn-btn-images .dn-btn-stats-number {
	color: var(--dn-images-color);
}

.dn-colored-files #dn-btn-audios .dn-btn-stats-number {
	color: var(--dn-audios-color);
}

.dn-colored-files #dn-btn-videos .dn-btn-stats-number {
	color: var(--dn-videos-color);
}

.dn-colored-files #dn-btn-pdf .dn-btn-stats-number {
	color: var(--dn-pdfs-color);
}

.dn-colored-files #dn-btn-other .dn-btn-stats-number {
	color: var(--dn-other-color);
}

/* Files */
.dn-colored-files a.dn-f-note {
	color: var(--dn-notes-color);
}

.dn-colored-files a.dn-f-canvas {
	color: var(--dn-canvas-color);
}

.dn-colored-files a.dn-f-image {
	color: var(--dn-images-color);
}

.dn-colored-files a.dn-f-audio {
	color: var(--dn-audios-color);
}

.dn-colored-files a.dn-f-video {
	color: var(--dn-videos-color);
}

.dn-colored-files a.dn-f-pdf {
	color: var(--dn-pdfs-color);
}

.dn-colored-files a.dn-f-other {
	color: var(--dn-other-color);
}

.dn-colored-files a.dn-f-note:hover,
.dn-colored-files a.dn-f-canvas:hover,
.dn-colored-files a.dn-f-image:hover,
.dn-colored-files a.dn-f-audio:hover,
.dn-colored-files a.dn-f-video:hover,
.dn-colored-files a.dn-f-pdf:hover,
.dn-colored-files a.dn-f-other:hover {
	color: var(--link-color-hover);
}

.dn-no-results-found {
	padding-left: var(--size-4-2);
	color: var(--text-faint);
	font-style: italic;
}

a.dn-fproperties::before {
	content: '•';
	color: var(--dn-border-color);
}

a.dn-fproperties:hover::before {
	content: '•';
	color: var(--text-accent-hover);
}

a.dn-fproperties {
	display: inline-block;
	border-radius: var(--tag-radius);
	margin-right: var(--size-4-1);
	padding-inline-end: var(--tag-padding-x);
	font-size: var(--font-size);
	background-color: var(--tag-background);
	color: var(--tag-color);
	text-decoration: none;
}

a.dn-fproperties:hover {
	color: var(--text-accent-hover);
}

#dn-other,
#dn-videos,
#dn-audios,
#dn-pdfs,
#dn-images,
#dn-canvas,
#dn-recent-notes,
#dn-recent-files,
#dn-last-opened-files {
	padding: var(--size-4-2);
	background-color: var(--background-primary);
	border: 1px solid var(--background-modifier-border);
	border-radius: var(--radius-s);
}

.dn-input-datesearch {
	font-weight: var(--font-semibold) !important;
	color: var(--text-accent) !important;
}

/* Table layout */

table#dn-table.dn-tbl-row tbody tr:nth-child(even) {
	background-color: var(--dn-even-background-color);
}

table#dn-table.dn-tbl-column td:nth-child(even of :not(.dn-hidden)) {
	background-color: var(--dn-even-background-color);
}

table#dn-table.dn-tbl-bordered th,
table#dn-table.dn-tbl-bordered td {
	border: 1px solid #82828248;
}

table#dn-table.dn-tbl-default th,
table#dn-table.dn-tbl-default td {
	margin: 0;
}

.dropdown.tbl-select {
	margin-right: var(--size-4-2);
}

.dn-tbl-label {
	margin-left: var(--size-4-3);
	margin-right: var(--size-4-2);
	color: var(--text-faint);
	font-size: .8em;
}

.tbl-selected {
	background-color: var(--tag-background) !important;
}

/* File properties */
.dn-properties {
	color: var(--dn-file-property-color);
	overflow-y: auto;
	word-wrap: break-word;
}

.dn-div-top-preview-btns {
	text-align: left;
	display: flex;
	justify-content: left;
	gap: 8px;
}

.dn-div-bottom-properties {
	text-align: left;
	display: flex;
	justify-content: space-between;
}

.dn-div-top-preview-btns button,
.dn-div-bottom-properties button {
	margin-top: var(--size-4-1);
	cursor: pointer;
}

.dn-btn-properties-close {
	width: 100px;
	margin-left: 1em;
}

.dn-btn-properties-open-file {
	width: 180px;
}

.dn-hidden {
	display: none;
}

.dn-properties-frontmatter-modal,
.dn-properties-frontmatter {
	overflow-y: auto;
	resize: vertical;
	min-height: 48px;
	height: 120px;
	border: 1px solid var(--dn-border-color);
	padding: var(--size-4-1);
	line-height: 1.8em;
	font-size: 1em;
	word-wrap: break-word;
	word-break: break-word;
}


.modal:has(.dn-properties-modal),
.modal:has(.dn-frontmatter-modal) {
	min-width: 30%;
	width: 60%;
	max-width: 100%;
	resize: both;
}

@media screen and (max-width: 1024px) {
	.dn-flex {
		display: flex;
		align-items: flex-start;
		flex-direction: row;
		flex-wrap: wrap;
	}

	.dn-flex>div {
		width: 100%;
	}

	.dn-search-input-container {
		width: 100%;
	}

	.dn-btn-stats {
		min-width: 30%;
		width: 30%;
	}

	table#dn-table th:nth-child(4),
	table#dn-table td:nth-child(4),
	table#dn-table th:nth-child(6),
	table#dn-table td:nth-child(6) {
		display: none;
	}

}

@media screen and (max-width: 600px) {
	.dn-flex {
		display: flex;
		justify-content: center;
		align-items: flex-start;
		flex-direction: row;
		flex-wrap: wrap;
	}

	.dn-flex>div {
		width: 100%;
	}

	.dn-search-input-container {
		width: 100%;
	}

	.dn-btn-stats {
		min-width: 100%;
	}

	.is-mobile .modal-container.mod-dim .modal:has(.dn-container) {
		width: 100%;
		max-width: 100%;
		height: 100%;
		max-height: 100%;
		background-color: var(--dn-background-color);
		color: var(--dn-foreground-color);
	}

	table#dn-table th:nth-child(2),
	table#dn-table td:nth-child(2),
	table#dn-table th:nth-child(3),
	table#dn-table td:nth-child(3),
	table#dn-table th:nth-child(4),
	table#dn-table td:nth-child(4),
	table#dn-table th:nth-child(5),
	table#dn-table td:nth-child(5),
	table#dn-table th:nth-child(6),
	table#dn-table td:nth-child(6),
	table#dn-table th:nth-child(7),
	table#dn-table td:nth-child(7) {
		display: none;
	}

	#dn-label-layout,
	.dropdown.tbl-select {
		display: none;
	}

}

.dn-preview {
	display: none;
	position: fixed;
	min-width: 400px;
	min-height: 300px;
	width: 600px;
	height: 480px;
	max-width: 100%;
	max-height: 100%;
	overflow: auto;
	border: 2px solid var(--dn-border-color);
	background: var(--background-primary);
	padding: 0;

	z-index: 90000;
	border-radius: var(--radius-m);
	box-shadow: 0 0 1em #00000082;
	resize: both;
}

.dn-preview:hover {
	border: 2px solid var(--dn-border-color-hover);
}

.dn-preview-top-bar {
	background-color: var(--background-primary);
	position: sticky;
	top: 0;
	left: 0;
	padding: var(--size-4-2);
	z-index: 10;
}

.dn-preview-titlebar {
	width: 90%;
	cursor: move;
	border-left: 4px solid var(--text-faint);
	padding-left: 8px;
}

.dn-preview-titlebar:hover {
	border-left: 4px solid var(--text-accent);
}

.dn-preview-titlebar .dn-property-value {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.dn-preview .dn-pr-file-name::before {
	content: 'File: ';
	color: var(--dn-file-property-color);
}

.dn-preview .dn-pr-file-path::before {
	content: 'Path: ';
	color: var(--dn-file-property-color);
}

.dn-preview .dn-pr-file-path {
	padding: var(--size-4-1) 0;
	border-bottom: 1px solid var(--dn-border-color);
}

.dn-preview .dn-pr-content {
	display: flex;
	flex-direction: column;
	flex-grow: 1;
	width: 100%;
	background: var(--background-primary);
	border-radius: var(--radius-s);
	cursor: auto;
	position: relative;
}

.dn-preview .dn-pr-content img {
	width: 100%;
	height: auto;
}

.dn-preview .dn-pr-content span.pdf-embed {
	width: 100% !important;
	height: 2000px;
	max-height: inherit;
	overflow: auto;
}

.dn-preview .dn-pr-content span.pdf-embed .pdf-toolbar-right {
	display: none;
}

.dn-preview .dn-pr-content span.pdf-embed .pdf-viewer-container.node-insert-event {
	overflow: auto;
}

.dn-preview .markdown-embed-title {
	display: none;
}

.dn-preview .markdown-embed {
	border-inline-start: 0;
}

.dn-property-row {
	display: flex;
	margin-bottom: var(--size-4-1);
}

.dn-property-name-sm {
	flex: 0 0 auto;
	width: 62px;
	text-align: left;
	color: var(--dn-file-property-color);
}

.dn-property-name {
	flex: 0 0 auto;
	width: 108px;
	text-align: left;
	color: var(--dn-file-property-color);
}

.dn-property-value {
	flex: 1;
	padding-left: 0;
	text-align: left;
}

.dn-property-value span.nav-file-tag {
	margin: 0;
}

/* Tags */
.dn-property-row a.tag,
table#dn-table a.tag {
	display: inline-block;
	margin-right: var(--size-4-1);
	font-size: var(--font-size);
	text-decoration: none;
}

a.dn-tag {
	display: inline-block;
	border-radius: var(--tag-radius);
	margin-right: var(--size-4-1);
	padding-inline-end: var(--tag-padding-x);
	font-size: var(--font-size);
	background-color: var(--tag-background);
	color: var(--tag-color);
	text-decoration: none;
}

a.dn-tag:hover {
	color: var(--text-accent-hover);
}