## Design
- https://graphsql.atlassian.net/wiki/spaces/TGDesignDocs/pages/3003351932/Marketplace+Integration+Design
- steps:
![[Pasted image 20250410154712.png|400]]
	- users click subscribe in aws, redirect to controller
	- controller validate token, set a cookie with token and redirect to fe
	- users login to fe
	- fe  get token/marketplace id from the cookie and call controller api
- tables
	- `(aws|azure|gcp)_marketplace_users` which will store all associations between `users` and their marketplace identities.
- when creating solution
	- skip credit card check
	- A `UserUsageInterval` will be created
	- `IsMarketplace` flag will be marked
- usage reporting
	- A go routine would hourly aggregate usage metrics from the users' `user_usage_intervals` and send the report to the corresponding cloud provider.
- Communication with the Marketplace
	- SQS, Azure - via a webhook, GCP - via pub/sub
## [Integrating Your SaaS Application with AWS Marketplace - YouTube](https://www.youtube.com/watch?v=glG44f-L8us)
- After register the product, will get product code, SNS topic, page url from aws
- verification: when users subscribe, a post request to the controller with token
	- token: customer ident, product code
- monitor changes: changes to customer subscription, entitlement statuses
	- aws push to the sns topic
	- need to set up a sqs queue that subscribes to the sns topic
- send metering records
## Tasks
- [x] #task Test marketplace association, marketplace status ➕ 2025-04-14 ⏳ 2025-04-18 ✅ 2025-04-18
- [x] #task Payment methods list UI ➕ 2025-04-14 📅 2025-04-14 ✅ 2025-04-14
- marketplace association
	- save platform info to session storage when redirected from marketplace
```js
  // Check if user comes from marketplace subscription.
  useEffect(() => {
    const params = new URLSearchParams(location.search);

    const isFromMarketplace = params.get('marketplace');
    const platform = params.get('platform');

    if (isFromMarketplace === 'true' && platform) {
      const marketplaceKey = `${localStoragePrefix}.${MARKETPLACE_SUBSCRIPTION_KEY}`;
      sessionStorage.setItem(marketplaceKey, platform);

      // Log user out when they come from marketplace.
      if (isAuthenticated) {
        logoutClearance();
        logout({
          returnTo: window.location.origin
        });
      }
    }
  }, [isAuthenticated]);
```
	- get platform info from session storage
	- call `/api/billing/marketplace/:platform`
```js
  useEffect(() => {
    const marketplaceKey = `${localStoragePrefix}.${MARKETPLACE_SUBSCRIPTION_KEY}`;
    const platform = sessionStorage.getItem(marketplaceKey);

    if (platform) {
      if (platform === 'azure') {
        const auth0Profile = JSON.parse(localStorage.getItem(`${localStoragePrefix}.auth0`));

        // Check if users log in with Azure account.
        if (!auth0Profile.sub?.startsWith(AZURE_AD_SUFFIX)) {
          setMarketplaceAssociationModal(true);
        } else {
          associateMarketplaceMutation.mutate(platform);
        }
      } else {
        associateMarketplaceMutation.mutate(platform);
      }

      // Remove the flag that triggers marketplace association.
      sessionStorage.removeItem(marketplaceKey);
    }
  }, []);
```
- display marketplace status
## QA
- dev marketplace:  https://aws.amazon.com/marketplace/pp/prodview-vuaogicwincuy
- when using marketplace subscription, do we still have usage info in billing?
- 是否应该deprecate set default card api，
- 如果用户unsubscribe了marketplace，支付方式是否会自动切换到Stripe
- 是否应该显示当前使用的支付方式？
- `Customer needs to switch payment method to STRIPE, then wait until next billing cycle before they unsubscribe from marketplace` 我们无法阻止