## Tasks
- [x] #task [[BY<PERSON>]Unable to get data profile in tg byoc workgroup](https://graphsql.atlassian.net/browse/TCE-5268) 📅 2024-09-05
- [x] #task [Unable to access the Admin Portal for workspaces within the BYOC workgroup.](https://graphsql.atlassian.net/browse/TCE-5122) ⏳ 2024-09-04 📅 2024-09-03
- [x] #task dark mode ⏳ 2024-08-29 📅 2024-08-26
- [-] #task [[BYOC]Unable to install solution for private workspace](https://graphsql.atlassian.net/browse/TCE-5267) 📅 2024-08-23 ❌ 2024-09-04
- [-] #task [[BYOC]Cannot create workgroup/workspace if there's no available CP](https://graphsql.atlassian.net/browse/TCE-5141) ⏳ 2024-08-07 ❌ 2024-08-07
- [x] [BYOC] UI - Cloud Provider List table should sort by create date of cloud provider (or follow WS style)
- [x] [Don' display failed providers in workgroup creation page](https://graphsql.atlassian.net/browse/TCE-5049)
- [x] [Error message exceeds the boundary](https://graphsql.atlassian.net/browse/TCE-5048)
## Notes
- arn:aws:iam::381491969203:role/duhao-cp-role-9203
- when enable secure_connection, it will disable public internet access, instead set up private link endpoint service.