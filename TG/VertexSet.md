---
up: "[[GSQL autocompletions]]"
---
- grammar
	- vSetVarDeclStmt
```
vSetVarDeclStmt := vertexSetName ["(" vertexType ")"]
                   "=" (seedSet | simpleSet | selectBlock)

seedSet := "{" [seed ["," seed ]*] "}"

seed := '_'
      | ANY
      | vertexSetName
      | globalAccumName
      | vertexType ".*"
      | paramName
      | "SelectVertex" selectVertParams

simpleSet := vertexSetName | "(" simpleSet ")"
           | simpleSet (UNION | INTERSECT | MINUS) simpleSet
```
- parameterType
```
parameterType := baseType
               | [ SET | BAG ] "<" baseType ">"
               | FILE
```

## Infer type of a seedSet
### Tasks
- [x] define the grammar rule of the seedSet
- [x] infer the type from seed: _, any, vertexType.*, paramName_
    - if name is not a vertex/edge type, infer its type
    - get vertex set type: string -> string
	    - search vertex set statements
		    - infer seeds type
			    - is `_`, "ANY", return `_`
			    - is `vertexType.*`, return vertexType
				- o/w, find from param vertex types
	- get param vertex types: string -> string
		- search the paramlist for vertex type
		- return a map
