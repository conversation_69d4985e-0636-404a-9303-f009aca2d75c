{"version": 2, "projects": [{"fieldConfig": {}, "defaultName": "", "templates": [], "excludedNotes": [], "isDefault": true, "dataSource": {"kind": "folder", "config": {"path": "Books", "recursive": false}}, "newNotesFolder": "", "views": [{"config": {"fieldConfig": {"path": {"hide": true}, "created": {"hide": true}}}, "filter": {"conjunction": "and", "conditions": []}, "colors": {"conditions": []}, "sort": {"criteria": []}, "id": "184b3e2b-e7dd-4e70-8ccb-00244ba95a47", "name": "Table", "type": "table"}, {"config": {"coverField": "", "includeFields": ["highlight"], "fitStyle": "cover"}, "filter": {"conjunction": "and", "conditions": []}, "colors": {"conditions": []}, "sort": {"criteria": []}, "id": "4b900532-bedc-4d06-a531-3f2feca0814f", "name": "Gallery", "type": "gallery"}], "id": "93ed9d10-0ac8-47f8-b7f7-2611e03e6137", "name": "Books"}], "archives": [], "preferences": {"projectSizeLimit": 1000, "frontmatter": {"quoteStrings": "PLAIN"}, "locale": {"firstDayOfWeek": "monday"}, "commands": [], "linkBehavior": "open-editor"}}