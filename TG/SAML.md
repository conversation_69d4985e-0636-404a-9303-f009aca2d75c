- identity provider(IDP): centralized point of authentication that source of truth that identity stored
- service provider(SP): the user is trying to log into
- saml assertion
	- name id
	- method of auth(saml context of class)
- saml request: SP -> IDP
- saml response: IDP -> SP
		- assertions: signed using xml signature(DSig)
- assertion consumer service: response is sent to a specific endpoint at SP
	- check the assertion signatures and validate the entire document
- relay state: where the user was before they triggered authentication
- what is saml trust;;the configuration between the IDP and SP
- provisioning: keep identities in sync(from IDP to SP, IDP is source of truth)