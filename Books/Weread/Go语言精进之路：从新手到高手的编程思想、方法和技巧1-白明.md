---
doc_type: weread-highlights-reviews
bookId: "42557145"
reviewCount: 0
noteCount: 29
author: 白明
cover: https://wfqqreader-1252317822.image.myqcloud.com/cover/145/42557145/t6_42557145.jpg
progress: 55%
readingTime: 6小时32分钟
readingDate: 2024-05-01
isbn: 9787111698210
lastReadDate: 2024-05-13

---
# 元数据
> [!abstract] Go语言精进之路：从新手到高手的编程思想、方法和技巧1
> - ![ Go语言精进之路：从新手到高手的编程思想、方法和技巧1|200](https://wfqqreader-1252317822.image.myqcloud.com/cover/145/42557145/t6_42557145.jpg)
> - 书名： Go语言精进之路：从新手到高手的编程思想、方法和技巧1
> - 作者： 白明
> - 简介： Go入门容易，精进难，如何才能像Go开发团队那样写出符合Go思维和语言惯例的高质量代码呢？本书将从编程思维和实践技巧2个维度给出答案，帮助你在Go进阶的路上事半功倍。编程思维层面：只有真正领悟了一门语言的设计哲学和编程思维，并能将之用于实践，才算精通了这门语言。本书从Go语言设计者的视角对Go背后的设计哲学和编程思想进行了梳理和分析，指引读者体会那些看似随意实则经过深思熟虑的设计背后的秘密。实践技巧层面：实践技巧源于对Go开发团队和Go社区开发的高质量代码的阅读、挖掘和归纳，从项目结构、代码风格、语法及其实现、接口、并发、同步、错误与异常处理、测试与调试、性能优化、标准库、第三方库、工具链、最佳实践、工程实践等多个方面给出了改善Go代码质量、写出符合Go思维和惯例的代码的有效实践。学完这本书，你将拥有和Go专家一样的编程思维，写出符合Go惯例和风格的高质量代码，从众多Go初学者中脱颖而出，快速实现从Go新手到专家的转变！
> - 出版时间： 2022-01-01 00:00:00
> - ISBN： 9787111698210
> - 分类： 计算机-编程设计
> - 出版社： 机械工业出版社
> - PC地址：https://weread.qq.com/web/reader/f343248072895ed9f34f408

# 高亮划线

## 封面

## 版权页

## 作者简介

## 推荐语

## 推荐序

## 前言

## 第一部分 熟知Go语言的一切

### 第1条 了解Go语言的诞生与演进

#### 1.1 Go语言的诞生

#### 1.2 Go语言的早期团队和演进历程

#### 1.3 Go语言正式发布并开源

### 第2条 选择适当的Go语言版本

#### 2.1 Go语言的先祖

#### 2.2 Go语言的版本发布历史

#### 2.3 Go语言的版本选择建议

### 第3条 理解Go语言的设计哲学

#### 3.1 追求简单，少即是多

#### 3.2 偏好组合，正交解耦

#### 3.3 原生并发，轻量高效

> 📌 为了解决这些问题，Go果断放弃了传统的基于操作系统线程的并发模型，而采用了用户层轻量级线程或者说是类协程（coroutine），Go将之称为goroutine 
> ⏱ 2024-05-02 12:02:30 ^42557145-19-2367-2547

> 📌 作系统的眼中只有线程，它甚至不知道goroutine的存在。goroutine的调度全靠Go自己完成，实现Go程序内goroutine之间公平地竞争CPU资源的任务就落到了Go运行时头上。 
> ⏱ 2024-05-02 12:02:14 ^42557145-19-2807-2901

> 📌 为此Rob Pike曾做过一次关于“并发不是并行”[2]的主题分享，图文并茂地讲解了并发（Concurrency）和并行（Parallelism）的区别 
> ⏱ 2024-05-01 23:54:32 ^42557145-19-3863-4036

#### 3.4 面向工程，“自带电池”

### 第4条 使用Go语言原生编程思维来写Go代码

#### 4.1 语言与思维——来自大师的观点

#### 4.2 现实中的“投影”

> 📌 算法描述：先用最小的素数2去筛，把2的倍数筛除；下一个未筛除的数就是素数（这里是3）。再用这个素数3去筛，筛除3的倍数……这样不断重复下去，直到筛完为止（算法图示见图4-1）。 
> ⏱ 2024-05-04 00:03:31 ^42557145-23-628-716

#### 4.3 Go语言原生编程思维

## 第二部分 项目结构、代码风格与标识符命名

### 第5条 使用得到公认且广泛使用的项目结构

#### 5.1 Go项目的项目结构

#### 5.2 Go语言典型项目结构

### 第6条 提交前使用gofmt格式化源码

#### 6.1 gofmt：Go语言在解决规模化问题上的最佳实践

#### 6.2 使用gofmt

#### 6.3 使用goimports

> 📌 oimports在gofmt功能的基础上增加了对包导入列表的维护功能，可根据源码的最新变动自动从导入包列表中增删包。安装goimports的方法很简单：$go get golang.org/x/tools/cmd/goimports 
> ⏱ 2024-05-04 00:41:51 ^42557145-32-721-879

#### 6.4 将gofmt/goimports与IDE或编辑器工具集成

### 第7条 使用Go命名惯例对标识符进行命名

#### 7.1 简单且一致

#### 7.2 利用上下文环境，让最短的名字携带足够多的信息

## 第三部分 声明、类型、语句与控制结构

### 第8条 使用一致的变量声明形式

#### 8.1 包级变量的声明形式

#### 8.2 局部变量的声明形式

### 第9条 使用无类型常量简化代码

#### 9.1 Go常量溯源

#### 9.2 有类型常量带来的烦恼

> 📌 我们看到，Go在处理不同类型的变量间的运算时不支持隐式的类型转换。Go的设计者认为，隐式转换带来的便利性不足以抵消其带来的诸多问题[1] 
> ⏱ 2024-05-04 10:10:08 ^42557145-43-712-866

#### 9.3 无类型常量消除烦恼，简化代码

> 📌 Go的无类型常量恰恰就拥有像字面值这样的特性，该特性使得无类型常量在参与变量赋值和计算过程时无须显式类型转换，从而达到简化代码的目的： 
> ⏱ 2024-05-04 10:13:13 ^42557145-44-1111-1178

### 第10条 使用iota实现枚举常量

> 📌 iota是Go语言的一个预定义标识符，它表示的是const声明块（包括单行声明）中每个常量所处位置在块中的偏移值（从零开始） 
> ⏱ 2024-05-04 10:19:03 ^42557145-45-1821-1883

### 第11条 尽量定义零值可用的类型

#### 11.1 Go类型的零值

#### 11.2 零值可用

### 第12条 使用复合字面值作为初值构造器

#### 12.1 结构体复合字面值

#### 12.2 数组/切片复合字面值

> 📌 / [10]float{-1, 0, 0, 0, -0.1, -0.1, 0, 0.1, 0, -1}fnumbers := [...]float{-1, 4: -0.1, -0.1, 7:0.1, 9: -1} 
> ⏱ 2024-05-04 10:39:03 ^42557145-51-569-680

#### 12.3 map复合字面值

### 第13条 了解切片实现原理并高效使用

#### 13.1 切片究竟是什么

> 📌 而在Go语言中传递数组是纯粹的值拷贝，对于元素类型长度较大或元素个数较多的数组，如果直接以数组类型参数传递到函数中会有不小的性能损耗。 
> ⏱ 2024-05-04 10:42:43 ^42557145-54-836-903

> 📌 切片之于数组就像是文件描述符之于文件。 
> ⏱ 2024-05-04 10:42:54 ^42557145-54-1075-1101

> 📌 操作的数组元素个数为4个（high-low）。切片的容量值（cap）取决于底层数组的长度。从切片s的第一个元素s[0]，即u[3]到数组末尾一共有7个存储元素的槽位，因此切片s的cap为7。 
> ⏱ 2024-05-04 10:45:51 ^42557145-54-3295-3390

#### 13.2 切片的高级特性：动态扩容

> 📌 通过语法u[low: high]形式进行数组切片化而创建的切片，一旦切片cap触碰到数组的上界，再对切片进行append操作，切片就会和原数组解除绑定： 
> ⏱ 2024-05-04 11:55:01 ^42557145-55-2747-2823

#### 13.3 尽量使用cap参数创建切片

### 第14条 了解map实现原理并高效使用

#### 14.1 什么是map

> 📌 和切片一样，创建map类型变量有两种方式：一种是使用复合字面值，另一种是使用make这个预声明的内置函数。 
> ⏱ 2024-05-04 11:57:23 ^42557145-58-938-991

> 📌 切片一样，map也是引用类型，将map类型变量作为函数参数传入不会有很大的性能损耗，并且在函数内部对map变量的修改在函数外部也是可见的，比如下面的例子： 
> ⏱ 2024-05-04 11:57:45 ^42557145-58-1513-1590

#### 14.2 map的基本操作

> 📌 Go语言的一个最佳实践是总是使用“comma ok”惯用法读取map中的值。 
> ⏱ 2024-05-04 11:59:10 ^42557145-59-2092-2166

#### 14.3 map的内部实现

#### 14.4 尽量使用cap参数创建map

### 第15条 了解string实现原理并高效使用

#### 15.1 Go语言的字符串类型

> 📌 Go string类型数据是不可变的，因此一旦有了初值，那块数据就不会改变，其长度也不会改变。 
> ⏱ 2024-05-04 12:18:00 ^42557145-63-3241-3288

> 📌 UTF-8中，大多数中文字符都使用三字节表示。[]byte(s)的转型让我们获得了s底层存储的“复制品”，从而得到每个汉字字符对应的UTF-8编码字节 
> ⏱ 2024-05-04 12:20:55 ^42557145-63-5754-5829

#### 15.2 字符串的内部表示

#### 15.3 字符串的高效构造

#### 15.4 字符串相关的高效转换

### 第16条 理解Go语言的包导入

#### 16.1 Go程序构建过程

#### 16.2 究竟是路径名还是包名

#### 16.3 包名冲突问题

### 第17条 理解Go语言表达式的求值顺序

#### 17.1 包级别变量声明语句中的表达式求值顺序

#### 17.2 普通求值顺序

#### 17.3 赋值语句的求值

#### 17.4 switch/select语句中的表达式求值

### 第18条 理解Go语言代码块与作用域

#### 18.1 Go代码块与作用域简介

#### 18.2 if条件控制语句的代码块

#### 18.3 其他控制语句的代码块规则简介

### 第19条 了解Go语言控制语句惯用法及使用注意事项

> 📌 witch的case语句执行完毕后，默认不会像C语言那样继续执行下一个case中的语句，除非显式使用fallthrough关键字，这“填补”了C语言中每个case语句都要以break收尾的“坑”； 
> ⏱ 2024-05-05 00:48:09 ^42557145-80-630-728

#### 19.1 使用if控制语句时应遵循“快乐路径”原则

#### 19.2 for range的避“坑”指南

> 📌 range表达式的复制行为还会带来一些性能上的消耗，尤其是当range表达式的类型为数组时，range需要复制整个数组；而当range表达式类型为数组指针或切片时，这个消耗将小得多，因为仅仅需要复制一个指针或一个切片的内部表示（一个结构体）即可。 
> ⏱ 2024-05-05 00:59:35 ^42557145-82-6344-6467

#### 19.3 break跳到哪里去了

#### 19.4 尽量用case表达式列表替代fallthrough

## 第四部分 函数与方法

### 第20条 在init函数中检查包级变量的初始状态

#### 20.1 认识init函数

#### 20.2 程序初始化顺序

#### 20.3 使用init函数检查包级变量的初始状态

### 第21条 让自己习惯于函数是“一等公民”

#### 21.1 什么是“一等公民”

#### 21.2 函数作为“一等公民”的特殊运用

> 📌 Go是类型安全的语言，不允许隐式类型转换， 
> ⏱ 2024-05-05 10:21:37 ^42557145-92-468-489

> 📌 也就是说，http.HandlerFunc(greeting)这句代码的真正含义是将函数greeting显式转换为HandlerFunc类型，而后者实现了Handler接口，这样转型后的greeting就满足了ListenAndServe函数第二个参数的要求。 
> ⏱ 2024-05-05 10:24:27 ^42557145-92-2636-2766

### 第22条 使用defer让函数更简洁、更健壮

#### 22.1 defer的运作机制

#### 22.2 defer的常见用法

> 📌 更为典型的莫过于在出入函数时打印留痕日志（一般在调试日志级别下） 
> ⏱ 2024-05-05 10:40:08 ^42557145-95-4483-4515

#### 22.3 关于defer的几个关键问题

### 第23条 理解方法的本质以选择正确的receiver类型

> 📌 }同理，可以推出：不能横跨Go包为其他包内的自定义类型定义方法。 
> ⏱ 2024-05-05 10:48:14 ^42557145-97-1389-1481

> 📌 eceiver参数的基类型本身不能是指针类型或接口类型，下面的示例展示了这点：type MyInt 
> ⏱ 2024-05-05 10:49:08 ^42557145-97-1634-1696

#### 23.1 方法的本质

#### 23.2 选择正确的receiver类型

> 📌 1）当receiver参数的类型为T时，选择值类型的receiver选择以T作为receiver参数类型时，T的M1方法等价为M1(t T)。Go函数的参数采用的是值复制传递，也就是说M1函数体中的t是T类型实例的一个副本，这样在M1函数的实现中对参数t做任何修改都只会影响副本，而不会影响到原T类型实例。 
> ⏱ 2024-05-05 10:54:45 ^42557145-99-675-857

> 📌 ）当receiver参数的类型为*T时，选择指针类型的receiver选择以*T作为receiver参数类型时，T的M2方法等价为M2(t *T)。我们传递给M2函数的t是T类型实例的地址，这样M2函数体中对参数t做的任何修改都会反映到原T类型实例上。 
> ⏱ 2024-05-05 10:54:59 ^42557145-99-888-1043

> 📌 到这里，我们可以得出receiver类型选用的初步结论。如果要对类型实例进行修改，那么为receiver选择*T类型。如果没有对类型实例修改的需求，那么为receiver选择T类型或*T类型均可；但考虑到Go方法调用时，receiver是以值复制的形式传入方法中的，如果类型的size较大，以值形式传入会导致较大损耗，这时选择*T作为receiver类型会更好些。 
> ⏱ 2024-05-13 08:28:52 ^42557145-99-2346-2569

#### 23.3 基于对Go方法本质的理解巧解难题

### 第24条 方法集合决定接口实现

#### 24.1 方法集合

#### 24.2 类型嵌入与方法集合

> 📌 按Go语言惯例，接口类型中仅包含少量方法，并且常常仅有一个方法。 
> ⏱ 2024-05-13 08:46:01 ^42557145-103-723-755

#### 24.3 defined类型的方法集合

#### 24.4 类型别名的方法集合

### 第25条 了解变长参数函数的妙用

#### 25.1 什么是变长参数函数

#### 25.2 模拟函数重载

#### 25.3 模拟实现函数的可选参数与默认参数

#### 25.4 实现功能选项模式

## 第五部分 接口

### 第26条 了解接口类型变量的内部表示

#### 26.1 nil error值!= nil

#### 26.2 接口类型变量的内部表示

#### 26.3 输出接口类型变量内部表示的详细信息

#### 26.4 接口类型的装箱原理

### 第27条 尽量定义小接口

#### 27.1 Go推荐定义小接口

#### 27.2 小接口的优势

#### 27.3 定义小接口可以遵循的一些点

### 第28条 尽量避免使用空接口作为函数参数类型

### 第29条 使用接口作为程序水平组合的连接点

#### 29.1 一切皆组合

#### 29.2 垂直组合回顾

#### 29.3 以接口为连接点的水平组合

### 第30条 使用接口提高代码的可测试性

#### 30.1 实现一个附加免责声明的电子邮件发送函数

#### 30.2 使用接口来降低耦合

## 第六部分 并发编程

### 第31条 优先考虑并发设计

#### 31.1 并发与并行

#### 31.2 Go并发设计实例

### 第32条 了解goroutine的调度原理

#### 32.1 goroutine调度器

#### 32.2 goroutine调度模型与演进过程

#### 32.3 对goroutine调度器原理的进一步理解

#### 32.4 调度器状态的查看方法

#### 32.5 goroutine调度实例简要分析

### 第33条 掌握Go并发模型和常见并发模式

#### 33.1 Go并发模型

#### 33.2 Go常见的并发模式

### 第34条 了解channel的妙用

#### 34.1 无缓冲channel

#### 34.2 带缓冲channel

#### 34.3 nil channel的妙用

#### 34.4 与select结合使用的一些惯用法

### 第35条 了解sync包的正确用法

#### 35.1 sync包还是channel

#### 35.2 使用sync包的注意事项

#### 35.3 互斥锁还是读写锁

#### 35.4 条件变量

#### 35.5 使用sync.Once实现单例模式

#### 35.6 使用sync.Pool减轻垃圾回收压力

### 第36条 使用atomic包实现伸缩性更好的并发读取

#### 36.1 atomic包与原子操作

#### 36.2 对共享整型变量的无锁读写

#### 36.3 对共享自定义类型变量的无锁读写

## 第七部分 错误处理

### 第37条 了解错误处理的4种策略

#### 37.1 构造错误值

#### 37.2 透明错误处理策略

#### 37.3 “哨兵”错误处理策略

#### 37.4 错误值类型检视策略

#### 37.5 错误行为特征检视策略

### 第38条 尽量优化反复出现的if err != nil

#### 38.1 两种观点

#### 38.2 尽量优化

#### 38.3 优化思路

### 第39条 不要使用panic进行正常的错误处理

#### 39.1 Go的panic不是Java的checked exception

#### 39.2 panic的典型应用

#### 39.3 理解panic的输出信息

# 读书笔记

# 本书评论

