## perceptron
  ![alt text](image-106.png)
  - A way you can think about the perceptron is that it's a device that makes decisions by weighing up evidence.
  - By varying the weights and the threshold, we can get different models of decision-making.j
  - a perceptron in the second layer can make a decision at a more complex and more abstract level than perceptrons in the first layer
  - The adder example demonstrates how a network of perceptrons can be used to simulate a circuit containing many NAND gates. And because NAND gates are universal for computation, it follows that perceptrons are also universal for computation.
  - It turns out that we can devise learning algorithms which can automatically tune the weights and biases of a network of artificial neurons.
## Sigmoid neurons
  - Sigmoid neurons are similar to perceptrons, but modified so that small changes in their weights and bias cause only a small change in their output. 
  - That's the crucial fact which will allow a network of sigmoid neurons to learn. 使用如下函数方便计算微分。
    ![alt text](image-107.png)
    ![alt text](image-108.png)
  - Indeed, it's the smoothness of the σ function that is the crucial fact, not its detailed form. The smoothness of σ means that small changes Δwj in the weights and Δb in the bias will produce a small change Δoutput in the output from the neuron. 
  - Δoutput is a linear function of the changes Δwj and Δb in the weights and bias.This linearity makes it easy to choose smallkj changes in the weights and biases to achieve any desired small change in the output. In fact, calculus tells us that ΔoutputΔoutput is well approximated by ![[Pasted image 20240414201730.png]]
  ## [The architecture of neural networks](http://neuralnetworksanddeeplearning.com/chap1.html#the_architecture_of_neural_networks)
  ![[Pasted image 20240414202530.png]]
  ## [A simple network to classify handwritten digits](http://neuralnetworksanddeeplearning.com/chap1.html#a_simple_network_to_classify_handwritten_digits)
  ![[Pasted image 20240414202626.png]]
  - our training data for the network will consist of many 28 by 28 pixel images of scanned handwritten digits, and so the input layer contains 784=28×28784=28×28 neurons.

  ## [Learning with gradient descent](http://neuralnetworksanddeeplearning.com/chap1.html#learning_with_gradient_descent)

### Cost function
- _quadratic_ cost function
	- Why introduce the quadratic cost?
	- our goal in training a neural network is to find weights and biases which minimize the quadratic cost function
 - imagine that we've simply been given a function of many variables and we want to minimize that function
 - We're going to develop a technique called _gradient descent_ which can be used to solve such minimization problems.
##  gradient descent
- think about a two variables function, how to find its minimum![[Pasted image 20240414204124.png]]
### gradient vector
![[Pasted image 20240414204145.png]]
### stochastic gradient descent
- can be used to speed up learning.
## [Implementing our network to classify digits](http://neuralnetworksanddeeplearning.com/chap1.html#implementing_our_network_to_classify_digits)
### _backpropagation_ algorithm
- is a fast way of computing the gradient of the cost function.