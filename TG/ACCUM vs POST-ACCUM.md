## ACCUM vs POST-ACCUM
- accum
	- 我们可以认为 FROM 和 WHERE 子句指定了一个绑定表，其中 FROM 子句指定了模式，而 WHERE 子句对匹配的模式实例进行后过滤——结果是一个表，表中的每一行都是一个模式实例
	- `ACCUM`在绑定表中独立处理每一行。
- post-accum
	- `POST-ACCUM`可以在没有`ACCUM`的情况下使用。如果它前面有一个`ACCUM`子句，那么它的语句可以访问由`ACCUM`子句计算出的累加器的新快照值。
	- `POST-ACCUM`从绑定表中选择的顶点集循环
	- “POST-ACCUM”子句旨在基于绑定表中的选定顶点集进行一些计算。它针对绑定表中引用的顶点列的每个不同值执行其语句一次。
```sql
USE GRAPH financialGraph

CREATE OR REPLACE DISTRIBUTED QUERY q8 () SYNTAX V3 {

     SumAccum<int> @@edgeCnt = 0;
     MaxAccum<int> @maxAmount = 0;
     MinAccum<int> @minAmount = 100000;

     MaxAccum<int> @@maxSenderAmount = 0;
     MinAccum<int> @@minReceiverAmount = 100000;
     SumAccum<int> @@bCnt = 0;
     SumAccum<int> @@aCnt = 0;

    S = SELECT b
        FROM (a:Account) - [e:transfer] -> (b:Account)
        WHERE NOT a.isBlocked
        ACCUM  a.@maxAmount += e.amount, //sender max amount
               b.@minAmount += e.amount, //receiver min amount
                @@edgeCnt +=1
        POST-ACCUM (a) @@maxSenderAmount += a.@maxAmount
        POST-ACCUM (b) @@minReceiverAmount += b.@minAmount
        POST-ACCUM (a) @@aCnt +=1
        POST-ACCUM (b) @@bCnt +=1 ;

  PRINT @@maxSenderAmount,  @@minReceiverAmount;
  PRINT @@edgeCnt, @@aCnt, @@bCnt;

}

INSTALL QUERY q8


RUN QUERY q8()
```