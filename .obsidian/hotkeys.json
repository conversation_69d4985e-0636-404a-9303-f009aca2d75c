{"editing-toolbar:hide-show-menu": [{"modifiers": ["Ctrl"], "key": "T"}], "editor:insert-link": [], "app:toggle-left-sidebar": [{"modifiers": ["Mod"], "key": "K"}], "app:toggle-right-sidebar": [{"modifiers": ["Mod"], "key": "L"}], "editor:toggle-checklist-status": [{"modifiers": ["Mod"], "key": "M"}], "editor:open-link-in-new-leaf": [], "workspace:new-tab": [{"modifiers": ["Mod"], "key": "T"}], "workspace:undo-close-pane": [], "tag-pane:open": [{"modifiers": ["Mod", "Shift"], "key": "T"}], "app:delete-file": [{"modifiers": ["Mod", "Shift"], "key": "D"}], "markdown:add-metadata-property": [{"modifiers": ["Ctrl"], "key": "P"}], "markdown:edit-metadata-property": [{"modifiers": ["Mod"], "key": ";"}], "bookmarks:open": [{"modifiers": ["Ctrl", "Shift"], "key": "B"}], "editing-toolbar:fullscreen-focus": [{"modifiers": ["Ctrl", "Shift"], "key": "F"}], "app:go-back": [{"modifiers": ["Mod"], "key": "["}], "app:go-forward": [{"modifiers": ["Mod"], "key": "]"}], "workspace:previous-tab": [{"modifiers": ["Ctrl"], "key": "["}], "workspace:next-tab": [{"modifiers": ["Ctrl"], "key": "]"}], "obsidian-excalidraw-plugin:excalidraw-autocreate-newtab": [{"modifiers": ["Ctrl"], "key": "D"}], "breadcrumbs:Refresh-Breadcrumbs-Index": [{"modifiers": ["Mod"], "key": "R"}], "obsidian-spaced-repetition:srs-review-flashcards": [{"modifiers": ["Ctrl"], "key": "R"}], "workspace:copy-path": [{"modifiers": ["Ctrl"], "key": "C"}], "remotely-save:start-sync": [{"modifiers": ["Mod"], "key": "S"}], "editor:save-file": [], "command-palette:open": [{"modifiers": ["Mod", "Shift"], "key": "P"}], "editor:toggle-fold-properties": [{"modifiers": ["Ctrl", "Shift"], "key": "P"}], "workspace:toggle-pin": [{"modifiers": ["Mod"], "key": "P"}], "quickadd:runQuickAdd": [{"modifiers": ["Mod", "Shift"], "key": "A"}], "workspace:open-in-new-window": [{"modifiers": ["Mod", "Shift"], "key": "O"}], "graph:open-local": [{"modifiers": ["Mod", "Shift"], "key": "G"}], "excalibrain:excalibrain-start": [{"modifiers": ["Mod"], "key": "G"}], "graph:open": []}