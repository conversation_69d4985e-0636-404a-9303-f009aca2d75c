/* === README ===
	Snippet: MCL Multi Column / Author: <PERSON><PERSON><PERSON> / twitter: @faizkhuzaimah / github: https://github.com/efemkay

	Ver 0.10.0 (updated 2024-09-01)
	- introduce fw dw and pw
	- added left-right margin for Float Callout
	- refactor List Column code to improve performance
	Ver 0.9.10 (updated 2024-06-26)
	- fix issues #81 and #89
	Ver 0.9.9 (updated 2024-01-25)
	- code cleanup (including -small -medium -large)
	- improve css performance on float but require cssclass use again
	Ver 0.9.8 (updated 2023-11-03)
	- fixes on sub-callout becomes flexible height (and introduce option for such)
	Ver 0.9.7 (updated 2023-10-13)
	- GH FR issue #41, #42 and fixed GH issue #35. MC Callout now supports sub-blockquote and sub-paragraph
	- GH FR issue #43. MC Callout now supports Multi Column within Multi Column
	Ver 0.9.5 (updated 2023-07-30)
	- fixes on minimal float affecting Minimal own wide table feature
	- fixes on properties padding affecting Minimal
	Ver 0.9.4 (updated 2023-07-06)
	- improved MC Callout compatibility with Minimal theme V7.0
	- improved MC Callout compatibility with Blue Topaz
	Ver 0.9.3 (updated 2023-06-24)
	- add support for List Column for li that's also a header
	- fixes on column ruler to apply properly only for List Column on first ul only
	- fixes on Float Callout in LP not floating due to additional div layer
	Ver 0.9.2 (updated 2023-05-27)
	- fix MC List Card mistakenly affect non List Card bullet list padding in same note
	- fix obsidian default bullet "draw" not rendered properly in List Column (for the ones using tag and contextual typography plugin)
	Ver 0.9.1 (updated 2023-04-06)
	- fix MC "fixed center" option not properly coded
	- fix MC List Grid previous not support sublist
	- fix MC List Column margin top for first column
	- fix MC List Grid for first bullet header
	Ver 0.9.0 (updated 2023-04-01)
	- MC - add option to hide SNW block ref for images in MC Callout
	- MC - added wide option for List Grid/Card
	- MC - added option to adjust float callout top margin
	- MC - added fixed width option
	- fix support minimal Callout style Outlined
	- List Column/Grid/Card fully support :has()

	What is this snippet for?
	- to allow Obsidian users to change the layout with preset css by just specifying
	  the cssClass in the frontmatter (YAML) or naming the Callout block
	- available cssClass
		- cssClass: two-column-list
		- cssClass: {.two-column-list-block}
		- cssClass: three-column-list
		- cssClass: {.three-column-list-block}
		- cssClass: multi-column-list
	- available Callout format
		- [!multi-column]
		- [!blank] / [!blank-container]

	Compatibility
	- tested to work fine with the following Community Theme
		- Default Theme
		- Big Sur / Blue Topaz / California Cost / Notation / Pisum / Prism / Things / Typewriter (with minor issue on title color)
	- known to be not working
		- Primary (due to the theme heaving theming, box-shadow and so on)

	Credits
	- Kevin Powell Youtube channel
	- https://css-tricks.com/
	- discord OMG members @Cáo thoát tục (Cáo thoát tục#6993), @mulfok (mulfok#6931) and @Rainbell (Rainbell#3617)
	- kepano for accomodating my snippets adjustment
	- trainbuffer for numerous help and assisted css snippets
*/



body{
	/* for list column, grid and card */
	--list-min-width: 200px;
	--col-rule-color: var(--background-modifier-border); /* #b3b3b3; */
	--col-rule-width: 1px;
	--list-grid-min-width: 250px;
	--list-grid-wide-min-width: 350px;

}
.theme-dark {
/*	--background-mcl-card: hsl(300, 5%, 15%); */
}
.theme-light {
/*	--background-mcl-card: hsl(300, 5%, 90%); */
}


/* === General === */

	/* -- Fixed Width definition -- */
	/* quadruple the last .callout to win specificity with main multi-column callout */
	div[data-callout-metadata*="fw"].callout.callout.callout { flex-grow: 0; flex-shrink: 0;}
	div[data-callout-metadata*="fw1"].callout.callout.callout.callout { flex-basis: 100px; width: 100px; }
	div[data-callout-metadata*="fw2"].callout.callout.callout.callout { flex-basis: 200px; width: 200px; }
	div[data-callout-metadata*="fw3"].callout.callout.callout.callout { flex-basis: 300px; width: 300px; }
	div[data-callout-metadata*="fw4"].callout.callout.callout.callout { flex-basis: 400px; width: 400px; }
	div[data-callout-metadata*="fw5"].callout.callout.callout.callout { flex-basis: 500px; width: 500px; }
	div[data-callout-metadata*="fw6"].callout.callout.callout.callout { flex-basis: 600px; width: 600px; }
	div[data-callout-metadata*="fw7"].callout.callout.callout.callout { flex-basis: 700px; width: 700px; }
	div[data-callout-metadata*="fw8"].callout.callout.callout.callout { flex-basis: 800px; width: 800px; }
	div[data-callout-metadata*="fw9"].callout.callout.callout.callout { flex-basis: 900px; width: 900px; }

	div[data-callout-metadata*="pw1"].callout.callout.callout.callout { flex-basis: 10%; width: 10%; }
	div[data-callout-metadata*="pw2"].callout.callout.callout.callout { flex-basis: 18%; width: 18%; }
	div[data-callout-metadata*="pw3"].callout.callout.callout.callout { flex-basis: 28%; width: 28%; }
	div[data-callout-metadata*="pw4"].callout.callout.callout.callout { flex-basis: 38%; width: 38%; }
	div[data-callout-metadata*="pw5"].callout.callout.callout.callout { flex-basis: 48%; width: 48%; }
	div[data-callout-metadata*="pw6"].callout.callout.callout.callout { flex-basis: 58%; width: 58%; }
	div[data-callout-metadata*="pw7"].callout.callout.callout.callout { flex-basis: 68%; width: 68%; }
	div[data-callout-metadata*="pw8"].callout.callout.callout.callout { flex-basis: 78%; width: 78%; }
	div[data-callout-metadata*="pw9"].callout.callout.callout.callout { flex-basis: 88%; width: 88%; }


	/* metadata "wide-x" to control subcallout flexible max width. only discrete 2,4,8,16 */
	div[data-callout="multi-column"].callout > .callout-content > div[data-callout-metadata*="mw0"].callout.callout { flex-basis: 0; }
	div[data-callout="multi-column"].callout > .callout-content > div[data-callout-metadata*="dw1"].callout { flex: 1 0 auto; }
	div[data-callout="multi-column"].callout > .callout-content > div[data-callout-metadata*="dw2"].callout { flex: 2 0 auto; }
	div[data-callout="multi-column"].callout > .callout-content > div[data-callout-metadata*="dw3"].callout { flex: 3 0 auto; }
	div[data-callout="multi-column"].callout > .callout-content > div[data-callout-metadata*="dw4"].callout { flex: 4 0 auto; }
	div[data-callout="multi-column"].callout > .callout-content > div[data-callout-metadata*="dw5"].callout { flex: 5 0 auto; }
	div[data-callout="multi-column"].callout > .callout-content > div[data-callout-metadata*="dw6"].callout { flex: 6 0 auto; }
	div[data-callout="multi-column"].callout > .callout-content > div[data-callout-metadata*="dw7"].callout { flex: 7 0 auto; }
	div[data-callout="multi-column"].callout > .callout-content > div[data-callout-metadata*="dw8"].callout { flex: 8 0 auto; }
	div[data-callout="multi-column"].callout > .callout-content > div[data-callout-metadata*="dw9"].callout { flex: 9 0 auto; }




/* === Multi Column Callout (MCC) === */
	/* 	some issues still persists
		(2) some theme uses transparency to affect .callout-title */

	/* common MCC variables */
	body {
		--mcc-img-snw-display: none;
		--callout-min-width: 200px;
		--callout-nowrap-min-width: 250px;
		--callout-gap: 1em;
		--callout-margin: 0px;
	}
	[data-callout="multi-column"].callout {
		--callout-blend-mode: normal;
	}

	/* -- Main MCC Code -- */

		/* remove callout-title and make callout-content as part of main callout for box formatting */
		div[data-callout="multi-column"].callout > .callout-title { display: none; }
		div[data-callout="multi-column"].callout > .callout-content { display: contents; }
		/* make the main callout a flex and remove box formatting */
		/* --columns: unset to undo Sanctum's mod */
		div[data-callout="multi-column"].callout
			{ display: flex; flex-wrap: wrap; gap: var(--callout-gap); background: unset; border: unset; margin: unset; padding: unset; clear: both; --columns: unset; }
		/* make the subcallout in flex column to allow growing vertically */
		div[data-callout="multi-column"].callout .callout:not([data-callout="multi-column"]) { display: flex; flex-direction: column; }
		div[data-callout="multi-column"].callout:not(.is-collapsed) .callout { margin-inline: var(--callout-margin); margin-block: var(--callout-margin); } /* to allow spacing for box shadow */
		div[data-callout="multi-column"].callout .callout .callout-content { flex-grow: 1}

		div[data-callout="multi-column"].callout > .callout-content > *:is(div,ul,blockquote,p) { flex: 1 1 var(--callout-min-width); margin: 0;}

		/* metadata "wide-x" to control subcallout flexible max width. only discrete 2,4,8,16 */
		div[data-callout="multi-column"].callout > .callout-content > div[data-callout-metadata*="min-0"] { flex-basis: 0; }
		div[data-callout="multi-column"].callout > .callout-content > div[data-callout-metadata*="wide-2"] { flex-grow: 2; }
		div[data-callout="multi-column"].callout > .callout-content > div[data-callout-metadata*="wide-3"] { flex-grow: 3; }
		div[data-callout="multi-column"].callout > .callout-content > div[data-callout-metadata*="wide-4"] { flex-grow: 4; }
		div[data-callout="multi-column"].callout > .callout-content > div[data-callout-metadata*="wide-5"] { flex-grow: 5; }

		@media (min-width: 500px) {
			/* .is-collapsed is used to draw the border-left only when it's collapsed */
			div[data-callout="multi-column"].callout > div.callout-content .callout.is-collapsed { box-shadow: unset; height: min-content;}
		}

	/* -- MCC Variant - Flexible Height for Subcallout / Children -- */
		/* also useful if one wants to have non callout children like table and don't want to fill the height */
		div[data-callout="multi-column"].callout {}
		div[data-callout="multi-column"][data-callout-metadata*="flex-h"].callout { align-items: flex-start; }

	/* -- MCC Variation - Fixed Columns -- */
		div[data-callout="multi-column"][data-callout-metadata*="col3"]:not([data-callout-metadata*="icons"]).callout {
			display: grid;
			grid-template-columns: 1fr 1fr 1fr;
		}

	/* -- MCC Variation - Icons -- */

		/* Main code */
		div[data-callout="multi-column"][data-callout-metadata*="icons"] {

			&.callout { justify-content: center; }
			&.callout .callout { display: flex; flex-direction: row; flex-wrap: wrap; }
			&.callout.callout .callout-content > p { flex-grow: 1; text-align: center; }
			/* Options for number of columns */
			&[data-callout-metadata*="col2"].callout.callout .callout-content > p { flex-basis: 40%; }
			&[data-callout-metadata*="col3"].callout.callout .callout-content > p { flex-basis: 30%; }
			&[data-callout-metadata*="col4"].callout.callout .callout-content > p { flex-basis: 22%; }
			&[data-callout-metadata*="col5"].callout.callout .callout-content > p { flex-basis: 18%; }

			/* listive theme */
			&.callout p:first-child { margin-top: revert; }
		}


	/* -- MCC Variation - Fixed Width Option -- */
		/* Fixed Width Option (using callout-metadata) */
		div[data-callout="multi-column"][data-callout-metadata*="center-fixed"].callout { justify-content: center; }
		div[data-callout="multi-column"][data-callout-metadata*="left-fixed"].callout { justify-content: flex-start; }
		div[data-callout="multi-column"][data-callout-metadata*="right-fixed"].callout { justify-content: flex-end; }
		div[data-callout="multi-column"][data-callout-metadata*="fixed"].callout > .callout-content > *:is(div,ul) { flex-grow: 0; }

		/* Callout size for Fixed Width Option */
		div[data-callout="multi-column"][data-callout-metadata*="fixed"][data-callout-metadata*="small"].callout > .callout-content > *:is(div,ul) { flex-basis: calc( var(--float-small-width) * 0.5 ); }
		div[data-callout="multi-column"][data-callout-metadata*="fixed"][data-callout-metadata*="medium"].callout > .callout-content > *:is(div,ul) { flex-basis: calc( var(--float-medium-width) * 0.65); }
		div[data-callout="multi-column"][data-callout-metadata*="fixed"][data-callout-metadata*="large"].callout > .callout-content > *:is(div,ul) { flex-basis: calc( var(--float-large-width) * 0.8); }


	/* -- MCC Variation - No Wrap Option -- */

		/* No-Wrap Option (using callout-metadata) - for single row with horizontal scrollbar */
		div[data-callout="multi-column"][data-callout-metadata*="no-wrap"].callout {
			flex-wrap: nowrap; overflow-x: scroll;
			& > .callout-content > *:is(div,ul) { flex-shrink: 0; }
		}

		/* No-Wrap Option, Width Options. Legacy width option. better to dw, pw and fw options at the top */
		.callout[data-callout="multi-column"][data-callout-metadata*="no-wrap"] .callout[data-callout-metadata*="small"] { flex-basis: 300px; }
		.callout[data-callout="multi-column"][data-callout-metadata*="no-wrap"] .callout[data-callout-metadata*="medium"] { flex-basis: 400px; }
		.callout[data-callout="multi-column"][data-callout-metadata*="no-wrap"] .callout[data-callout-metadata*="large"] { flex-basis: 600px; }

		/* Align center for items in callout */
		div[data-callout="multi-column"] .callout[data-callout-metadata*="center"] { text-align: center; }


	/* -- Special Adjustment for Community Themes -- */

		 /* Special Adjustment -- for theme with negative margin & box shadow. (e.g. Prism) */
		div[data-callout="multi-column"].callout > .callout-content { margin-top: unset;}
		div[data-callout="multi-column"].callout:not(.is-collapsed) { box-shadow: unset;}

		/* Blue Topaz - remove box shadow from main [!multi-column] */
		body[class*="notebook-liked-markdown-page"].shade-callout-style .callout[data-callout="multi-column"] { box-shadow: unset !important; }

		/* Special Adjustment -- for minimal left margin *//*
		.markdown-source-view.mod-cm6.is-readable-line-width .cm-embed-block.cm-callout > div[data-callout="multi-column"].callout
			{ width: 100%; max-width: 100%; margin-inline: 0 !important; }

		/* Minimal -  .dataview.list-view-ul width - Minimal set the width with --line-width-adaptive (40rem by default) */
		body:not(.table-100):not(.table-max):not(.table-wide) .markdown-preview-view.is-readable-line-width:not(.table-100):not(.table-max):not(.table-wide) div[data-callout="multi-column"].callout .dataview.list-view-ul {width: auto;}

		/* Minimal -- Callout Outline, to ensure sub-callout not overlapped and hidden */
		.callouts-outlined div[data-callout="multi-column"].callout:not(.is-collapsed) .callout:not([data-callout="multi-column"]) {
			margin-top: 1em;
		}


	/* -- Special Adjustment for Plugins / Markdown Elements -- */

		/* SNW plugin */
		div[data-callout*="multi-column"].callout .callout-content span.image-embed + .snw-reference { display: var(--mcc-img-snw-display); }




/* === Blank Container "DIV"'s - can be used with Main Multi-Column layout above === */

	/* "blank-container" - no bg, no border, no title, no nothing, and remove the ligthen effect */
	div[data-callout*="blank"].callout {--callout-blend-mode: normal;}
	div[data-callout*="blank"].callout > .callout-title { display: none; }
	div[data-callout*="blank"].callout > .callout-content {display: contents;}
	div[data-callout*="blank"].callout { border: 0; padding-top: 0; padding-inline: 5px; background: unset; box-shadow: unset; }

	/* -- No Margin -- */
	/* issues still persists
	   - headers font size not same
	   - "no-margin" not fully workable in editing view due to wide-page override for Minimal theme
	*/
	div[data-callout-metadata*="no-margin"]:is(.cm-callout, .callout.callout, .callout.callout .callout-content) {margin: 0; padding: 0;}

	/* -- No Margin for only child -- */
		div[data-callout*="blank"].callout .callout-content > p:only-child {
			margin-block: 0;
		}


	/* -- Special Adjustment for Themes -- */

		/* Mado. Remove  theme's pseudo left border */
		div[data-callout*="blank"] .callout-content::before { display: none; }


/* === Text Justify === */

	.text-justify :is(.markdown-preview-view p,.markdown-source-view .cm-line) { text-align: justify; }


/* === Floating Callout - Base === */
	/* float uses callout-metadata to control the layout. can apply to any callout */

	/* variables */
	body {
		--float-small-width: 300px;
		--float-medium-width: 400px;
		--float-large-width: 600px;

		--float-left-callout-margin: 0 15px 0 0;
		--float-right-callout-margin: 0 0 0 15px;
		--float-callout-top-margin: 0em;
		--float-callout-bottom-margin: 0em;
		--float-callout-snw-display: none;
	}
	/* variables */
	body {
		--float-left-callout-margin-inline: 0 12px;
		--float-right-callout-margin-inline: 12px 0;
		--float-snw-display: none;
	}


	/* -- Main Code. Use media query so that floats only works when there's enough width */
	@media (min-width: 500px){
		.markdown-source-view .cm-embed-block.cm-callout:has(> div.markdown-rendered > [data-callout-metadata*="float-left"]) { display: inline; z-index: 30;}
		.markdown-source-view .cm-embed-block.cm-callout:has(> div.markdown-rendered > [data-callout-metadata*="float-left"]) > div > .callout { float: left; z-index: 30; }
		.markdown-source-view .cm-embed-block.cm-callout:has(> div.markdown-rendered > [data-callout-metadata*="float-left"]) > .edit-block-button { display: none; }
		.markdown-reading-view div[data-callout-metadata*="left"].callout {float: left;}

		.markdown-source-view .cm-embed-block.cm-callout:has(> div.markdown-rendered > [data-callout-metadata*="float-right"]) { display: inline; z-index: 30;}
		.markdown-source-view .cm-embed-block.cm-callout:has(> div.markdown-rendered > [data-callout-metadata*="float-right"]) > div > .callout { float: right; z-index: 30; }
		.markdown-source-view .cm-embed-block.cm-callout:has(> div.markdown-rendered > [data-callout-metadata*="float-right"]) > .edit-block-button { display: none; }
		.markdown-reading-view div[data-callout-metadata*="right"].callout {float: right; }

		.markdown-source-view.mod-cm6 .cm-embed-block.cm-callout :is(.callout[data-callout-metadata*="left"], .callout[data-callout-metadata*="right"]) .callout-content > p:last-of-type { margin-bottom: auto;}

		[data-callout-metadata*="float-right"].cm-embed-block.cm-callout {width: auto !important;}

		/* remove margin from first para in the float callout */
		.callout:is(div[data-callout-metadata*="left"],div[data-callout-metadata*="right"]) .callout-content > :is(p:first-child,ul:first-child,ol:first-child) {margin-top: 0;}

		/* Adjustable Gap */
		/* 26Mar2023 only top for now, to work on left-right later */
		.markdown-source-view.mod-cm6 .callout:is(div[data-callout-metadata*="left"]),
		.markdown-source-view.mod-cm6 .callout:is(div[data-callout-metadata*="right"])
			{ margin-top: calc(1em * var(--line-height-normal) + var(--float-callout-top-margin)); }

		/**/
		.markdown-source-view.mod-cm6 .callout:is(div[data-callout-metadata*="left"]),
		.markdown-reading-view div[data-callout-metadata*="left"].callout {
			margin-inline: var(--float-left-callout-margin-inline);
		}
		.markdown-source-view.mod-cm6 .callout:is(div[data-callout-metadata*="right"]),
		.markdown-reading-view div[data-callout-metadata*="right"].callout {
			margin-inline: var(--float-right-callout-margin-inline);
		}
	}

	/* -- FC Size Options -- */

		/* legacy width option. better to dw, pw and fw options at the top */
		[data-callout-metadata*="-small"].callout {width: var(--float-small-width);}
		[data-callout-metadata*="-medium"].callout {width: var(--float-medium-width);}
		[data-callout-metadata*="-large"].callout {width: var(--float-large-width);}


	/* -- Special Adjustment for Plugins / Markdown Elements -- */

		/* Option for Caption */
		div:is([data-callout-metadata*="caption"]).callout > .callout-content > p .image-embed[alt]:not([alt$=".png"], [alt$=".jpg"], [alt$=".jpeg"], [alt$=".tiff"])::after {content: attr(alt); text-align: center; display: block; font-style: italic; color: var(--text-muted); }
		div:is([data-callout-metadata*="caption"]).callout > .callout-content > p img:not(.emoji) { display: block; margin-right: auto; margin-left: auto;}

		/* Introduce clear to break the float */
			/* .markdown-preview-view h2, *//* remove this as it has weird spacing effect */
			.embedded-backlinks { clear: both; }

		/* for Blockquote underlap the float layout -- to remove by 30Jun2024, no longer needed */
			/* .markdown-preview-view :is(blockquote) { overflow-x: auto; } /* fix for blockquote underlap */
			/* .markdown-preview-view :is(p) { overflow: unset; } /* fix for para not wrapping around float. applicable for Willemstad */

		/* for SNW plugin */
		div:is([data-callout-metadata*="caption"],[data-callout-metadata*="left"],[data-callout-metadata*="right"]).callout > .callout-content > p .snw-embed-preview { display: var(--float-callout-snw-display); width: min-content; height: min-content; }
		div:is([data-callout-metadata*="caption"],[data-callout-metadata*="left"],[data-callout-metadata*="right"]).callout > .callout-content > p .internal-embed.image-embed ~ br {display: none;}


	/* -- Special Adjustment for Themes -- */

		/* Minimal. Minimal Float. Updated for Minimal V7.4.7 */
		/* "Reset" the default "cm-sizer" width but respect Minimal Settings, and require cssclass ".minimal-float" as Minimal own wide table will not work together with float i.e. removing :not(:has(table)) from .markdown-source-view.mod-cm6.is-readable-line-width:not(:has(table)) */
		.minimal-float.markdown-source-view.mod-cm6 {
			.cm-sizer {
				max-width: var(--max-width); width: var(--line-width);
			}
			.cm-contentContainer.cm-contentContainer > .cm-content > div,
			.cm-sizer > .embedded-backlinks,
			.cm-sizer > .inline-title,
			.cm-sizer > .metadata-container {
				max-width: none; width: auto;
			}
		}

		/* Minimal. Previous Minimal Float but with performance issue. Kept for reference until 30 June 2024 */ /*
		:is(body[class*="minimal-tab-title"] .markdown-source-view.mod-cm6.is-readable-line-width:not(:has(table)), .minimal-float.markdown-source-view.mod-cm6) .cm-sizer { max-width: var(--max-width); width: var(--line-width); }

		:is(body[class*="minimal-tab-title"] .markdown-source-view.mod-cm6.is-readable-line-width:not(:has(table)), .minimal-float.markdown-source-view.mod-cm6) .cm-contentContainer.cm-contentContainer>.cm-content>div,
		:is(body[class*="minimal-tab-title"] .markdown-source-view.mod-cm6.is-readable-line-width:not(:has(table)), .minimal-float.markdown-source-view.mod-cm6) .cm-sizer>.embedded-backlinks,
		:is(body[class*="minimal-tab-title"] .markdown-source-view.mod-cm6.is-readable-line-width:not(:has(table)), .minimal-float.markdown-source-view.mod-cm6) .cm-sizer>.inline-title,
		:is(body[class*="minimal-tab-title"] .markdown-source-view.mod-cm6.is-readable-line-width:not(:has(table)), .minimal-float.markdown-source-view.mod-cm6) .cm-sizer>.metadata-container
			{ max-width: none; width: auto; }


		/* Minimal - fix to support Breadcrumbs plugin. Sub to the above LP float fix for Minimal */
			body[class*="minimal-tab-title"].contextual-typography .minimal-float.markdown-preview-view.markdown-preview-view .BC-trail.markdown-preview-sizer { display: flex; }

		/* Minimal - Size Option fix due to !important */
		/* Minimal. Uses body[class*="minimal-tab-title"] as identifier. To override (have higher specificity) the width param. Can be merged to main code later on */
		@media (min-width: 500px){
			body[class*="minimal-tab-title"] .markdown-source-view.mod-cm6.is-readable-line-width .cm-embed-block.cm-callout > div[data-callout-metadata$="small"].callout { width: var(--float-small-width) !important; margin-left: 0 !important; }
			body[class*="minimal-tab-title"] .markdown-source-view.mod-cm6.is-readable-line-width .cm-embed-block.cm-callout > div[data-callout-metadata$="medium"].callout { width: var(--float-medium-width) !important; margin-left: 0 !important; }
		}
		@media (min-width: 750px){
			body[class*="minimal-tab-title"] .markdown-source-view.mod-cm6.is-readable-line-width .cm-embed-block.cm-callout > div[data-callout-metadata$="large"].callout { width: var(--float-large-width); margin-left: 0 !important; }
		}


/* === List Column ===*/
	/* supported for (1) css :has(), (2) yaml cssclass, (3) CT plugin, and (4) MA plugin
		- CSS :has() can be applied to sublists, so no column rule. CT plugin apply to div that houses the ul, so column gap is different
		- xx-column-list for first level only list, but apply anywhere but xx-column-list-block apply at block level. Both require JV's Markdown Attributes plugin
	*/


	/* -- List Column/Grid/Card general styling -- */

		/* hide tag in rendered view, dim it in edit mode and small size */
		:is(div[class*="mcl"].markdown-rendered, .markdown-rendered) a[href*="#mcl"] {display: none;}
		/* :is(div[class*="mcl"].markdown-rendered, .markdown-rendered div[class*="mcl"]) a[href*="#mcl"] {display: none;} */
		.cm-s-obsidian .HyperMD-list-line span[class*="mcl"] {background-color: var(--background-primary) !important; color: var(--text-faint); font-size: 0.9rem; }


	/* -- List Column using YAML cssclass and Markdown Attributes i.e. {.three-column-list-block} -- */

		/* define the column gap and column rule */
		.two-column-list div > ul,   .two-column-list-block,
		.three-column-list div > ul,  .three-column-list-block,
		.four-column-list div > ul,  .four-column-list-block,
		.multi-column-list div > ul,  .multi-column-list-block
			{ column-gap: 3rem; column-rule: var(--col-rule-width) solid var(--col-rule-color); }

		/* define the column count, for fixed number of columns, to subject it to note's width (in future can use @container when installer v1.2.7 widely used ) */
		@media (min-width: 500px ){
			.two-column-list div > ul,   .two-column-list-block {columns: 2; }
			.three-column-list div > ul,  .three-column-list-block {columns: 3; }
			.four-column-list div > ul,  .four-column-list-block {columns: 4; }
		}
		.multi-column-list div > ul,  .multi-column-list-block { column-width: var(--list-min-width); }

		/* Special adjustment for footnotes applied with -colum-list-block -- obsidian creates a <br> tag at the last <li> */
		.footnotes [class$="-column-list-block"] li:last-of-type br:last-of-type {display: none;}


	/* -- List Column Using Tag (using CSS has) -- */

		/* set the column rules at ul level and adjust the margin */
		ul:has(> li > [data-heading] > [href*="#mcl/list-column"]),
		ul:has(> li > [href*="#mcl/list-column"]) {
			margin-top: 0;
            column-width: var(--list-min-width); column-gap: 1rem;
            column-rule: var(--col-rule-width) solid var(--col-rule-color);
        }

		/* give some margin for the div, for first ul only */
		div:has(> ul > li > [href*="#mcl/list-column"]),
		div:has(> ul > li > [data-heading] > [href*="#mcl/list-column"])
			{ margin-top: 0.8em; }

		/* readjust [!blank] display contents */
		div[data-callout*="blank"] > .callout-content:has(li [href*="#mcl/list-column"]) {display: block; margin: 0; padding: 0; }


	/* -- General Adjustment for List Column -- */

		/* Adjustment for misaligned bullets (this is a hack, need to find better solution later) */
		ul:has([href="#mcl/list-column"]) li > .list-bullet::after, /* :has() */
		.markdown-preview-view[class*="column-list"] li > .list-bullet::after, /* yaml cssclass */
		.tag-mcllist-column ul li > .list-bullet::after, /* contextual typography */
		ul[class*="column-list"] li > .list-bullet::after /* markdown attributes */
			{ position: relative; }


	/* -- Special Adjustment for Plugins for List Column -- */

		/* for Custom Classes */
		div.cc-container:has(li > [href="#mcl/list-column"]) { width: 100%; }



/* === List Grid (using hashtag) === */
	/* supported (1) :has() and (2) Contextual Typography plugin */

	/* -- Main Code -- */

		ul:has(> li > [data-heading] > [href*="#mcl/list-grid"]),
		ul:has(> li > [href*="#mcl/list-grid"]),
		div[class*="mcllist-grid"] ul:first-child
			{ display: grid; grid-template-columns: repeat(auto-fit,minmax(var(--list-grid-min-width),1fr)); }

		ul:has(> li > [data-heading] > [href*="#mcl/list-grid-wide"]),
		ul:has(> li > [href*="#mcl/list-grid-wide"]),
		div[class*="mcllist-grid-wide"] ul:first-child
			{ grid-template-columns: repeat(auto-fit,minmax(var(--list-grid-wide-min-width),1fr)); }

		ul:has(> li > [data-heading] > [href*="#mcl/list-grid"]) > li:not(:last-of-type),
		ul:has(> li > [href*="#mcl/list-grid"]) > li:not(:last-of-type),
		div[class*="mcllist-grid"] ul:first-child > li:not(:last-of-type)
			{ padding-right: 1.6em; }

		ul:has(> li > [data-heading] > [href*="#mcl/list-grid-sm"]),
		ul:has(> li > [href*="#mcl/list-grid-sm"]),
		div[class*="mcllist-grid-sm"] ul:first-child
			{ --list-grid-min-width: 20%; }


		/* - dashboard style, page level only, apply to any first level */
		/* -- applicable only when width > 400pt, else just single column */
		/* grid list block level cannot be done, it breaks formatting */
		@media (min-width: 500px) {
			.two-column-grid-list div > ul { display: grid; grid-column-gap:3em; grid-template-columns:repeat(2,1fr);}
		}
		@media (min-width: 600px) {
			.three-column-grid-list div > ul { display: grid; grid-column-gap:3em; grid-template-columns:repeat(3,1fr);}
		}



/* === List Card (using hashtag) === */
	/* supported (1) :has() and (2) Contextual Typography plugin */

	body {
		--mcl-card-header-border-width: 1px;
		--mcl-card-bg-color: var(--background-secondary);
		--mcl-card-gap: 0.2em;
		--mcl-card-border-width: 1px;
		--mcl-card-border-color: var(--background-modifier-border);
	}

	/* -- Main Code -- */

		/* make the list grid, remove some ul padding */

		:is(div:has(> ul [data-heading*="#mcl/list-card"]), div:has(> ul [href*="#mcl/list-card"]), [class*="mcllist-card"]) ul:first-child { display: grid; grid-template-columns: repeat(auto-fit,minmax(var(--list-grid-min-width),1fr)); margin-block-start: 0.5em; }
		:is(div:has(> ul [data-heading*="#mcl/list-card-wide"]), [class*="mcllist-card-wide"]) ul:first-child { grid-template-columns: repeat(auto-fit, minmax(var(--list-grid-wide-min-width),1fr));}
		/* unindent first level bullet */
		:is(div:has(> ul [data-heading*="#mcl/list-card"]), div:has(> ul [href*="#mcl/list-card"]), .tag-mcllist-card) ul ul:not(ul ul ul) { padding-left: 0.1em; } /* with v1.6 this is somewhat redundant */
		:is(div:has(> ul [data-heading*="#mcl/list-card"]), div:has(> ul [href*="#mcl/list-card"]), .tag-mcllist-card) ul ul:not(ul ul ul) > li { margin-inline-start: 0; }
		:is(div:has(> ul [data-heading*="#mcl/list-card"]), div:has(> ul [href*="#mcl/list-card"]), div:has(> ul [data-heading*="#mcl/list-card"]) .callout-content, .tag-mcllist-card, .tag-mcllist-card .callout-content) > ul { padding-left: 0; }
		:is(.is-mobile .markdown-preview-section) :is(div:has([data-heading*="#mcl/list-card"]), .tag-mcllist-card) :is(ol:not(ol ol ol),  ul:not(ul ul ul)) {padding-left: 0;}
		/* reposition the collapse indicator and hover behaviour */
		:is(div:has(> ul [data-heading*="#mcl/list-card"]), div:has(> ul [href*="#mcl/list-card"]), div:has(> ul [data-heading*="#mcl/list-card"]) .callout-content, .tag-mcllist-card, .tag-mcllist-card .callout-content) > ul > li {position: relative;}
		:is(div:has(> ul [data-heading*="#mcl/list-card"]), div:has(> ul [href*="#mcl/list-card"]), div:has(> ul [data-heading*="#mcl/list-card"]) .callout-content, .tag-mcllist-card, .tag-mcllist-card .callout-content) > ul > li:hover > .collapse-indicator,
		:is(div:has(> ul [data-heading*="#mcl/list-card"]), div:has(> ul [href*="#mcl/list-card"]), div:has(> ul [data-heading*="#mcl/list-card"]) .callout-content, .tag-mcllist-card, .tag-mcllist-card .callout-content) > ul > li > ul > li:hover > .collapse-indicator {opacity: 1;}
		:is(div:has(> ul [data-heading*="#mcl/list-card"]), div:has(> ul [href*="#mcl/list-card"]), div:has(> ul [data-heading*="#mcl/list-card"]) .callout-content, .tag-mcllist-card, .tag-mcllist-card .callout-content) > ul > li > .collapse-indicator,
		:is(div:has(> ul [data-heading*="#mcl/list-card"]), div:has(> ul [href*="#mcl/list-card"]), div:has(> ul [data-heading*="#mcl/list-card"]) .callout-content, .tag-mcllist-card, .tag-mcllist-card .callout-content) > ul > li > ul > li > .collapse-indicator {position: absolute; right: 0.7em; padding-right: 0;}
		:is(div:has(> ul [data-heading*="#mcl/list-card"]), div:has(> ul [href*="#mcl/list-card"]), .tag-mcllist-card) ul::before {display: none;}
		/* decorate the first heading for cards */
		:is(div:has(> ul [data-heading*="#mcl/list-card"]), div:has(> ul [data-heading*="#mcl/list-card"]) .callout-content, .tag-mcllist-card, .tag-mcllist-card .callout-content) > ul > li:not(.is-collapsed) > [data-heading] {border-bottom: var(--mcl-card-header-border-width) solid var(--mcl-card-border-color); margin-bottom: 0.2em;}
		/* hide the list bullet for list-card for first and second level */
		:is(div:has(> ul [data-heading*="#mcl/list-card"]), div:has(> ul [href*="#mcl/list-card"]), .tag-mcllist-card) > div > div > ul > li > .list-bullet::after,
		:is(div:has(> ul [data-heading*="#mcl/list-card"]), div:has(> ul [href*="#mcl/list-card"]), .tag-mcllist-card) > div > div > ul > li > ul > li > .list-bullet::after,
		:is(div:has(> ul [data-heading*="#mcl/list-card"]), div:has(> ul [href*="#mcl/list-card"]), .tag-mcllist-card) > ul > li::before,
		:is(div:has(> ul [data-heading*="#mcl/list-card"]), div:has(> ul [href*="#mcl/list-card"]), .tag-mcllist-card) > ul > li > ul > li::before,
		:is(div:has(> ul [data-heading*="#mcl/list-card"]), div:has(> ul [href*="#mcl/list-card"]), .tag-mcllist-card) > ul > li > .list-bullet::after,
		:is(div:has(> ul [data-heading*="#mcl/list-card"]), div:has(> ul [href*="#mcl/list-card"]), .tag-mcllist-card) > ul > li > ul > li > .list-bullet::after {visibility: hidden;}
		/* remove the indentation guide */
		:is(div:has(> ul [data-heading*="#mcl/list-card"]), div:has(> ul [href*="#mcl/list-card"]), .tag-mcllist-card) ul:not(ul ul ul ul)::before {--indentation-guide-width: 0;}
		.markdown-rendered.show-indentation-guide :is(div:has(> ul [data-heading*="#mcl/list-card"]), .tag-mcllist-card) li > ul::before,
		.markdown-rendered.show-indentation-guide :is(div:has(> ul [data-heading*="#mcl/list-card"]), .tag-mcllist-card) li > ol::before {border-right: 0; border-left: 0; }
		:is(div:has(> ul [data-heading*="#mcl/list-card"]), div:has(> ul [href*="#mcl/list-card"]), .tag-mcllist-card) li {list-style: none;}
		:is(div:has(> ul [data-heading*="#mcl/list-card"]), div:has(> ul [href*="#mcl/list-card"]), .tag-mcllist-card) li li li {list-style: circle;}
		:is(div:has(> ul [data-heading*="#mcl/list-card"]), div:has(> ul [href*="#mcl/list-card"]), .tag-mcllist-card) .callout ul:not(ul ul) > li,
		:is(div:has(> ul [data-heading*="#mcl/list-card"]), div:has(> ul [href*="#mcl/list-card"]), .tag-mcllist-card) ul:not(ul ul) > li
			{ margin-inline: var(--mcl-card-gap); margin-bottom: calc(var(--mcl-card-gap)*2); padding: 0.3em 0.5em; outline: var(--mcl-card-border-width) solid var(--mcl-card-border-color); border-radius: 0.5em; background-color: var(--mcl-card-bg-color);}
		:is(div:has(> ul [data-heading*="#mcl/list-card"]), div:has(> ul [href*="#mcl/list-card"]), .tag-mcllist-card) ul:not(ul ul) > li img {width: 100%;}
		:is(div:has(> ul [data-heading*="#mcl/list-card"]), div:has(> ul [href*="#mcl/list-card"]), .tag-mcllist-card) ul img {margin: 0; float: none;}


	/* -- Special Adjustment for Themes -- */

		/* Sanctum */
		.markdown-rendered .tag-mcllist-card ul img[alt*=right] {margin: 0;}



/* === Style Settings === */

/* @settings

name: Modular CSS Layout - Multi Column
id: modular-css-layout-mc
settings:

    -
        id: multi-column-title
        title: Multi Column Callout Settings
        type: heading
        level: 1
		collapsed: true

	-
        id: mc-callout-general-title
        title: MCC -- General
        type: heading
        level: 2
		collapsed: false
	-
		id: mcc-img-snw-display
		title: Hide SNW indicator for images in MC Callout
		type: variable-select
		default: none
		options:
			-
				label: Show
				value: inline
			-
				label: Hide
				value: none

	-
        id: mc-callout-width-title
        title: MCC -- Width
        type: heading
        level: 2
		collapsed: false
    -
        id: callout-min-width
        title: Minimum Sub-Callout Width
        description: for sub-callout in [!multi-column]. in px units
        type: variable-number-slider
        default: 200
        min: 100
        max: 500
        step: 50
		format: px
	-
        id: callout-nowrap-min-width
        title: Minimum NO-WRAP Sub-Callout Width
        description: for sub-callout in [!multi-column|no-wrap]. in px units
        type: variable-number-slider
        default: 250
        min: 100
        max: 500
        step: 50
		format: px

	-
        id: mc-callout-gap-title
        title: MCC -- Gap & Margin
        type: heading
        level: 2
		collapsed: false
    -
        id: callout-gap
        title: Sub-Callout Gap (any unit)
        description: NO spacing between figure and unit
        type: variable-text
        default: 1em
	-
		id: callout-margin
		title: Sub-Callout Margin (any unit)
		description: to allow some spacing for box-shadow
		type: variable-text
		default: 0px



	-
        id: float-callout-title
        title: Float Callout Settings
        type: heading
        level: 1
		collapsed: true

	-
        id: float-callout-general-title
        title: FC -- General
        type: heading
        level: 2
		collapsed: false

	-
		id: float-callout-snw-display
		title: Hide SNW indicator in Callout Float / Aside
		type: variable-select
		default: none
		options:
			-
				label: Show
				value: inline-block
			-
				label: Hide
				value: none

	-
        id: float-width-title
        title: FC -- Width
        type: heading
        level: 2
		collapsed: false
    -
        id: float-small-width
        title: Floating Callout Width - Small (in px)
        type: variable-number-slider
        default: 300
        min: 200
        max: 450
        step: 50
		format: px
    -
        id: float-medium-width
        title: Floating Callout Width - Medium (in px)
        type: variable-number-slider
        default: 400
        min: 300
        max: 550
        step: 50
		format: px
    -
        id: float-large-width
        title: Floating Callout Width - Large (in px)
        type: variable-number-slider
        default: 600
        min: 500
        max: 750
        step: 50
		format: px

	-
        id: float-gap-title
        title: FC -- Gap & Margin
        type: heading
        level: 2
		collapsed: false
	-
	    id: float-callout-top-margin
		title: Adjust top margin for Float Left and Float Right
		description: Can use em or px unit
	    type: variable-text
	    default: 0em
	-
	    id: float-left-callout-margin-inline
		title: Adjust left-right margin for Float Left Callout
		description: Enter in this order - "left right". Can use em or px unit
		type: variable-text
	    default: 0px 12px
	-
	    id: float-right-callout-margin-inline
		title: Adjust left-right margin for Float Right Callout
		description: Enter in this order - "left right". Can use em or px unit
	    type: variable-text
	    default: 12px 0px



    -
        id: mc-list-column-title
        title: List Column
		description: using `{.xxx-column-list-xxx}` and `#mcl/list-column`
        type: heading
        level: 1
		collapsed: true
	-
        id: list-column-width-title
        title: Width control for List Column
        type: heading
        level: 2
		collapsed: false
    -
        id: list-min-width
        title: Minimum Column Width (in px)
        type: variable-number-slider
        default: 200
        min: 100
        max: 500
        step: 50
		format: px

	-
        id: list-column-deco-title
        title: Decoratives control for List Column
        type: heading
        level: 2
		collapsed: false
	-
        id: col-rule-width
        title: Ruler (vertical line between columns) width for List Column
		description: in px unit. Set to 0 to disable ruler
        type: variable-number-slider
        default: 1
        min: 0
        max: 4
        step: 1
		format: px
    -
        id: col-rule-color
        title: Ruler (vertical line between columns) color for List Column
		type: variable-themed-color
		format: hsl
		opacity: true
		default-light: '#'
		default-dark: '#'


	-
        id: mc-list-grid-title
        title: List Grid and List Card
		description: using `#mcl/list-grid` and `#mcl/list-card`
        type: heading
        level: 1
		collapsed: true

	-
        id: list-grid-card-width-title
        title: Width control for List Grid and List Card
        type: heading
        level: 2
		collapsed: false
	-
        id: list-grid-min-width
        title: Minimum width for normal List Grid/Card
		description: For `#mcl-list-grid` or `#mcl-list-card`. Can use em or px unit
		type: variable-text
		default: 250px
	-
        id: list-grid-wide-min-width
        title: Minimum width for Wide List Grid/Card
		description: For `#mcl/list-grid-wide` or `#mcl/list-card-wide`. Can use em or px unit
		type: variable-text
		default: 350px

	-
        id: list-card-deco-title
        title: Decoratives control for List Card
        type: heading
        level: 2
		collapsed: false
	-
		id: mcl-card-header-border-width
		title: Bottom border for first header in List Card
		description: Can use em or px unit. Set to 0 to disable the border
		type: variable-text
		default: 1px
	-
		id: mcl-card-bg-color
		title: Background color for List Card
		type: variable-themed-color
		format: hsl
		opacity: true
		default-light: '#'
		default-dark: '#'
	-
        id: mcl-card-gap
        title: Gap between cards for List Card
		description: Can use em or px unit
		type: variable-text
		default: 0.2em
	-
        id: mcl-card-border-width
        title: Border width for each card in List Card
		description: Can use em or px unit. Set to 0 to disable the border
		type: variable-text
		default: 1px
	-
		id: mcl-card-border-color
		title: Border color for each card in List Card
		type: variable-themed-color
		format: hsl
		opacity: true
		default-light: '#'
		default-dark: '#'

*/
