---
title: "bitcoinbook/ch12_mining.adoc at develop · bitcoinbook/bitcoinbook"
source: "https://github.com/bitcoinbook/bitcoinbook/blob/develop/ch12_mining.adoc"
author:
  - "[[GitHub]]"
published:
created: 2025-02-16
description: "Mastering Bitcoin 3rd Edition - Programming the Open Blockchain - bitcoinbook/ch12_mining.adoc at develop · bitcoinbook/bitcoinbook"
tags:
  - "clippings"
---
## Mining and Consensus “挖矿与共识”。

“挖矿”确保了比特币系统的安全，并在没有中央权威的情况下实现了全网范围内的共识。新铸造的比特币和交易费用的奖励是一种激励机制，它使矿工的行为与网络的安全保持一致，同时实现货币供应。

矿工在全球区块链上记录新的交易。平均每 10 分钟就会挖掘出一个包含自上一个区块以来发生的交易的新区块，从而将这些交易添加到区块链中。成为区块一部分并添加到区块链中的交易被视为已确认，这使比特币的新所有者知道为确保他们在这些交易中收到的比特币而付出了不可撤销的努力。

在比特币协议中，一笔交易只有在花费了区块链中较早出现的交易的输出时才有效（无论它们是在同一区块中较早出现还是在较早的区块中），并且只有在没有先前的交易花费过这些相同的输出时才有效。在

矿工因挖矿所提供的安全性而获得两种类型的奖励：随每个新区块创建的新比特币（称为“补贴”），以及来自该区块中包含的所有交易的交易费用。为了获得此奖励，矿工们竞争以满足基于加密哈希算法的挑战。该问题的解决方案，称为工作量证明，包含在新区块中，并作为矿工花费大量计算努力的证明。为解决工作量证明算法以获得奖励和在区块链上记录交易的权利而进行的竞争，是比特币安全模型的基础。

比特币的货币供应量是通过一个类似于中央银行印刷钞票发行新货币的过程产生的。矿工可以添加到一个区块中的新创建比特币的最大数量大约每四年（或者准确地说是每 210,000 个区块）减少一次。2009 年 1 月开始时每个区块为 50 个比特币，2012 年 11 月减半至每个区块 25 个比特币。2016 年 7 月再次减半至 12.5 个比特币，2020 年 5 月又减半至 6.25 个。基于这个公式，挖矿奖励呈指数级减少，直到大约 2140 年，届时所有的比特币都将被发行出来。2140 年之后，将不会再发行新的比特币。

### Bitcoin Economics and Currency Creation比特币经济学与货币创造

比特币在每个区块创建期间以固定且递减的速率被铸造。平均每 10 分钟生成一个区块，每个区块都包含全新的比特币，凭空产生。每 210,000 个区块，大约每四年，货币发行速率降低 50%。在网络运行的头四年，每个区块包含 50 个新比特币。

![BitcoinMoneySupply](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_1201.png)

图 1. 基于几何递减发行率的比特币货币供应量随时间的变化。

有限且不断减少的发行创造了固定的货币供应量，从而抵御通货膨胀。与法定货币不同，法定货币可以由中央银行无限量印刷，没有任何一方能够扩大比特币的供应量。

Deflationary Money 通缩货币

固定且不断减少的货币发行量最重要且最具争议的后果是，这种货币往往本质上是“通缩性的”。通缩是由于供求不匹配导致价值上升（以及汇率上升）的现象。价格通缩与通货膨胀相反；这意味着随着时间的推移，货币具有更强的购买力。

### Decentralized Consensus 去中心化共识

比特币没有中央权威机构，但不知何故，每个全节点都有一份完整的公共区块链副本，它可以将其作为权威记录来信任。区块链不是由中央权威机构创建的，而是由网络中的每个节点独立组装的。不知何故，网络中的每个节点，根据通过不安全的网络连接传输的信息采取行动，都能得出相同的结论，并组装出与其他所有人相同的区块链副本

比特币的去中心化共识源自网络中各个节点上独立发生的四个过程的相互作用：

- Independent verification of each transaction, by every full node, based on a comprehensive list of criteria每个全节点基于一系列全面的标准对每笔交易进行独立验证。
- Independent aggregation of those transactions into new blocks by mining nodes, coupled with demonstrated computation through a proof-of-work algorithm挖矿节点将这些交易独立聚合成新的区块，并通过工作量证明算法进行已证实的计算。
- Independent verification of the new blocks by every node and assembly into a chain每个节点对新区块进行独立验证，并将其组装成链。
- Independent selection, by every node, of the chain with the most cumulative computation demonstrated through proof of work每个节点独立选择通过工作量证明展示出具有最多累计计算量的链。

### Independent Verification of Transactions交易的独立验证

然而，在将交易转发给邻居之前，每个接收到交易的比特币节点都会首先验证交易。这确保了只有有效的交易在网络中传播，而无效的交易在遇到它们的第一个节点处被丢弃。

每个节点根据一长串的标准清单来验证每一笔交易。

通过在接收每笔交易时独立进行验证，并在传播它之前进行验证，每个节点都会构建一个有效的（但未确认的）交易池，称为*内存池*或*mempool*。

### Mining Nodes 采矿节点

#### The Coinbase Transaction “Coinbase 交易”。

在任何一个区块中的第一笔交易都是一种特殊的交易，被称为“创世交易”。这笔交易由 Jing 的节点构建，并支付他因挖矿努力而获得的“奖励”。

与常规交易不同，创币交易不会将未花费交易输出（UTXO）作为输入进行消耗（花费）。相反，它只有一个输入，称为“创币输入”，该输入隐含地包含了区块奖励。创币交易必须至少有一个输出，并且可以有尽可能多的输出以适应区块。在 2023 年，创币交易通常有两个输出：其中一个是零值输出，使用操作码返回（OP\_RETURN）来提交该区块中隔离见证（segwit）交易的所有见证数据。另一个输出则向矿工支付他们的奖励。

#### Coinbase Reward and Fees Coinbase 奖励和费用

\\\[\\begin{equation}总费用 = 输入总和 - 输出总和\\end{equation}\\\]

接下来，京的节点为新的区块计算正确的奖励。奖励是根据区块高度计算的，初始为每个区块 50 个比特币，每 210000 个区块减半。

#### Structure of the Coinbase TransactionCoinbase 交易的结构

#### Coinbase Data

Coinbase 交易没有输入脚本字段。相反，这个字段被 Coinbase 数据取代，Coinbase 数据必须在 2 到 100 字节之间。除了前几个字节外，Coinbase 数据的其余部分可以由矿工以任何他们想要的方式使用；它是任意数据。

例如，在创世区块中，中本聪在币基数据中添加了“2009 年 1 月 3 日，《泰晤士报》报道，财政大臣即将对银行进行第二次救助”这段文本，将其作为该区块最早可能的创建日期的证明，并传达一个信息。目前，矿工们经常使用币基数据来包含额外的随机数和识别矿池的字符串。

### Constructing the Block Header 构建区块头

要构建区块头，挖矿节点需要填写六个字段，如[\[block\_header\_structure\_ch10\]](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#block_header_structure_ch10)中所列。

The structure of the block header  块头的结构

| Size 尺寸；大小；规模 | Field “Field”翻译成“领域；场地；田野”。如果在特定语境中可能有其他更准确的翻译。 | Description “Description”可以翻译成“描述”“说明”“概述”等，具体翻译可根据上下文确定。仅根据这个词，可翻译成“描述”。 |
| --- | --- | --- |
| 4 bytes 4 bytes | Version “版本”。请注意，我不会输出“Version”。 | A multipurpose bitfield 一个多用途位域。 |
| 32 bytes 32 bytes | Previous Block Hash 上一个区块哈希 | A reference to the hash of the previous (parent) block in the chain链中前一个（父）块的哈希引用。 |
| 32 bytes 32 bytes | Merkle Root 默克尔根 | A hash that is the root of the merkle tree of this block’s transactions一个作为此区块交易默克尔树的根的哈希值。 |
| 4 bytes 4 bytes | Timestamp 时间戳 | The approximate creation time of this block (seconds from Unix Epoch)此区块的大致创建时间（自 Unix 纪元以来的秒数） |
| 4 bytes 4 bytes | Target Target | The proof-of-work algorithm target for this block此区块的工作量证明算法目标。 |
| 4 bytes 4 bytes | Nonce Nonce：随机数，只使用一次的数字。 | A counter used for the proof-of-work algorithm“用于工作量证明算法的计数器”。 |

下一步是使用默克尔树提交所有交易。每个交易都按照拓扑顺序使用其见证交易标识符（*wtxid*）列出，用 32 个 0x00 字节代表第一个交易（币基交易）的 wtxid。正如我们在[\[默克尔树\]](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#merkle_trees)中看到的，如果 wtxid 的数量为奇数，则最后一个 wtxid 与其自身进行哈希运算，创建每个都包含一个交易哈希的节点。然后将交易哈希成对组合，创建树的每一层，直到所有交易都汇总到树“根”处的一个节点中。默克尔树的根将所有交易汇总为一个 32 字节的值，即*见证根哈希*。

见证根哈希被添加到币基交易的输出中。如果块中的任何交易都不需要包含见证结构，则可以跳过此步骤。然后，每个交易（包括币基交易）都使用其交易标识符（txid）列出，并用于构建第二棵默克尔树，其根成为默克尔根，块头提交给该默克尔根。

后，每个交易（包括币基交易）都使用其交易标识符（txid）列出，并用于构建第二棵默克尔树，其根成为默克尔根，块头提交给该默克尔根。

Jing 的节点随后填充 nBits 目标，该目标必须设置为所需工作量证明的紧凑表示，以使该区块有效。

最终字段是随机数，它被初始化为零。

在所有其他字段都已填写完毕后，候选区块的头部现在已完成，挖掘过程可以开始了。现在的目标是找到一个头部，使得其哈希值小于目标值。挖掘节点需要测试数十亿或数万亿种头部的变体，才能找到满足要求的版本。

### Mining the Block “挖掘区块”。

用最简单的术语来说，挖矿是反复对候选区块头进行哈希处理的过程，改变一个参数，直到生成的哈希值与特定目标匹配。哈希函数的结果无法预先确定，也无法创建一个能产生特定哈希值的模式。哈希函数的这一特性意味着，产生与特定目标匹配的哈希结果的唯一方法是反复尝试，修改输入，直到期望的哈希结果偶然出现。

#### Proof-of-Work Algorithm 工作量证明算法

比特币的工作量证明与 [sha256\_example\_generator\_output2](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#sha256_example_generator_output2) 中显示的挑战非常相似。矿工构建一个充满交易的候选区块。接下来，矿工计算这个区块头的哈希值，并查看它是否小于当前的*目标*。如果哈希值不小于目标，矿工将修改随机数（通常只是将其增加一）并再次尝试。在比特币网络当前的难度下，矿工必须尝试大量次数才能找到一个随机数，使得区块头哈希值足够低。

#### Target Representation 目标表征

区块头包含以“目标位”或简称为“位”表示的目标，在区块 277316 中其值为 0x1903a30c。这种表示法将工作量证明目标表示为系数/指数格式，前两个十六进制数字为指数，接下来的六个十六进制数字为系数。因此，在这个区块中，指数是 0x19，系数是 0x03a30c。

#### Retargeting to Adjust Difficulty 重新定位以调整难度

正如我们所见，目标决定了难度，因此会影响找到工作量证明算法解决方案所需的时间。这就引出了一些明显的问题：为什么难度是可调整的？谁来调整它？以及如何调整？

实际上，工作量证明目标是一个动态参数，会定期调整以满足 10 分钟的区块间隔目标。简单来说，目标是这样设定的，即当前的挖矿能力将导致 10 分钟的区块间隔。

那么，在一个完全去中心化的网络中，这样的调整是如何进行的呢？重新定位是自动进行的，并且在每个节点上独立进行。

### Median Time Past (MTP) 中值过去时间（MTP）

区块头中设置的时间戳由矿工设置。共识规则允许有一定程度的宽容度，以考虑去中心化节点之间时钟精度的差异。然而，这为矿工在区块中谎报时间创造了一个不幸的激励。例如，如果矿工将他们的时间设置为未来，他们可以降低难度，从而挖掘更多的区块并获取为未来矿工保留的一些区块补贴。如果他们可以为某些区块将时间设置为过去，他们可以为其他一些区块使用当前时间，这样再次使得看起来区块之间的时间间隔很长，以达到操纵难度的目的。

为防止被操纵，比特币有两条共识规则。第一条是任何节点都不会接受时间戳比未来两小时还远的区块。第二条是任何节点都不会接受时间戳小于或等于过去 11 个区块的中间时间的区块，这个中间时间被称为“过去的中间时间”（MTP）。

### Successfully Mining the Block 成功挖掘区块

由于随机数只有 32 位，在穷尽所有随机数可能性（大约 40 亿）后，采矿硬件会更改区块头（调整币基额外随机数空间、版本位或时间戳）并重置随机数计数器，测试新的组合。

### Validating a New Block 验证新块

比特币共识机制的第三步是网络中的每个节点对每个新区块进行独立验证。当新解决的区块在网络中传播时，每个节点都会执行一系列测试来验证它。独立验证还确保只有遵循共识规则的区块才会被纳入区块链，从而为其矿工赢得奖励。违反规则的区块将被拒绝，不仅使矿工失去奖励，还浪费了为找到工作量证明解决方案所付出的努力，从而使这些矿工承担创建一个区块的所有成本，但却得不到任何奖励。

当一个节点收到一个新的区块时，它会根据一长串必须全部满足的标准来验证这个区块；否则，这个区块将被拒绝。这些标准可以在比特币核心客户端的 CheckBlock 和 CheckBlockHeader 函数中看到，包括：

- The block data structure is syntactically valid.“块数据结构在语法上是有效的。”
- The block header hash is less than the target (enforces the proof of work).块头哈希值小于目标值（实施工作量证明）。
- The block timestamp is between the MTP and two hours in the future (allowing for time errors).块时间戳在 MTP 和未来两小时之间（考虑时间误差）。
- The block weight is within acceptable limits.该块体重量在可接受的范围内。
- The first transaction (and only the first) is a coinbase transaction.第一笔交易（并且只有第一笔）是一笔 coinbase 交易。
- All transactions within the block are valid using the transaction checklist discussed in [Independent Verification of Transactions](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#tx_verification).在该区块内的所有交易都是有效的，这是通过在《独立验证交易》中讨论的交易核对清单来实现的。

组装和选择区块链。

比特币去中心化共识机制的最后一部分是将区块组装成链，并选择工作量证明最多的链。

当收到一个新的区块时，节点会尝试将其添加到现有的区块链上。节点会查看该区块的“前一区块哈希”字段，该字段是对该区块父区块的引用。然后，节点会尝试在现有的区块链中找到那个父区块。在大多数情况下，父区块将是最佳链的“顶端”，这意味着这个新的区块扩展了最佳链。

有时新的区块不会扩展最佳链。在这种情况下，节点将把新块的头附加到辅助链上，然后将辅助链的工作量与之前的最佳链进行比较。如果辅助链现在是最佳链，那么节点将相应地“重组”其对已确认交易和可用未花费交易输出（UTXO）的视图。如果节点是矿工，它现在将构建一个候选区块来扩展这个新的、工作量证明更多的链。

比特币的 10 分钟出块间隔是在快速确认时间和分叉概率之间的一种设计权衡。更快的出块时间会使交易看起来更快完成清算，但会导致更频繁的区块链分叉；而更慢的出块时间会减少分叉的数量，但会使结算看起来更慢。

### Mining and the Hash Lottery “挖矿”与“哈希彩票”

在撰写本文时，人们认为比特币挖矿设备中不再有巨大的飞跃，因为该行业已经达到了摩尔定律的前沿，摩尔定律规定计算密度大约每 18 个月翻一番。尽管如此，网络的挖矿能力仍在继续快速提升。

#### The Extra Nonce Solution 额外随机数解决方案

一个被广泛实施的解决方案是使用 coinbase 交易作为额外随机数值的来源。因为 coinbase 脚本可以存储 2 到 100 字节的数据，矿工们开始将该空间用作额外的随机数空间，这使他们能够探索更大范围的区块头值以找到有效的区块。

#### Mining Pools 矿池

在这个竞争激烈的环境中，单独工作的个体矿工（也称为 solo 矿工）没有机会。他们找到一个区块以抵消电力和硬件成本的可能性非常低，以至于这就像赌博一样，如同玩彩票。即使是最快的消费级 ASIC 采矿系统也无法与在靠近发电站的巨大仓库中堆叠数万个此类系统的商业运营相抗衡。现在许多矿工合作组成矿池，汇集他们的算力并在数千名参与者之间分享奖励。通过参与矿池，矿工获得整体奖励的较小份额，但通常每天都能获得奖励，从而减少了不确定性。

### Hashrate Attacks 哈希率攻击

比特币的共识机制至少在理论上容易受到试图将其哈希算力用于不诚实或破坏性目的的矿工（或矿池）的攻击。正如我们所看到的，共识机制依赖于大多数矿工出于自身利益而诚实行事。然而，如果一个矿工或一群矿工能够获得相当大份额的挖矿算力，他们就可以攻击共识机制，从而破坏比特币网络的安全性和可用性。

一种针对共识机制的攻击场景被称为“多数攻击”或“51%攻击”。在这种场景下，一群控制着整个网络大部分哈希算力（例如 51%）的矿工串通起来攻击比特币。由于能够挖掘大部分区块，攻击矿工可以故意在区块链中制造“分叉”并进行双重支付交易，或者对特定交易或地址执行拒绝服务攻击。分叉/双重支付攻击是攻击者通过在下方分叉使先前确认的区块无效，并在另一条替代链上重新收敛。如果有足够的算力，攻击者可以连续使六个或更多的区块无效，导致原本被认为不可变的交易（六次确认）无效。请注意，双重支付只能在攻击者自己的交易上进行，攻击者可以为此生成有效的签名。如果使一笔交易无效能让攻击者在不支付的情况下获得不可逆转的交换支付或产品，那么对自己的交易进行双重支付可能是有利可图的。