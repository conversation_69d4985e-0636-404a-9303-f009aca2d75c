---
tags:
  - news
status: 📍 Todo
---
## Source

## Summary
优秀的生成式人工智能应用工程师需具备两大
核心能力：（i）能运用新型AI模块快速构建强
大应用；（ii）能通过AI辅助实现高效工程开
发，将系统构建时间大幅压缩。此外，出色的
产品设计直觉将成重要加分项。

【AI模块构建力】如同乐高积木：单一类型的
积木只能搭建基础结构，掌握多种积木才能组
合出复杂功能。软件框架、SDK等工具也是如
此。只会调用大语言模型（LLM）API是良好开
端，但若能掌握提示工程、智能体框架、评估
系统、安全防护、RAG、语音栈、异步编程、
数据提取、embedding/向量数据库、模型微
调、图数据库与大语言模型的结合使用、智能
体浏览器/计算机使用、模型控制协议（Model
Control Protocol，简称MCP）、推理模型等技
术模块，就能创造出更丰富的技术组合。

如今我们已有高度智能化的编码助手，如
OpenAI 的Codex 和 Anthropic 的Claude
Code（我个人非常喜欢使用这款工具，它在多
次迭代中自主编写、测试和调试代码的能力令
人印象深刻）。对于那些不仅“凭感觉写代
码”，而是深入理解AI和软件架构基础，并能
引导系统朝着精心选择的产品目标前进的工程
师来说，这些工具使得软件构建的速度和效率
达到前所未有的水平。

觉。我还发现一个问题特别能预测候选人的能
力：“你是如何跟进 AI领域最新发展的？"
由于 AI发展如此之快，那些有良好学习策略的
人（比如阅读《The Batch》新闻通讯、参加短
期课程
、定期动手实践项目、加入技术交
流社区）往往会比那些依赖社交媒体获取碎片
化信息（往往缺乏深度）的人领先得多。
## 想法

## Tags