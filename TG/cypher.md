- [TG Tu<PERSON>l](https://github.com/tigergraph/ecosys/blob/50c23d22678cea3c08b272ab645f0c2ae9cf5473/demos/guru_scripts/docker/tutorial/4.x/Cypher-Mini-tutorial.md)
## Notes
- complete outside query block
	- script keywords
	- query parameter list
- complete in cypher block
	- can find a CreateQuery
	- can find a Document
- complete in gsql block
	-  can find a CreateQuery
## Tasks
- [x] #task fix highlight issue 🔺 ⏳ 2025-01-23 ✅ 2025-01-22
	- [x] node, edge
	- [x] parameter types
- [ ] #task add autocompletion for cypher ⏫
	- [x] move all related code files to cloud portal
	- [x] configure codemirror with extensions: cypher completion, state field
	- [x] make autocompletion work when there is no gsql statement
	- [x] set graph schema
	- [x] fix position issue
- [x] #task seperate cypher/gsql autocompletion 🔺 ⏳ 2025-02-08 ✅ 2025-02-08
- [x] #task fix gsql lint 🔺 ⏳ 2025-02-08 ✅ 2025-02-08
- [x] #task fix cypher autocompletion styles ⏳ 2025-02-08 ✅ 2025-02-08
- [x] #task fix GSQL indention 🔺 ⏳ 2025-02-13 ✅ 2025-02-13
- [x] #task autocompletion in cypher parameter list 🔺 ⏳ 2025-02-10 ✅ 2025-02-10
- [x] #task fix GSQL autocompletion 🔺 ⏳ 2025-02-10 ✅ 2025-02-10
- [-] #task support cypher parameters autocompletion 🔺 ⏳ 2025-02-12 ❌ 2025-02-12
- [x] support trigger strings for cypher autocompletion
- [x] #task fix cypher autocompletion bugs ⏳ 2025-02-11 ✅ 2025-02-11
- [x] #task fix: current input word display in the list ⏳ 2025-02-12 ✅ 2025-02-12
- [x] #task fix: autocompletion list displays when insert "{}" ⏳ 2025-02-12 ✅ 2025-02-12
- [x] #task fix: sometimes property list not show ⏳ 2025-02-12 ✅ 2025-02-12
## Examples
```cypher
USE GRAPH financialGraph
CREATE OR REPLACE OPENCYPHER QUERY multipleMatchExample02(){
  MATCH (s:Account {name: "Paul"})-[:transfer]->(mid)
  MATCH (ss)-[:transfer]->(t)
  WHERE ss = mid
  RETURN t
}
```
| Rule File | Triggers When... | Suggests... | 
|------------------------------------|--------------------------------------|--------------------------------------| 
| `ruleNodePattern.js` | In node patterns `(n:Label)` | Labels 🏷️, Variables 🔠 |
| `rulePropertyLookup.js` | After `.` in property access | Property keys 🔑 | 
| `ruleProcedureOutputsInCallClause` | In `CALL {procedure}` YIELD clauses | Procedure outputs 📤 | 
| `ruleConsoleCommandSubcommands` | After console commands like `:help` | Subcommands 🛠️ |
| `ruleParamStartsWithDollar` | After typing `$` | Parameters 💰 |
```mermaid
sequenceDiagram
    CypherEditorSupport->>AutoCompletion: getCompletions()
    AutoCompletion->>RuleSystem: collectApplicableRules()
    RuleSystem->>Rule1: matchesContext()
    RuleSystem->>Rule2: matchesContext()
    RuleSystem->>RuleN: matchesContext()
    RuleSystem-->>AutoCompletion: [applicable_rules]
    AutoCompletion->>SchemaBasedCompletion: complete()
    AutoCompletion->>QueryBasedCompletion: complete()
    AutoCompletion-->>CypherEditorSupport: completion_items
```
```mermaid
sequenceDiagram
    Editor->>CypherEditorSupport: update(queryText)
    CypherEditorSupport->>Parser: parse(queryText)
    CypherEditorSupport->>AutoCompletion: updateSchema()/updateRefProviders()
    Editor->>CypherEditorSupport: getCompletion()
    CypherEditorSupport->>ContextSystem: getCompletionContext()
    ContextSystem->>RuleEngine: applyRules()
    RuleEngine->>AutoCompletion: getItems()
    AutoCompletion-->>Editor: completion suggestions
```