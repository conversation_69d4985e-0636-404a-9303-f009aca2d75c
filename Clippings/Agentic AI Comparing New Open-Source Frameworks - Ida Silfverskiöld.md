---
title: "Agentic AI：比较新的开源框架 - <PERSON>ld --- Agentic AI: Comparing New Open-Source Frameworks - Ida Silfverskiöld"
source: "https://www.ilsilfverskiold.com/articles/agentic-aI-comparing-new-open-source-frameworks"
author:
published: 2025-04-15
created: 2025-05-05
description: "Did you know there are dozens of open-source agentic frameworks out there? I have briefly tested some of the more popular ones to get a feel for how they work and how easy they are to get started with."
tags:
  - "clippings"
---
![Looking at mentions and stars of open source agentic frameworks](https://www.ilsilfverskiold.com/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fcomparing-open-source-agentic-ai-frameworks.1aa80edb.png&w=3840&q=75)

我们都听说过 CrewAI 和 AutoGen，但您知道吗，市面上有数十种开源代理框架——其中许多是近一年内发布的。

我简单测试了几款较为流行的产品，以感受它们的工作原理及上手难易程度。接下来，请跟随我的脚步，逐一了解它们各自的特点。

重点将放在 LangGraph、Agno、SmolAgents、Mastra、PydanticAI 和 Atomic Agents 上。我们会将它们与稍早但更主流的 CrewAI 和 AutoGen 进行对比。

![Different features of various open source agentic frameworks](https://www.ilsilfverskiold.com/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ftwo.8ee8e55b.png&w=3840&q=75)

我们将探讨框架的实际功能、不同的设计选择、它们之间的差异，以及背后的一些设计理念。

## Agentic AI

代理式 AI 的核心是围绕 LLMs 构建系统，使其具备准确的知识、数据访问能力和执行动作的功能。你可以将其理解为利用自然语言来自动化流程和任务。

在自动化中应用自然语言处理并非新鲜事——我们使用 NLP 技术提取和处理数据已有多年。真正的新颖之处在于，我们现在能赋予语言模型更多的自由度，使其能够处理模糊性并动态做出决策。

![How we work with dynamic routing with large language models](https://www.ilsilfverskiold.com/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fthree.d6000c08.png&w=3840&q=75)

但仅仅因为 LLMs 能理解语言，并不意味着它们拥有自主权——甚至未必理解你试图自动化的任务。这就是为什么构建可靠系统需要大量工程投入的原因。

如果你想获得对 Agentic AI（代理式人工智能）的初学者友好概述，可以在这里和这里查看我更详细的讲解。

## What does a framework do?

代理框架的核心功能是协助进行提示工程，并在 LLMs 之间路由数据——但它们还提供了额外的抽象层，让入门变得更加容易。

如果要从零开始构建一个系统，让 LLM 能够调用不同的 API——即工具——你会在系统提示中定义这一点。然后要求 LLM 在返回响应时附带它想调用的工具，以便系统能解析并执行该 API 调用。

![illustration of a system prompt for an agent that has access to tools](https://www.ilsilfverskiold.com/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffour.e05cd2ab.png&w=3840&q=75)

所以本质上，我们讨论的是提示词工程——它构成了任何框架的基础。

框架通常通过两种方式提供帮助：一是正确构建提示词以确保 LLM 以恰当的格式响应，二是解析响应并将其路由到正确的工具——无论是 API、文档还是其他什么。

当我们建立知识体系时，一个框架可能有助于分块处理文档、嵌入并存储它们。这些内容会作为上下文添加到提示中，类似于我们构建标准 RAG 系统的方式。

框架还能协助处理错误处理、结构化输出、验证、可观测性、部署等事务，并通常帮助你组织代码，以便构建更复杂的系统，如多代理设置。

尽管如此，许多人认为使用完整的框架有些大材小用。

问题在于：如果 LLM 未能正确使用工具或出现故障，这种抽象就会变得棘手，因为你无法轻松调试。更换模型时也可能遇到同样的问题——系统提示词可能是为某一模型量身定制的，迁移到其他模型上效果不佳。

正因如此，部分开发者最终选择重写框架中的部分代码——例如 LangGraph 中的 create\_react\_agent——以获得更好的控制权。

有些框架较为轻量，有些则更为庞大并提供额外功能，但它们周围都有社区帮助你入门。一旦你学会了一个（包括其底层工作原理），掌握其他框架就会变得更容易。

## Different open source frameworks

我们确实依赖社区来了解框架在实际案例中的表现。然而，最受欢迎的框架未必总是理想之选。

我们耳熟能详的有 CrewAI 和 AutoGen。

CrewAI 是一个高度抽象的框架，通过隐藏底层细节，让您快速构建代理系统。AutoGen 专注于自主、异步的代理协作，其中代理可以自由地按需协作——这可能使其更适合测试和研究。

![The most popular frameworks: LangGraph, CrewAI and Autogen](https://www.ilsilfverskiold.com/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffive.4afc1bb2.png&w=3840&q=75)

LangGraph 仍然是一个相当知名的系统，但值得被强调为开发者的主要框架之一。它采用基于图的方法，通过构建节点并通过代理连接它们。与其他两者相比，它为工作流提供了更严格的工程控制，并且不假设代理应具备过多自主性。

需要注意的是，许多人认为 LangGraph 的抽象层次过于复杂且难以调试。其理念在于学习曲线陡峭，但一旦掌握了基础，后续会变得容易。

现在，我还想在这里介绍一些较新的框架。

下一个是 Agno（前身为 Phi-Data），它专注于提供极佳的开发者体验。其文档也是我所见过最清晰之一。它非常即插即用，通过大量内置功能帮助您快速上手，这些功能被组织成逻辑清晰、易于理解的抽象层。

SmolAgents 是一个极其基础的框架，它引入了一个名为 CodingAgent 的代理，该代理通过代码而非 JSON 来路由数据。此外，它还让你能够直接使用整个 Hugging Face 模型库，开箱即用。

![SmolAgent's CodingAgent system prompt](https://www.ilsilfverskiold.com/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fsix.530809e9.png&w=3840&q=75)

至于那些不太常被提及的开源框架：

PydanticAI 基于 Pydantic 构建，采用极简抽象，提供了一个高度透明的轻量级框架。当你需要严格的类型安全、可预测且经过验证的输出时，它表现卓越，便于精细控制，使调试更加轻松。

Atomic Agents 由一位独立代理构建者开发，采用类似乐高积木的模式驱动构建模块，高度注重结构与控制。其诞生源于市场上缺乏实际应用中表现良好的替代方案。

PydanticAI 和 Atomic Agent 的目标都是摆脱独立运作的黑盒 AI。

Mastra 由 Gatsby 团队打造，是一款专为前端开发者设计的 JavaScript 框架，旨在帮助开发者轻松构建其自有生态系统内的代理程序。

![Less mentioned open source agentic frameworks](https://www.ilsilfverskiold.com/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fseven.6ee342f6.png&w=3840&q=75)

我们将逐一探讨它们各自的特点以及它们之间的差异。

## What they all have

大多数框架都包含相同的核心构建模块：支持不同模型、工具、内存和 RAG。

大多数开源框架或多或少都是模型无关的。这意味着它们被设计成支持多种提供商。然而，如前所述，每个框架都有其独特的系统提示结构——这种结构可能对某些模型比其他模型更有效。

这也是为什么理想情况下，你需要能够访问系统提示并在必要时调整它。

所有代理框架都支持工具集成，因为工具对于构建能够执行行动的系统至关重要。它们还通过简单的抽象让自定义工具的定义变得容易。目前，大多数框架官方或通过社区解决方案支持 MCP。

![The features most open source agentic frameworks have](https://www.ilsilfverskiold.com/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Feight.a875a371.png&w=3840&q=75)

需要理解的是，并非所有模型都内置了函数调用功能，而这是使用工具所必需的。要确定哪些模型最适合作为基础 LLM，可以参考 Hugging Face 的代理排行榜。

为了让代理能在 LLM 调用之间保留短期记忆，所有框架都利用了状态机制。状态帮助 LLM 记住之前步骤或对话部分中提及的内容。

大多数框架还提供简单选项，用于设置与不同数据库的 RAG，以便为代理提供知识。

最后，几乎所有框架都支持异步调用、结构化输出、流式处理以及可观测性增强功能。

我已将大量研究整理至这份谷歌表格中，若需更清晰概览，也可在本代码库中找到相关表格。

## What some don’t have

各框架在支持多模态输入、记忆功能及多代理系统等方面存在差异，有的为你内置实现，有的则需自行配置。

首先，一些框架内置了处理多模态（即文本、图像和语音）的解决方案。只要模型支持，完全可以自行实现这一功能。

如前所述，短期记忆（状态）始终包含在内——没有它，就无法构建一个使用工具的系统。然而，长期记忆的实现更为棘手，这也是各框架差异所在。一些框架提供内置解决方案，而其他框架则需要你自行连接其他解决方案。

![Repository table of different features of open source agentic frameworks](https://www.ilsilfverskiold.com/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fnine.f5882c24.png&w=3840&q=75)

框架在处理多智能体能力方面也有所不同。多智能体系统允许您通过监督者连接的智能体团队构建协作或分层设置。

大多数框架建议保持代理的专注性——即限定工具集的狭窄范围。这意味着你可能需要组建代理团队来处理复杂的工作流程。所有框架都允许你构建一个团队，但在扩展到具有多个层次的多层级系统时，某些框架会变得复杂。

这正是 LangGraph 的突出之处——您可以构建节点，将它们连接到不同的监督器，并可视化不同团队间的互动方式。在构建大规模多智能体系统时，它显然是最灵活的选择。

Agno 最近增加了对团队的支持，包括协作型和层级型，但对于更复杂的多层级设置，目前示例还不多。

SmolAgents 允许你将代理连接到监督器，但随着系统扩展可能会变得复杂。它让我想起了 CrewAI 在构建代理团队方面的结构方式。Mastra 在这方面也有相似之处。

借助 PydanticAI 和 Atomic Agents，您需要手动链接您的代理团队，因此编排工作落在您身上。

## 它们有何不同

框架在抽象程度、赋予代理的控制权以及实现功能所需的编码量上各有不同。

首先，一些框架特意包含了许多内置功能，使得快速上手变得容易。

我认为 Mastra、CrewAI，以及某种程度上 Agno，都是为即插即用而设计的。

![Abstractions of different open source agentic frameworks](https://www.ilsilfverskiold.com/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ften.ad7e64e6.png&w=3840&q=75)

LangGraph 同样具备相当程度的抽象性，但它采用基于图的系统，需要手动连接节点。这种方式提供了更高的控制权，但也意味着你必须自行设置和管理每个连接，因此学习曲线更为陡峭。

然后我们有像 PydanticAI、SmolAgents 和 Atomic Agents 这样的低级抽象框架。

这些工具强调透明性，但通常需要您自行构建编排流程。这赋予您完全的控制权并有助于调试——但同时也增加了构建时间。

另一个不同之处在于框架假设代理应具备多大的自主性。一些框架基于 LLMs 应足够智能以自行完成任务的想法构建。另一些则倾向于严格控制——赋予代理单一职责并逐步引导其执行。

AutoGen 和 SmolAgents 属于第一阵营。其余的则更倾向于控制。

![Agency vs no agency in different open source agentic frameworks](https://www.ilsilfverskiold.com/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Feleven.112e6a83.png&w=3840&q=75)

这里有一点值得考虑：当开发者构建那些强调严格控制框架时，往往是因为他们尚未找到让代理自主工作的方法——至少目前还不可靠。

这个领域也开始越来越像工程学了。

如果要构建这些系统，确实需要懂得如何编程。真正的问题在于不同框架在技术要求上的差异有多大。

![Experience level needed to build agentic systems with different open source frameworks](https://www.ilsilfverskiold.com/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ftwelve.0511cd2d.png&w=3840&q=75)

如果你经验较少，选择 CrewAI、Agno 或 Mastra 可能是个不错的选择。

SmolAgents 在简单用例中也相当直观。

至于 PydanticAI、Atomic Agents 和 LangGraph——你将需要自己编写更多的逻辑。不过公平地说，构建一个代理来帮助你正确组织代码也总是可行的。

如果你完全不懂编程，可以试试 Flowise 或 Dify。

最后，值得一提的是这些框架的开发者体验。

据我所见，大多数开发者认为 CrewAI 和 AutoGen 调试起来很棘手。SmolAgents 的 CodeAgent 引入了一种新方法，让代理输出代码来路由数据——这个想法很酷，但并不总能按预期工作。

LangGraph，尤其是与 LangChain 搭配使用时，学习曲线陡峭且存在一些令人困惑的抽象概念，最终您可能不得不拆解并重建它们。

PydanticAI 和 Atomic Agents 普遍受到开发者喜爱，但它们确实需要您自行构建编排逻辑。

Agno 和 Mastra 是稳妥的选择，但您可能会遇到诸如循环调用等难以调试的问题。

## Some notes

入门的最佳方式就是直接动手尝试。但希望本文能为你提供一个关于现有开源框架的概览——以及哪些可能适合你。

不过，这只是对每一项的浅显概述，我并未深入探讨企业级可扩展性或运营稳健性等话题。如果你正为此类需求构建系统，还需单独研究这些方面。

一些开发者认为 AI 代理框架是最糟糕的抽象形式之一——它们往往把事情搞得比直接使用官方 LLM 提供商的 SDK 更复杂。

这个决定权就交给你了。

记住每个框架的完整功能列表可以在这里找到，而包含所有代理框架完整列表的仓库可以在这里找到。

希望你喜欢，如果喜欢的话，记得在 LinkedIn 上关注我，我会尽量每周发布内容。

❤