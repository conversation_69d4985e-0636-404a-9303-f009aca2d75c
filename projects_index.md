# 📋 Projects Overview

> [!info]+ Project Dashboard
> Central hub for tracking all projects, courses, and books across your vault.

---

## 🚀 Projects

### Active Projects
```dataview
TABLE
  status as Status,
  (round((progress / target) * 100) + "%") as Progress,
  started as "Started"
FROM #project
WHERE category = "project" AND !contains(file.path, "Template/")
SORT file.mtime DESC
```

---

## 📚 Courses

### Active Courses
```dataview
TABLE
  status as Status,
  (round((progress / target) * 100) + "%") as Progress,
  started as "Started"
FROM #course
WHERE !contains(file.path, "Template/")
SORT file.mtime DESC
```

---

## 📖 Books
```dataview
TABLE
  author as Author,
  status as Status,
  rating as Rating,
  (round((progress / target) * 100) + "%") as Progress,
  started as "Started"
FROM #book
WHERE !contains(file.path, "Template/")
SORT file.mtime DESC
```
---

*Last updated: `=this.file.mtime`*