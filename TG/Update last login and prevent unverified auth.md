### questions
- user
- context
- callback
- 调用时机
### Summary
- if sso user
	- email_verified自动为true
	- create a user in iam service
- record last_login_time in iam service
- configuration中包含跟iam service交互的配置
### Update last login and prevent unverified auth
```js
function (user, context, callback) {
  if (user.clientID !== configuration.organization_client_id) {
  	return callback(null, user, context);
  }
  console.log("========user=========", user);
  console.log("=========context=====", context);
  console.log("=======end context====");
	if (user.email) {
    console.log("====useremail before change====", user.email);
  	user.email = user.email.toLowerCase(); 
  }
  console.log("====useremail after change====", user.email);
  var supportedStrategies = configuration.supported_strategies;
  const strategies = supportedStrategies.split(",");
  var ssoUser = false;
  if (strategies.includes(context.connectionStrategy)) {
    ssoUser = true;
  }
  var map = require('array-map');
  var ManagementClient = require('auth0@2.17.0').ManagementClient;
  var management = new ManagementClient({
    token: auth0.accessToken,
    domain: auth0.domain
  });
  if (!user.email_verified && !ssoUser) {
  	return callback(new UnauthorizedError('Access denied: unverified user. Please check the email we sent you for verification.'));
  }
  
  if (!user.email && ssoUser) {
  	return callback(new UnauthorizedError('Unable to login: Missing email field. Incomplete user profile or invalid SAML configuration. Please check on your SAML provider.'));
  }
  if (context.organization === undefined) {
    return callback(null, user, context);
  }

  const loginTrigger = async () => {
    var axios = require('axios');
  	var clientId = configuration.client_id;
  	var audience = configuration.audience;
  	var clientSecret = configuration.client_secret;
  	var tokenEndpoint = configuration.token_endpoint;
    var userEndpoint = configuration.update_user_endpoint;
    // var supportedStrategies = configuration.supported_strategies;
    // const strategies = supportedStrategies.split(",");
  	const tokenRequestData = {
    	audience: audience,
    	grant_type: 'client_credentials',
    	client_id: clientId,
    	client_secret: clientSecret
  	};
    const tokenRequestOptions = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
  	const resp = await axios.post(tokenEndpoint, tokenRequestData, tokenRequestOptions);
    const accessToken = resp.data.access_token;


    const orgId = context.organization.id;
    const email = user.email;

    if (ssoUser) {
      // This is an SSO user
      console.log("======user is an SSO user====");
      user.email_verified = true;
      const createUserUrl = userEndpoint;
      const createUserRequestData = {
        enabled: true,
        tenantID: orgId,
        path: "/",
        username: email,
        email: email,
        password: "dummy",
        confirmPassword: "dummy",
        firstName: user.given_name,
        lastName:user.family_name
      };
      try {
        const createUserRequestOptions = {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${accessToken}`
          },
          params: {
            invite: false
          }
        };
        const resp = await axios.post(
          createUserUrl, createUserRequestData, createUserRequestOptions);
      } catch (createionError) {
        if (![400, 409].includes(createionError.response.status)) {
          console.log("========error creating user======", createionError.response);
        } else {
          console.log("===create user error caused by 400 or 409===");
          console.log(createionError.response);
          console.log("===create user url====", createUserUrl);
          console.log("===create user request data===", createUserRequestData);
          console.log("User already exists, omitting creation...");
        }
      }
    }

    const dt = new Date();
    const loginTime = dt.toISOString();
    const userTrn = `trn:tigergraph:${orgId}::user/${email.toLowerCase()}`;
    const userId = Buffer.from(userTrn).toString('base64');


    const userUrl = `${userEndpoint}/${userId}`;
    try {
      const requestOptions = {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`
        }
      };
      const resp = await axios.get(userUrl, requestOptions);
      var userMeta = resp.data.data.metadata;
      console.log("=========usermeta", userMeta);
      if (userMeta === undefined) {
        userMeta = {};
      }
      userMeta.lastLogin = loginTime;
      const updateLoginRequestData = {
      	metadata: userMeta,
      	enabled: true
   	 };
    	await axios.put(userUrl, updateLoginRequestData, requestOptions);
    } catch (error) {
      console.log("==========error=========", error);
    }
  };

  loginTrigger();

  var params = { id: user.user_id, page: 0, per_page: 50, include_totals: true };
  management.getUserPermissions(params, function (err, permissions) {
    if (err) {
      // Handle error.
      console.log('err: ', err);
      callback(err);
    } else {
      var permissionsArr = map(permissions.permissions, function (permission) {
        return permission.permission_name;
      });
      context.idToken["https://tgcloud.io/permissions"] = permissionsArr;
      if (user.email_verified) {
        context.idToken.email_verified = true;
      }
    }
    callback(null, user, context);
  });
}

```