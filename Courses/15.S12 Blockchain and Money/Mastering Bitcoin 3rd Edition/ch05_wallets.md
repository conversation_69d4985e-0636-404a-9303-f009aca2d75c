---
title: bitcoinbook/ch05_wallets.adoc at develop · bitcoinbook/bitcoinbook
source: https://github.com/bitcoinbook/bitcoinbook/blob/develop/ch05_wallets.adoc
author:
  - "[[GitHub]]"
published: 
created: 2025-02-04
description: Mastering Bitcoin 3rd Edition - Programming the Open Blockchain - bitcoinbook/ch05_wallets.adoc at develop · bitcoinbook/bitcoinbook
tags:
  - clippings
  - flash
---
## Wallet Recovery 钱包恢复

### Independent Key Generation 独立密钥生成

简单的钱包数据库既包含接收比特币的公钥，也包含允许创建授权花费这些比特币所需签名的私钥。其他钱包的数据库可能只包含公钥，或者只包含授权支出交易所需的部分私钥。他们的钱包应用程序通过与外部工具（如硬件签名设备或多签名方案中的其他钱包）协作来生成必要的签名。

现代钱包应用程序并非独立生成密钥，而是使用可重复（确定性）算法从单个随机种子中派生密钥。

#### Deterministic Key Generation 确定性密钥生成

这使我们能够获取一个随机值，并将其转换为几乎无限数量的看似随机的值。更有用的是，以后使用相同的哈希函数和相同的输入（称为“种子”）将产生相同的看似随机的值。

基本顺序确定性密钥生成的逻辑图如[确定性密钥生成：从钱包数据库的种子派生的确定性密钥序列。](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#Type1_wallet)所示。然而，现代钱包应用程序有一种更巧妙的方法来实现这一点，它允许公钥与其对应的私钥分开派生，使得私钥比公钥能够更安全地存储。

![Deterministic Wallet](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_0502.png)

图 2. 确定性密钥生成：从钱包数据库的种子派生的确定性密钥序列。

#### Public Child Key Derivation 公共子密钥推导

考虑我们在[\[公钥推导\]](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#public_key_derivation)中用于使用生成点（*G*）从私钥（*k*）生成公钥（*K*）的操作：

可以通过在等式两边简单地添加相同的值来创建一个派生密钥对，称为子密钥对。

K + ( 123 × G ) == ( k + 123 ) × G  K+(123×G)==(k+123)×G

一个有趣的结果是，向公钥添加 123 可以完全使用公开信息来完成。例如，爱丽丝生成公钥*K*并将其交给鲍勃。鲍勃不知道私钥，但他知道全局常量*G*，所以他可以向公钥添加任何值以生成派生的公钥子密钥。如果他随后告诉爱丽丝他添加到公钥的值，她可以将相同的值添加到私钥，从而生成与鲍勃创建的公钥子密钥相对应的派生私钥子密钥。

换句话说，即使你对父私钥一无所知，也有可能创建子公钥。添加到公钥的值被称为“密钥调整”。如果使用确定性算法生成密钥调整，那么不知道私钥的人可以从单个父公钥创建基本上无限数量的子公钥序列。控制父私钥的人可以使用相同的密钥调整来创建所有相应的子私钥。

这种技术通常用于将钱包应用程序的前端（不需要私钥）与签名操作（需要私钥）分开。例如，爱丽丝的前端将她的公钥分发给想要向她付款的人。后来，当她想花收到的钱时，她可以将她使用的密钥调整提供给一个“硬件签名设备”（有时令人困惑地称为“硬件钱包”），该设备安全地存储她的原始私钥。硬件签名器使用这些调整来推导必要的子私钥，并使用它们对交易进行签名，然后将已签名的交易返回给安全性较低的前端，以便广播到比特币网络。

公共子密钥推导可以生成一系列类似于之前见过的线性密钥序列[确定性密钥生成：从钱包数据库的种子中派生出来的确定性密钥序列。](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#Type1_wallet)，但是现代钱包应用程序使用了一个技巧，即提供一个密钥树而不是单个序列，如下一节所述。

### Hierarchical Deterministic (HD) Key Generation (BIP32)

我们所知的每一个现代比特币钱包默认都使用分层确定性（HD）密钥生成。

![HD wallet](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_0503.png)

Figure 3. HD wallet: a tree of keys generated from a single seed.

#### Seeds and Recovery Codes 种子和恢复码

HD 钱包是一种非常强大的机制，用于管理许多密钥，这些密钥都从单个种子派生而来。如果你的钱包数据库被损坏或丢失，你可以使用原始种子重新生成钱包的所有私钥

#### Backing Up Nonkey Data 备份非关键数据

#### Backing Up Key Derivation Paths 备份密钥派生路径

这意味着从数据丢失中恢复不仅需要知道恢复代码、获取种子的算法（例如 BIP39）和确定性密钥派生算法（例如 BIP32），还需要知道钱包应用程序在生成其分发的特定密钥时在密钥树中使用了哪些路径。

第一种是使用标准路径。每当与钱包应用程序可能想要生成的地址相关的内容发生变化时，就会有人创建一个 BIP，定义要使用的密钥派生路径。例如，BIP44 将`m/44'/0'/0'`定义为 P2PKH 脚本（传统地址）中密钥的使用路径。

第二种解决方案是用恢复码备份路径信息，明确哪个路径与哪个脚本一起使用。我们称此为“显式路径”。

A Wallet Technology Stack in Detail

2023 年初钱包中使用最广泛的技术堆栈：

- BIP39 recovery codes BIP39 恢复码
- BIP32 HD key derivation BIP32 分层确定性密钥推导。
- BIP44-style implicit paths BIP44 风格隐式路径

#### BIP39 Recovery Codes BIP39 恢复码

BIP39 恢复码是单词序列，代表（编码）一个用作种子以派生确定性钱包的随机数。单词序列足以重新创建种子，并由此重新创建所有派生密钥。

##### Generating a recovery code 生成恢复代码

图 4. 生成熵并编码为恢复码。

##### From recovery code to seed 从恢复码到种子。

恢复码代表长度为 128 到 256 位的熵。然后，通过使用[密钥扩展函数 PBKDF2](https://oreil.ly/6lwbd)，该熵被用于推导出更长的（512 位）种子。生成的种子随后被用于构建确定性钱包并推导出其密钥。

密钥扩展函数有两个参数：熵和一个“盐”。

![From recovery code to seed](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_0505.png)

Figure 5. From recovery code to seed.

#### Creating an HD Wallet from the Seed从种子创建HD钱包

HD 钱包由单个“根种子”创建，根种子是一个 128 位、256 位或 512 位的随机数。最常见的情况是，这个种子由恢复码生成或从恢复码解密而来，如前一节所述。

在 HD 钱包中，每个密钥都是从这个根种子确定性地派生出来的，这使得可以从该种子在任何兼容的 HD 钱包中重新创建整个 HD 钱包。这使得只需通过仅传输根种子派生的恢复码，就可以轻松备份、恢复、导出和导入包含数千甚至数百万个密钥的 HD 钱包。

如何从seed中创建出HD wallet的master private key？;;![HDWalletFromRootSeed](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_0506.png)
<!--SR:!2025-02-06,1,130-->

图 6. 从根种子创建主密钥和链码。

根种子被输入到 HMAC-SHA512 算法中，生成的哈希值用于创建一个*主私钥*（*m*）和一个*主链码*（*c*）。

主私钥（*m*）随后使用我们在 [\[公钥推导\]](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#public_key_derivation)中看到的常规椭圆曲线乘法过程 *m* × *G*生成相应的主公钥（*M*）。

主链码（*c*）用于在从父密钥创建子密钥的函数中引入熵，正如我们将在下一节中看到的那样。

##### Private child key derivation 私钥派生子密钥

HD 钱包使用“子密钥推导”（CKD）函数从父密钥推导子密钥。

![ChildPrivateDerivation](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_0507.png)

图 7. 扩展父私钥以创建子私钥。

改变索引允许我们扩展父节点并按顺序创建其他子节点（例如，子节点 0、子节点 1、子节点 2 等）。每个父键可以有 2,147,483,647（2 的 31 次方）个子节点（2 的 31 次方是整个 2 的 32 次方范围的一半，因为另一半被保留用于我们将在本章后面讨论的一种特殊类型的派生）。

##### Extended keys 扩展键

正如我们之前看到的，HD wallet密钥派生函数可基于三个输入在树的任何层级创建子节点: ;; 一个密钥、一个链码以及所需子节点的索引。两个基本要素是密钥和链码，它们组合在一起被称为*扩展密钥*。术语“扩展密钥”也可以被视为“可扩展密钥”，因为这样的密钥可用于派生子节点。
<!--SR:!2025-02-06,1,130-->

扩展密钥的存储和表示方式很简单，就是密钥和链码的连接。有两种类型的扩展密钥。扩展私钥是私钥和链码的组合，可用于推导子私钥（并由此推导子公钥）。扩展公钥是公钥和链码的组合，可用于创建子公钥（仅*公钥*），如[\[公钥推导\]](https://github.com/bitcoinbook/bitcoinbook/blob/develop/#public_key_derivation)中所述。

将扩展密钥视为 HD 钱包树结构中一个分支的根。有了分支的根，就可以推导出分支的其余部分。扩展私钥可以创建一个完整的分支，而扩展公钥只能创建一个公钥分支。

扩展密钥使用 base58check 编码，以便在不同的 BIP32 兼容钱包之间轻松导出和导入。

##### Public child key derivation 公共子密钥推导

如前所述，HD 钱包的一个非常有用的特性是能够从公共父密钥中推导出公共子密钥，而无需拥有私钥。这为我们提供了两种推导子公共密钥的方法：;;要么从子私钥推导，要么直接从父公共密钥推导。
<!--SR:!2025-02-06,1,130-->

因此，扩展公钥可用于推导出 HD 钱包结构中该分支中的所有*公共*密钥（且仅为公共密钥）。

此快捷方式可用于创建仅公钥部署，其中服务器或应用程序拥有扩展公钥的副本，而完全没有私钥。这种部署可以生成无限数量的公钥和比特币地址，但无法花费发送到这些地址的任何资金。同时，在另一个更安全的服务器上，扩展私钥可以派生所有相应的私钥来签署交易并花费资金。

这种解决方案的一个常见应用是在为电子商务应用程序提供服务的 Web 服务器上安装扩展公钥。Web 服务器可以使用公钥派生函数为每笔交易（例如，对于客户购物车）创建一个新的比特币地址。

![ChildPublicDerivation](https://github.com/bitcoinbook/bitcoinbook/raw/develop/images/mbc3_0508.png)

图 8. 扩展父公钥以创建子公钥。

在网络商店中使用扩展公钥

Gabriel 的 HD 钱包通过在不知道私钥的情况下派生公共子密钥的能力提供了更好的解决方案。Gabriel 可以在他的网站上加载一个扩展公钥（xpub），该公钥可用于为每个客户订单派生一个唯一的地址。这个唯一的地址立即提高了隐私性，并且还为每个订单提供了一个唯一的标识符，可用于跟踪哪些发票已被支付。

从扩展公钥（xpub）派生公钥分支的能力非常有用，但也存在潜在风险。访问扩展公钥（xpub）并不能访问子私钥。然而，由于扩展公钥（xpub）包含链码，如果一个子私钥已知或被泄露，它可以与链码一起用于派生所有其他子私钥。单个泄露的子私钥与父链码一起会泄露所有子私钥。更糟糕的是，子私钥与父链码一起可用于推导出父私钥。

简单来说，如果你想利用扩展公钥（xpub）的便利性来派生公钥分支，同时又不想面临链码泄露的风险，那么你应该从强化父节点而不是普通父节点派生它。作为最佳实践，主密钥的一级子节点始终通过强化派生方式派生，以防止主密钥被泄露。
![[Pasted image 20250204184510.png]]
##### HD wallet key identifier (path) HD 钱包密钥标识符（路径）

HD 钱包中的密钥使用“路径”命名约定进行标识，树的每个级别由斜杠（/）字符分隔（见\[b0\]\[表 4-8\]）。从主私钥派生的私钥以“m.”开头。从主公钥派生的公钥以“M.”开头。(m/0/1的含义是;;主私钥的第一个子节点的第二个孙节点是 )
<!--SR:!2025-02-06,1,130-->
因此，主私钥的第一个子私钥是 m/0。第一个子公钥是 M/0。第一个子节点的第二个孙节点是 m/0/1，依此类推。

##### Navigating the HD wallet tree structure导航 HD 钱包树状结构。

BIP44 规定结构由五个预定义的树级别组成：

```
m / purpose' / coin_type' / account' / change / address_indexm / purpose' / coin_type' / account' / change / address_indexm / purpose' / coin_type' / account' / change / address_index
```