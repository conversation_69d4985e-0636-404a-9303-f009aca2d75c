---
title: "在 LlamaIndex 中创建智能工作流 - Hugging Face Agents Course"
source: "https://huggingface.co/learn/agents-course/zh-CN/unit2/langgraph/when_to_use_langgraph"
author:
published:
created: 2025-05-26
description: "We’re on a journey to advance and democratize artificial intelligence through open source and open science."
tags:
  - "clippings"
---
## 何时应该使用 LangGraph ？

### 控制 vs 自由度

在设计 AI 应用时，你面临 **控制** 与 **自由度** 的基本权衡：

- **自由度** 赋予 LLM 更多创造性地解决问题的空间
- **控制** 能确保可预测的行为并维持安全护栏

当你需要 **对应用程序保持控制** 时，LangGraph 特别有价值。它为你提供了构建可预测流程应用程序的工具，同时仍能利用 LLM 的强大能力。

在我看来，`LangGraph` 是市场上最适合生产环境的智能体框架。

## LangGraph 如何工作？

其核心在于，`LangGraph` 使用有向图结构来定义应用程序的流程：

- **节点** 表示独立的处理步骤（如调用 LLM、使用工具或做出决策）
- **边** 定义步骤之间可能的转换
- **状态** 由用户定义和维护，并在执行期间在节点间传递。当决定下一个目标节点时，我们查看的就是当前状态