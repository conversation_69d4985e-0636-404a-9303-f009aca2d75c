# 🏠 Dashboard

> [!info]+ Welcome to Your Knowledge Vault
> Today is `=date(today)` | Last modified: `=this.file.mtime`

```meta-bind-button
style: primary
label: 📝 Open Daily Note
hidden: false
id: daily-note
action: { "type": "command", "command": "daily-notes" }
```
---
> [!multi-column]
>
>> [!note]+ Important Notes
>> - [[Depression|🧠 Depression]]
>> - [[资源Resources|📚 Resources]]
>> - [[灵感Ideas|💡 Ideas]]
>> - [[人生观Life lessons|🌱 Life Lessons]]
>> - [[Inbox\|📥 Inbox]]
>
>> [!tip]+ 🎯 Make Progress
>> - [[projects_index|📋 Projects Index]]
>> - [[guitar_practice|🎸 Guitar Practice]]
>> - [[meditation_practice|🧘 Meditation Practice]]
>> - [[碎片知识News|📰 碎片News]]

---

## Task Management

### Today's Focus
```tasks
(scheduled after yesterday) or (due after yesterday)
not done
path does not include TG
show tree
shortmode
group by scheduled
```

### Unscheduled Tasks
```tasks
no scheduled date
not done
path does not include TG
show tree
shortmode
limit 10
```

---

## 🚀 Active Projects
```dataview
TABLE
  category as Category,
  choice(progress != null AND target != null AND target != 0, round((progress / target) * 100) + "%", "0%") as Progress,
  started as Started
FROM #project OR #course OR #book
WHERE !contains(file.path, "Template/") AND (status = "🔄 In Progress" OR contains(status, "In Progress"))
SORT file.mtime DESC
```

---

## 📚 Knowledge Areas
- todo
---

## 🗂️ Vault Statistics
- **Total Notes**: `$=dv.pages().length`
- **Notes This Week**: `$=dv.pages().where(p => p.file.ctime >= dv.date('today') - dv.duration('7 days')).length`
- **Orphaned Notes**: `$=dv.pages().where(p => p.file.inlinks.length === 0 && p.file.outlinks.length === 0).length`

---