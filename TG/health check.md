## Ref
- [GUS design](https://graphsql.atlassian.net/wiki/spaces/~************************/pages/**********/Health+check+on+Admin+Portal+design+doc)
## Tasks
- [x] handle error when start health checks
- [x] report component
- [x] script list
- [x] count of great, warning, critical
- [x] loading
- [x] recover
- [x] wip product#269 er#1138 tools#1983 gus#887
- [x] fix layout bug in initial load time
- [x] remove useless fields: type, error msg
- [x] test new wip package without cqrs PR
- [x] approve PRs
- [x] mit tools#1983 gus#887
## Design
![[assets/health check 2024-06-17 09.51.59.excalidraw.md]]
## Feature list
- show scripts and their latest reports
	- get scripts
	- get report ids from last 7 days and get report detail
- show empty page
	- if no reports
- select scripts to run
- running dialog
## Sync with TSE
- fix running script error to return completed report
- ensure every parameter has default value
- recommendation links
## Features
- list all scripts and their latest reports
- run multiple scripts
	- show loading
		- show running scripts, failed scripts, completed scripts
	- when completed
		- update reports
	- what if users close the page and re-enter
		- they need to refresh reports manually
## GUI Design
## PR
- [APPS-2662 feat(health check): Add endpoints for list and run health check scripts;](https://github.com/tigergraph/gus/pull/887)
## Tasks
- [x] add an endpoint to GUS to call ListHealthCheckScripts
## Notes
- cli -> ctrl -> exe -> informant
- need to add api to gus to call run/list scripts
- call ctrl to run healthcheck
```go
ctrlResp, err := ctrl.RunHealthCheck(ctx, &pb.RunHealthCheckRequest{
		ModuleName: strings.TrimSpace(hcConfig.Module),
		Params:     moduleParams,
		SpanId:     reportId,
	})
```
- polling informant for report
```go
	maxTries := int(report_wait_timeout.Seconds()) / int(report_wait_interval.Seconds())
	for i := 0; i < maxTries; i++ {
		resp, err = ifm.GetHealthCheckReport(ctx, &pb.GetHealthCheckReportRequest{
			ReportId: reportId,
		})
		if err != nil {
			return err
		}
		if resp.Report != nil {
			break
		}
		fmt.Print(".")
		time.Sleep(report_wait_interval)
	}

```
## PR
- [SUPP-54 feat(healthcheck): Add run health check to controller;](https://github.com/tigergraph/cqrs/pull/3503/files#top)
- [SUPP-44 feat(healthcheck): Add models and APIs for Health check script running / listing;](https://github.com/tigergraph/cqrs/pull/3431/files#top)
- [SUPP-46 feat(healthcheck): Add Informant API for Health Check Report with stubs for service;](https://github.com/tigergraph/cqrs/pull/3491)
- [SUPP-59 feat(healthcheck): Add cli for running health checks;](https://github.com/tigergraph/cqrs/pull/3511/files#top)
## API: List and run scripts
```protobuf
message RunHealthCheckRequest {
  string ModuleName = 1;
  repeated HealthCheckModuleParam Params = 2;
  string SpanId = 3;
}

message RunHealthCheckResponse {
    PbError Error = 1;
}

rpc RunHealthCheck(RunHealthCheckRequest) returns (RunHealthCheckResponse) {}

```

```protobuf
message ListHealthCheckScriptsRequest {}

message ListHealthCheckScriptsResponse {
    repeated Script Scripts = 1;
    PbError Error = 2;
}

// The type of the script
enum ScriptType {
    SCRIPT_TYPE_UNKNOWN = 0; // Need to know when client doesn't specify a type to filter on
    HEALTH_CHECK = 1; // User triggerable health check
    METRIC = 2; // Scheduled scripts that generate metrics
}

// Parameter types for the script
enum ScriptParamType {
    STRING = 0;
    INT = 1;
    FLOAT = 2;
    BOOL = 3;
}

// Parameters for the script
message ScriptParam {
    string Name = 1;
    string Description = 2;
    ScriptParamType Type = 4;
    bool Required = 5;
    string DefaultValue = 6;
}

// The script metadata
message Script {
    string Name = 1; // unique logical name
    ScriptType Type = 2;
    string Description = 3; // Used for display purposes, when listing scripts
    repeated ScriptParam Params = 5;
}

rpc ListHealthCheckScripts(ListHealthCheckScriptsRequest) returns (ListHealthCheckScriptsResponse) {}
```
## API: get and list reports
- call informant API
```protobuf
message HealthCheckReportItem {
	string Name = 1; // the fully qualified name of the item
    string Message = 2; // a message describing the check
    HealthCheckItemStatus Status = 3;
    repeated KV KVs = 4; // dimensions for the check
    int64 TimestampNS = 5;
    // other info here
}

message Metric {
    string Name = 1;
    MetricType Type = 2;
    float Value = 3;
    repeated KV KVs = 4; // KVs are key-value pairs e.g.: "host_id"="m1"
    int64 TimestampNS = 5;
}

// Represents the output of a health check
// Health Check scripts will output a list of these, one per line
// An output can be one of two types: a metric or a health check item
message HealthCheckReportOutput {
    Metric Metric = 1;
    HealthCheckReportItem Item = 2;
}

// The health check report
message HealthCheckReport {
    string Id = 1; // The unique identifier for the report, set to SpanId when user triggered
    string ModuleName = 2; // The name of the script that generated the report
    repeated HealthCheckModuleParam Parameters = 3;
    repeated HealthCheckReportOutput Output = 4; // The output of the script
    HealthCheckReportStatus Status = 5;
    string ErrorMessage = 6; // If the script/report failed, this will contain the error message
    int64 StartedAt = 7; // The time the script started running
    int64 CompletedAt = 8; // The time the script completed running
    string HostId = 9; // The host that ran the script
    ScriptType Type = 10; // The type of script that generated the report
    int32 ExpectedCount = 11; // The total number of nodes this script was expected to run on
}

// Returns an individual health check report, with all metrics/items aggregated
message GetHealthCheckReportRequest {
    string ReportId = 1; // Can be SpanID if the report was user generated
}

message GetHealthCheckReportResponse {
    PbError Error = 1;
    HealthCheckReport Report = 2;
}

// Returns a list of health check reports
// supports several filters
// note: does not include items/metrics in the reports, only the report details
// use GetHealthCheckReportRequest to get the full report
message ListHealthCheckReportsRequest {
    ScriptType Type = 1; // ScriptType of the report ie. HEALTH_CHECK, METRIC, etc
    string ModuleName = 2; // Module Name that produced the report. This typically corresponds the script name
    TimeRange TimeRange = 3; // Time range to filter the list of reports by. Based on CompletedAt
    int32 Limit = 4; // Limit the number of reports returned
}

message ListHealthCheckReportsResponse {
    PbError Error = 1;
    repeated HealthCheckReport Reports = 2;
}
```

## Response example
- curl
```bash
# list
curl -X GET "http://localhost:14240/api/healthcheck"

# run
curl -X POST 'http://localhost:14240/api/healthcheck/run' --data '{"items":[{"module_name":"vtx_consistency_over_replica","params":[]},{"module_name":"query_error_codes","params":[]}]}'
```

```
curl 'http://**************:14240/api/informant/healthcheck/get/report' \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Accept-Language: en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7' \
  -H 'Cache-Control: no-cache' \
  -H 'Content-Type: application/json' \
  -H 'Cookie: TigerGraphApp=f524bdd4-f9d5-42ec-920f-4901f45e8a51' \
  -H 'Origin: http://**************:14240' \
  -H 'Pragma: no-cache' \
  -H 'Proxy-Connection: keep-alive' \
  -H 'Referer: http://**************:14240/admin/' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-test-env: cloud-portal#TCE-4337-2' \
  --data-raw '{"ReportId":"hc-c48b332426dd4eacb24acae3784c1fb8"}' \
  --insecure
```
### List report meta
```
{
    "CompletedAt": "**********",
    "ExpectedCount": 1,
    "HostId": "m1",
    "Id": "hc-21470fbe08524b61a619205c71919877",
    "ModuleName": "example",
    "StartedAt": "**********",
    "Status": "COMPLETED",
    "Type": "HEALTH_CHECK"
}
```