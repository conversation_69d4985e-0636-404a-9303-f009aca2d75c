## Global vs Vertex Attached Accumulator
- vertex attached accumulator只能在select语句中更新
## Edge Attached Accumulator
```sql
USE GRAPH financialGraph

CREATE OR REPLACE QUERY q9 (string acctName) SYNTAX v3 {

  OrAccum EDGE @visited;

  v = SELECT b
      FROM (a:Account {name: acctName})-[e:transfer]->(b:Account)
      ACCUM  e.@visited += TRUE;

  v = SELECT b
      FROM (a:Account)-[e:transfer]->(b:Account)
      WHERE NOT e.@visited;

  //output each v and their static attribute and runtime accumulators' state
  PRINT v;

}

//it is only supported for single node, or single node mode in distributed enviroment
install query -single q9
run query q9("Jenny")
```