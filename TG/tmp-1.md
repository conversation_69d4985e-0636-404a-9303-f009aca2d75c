## Globaldesigner
- globaldesigner
	- 没有对其他人创建query的权限
- localdesigner
	- 没有其他query权限
- localadmin
	- 有所有query权限
## [TCE-6452](https://graphsql.atlassian.net/browse/TCE-6452)
- /showprocesslist/{graph_name} (from `/endpoint`)
```json
{
    "graph_name": {
        "default": "",
        "max_count": 1,
        "min_count": 0,
        "type": "STRING"
    },
    "seconds": {
        "max_count": 1,
        "min_count": 1,
        "type": "UINT32"
    },
    "segments": {
        "default": 10,
        "max_count": 1,
        "max_value": 100,
        "min_count": 0,
        "min_value": 1,
        "type": "UINT32"
    }
}
```
- /query/new_query_6 (from `/endpoint`)
```json
{
    "int_set": {
        "max_count": 2147483647,
        "min_count": 0,
        "type": "INT64"
    },
    "int_var": {
        "min_count": 0,  // no max_count?, no default_value?
        "type": "INT64"
    },
    "query": {
        "default": "new_query_6",
        "type": "STRING"
    },
    "read_committed": {
        "max_count": 1,
        "min_count": 0,
        "type": "BOOL"
    }
}
```
- new_query_6 (from `/graph_name/info`)
```json
{
    "int_set": {
        "index": 0,
        "max_count": 2147483647,
        "min_count": 0,
        "type": "INT64"
    },
    "int_var": {
        "index": 1,
        "min_count": 0,
        "type": "INT64"
    },
    "query": {
        "default": "new_query_6",
        "type": "STRING"
    }
}
```
## [[TG/marketplace]]
## Table parse logic
- parseTable(input): parse input to multiple tables
	- if not an array, return [k k]
	- if all objs has same keys, parse to a single table
	- else: parse every key in every obj to a single table
- parseIntoTable(objs): parse to a single table

[[Monitor Amazon SES]]
[[Query Parameters]]
## Optimize cloud-portal initial bundle size
## Apple Phantom query issue
- q1 364.RESTPP_1_1.1742506702347.N
	- started: 3/20/2025, 2:38:22 PM(browser time)
	- failed at?
- q2 131751.RESTPP_1_1.1742578703373.N
	- started: 3/21/2025, 10:38:23 AM(browser time)
		- 0321 18:24:00(restpp time)
	- server ahead browser: 7h46min
## TCE-6419 Redirect users to TGCloud v3 for paying v3 customers
- for paying v3 users
	- tgcloud.io -> savanna fe
		- after login, check org_id, user_last_visit_version
			- redirect to v3
- add field user_last_visit_version to iam service
	- users access classic.tgcloud.io: set user_last_visit_version to v3
	- users click switch v4 button on v3:  set user_last_visit_version to v4
## [JP Morgan Chase] Failed to create query and report "query not found" double merge to 4.2.0
- [x] #task [JP Morgan Chase] Failed to create query and report "query not found" double merge to 4.2.0 ⏳ 2025-03-17 ✅ 2025-03-17
	- [x] #task Merge: https://github.com/tigergraph/tools/pull/2409 ⏳ 2025-03-17 ✅ 2025-07-07
## [GS][4.2.0] New GraphStudio Privileges
- [-] #task APPS-2536 [GS][4.2.0] New GraphStudio Privileges TigerGraph ⏳ 2025-03-13 ❌ 2025-03-14
- PR
	- [x] tools
		- [x] gray buttons in the home page
		- [x] gray buttons in the headers
		- [x] check privileges in the header
	- [x] gle
	- [x] regress
- self-test
	- replace home binary
	- create a user with globalobserver role, access tools
	- create a user with ACCESS_GRAPHSTUDIO privilege, access tools

## 2025-03-07 demo
## changes to tg_downloads.csv for community version
- [x] test use tg_downloads_test.csv
- [ ] column1: `enterprise` > "community"
- [ ] column2: `4.2.0-alpha` > `4.2.0`
- [ ] column3: 'Community Edition (Alpha)'
- [ ] modify in prod: `tg_downloads.csv` and update lambda/pages
## [[JP Morgan Chase] Failed to create query and report "query not found"](https://graphsql.atlassian.net/browse/APPS-3624)
- [-] #task [JP Morgan Chase] Failed to create query and report "query not found" double merge to 4.2.0 ⏳ 2025-03-13 ❌ 2025-03-13
- [x] #task build 4.1.1 patch for jpmc ✅ 2025-07-14
- user can't manipute/see queries created by him
	- method1: revert rbac
## AI Poc
- [x] try deepseek endpoints
- [x] extract schema parts from gsql `ls` output
- [x] generate system message based on the schema
- [x] construct user message based on the user input and request ai
- [x] insert ai response content to the editor
- [x] #task Try different AI models ⏳ 2025-02-28 ✅ 2025-02-28
- [x] #task Insert generated query to editor and improve UI ⏳ 2025-02-28 ✅ 2025-02-28
## [APPS-3577](https://graphsql.atlassian.net/browse/APPS-3577) Update download page to facilitate with the community edition
- [x] #task remove request license button ⏳ 2025-02-25 ✅ 2025-02-25
- [x] #task remove license link from community version email ⏳ 2025-02-25 ✅ 2025-02-25
- [x] #task deploy front-end and lambda ⏳ 2025-02-25 ✅ 2025-02-25
- [x] #task update lambda/front-end to support download community/enterprise versions ⏳ 2025-02-24 ✅ 2025-02-24
- [x] #task disable kafka ssl, nginx ssl ⏳ 2025-02-25 ✅ 2025-02-25
- [x] #task Performance review ⏳ 2025-02-24 ✅ 2025-02-24
## APPS-3575 Disable enterprise features in community versio
 - [x] disable ldap, sso menu and router
 - [x] proxy user
 - [x] user-defined roles
 - [x] system(cdc) route an guard
## useResize
- props
	- type: 'horizontal' | 'vertical'
	- target: 可调整size的元素
	- handle：添加拖动时间的元素
- methods
	- 添加事件
		- mousedown: 获取元素的初始size；起始坐标
		- mousemove: 根据坐标变化，修改元素size

## [TCE-5554](https://graphsql.atlassian.net/browse/TCE-5554)
### Task
- [x] split the sse messages, print split messages to the console
- [x] remove `errorMsg` field in `CmdResult`, use `result` instead
- [x] change the message listeners in ResultTab component
- [x] refactor the result panel component if necessary
- [x] adjust the result component base on the new design
- [ ] use jsonbig
- [x] #task  performance issue ⏳ 2025-02-19 ✅ 2025-02-19
### Notes
- 何时一个chunk结束
	- 当gsql.done = true
	- 每个消息结束时
		- 当前chunk以 `{\n`开头，尝试parseJSON
	- 消息以`{\n`开头
		- 若last chunk存在content，且last chunk不以`{\n`开头
#### Procedures
- get sse message
	- if `msg` begins at `{\n`
		- mark `coundBeJSON` as true
		- append to`currentMsgPart`
	- else
		- append to `currentMsgPart`
		- if `coundBeJSON` is true, try parse it as JSON
#### Types
- `MessagePart`
	- type: 'text' | 'json'
	- content: string | object